/**
 * ✅ PERFORMANCE FIX: Performance Monitoring Hook
 * 
 * Provides performance monitoring capabilities for file explorer operations
 * including timing, memory usage tracking, and performance alerts.
 */

import { useCallback, useRef, useEffect } from 'react';

export interface PerformanceMetrics {
  operationName: string;
  startTime: number;
  endTime: number;
  duration: number;
  memoryUsage?: {
    used: number;
    total: number;
  };
  metadata?: Record<string, any>;
}

export interface PerformanceThresholds {
  slowOperationMs: number;
  memoryWarningMB: number;
  maxOperationsToTrack: number;
}

const DEFAULT_THRESHOLDS: PerformanceThresholds = {
  slowOperationMs: 1000, // 1 second
  memoryWarningMB: 100,   // 100 MB
  maxOperationsToTrack: 50
};

/**
 * Hook for monitoring performance of file explorer operations
 */
export const usePerformanceMonitor = (thresholds: Partial<PerformanceThresholds> = {}) => {
  const metricsRef = useRef<PerformanceMetrics[]>([]);
  const activeOperationsRef = useRef<Map<string, number>>(new Map());
  const config = { ...DEFAULT_THRESHOLDS, ...thresholds };

  // Get memory usage (if available)
  const getMemoryUsage = useCallback(() => {
    if (typeof window !== 'undefined' && 'performance' in window && 'memory' in (window.performance as any)) {
      const memory = (window.performance as any).memory;
      return {
        used: Math.round(memory.usedJSHeapSize / 1024 / 1024), // MB
        total: Math.round(memory.totalJSHeapSize / 1024 / 1024) // MB
      };
    }
    return undefined;
  }, []);

  // Start timing an operation
  const startOperation = useCallback((operationName: string, metadata?: Record<string, any>) => {
    const startTime = performance.now();
    const operationId = `${operationName}-${startTime}`;
    
    activeOperationsRef.current.set(operationId, startTime);
    
    console.log(`🚀 Starting operation: ${operationName}`, metadata);
    
    return operationId;
  }, []);

  // End timing an operation
  const endOperation = useCallback((operationId: string, metadata?: Record<string, any>) => {
    const endTime = performance.now();
    const startTime = activeOperationsRef.current.get(operationId);
    
    if (!startTime) {
      console.warn(`⚠️ No start time found for operation: ${operationId}`);
      return null;
    }
    
    const duration = endTime - startTime;
    const operationName = operationId.split('-')[0];
    const memoryUsage = getMemoryUsage();
    
    const metrics: PerformanceMetrics = {
      operationName,
      startTime,
      endTime,
      duration,
      memoryUsage,
      metadata
    };
    
    // Add to metrics history
    metricsRef.current.push(metrics);
    
    // Keep only recent metrics
    if (metricsRef.current.length > config.maxOperationsToTrack) {
      metricsRef.current = metricsRef.current.slice(-config.maxOperationsToTrack);
    }
    
    // Clean up active operation
    activeOperationsRef.current.delete(operationId);
    
    // Log performance info
    const durationMs = Math.round(duration);
    if (duration > config.slowOperationMs) {
      console.warn(`🐌 Slow operation detected: ${operationName} took ${durationMs}ms`, metadata);
    } else {
      console.log(`✅ Operation completed: ${operationName} in ${durationMs}ms`, metadata);
    }
    
    // Memory warning
    if (memoryUsage && memoryUsage.used > config.memoryWarningMB) {
      console.warn(`🧠 High memory usage: ${memoryUsage.used}MB / ${memoryUsage.total}MB`);
    }
    
    return metrics;
  }, [config, getMemoryUsage]);

  // Measure a function execution
  const measureOperation = useCallback(async <T>(
    operationName: string,
    operation: () => Promise<T> | T,
    metadata?: Record<string, any>
  ): Promise<T> => {
    const operationId = startOperation(operationName, metadata);
    
    try {
      const result = await operation();
      endOperation(operationId, { ...metadata, success: true });
      return result;
    } catch (error) {
      endOperation(operationId, { ...metadata, success: false, error: error.message });
      throw error;
    }
  }, [startOperation, endOperation]);

  // Get performance statistics
  const getStats = useCallback(() => {
    const metrics = metricsRef.current;
    
    if (metrics.length === 0) {
      return {
        totalOperations: 0,
        averageDuration: 0,
        slowOperations: 0,
        fastestOperation: null,
        slowestOperation: null,
        operationCounts: {}
      };
    }
    
    const durations = metrics.map(m => m.duration);
    const operationCounts = metrics.reduce((acc, m) => {
      acc[m.operationName] = (acc[m.operationName] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    const slowOperations = metrics.filter(m => m.duration > config.slowOperationMs);
    const fastestOperation = metrics.reduce((fastest, current) => 
      current.duration < fastest.duration ? current : fastest
    );
    const slowestOperation = metrics.reduce((slowest, current) => 
      current.duration > slowest.duration ? current : slowest
    );
    
    return {
      totalOperations: metrics.length,
      averageDuration: durations.reduce((sum, d) => sum + d, 0) / durations.length,
      slowOperations: slowOperations.length,
      fastestOperation,
      slowestOperation,
      operationCounts,
      recentMetrics: metrics.slice(-10) // Last 10 operations
    };
  }, [config]);

  // Clear metrics history
  const clearMetrics = useCallback(() => {
    metricsRef.current = [];
    activeOperationsRef.current.clear();
    console.log('🧹 Performance metrics cleared');
  }, []);

  // Log performance summary
  const logSummary = useCallback(() => {
    const stats = getStats();
    
    console.group('📊 File Explorer Performance Summary');
    console.log(`Total Operations: ${stats.totalOperations}`);
    console.log(`Average Duration: ${Math.round(stats.averageDuration)}ms`);
    console.log(`Slow Operations: ${stats.slowOperations}`);
    console.log('Operation Counts:', stats.operationCounts);
    
    if (stats.fastestOperation) {
      console.log(`Fastest: ${stats.fastestOperation.operationName} (${Math.round(stats.fastestOperation.duration)}ms)`);
    }
    
    if (stats.slowestOperation) {
      console.log(`Slowest: ${stats.slowestOperation.operationName} (${Math.round(stats.slowestOperation.duration)}ms)`);
    }
    
    console.groupEnd();
  }, [getStats]);

  // Auto-log summary periodically in development
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      const interval = setInterval(() => {
        if (metricsRef.current.length > 0) {
          logSummary();
        }
      }, 30000); // Every 30 seconds
      
      return () => clearInterval(interval);
    }
  }, [logSummary]);

  return {
    startOperation,
    endOperation,
    measureOperation,
    getStats,
    clearMetrics,
    logSummary,
    metrics: metricsRef.current,
    activeOperations: Array.from(activeOperationsRef.current.keys())
  };
};

/**
 * Hook for monitoring file system operation performance specifically
 */
export const useFileSystemPerformanceMonitor = () => {
  const monitor = usePerformanceMonitor({
    slowOperationMs: 2000, // File operations can be slower
    memoryWarningMB: 150,
    maxOperationsToTrack: 100
  });

  const measureFileOperation = useCallback(async <T>(
    operationType: 'load' | 'save' | 'delete' | 'create' | 'watch' | 'refresh',
    filePath: string,
    operation: () => Promise<T> | T
  ): Promise<T> => {
    return monitor.measureOperation(
      `file-${operationType}`,
      operation,
      { filePath, type: operationType }
    );
  }, [monitor]);

  return {
    ...monitor,
    measureFileOperation
  };
};
