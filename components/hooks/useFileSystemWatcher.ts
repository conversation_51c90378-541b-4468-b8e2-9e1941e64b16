/**
 * ✅ CRITICAL FIX: useFileSystemWatcher Hook
 * 
 * A reusable hook for monitoring file system changes in project directories.
 * This provides a clean interface for components to watch for file changes
 * without dealing with the complexity of the FileSystemMonitor directly.
 */

import { useEffect, useRef, useCallback } from 'react';

export interface FileSystemWatchOptions {
  recursive?: boolean;
  includePatterns?: string[];
  excludePatterns?: string[];
  debounceMs?: number;
}

export interface FileSystemEvent {
  type: 'created' | 'modified' | 'deleted' | 'renamed';
  path: string;
  oldPath?: string; // For rename events
  isDirectory?: boolean;
  timestamp: number;
}

export type FileSystemEventHandler = (event: FileSystemEvent) => void;

const DEFAULT_OPTIONS: Required<FileSystemWatchOptions> = {
  recursive: true,
  includePatterns: ['*'],
  excludePatterns: [
    'node_modules/**',
    '.git/**',
    '.next/**',
    'dist/**',
    'build/**',
    '*.log',
    '.DS_Store',
    'Thumbs.db',
    '*.tmp',
    '*.temp'
  ],
  debounceMs: 500
};

/**
 * Hook for watching file system changes in a specific path
 * 
 * @param watchPath - The path to watch for changes
 * @param onEvent - Callback function to handle file system events
 * @param options - Configuration options for the watcher
 * @returns Object with watcher status and control functions
 */
export const useFileSystemWatcher = (
  watchPath: string | null,
  onEvent: FileSystemEventHandler,
  options: FileSystemWatchOptions = {}
) => {
  const watchIdRef = useRef<string | null>(null);
  const isWatchingRef = useRef(false);
  const debounceTimerRef = useRef<{ [key: string]: NodeJS.Timeout }>({});
  const eventHandlerRef = useRef<FileSystemEventHandler>(onEvent);

  // Update event handler ref when it changes
  useEffect(() => {
    eventHandlerRef.current = onEvent;
  }, [onEvent]);

  // Merge options with defaults
  const watchOptions = { ...DEFAULT_OPTIONS, ...options };

  // Debounced event handler to prevent excessive updates
  const debouncedEventHandler = useCallback((event: FileSystemEvent) => {
    const key = `${event.type}-${event.path}`;
    
    // Clear existing timer for this event
    if (debounceTimerRef.current[key]) {
      clearTimeout(debounceTimerRef.current[key]);
    }
    
    // Set new timer
    debounceTimerRef.current[key] = setTimeout(() => {
      eventHandlerRef.current(event);
      delete debounceTimerRef.current[key];
    }, watchOptions.debounceMs);
  }, [watchOptions.debounceMs]);

  // Start watching
  const startWatching = useCallback(async () => {
    if (!watchPath || isWatchingRef.current) {
      return false;
    }

    try {
      const { globalFileSystemMonitor } = await import('../background/file-system-monitor');
      
      console.log(`🔍 Starting file system watcher for: ${watchPath}`);
      
      watchIdRef.current = await globalFileSystemMonitor.watchPath(watchPath, {
        recursive: watchOptions.recursive,
        includePatterns: watchOptions.includePatterns,
        excludePatterns: watchOptions.excludePatterns
      });

      // Set up event handler
      globalFileSystemMonitor.onFileSystemEvent(debouncedEventHandler);
      
      isWatchingRef.current = true;
      console.log(`✅ File system watcher active for: ${watchPath}`);
      return true;
    } catch (error) {
      console.error(`❌ Failed to start file system watcher for ${watchPath}:`, error);
      return false;
    }
  }, [watchPath, watchOptions, debouncedEventHandler]);

  // Stop watching
  const stopWatching = useCallback(async () => {
    if (!watchIdRef.current || !isWatchingRef.current) {
      return;
    }

    try {
      const { globalFileSystemMonitor } = await import('../background/file-system-monitor');
      await globalFileSystemMonitor.unwatchPath(watchIdRef.current);
      
      // Clear all debounce timers
      Object.values(debounceTimerRef.current).forEach(timer => clearTimeout(timer));
      debounceTimerRef.current = {};
      
      watchIdRef.current = null;
      isWatchingRef.current = false;
      console.log(`🔄 File system watcher stopped for: ${watchPath}`);
    } catch (error) {
      console.error(`❌ Failed to stop file system watcher:`, error);
    }
  }, [watchPath]);

  // Auto-start/stop watching based on watchPath changes
  useEffect(() => {
    if (watchPath) {
      startWatching();
    } else {
      stopWatching();
    }

    // Cleanup on unmount or path change
    return () => {
      stopWatching();
    };
  }, [watchPath, startWatching, stopWatching]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      // Clear all debounce timers
      Object.values(debounceTimerRef.current).forEach(timer => clearTimeout(timer));
      debounceTimerRef.current = {};
    };
  }, []);

  return {
    isWatching: isWatchingRef.current,
    watchId: watchIdRef.current,
    startWatching,
    stopWatching,
    watchPath
  };
};

/**
 * Hook for watching the currently active project
 * 
 * @param onEvent - Callback function to handle file system events
 * @param options - Configuration options for the watcher
 * @returns Object with watcher status and active project info
 */
export const useActiveProjectWatcher = (
  onEvent: FileSystemEventHandler,
  options: FileSystemWatchOptions = {}
) => {
  const activeProjectPathRef = useRef<string | null>(null);

  // Get active project path
  useEffect(() => {
    const getActiveProjectPath = async () => {
      try {
        const { activeProjectService } = await import('../../services/active-project-service');
        const activeProject = activeProjectService.getActiveProject();
        activeProjectPathRef.current = activeProject?.path || null;
      } catch (error) {
        console.error('Failed to get active project path:', error);
        activeProjectPathRef.current = null;
      }
    };

    getActiveProjectPath();
  }, []);

  const watcher = useFileSystemWatcher(activeProjectPathRef.current, onEvent, options);

  return {
    ...watcher,
    activeProjectPath: activeProjectPathRef.current
  };
};

/**
 * Hook for watching multiple paths simultaneously
 * 
 * @param watchPaths - Array of paths to watch
 * @param onEvent - Callback function to handle file system events
 * @param options - Configuration options for the watchers
 * @returns Object with watcher status for all paths
 */
export const useMultiPathWatcher = (
  watchPaths: string[],
  onEvent: FileSystemEventHandler,
  options: FileSystemWatchOptions = {}
) => {
  const watchersRef = useRef<{ [path: string]: any }>({});

  // Create individual watchers for each path
  watchPaths.forEach(path => {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    const watcher = useFileSystemWatcher(path, onEvent, options);
    watchersRef.current[path] = watcher;
  });

  const allWatching = Object.values(watchersRef.current).every(w => w.isWatching);
  const anyWatching = Object.values(watchersRef.current).some(w => w.isWatching);

  return {
    watchers: watchersRef.current,
    allWatching,
    anyWatching,
    watchPaths
  };
};
