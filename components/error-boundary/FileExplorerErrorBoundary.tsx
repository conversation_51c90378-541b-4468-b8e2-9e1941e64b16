/**
 * ✅ ERROR HANDLING FIX: File Explorer Error Boundary
 * 
 * Comprehensive error boundary specifically designed for file explorer operations.
 * Provides graceful fallbacks, error reporting, and recovery mechanisms.
 */

"use client"

import React, { Component, ReactNode } from 'react';
import { AlertTriangle, RefreshCw, Home, FileX } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

interface ErrorInfo {
  componentStack: string;
  errorBoundary?: string;
  errorBoundaryStack?: string;
}

interface FileExplorerErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string;
  retryCount: number;
  lastErrorTime: number;
}

interface FileExplorerErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  maxRetries?: number;
  resetTimeoutMs?: number;
}

export class FileExplorerErrorBoundary extends Component<
  FileExplorerErrorBoundaryProps,
  FileExplorerErrorBoundaryState
> {
  private resetTimeoutId: NodeJS.Timeout | null = null;

  constructor(props: FileExplorerErrorBoundaryProps) {
    super(props);

    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: '',
      retryCount: 0,
      lastErrorTime: 0
    };
  }

  static getDerivedStateFromError(error: Error): Partial<FileExplorerErrorBoundaryState> {
    // Generate unique error ID for tracking
    const errorId = `fe-error-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    return {
      hasError: true,
      error,
      errorId,
      lastErrorTime: Date.now()
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log error details
    console.error('🚨 File Explorer Error Boundary caught an error:', error);
    console.error('Error Info:', errorInfo);

    // Update state with error info
    this.setState(prevState => ({
      errorInfo,
      retryCount: prevState.retryCount + 1
    }));

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Report error to monitoring service (if available)
    this.reportError(error, errorInfo);

    // Auto-reset after timeout if configured
    if (this.props.resetTimeoutMs) {
      this.scheduleReset();
    }
  }

  private reportError = (error: Error, errorInfo: ErrorInfo) => {
    try {
      // Log structured error data for monitoring
      const errorReport = {
        errorId: this.state.errorId,
        message: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href,
        retryCount: this.state.retryCount
      };

      console.error('📊 File Explorer Error Report:', errorReport);

      // Send to error reporting service (implement as needed)
      // Example: errorReportingService.report(errorReport);
    } catch (reportingError) {
      console.error('Failed to report error:', reportingError);
    }
  };

  private scheduleReset = () => {
    if (this.resetTimeoutId) {
      clearTimeout(this.resetTimeoutId);
    }

    this.resetTimeoutId = setTimeout(() => {
      console.log('🔄 Auto-resetting File Explorer Error Boundary');
      this.handleReset();
    }, this.props.resetTimeoutMs || 5000);
  };

  private handleReset = () => {
    console.log('🔄 Resetting File Explorer Error Boundary');
    
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: '',
      retryCount: 0,
      lastErrorTime: 0
    });

    if (this.resetTimeoutId) {
      clearTimeout(this.resetTimeoutId);
      this.resetTimeoutId = null;
    }
  };

  private handleRetry = () => {
    const maxRetries = this.props.maxRetries || 3;
    
    if (this.state.retryCount >= maxRetries) {
      console.warn(`⚠️ Max retries (${maxRetries}) reached for File Explorer`);
      return;
    }

    console.log(`🔄 Retrying File Explorer (attempt ${this.state.retryCount + 1}/${maxRetries})`);
    this.handleReset();
  };

  private handleReload = () => {
    console.log('🔄 Reloading page due to File Explorer error');
    window.location.reload();
  };

  componentWillUnmount() {
    if (this.resetTimeoutId) {
      clearTimeout(this.resetTimeoutId);
    }
  }

  render() {
    if (this.state.hasError) {
      // Use custom fallback if provided
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default error UI
      const { error, errorInfo, errorId, retryCount } = this.state;
      const maxRetries = this.props.maxRetries || 3;
      const canRetry = retryCount < maxRetries;

      return (
        <div className="flex items-center justify-center min-h-[400px] p-4">
          <Card className="w-full max-w-md">
            <CardHeader className="text-center">
              <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
                <AlertTriangle className="h-6 w-6 text-red-600" />
              </div>
              <CardTitle className="text-lg font-semibold text-red-900">
                File Explorer Error
              </CardTitle>
              <CardDescription>
                Something went wrong with the file explorer. Don't worry, your data is safe.
              </CardDescription>
            </CardHeader>
            
            <CardContent className="space-y-4">
              <Alert variant="destructive">
                <FileX className="h-4 w-4" />
                <AlertTitle>Error Details</AlertTitle>
                <AlertDescription className="text-sm">
                  <div className="mt-2 space-y-1">
                    <div><strong>Error:</strong> {error?.message || 'Unknown error'}</div>
                    <div><strong>Error ID:</strong> {errorId}</div>
                    <div><strong>Retry Count:</strong> {retryCount}/{maxRetries}</div>
                  </div>
                </AlertDescription>
              </Alert>

              <div className="flex flex-col gap-2">
                {canRetry && (
                  <Button 
                    onClick={this.handleRetry}
                    className="w-full"
                    variant="default"
                  >
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Try Again ({maxRetries - retryCount} attempts left)
                  </Button>
                )}
                
                <Button 
                  onClick={this.handleReload}
                  variant="outline"
                  className="w-full"
                >
                  <Home className="mr-2 h-4 w-4" />
                  Reload Page
                </Button>
              </div>

              {process.env.NODE_ENV === 'development' && (
                <details className="mt-4">
                  <summary className="cursor-pointer text-sm font-medium text-gray-700">
                    Developer Details
                  </summary>
                  <div className="mt-2 space-y-2 text-xs">
                    <div>
                      <strong>Stack Trace:</strong>
                      <pre className="mt-1 overflow-auto rounded bg-gray-100 p-2 text-xs">
                        {error?.stack}
                      </pre>
                    </div>
                    {errorInfo && (
                      <div>
                        <strong>Component Stack:</strong>
                        <pre className="mt-1 overflow-auto rounded bg-gray-100 p-2 text-xs">
                          {errorInfo.componentStack}
                        </pre>
                      </div>
                    )}
                  </div>
                </details>
              )}
            </CardContent>
          </Card>
        </div>
      );
    }

    return this.props.children;
  }
}

/**
 * HOC for wrapping components with File Explorer Error Boundary
 */
export function withFileExplorerErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<FileExplorerErrorBoundaryProps, 'children'>
) {
  const WrappedComponent = (props: P) => (
    <FileExplorerErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </FileExplorerErrorBoundary>
  );

  WrappedComponent.displayName = `withFileExplorerErrorBoundary(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
}

/**
 * Hook for handling errors in functional components
 */
export function useFileExplorerErrorHandler() {
  const handleError = React.useCallback((error: Error, context?: string) => {
    console.error(`🚨 File Explorer Error${context ? ` in ${context}` : ''}:`, error);
    
    // You can integrate with error reporting service here
    // Example: errorReportingService.captureException(error, { context });
  }, []);

  const handleAsyncError = React.useCallback(async <T>(
    operation: () => Promise<T>,
    context?: string,
    fallback?: T
  ): Promise<T | undefined> => {
    try {
      return await operation();
    } catch (error) {
      handleError(error as Error, context);
      return fallback;
    }
  }, [handleError]);

  return {
    handleError,
    handleAsyncError
  };
}
