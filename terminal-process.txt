Can you analyse this terminal output and report your findings:

[SUCCESS] Successfully parsed PRD via AI service.
[1] [SUCCESS] Successfully generated 10 tasks in .taskmaster/tasks/tasks.json
[1] [INFO] Preparing to regenerate 10 task files
[1] [INFO] Validating and fixing dependencies
[1] [INFO] Checking for orphaned task files to clean up...
[1] [INFO] No orphaned task files found
[1] [INFO] Generating individual task files...
[1] [SUCCESS] All 10 tasks have been generated into '.taskmaster/tasks'.
[1] ╭──────────────────────────────────────────────────────────╮
[1] │                                                          │
[1] │   Successfully generated 10 new tasks. Total tasks in    │
[1] │   .taskmaster/tasks/tasks.json: 10                       │
[1] │                                                          │
[1] ╰──────────────────────────────────────────────────────────╯
[1] 
[1] ╭────────────────────────────────────────────────────────────────────────────╮
[1] │                                                                            │
[1] │   Next Steps:                                                              │
[1] │                                                                            │
[1] │   1. Run task-master list to view all tasks                                │
[1] │   2. Run task-master expand --id=<id> to break down a task into subtasks   │
[1] │                                                                            │
[1] ╰────────────────────────────────────────────────────────────────────────────╯
[1] 
[1] ╭───────────────── 💡 Telemetry ─────────────────╮
[1] │                                                │
[1] │   AI Usage Summary:                            │
[1] │     Command: parse-prd                         │
[1] │     Provider: anthropic                        │
[1] │     Model: claude-3-7-sonnet-20250219          │
[1] │     Tokens: 4342 (Input: 1822, Output: 2520)   │
[1] │     Est. Cost: $0.043266                       │
[1] │                                                │
[1] ╰────────────────────────────────────────────────╯
[1] 
[1] ╭──────────────────────────────────────────────────────────────────────────────╮
[1] │                                                                              │
[1] │   Update Available! 0.16.1 → 0.16.2                                          │
[1] │                                                                              │
[1] │   Run npm i task-master-ai@latest -g to update to the latest version with    │
[1] │   new features and bug fixes.                                                │
[1] │                                                                              │
[1] ╰──────────────────────────────────────────────────────────────────────────────╯
[1] 
[1] 
[1] [PRD Parser] stderr: - Parsing PRD and generating tasks...
[1] 
[1] ✔ Tasks generated successfully!
[1] 
[1] [PRD Parser] 🕒 Waiting for tasks.json to be created...
[1] [PRD Parser] ⏱️ Allowing brief file system sync delay...
[1] [PRD Parser] 🚀 OPTIMIZED: Checking primary location first...
[1] [PRD Parser] 🕒 Quick check of primary location: /Users/<USER>/Desktop/testproject001/.taskmaster/tasks/tasks.json
[1] [waitForFile] ✅ File ready: /Users/<USER>/Desktop/testproject001/.taskmaster/tasks/tasks.json
[1] [PRD Parser] ✅ Found tasks file in primary location: /Users/<USER>/Desktop/testproject001/.taskmaster/tasks/tasks.json
[1] [PRD Parser] 🔍 Validating tasks.json content...
[1] [PRD Parser] ✅ Successfully created 10 tasks in /Users/<USER>/Desktop/testproject001/.taskmaster/tasks/tasks.json
[1] [PRD Parser] ✅ PRD parsing completed successfully - tasks.json exists and is valid
[1] IPC: board:get-state for board main
[1] IPC: board:get-state for board main
[1] AgentStateService: Agent states saved successfully
[dev]  ✓ Compiled in 724ms (2645 modules)
[dev]  GET / 200 in 19ms
[dev]  ✓ Compiled in 603ms (2645 modules)
[dev]  GET / 200 in 40ms
[1] 🔍 Checking Claude Taskmaster installation...
[1] 🔍 Checking via npx...
[1] ✅ Found via npx: 0.16.1
[1] 🔄 Initializing Taskmaster project at: /Users/<USER>/Desktop/testproject001
[1] ✅ Taskmaster init output: DEBUG: Running init command action in commands.js
[1] DEBUG: Options received by action: {"version":"0.1.0","yes":true}
[1]   _____         _      __  __           _                 _    ___ 
[1]  |_   _|_ _ ___| | __ |  \/  | __ _ ___| |_ ___ _ __     / \  |_ _|
[1]    | |/ _` / __| |/ / | |\/| |/ _` / __| __/ _ \ '__|   / _ \  | | 
[1]    | | (_| \__ \   <  | |  | | (_| \__ \ ||  __/ |     / ___ \ | | 
[1]    |_|\__,_|___/_|\_\ |_|  |_|\__,_|___/\__\___|_|    /_/   \_\___|
[1]                                                                    
[1] by https://x.com/eyaltoledano
[1] ╭───────────────────────────────────╮
[1] │                                   │
[1] │   Initializing your new project   │
[1] │                                   │
[1] ╰───────────────────────────────────╯
[1] 
[1] SKIPPING PROMPTS - Using defaults or provided values
[1] ℹ️ Initializing project in /Users/<USER>/Desktop/testproject001
[1] ℹ️ Setting up MCP configuration for Cursor integration...
[1] ℹ️ MCP configuration file already exists, checking for existing task-master-mcp...
[1] ℹ️ Found existing task-master-ai MCP configuration in mcp.json, leaving untouched
[1] ℹ️ /Users/<USER>/Desktop/testproject001/.gitignore already exists, merging content...
[1] ℹ️ No new content to add to /Users/<USER>/Desktop/testproject001/.gitignore
[1] ℹ️ Generating Roo rules from Cursor rules...
[1] [INFO] Converting Cursor rule cursor_rules.mdc to Roo rule roo_rules.md
[1] [SUCCESS] Successfully converted cursor_rules.mdc to roo_rules.md
[1] [INFO] Converting Cursor rule dev_workflow.mdc to Roo rule dev_workflow.md
[1] [SUCCESS] Successfully converted dev_workflow.mdc to dev_workflow.md
[1] [INFO] Converting Cursor rule self_improve.mdc to Roo rule self_improve.md
[1] [SUCCESS] Successfully converted self_improve.mdc to self_improve.md
[1] [INFO] Converting Cursor rule taskmaster.mdc to Roo rule taskmaster.md
[1] [SUCCESS] Successfully converted taskmaster.mdc to taskmaster.md
[1] [INFO] Rule conversion complete: 4 successful, 0 failed
[1] ℹ️ /Users/<USER>/Desktop/testproject001/.windsurfrules already exists, appending content instead of overwriting...
[1] ✅ Updated /Users/<USER>/Desktop/testproject001/.windsurfrules with additional rules
[1]  ╭─────────────────────────────╮
[1]  │ Installing dependencies...  │
[1]  ╰─────────────────────────────╯
[1] ℹ️ Skipping interactive model setup due to --yes flag.
[1] ℹ️ Default AI models will be used. You can configure different models later using "task-master models --setup" or "task-master models --set-..." commands.
[1] 
[1]    ╔════════════════════════════════════════════╗
[1]    ║                                            ║
   ║     ____                              _    ║
[1]    ║    / ___| _   _  ___ ___ ___  ___ ___| |   ║
[1]    ║    \___ \| | | |/ __/ __/ _ \/ __/ __| |   ║
[1]    ║     ___) | |_| | (_| (_|  __/\__ \__ \_|   ║
[1]    ║    |____/ \__,_|\___\___\___||___/___(_)   ║
[1]    ║                                            ║
[1]    ║   Project initialized successfully!        ║
[1]    ║                                            ║
[1]    ╚════════════════════════════════════════════╝
[1] 
[1] 
[1] ╭────────────────────────────── Getting Started ───────────────────────────────╮
[1] │                                                                              │
[1] │   Things you should do next:                                                 │
[1] │                                                                              │
[1] │   1. Configure AI models (if needed) and add API keys to `.env`              │
[1] │   ├─ Models: Use `task-master models` commands                               │
[1] │   └─ Keys: Add provider API keys to .env (or inside the MCP config file      │
[1] │   i.e. .cursor/mcp.json)                                                     │
[1] │   2. Discuss your idea with AI and ask for a PRD using example_prd.txt,      │
[1] │   and save it to scripts/PRD.txt                                             │
[1] │   3. Ask Cursor Agent (or run CLI) to parse your PRD and generate initial    │
[1] │   tasks:                                                                     │
[1] │   └─ MCP Tool: parse_prd | CLI: task-master parse-prd scripts/prd.txt        │
[1] │   4. Ask Cursor to analyze the complexity of the tasks in your PRD using     │
[1] │   research                                                                   │
[1] │   └─ MCP Tool: analyze_project_complexity | CLI: task-master                 │
[1] │   analyze-complexity                                                         │
[1] │   5. Ask Cursor to expand all of your tasks using the complexity analysis    │
[1] │   6. Ask Cursor to begin working on the next task                            │
[1] │   7. Add new tasks anytime using the add-task command or MCP tool            │
[1] │   8. Ask Cursor to set the status of one or many tasks/subtasks at a time.   │
[1] │   Use the task id from the task lists.                                       │
[1] │   9. Ask Cursor to update all tasks from a specific task id based on new     │
[1] │   learnings or pivots in your project.                                       │
│   10. Ship it!                                            ⚠️ Taskmaster init warnings: ⚠️ /Users/<USER>/Desktop/testproject001/.env.example already exists, skipping.
[1] ⚠️ /Users/<USER>/Desktop/testproject001/.taskmaster/config.json already exists, skipping.
[1] ⚠️ /Users/<USER>/Desktop/testproject001/.cursor/rules/dev_workflow.mdc already exists, skipping.
[1] ⚠️ /Users/<USER>/Desktop/testproject001/.cursor/rules/taskmaster.mdc already exists, skipping.
[1] ⚠️ /Users/<USER>/Desktop/testproject001/.cursor/rules/cursor_rules.mdc already exists, skipping.
[1] ⚠️ /Users/<USER>/Desktop/testproject001/.cursor/rules/self_improve.mdc already exists, skipping.
[1] ⚠️ /Users/<USER>/Desktop/testproject001/.roomodes already exists, skipping.
[1] ⚠️ /Users/<USER>/Desktop/testproject001/.roo/rules-architect/architect-rules already exists, skipping.
[1] ⚠️ /Users/<USER>/Desktop/testproject001/.roo/rules-ask/ask-rules already exists, skipping.
[1] ⚠️ /Users/<USER>/Desktop/testproject001/.roo/rules-boomerang/boomerang-rules already exists, skipping.
[1] ⚠️ /Users/<USER>/Desktop/testproject001/.roo/rules-code/code-rules already exists, skipping.
[1] ⚠️ /Users/<USER>/Desktop/testproject001/.roo/rules-debug/debug-rules already exists, skipping.
[1] ⚠️ /Users/<USER>/Desktop/testproject001/.roo/rules-test/test-rules already exists, skipping.
[1] ⚠️ /Users/<USER>/Desktop/testproject001/.taskmaster/templates/example_prd.txt already exists, skipping.
[1] 
[1]                    │
[1] │                                                                              │
[1] │   * Review the README.md file to learn how to use other commands via         │
[1] │   Cursor Agent.                                                              │
[1] │   * Use the task-master command without arguments to see all available       │
[1] │   commands.                                                                  │
[1] │                                                                              │
[1] ╰──────────────────────────────────────────────────────────────────────────────╯
[1] 
[1] 
[1] ╭──────────────────────────────────────────────────────────────────────────────╮
[1] │                                                                              │
[1] │   Update Available! 0.16.1 → 0.16.2                                          │
[1] │                                                                              │
[1] │   Run npm i task-master-ai@latest -g to update to the latest version with    │
[1] │   new features and bug fixes.                                                │
[1] │                                                                              │
[1] ╰──────────────────────────────────────────────────────────────────────────────╯
[1] 
[1] 
[1] IPC: board:get-state for board main
[1] IPC: board:get-state for board main
[1] IPC: board:get-state for board main
[1] Successfully moved card card-1749783857030-q8tx2nb7k from column-1 to column-3
[1] IPC: board:get-state for board main
[1] IPC: board:get-state for board main
[1] IPC: board:get-state for board main
[1] Successfully moved card card-1749783857030-q8tx2nb7k from column-1 to column-6
[1] Successfully moved card card-1749783857031-pb9p8vpf2 from column-1 to column-3
[1] IPC: board:get-state for board main
[1] Card card-1749783857031-pb9p8vpf2 not found in specified source column column-1, searching all columns...
[1] Found card card-1749783857031-pb9p8vpf2 in column column-3 instead of column-1
[1] Card card-1749783857031-pb9p8vpf2 is already in destination column column-3, no move needed
[1] IPC: board:get-state for board main
[1] 🎯 ConcurrencyManager: Queuing anthropic haiku-3 completion (active: 0/3)
[1] 🚀 ConcurrencyManager: Starting anthropic haiku-3 completion (waited 0ms, active: 1/3)
[1] 🎯 ConcurrencyManager: Queuing anthropic haiku-3 completion (active: 1/3)
[1] 🚀 ConcurrencyManager: Starting anthropic haiku-3 completion (waited 0ms, active: 2/3)
[1] AgentStateService: Agent states saved successfully
[1] LLM request failed for anthropic: Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] ❌ ConcurrencyManager: Failed anthropic haiku-3 completion after 275ms: Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] Error occurred in handler for 'llm:callLLM': Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] LLM request failed for anthropic: Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] ❌ ConcurrencyManager: Failed anthropic haiku-3 completion after 160ms: Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] Error occurred in handler for 'llm:callLLM': Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[dev]  ✓ Compiled in 542ms (2645 modules)
[1] IPC: board:get-state for board main
[1] 🎯 ConcurrencyManager: Queuing anthropic haiku-3 completion (active: 0/3)
[1] 🚀 ConcurrencyManager: Starting anthropic haiku-3 completion (waited 0ms, active: 1/3)
[1] 🎯 ConcurrencyManager: Queuing anthropic haiku-3 completion (active: 1/3)
[1] 🚀 ConcurrencyManager: Starting anthropic haiku-3 completion (waited 0ms, active: 2/3)
[dev]  GET / 200 in 53ms
[1] LLM request failed for anthropic: Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] ❌ ConcurrencyManager: Failed anthropic haiku-3 completion after 213ms: Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] Error occurred in handler for 'llm:callLLM': Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] LLM request failed for anthropic: Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] ❌ ConcurrencyManager: Failed anthropic haiku-3 completion after 210ms: Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] Error occurred in handler for 'llm:callLLM': Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] IPC: board:get-state for board main
[1] IPC: board:get-state for board main
[1] 🎯 ConcurrencyManager: Queuing anthropic haiku-3 completion (active: 0/3)
[1] 🚀 ConcurrencyManager: Starting anthropic haiku-3 completion (waited 0ms, active: 1/3)
[1] 🎯 ConcurrencyManager: Queuing anthropic haiku-3 completion (active: 1/3)
[1] 🚀 ConcurrencyManager: Starting anthropic haiku-3 completion (waited 0ms, active: 2/3)
[1] IPC: board:get-state for board main
[1] IPC: board:get-state for board main
[1] IPC: board:get-state for board main
[1] IPC: board:get-state for board main
[1] LLM request failed for anthropic: Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] ❌ ConcurrencyManager: Failed anthropic haiku-3 completion after 142ms: Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] Error occurred in handler for 'llm:callLLM': Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] Successfully moved card card-1749783857031-pb9p8vpf2 from column-1 to column-6
[1] Successfully moved card card-1749783857047-i4aprpxlb from column-1 to column-3
[1] Card card-1749783857047-i4aprpxlb not found in specified source column column-1, searching all columns...
[1] Found card card-1749783857047-i4aprpxlb in column column-3 instead of column-1
[1] Card card-1749783857047-i4aprpxlb is already in destination column column-3, no move needed
[1] LLM request failed for anthropic: Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] ❌ ConcurrencyManager: Failed anthropic haiku-3 completion after 158ms: Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] Error occurred in handler for 'llm:callLLM': Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] IPC: board:get-state for board main
[1] IPC: board:get-state for board main
[1] Successfully moved card card-1749783857031-pb9p8vpf2 from column-1 to column-6
[1] Successfully moved card card-1749783857047-i4aprpxlb from column-1 to column-6
[1] Card card-1749783857047-i4aprpxlb not found in specified source column column-1, searching all columns...
[1] Found card card-1749783857047-i4aprpxlb in column column-6 instead of column-1
[1] Card card-1749783857047-i4aprpxlb is already in destination column column-6, no move needed
[1] Successfully moved card card-1749783857031-pb9p8vpf2 from column-1 to column-3
[1] IPC: board:get-state for board main
[1] IPC: board:get-state for board main
[1] Successfully moved card card-1749783857048-zbml9pdo8 from column-1 to column-3
[1] Successfully moved card card-1749783857031-pb9p8vpf2 from column-1 to column-3
[1] 🎯 ConcurrencyManager: Queuing anthropic haiku-3 completion (active: 0/3)
[1] 🚀 ConcurrencyManager: Starting anthropic haiku-3 completion (waited 0ms, active: 1/3)
[1] IPC: board:get-state for board main
[1] IPC: board:get-state for board main
[1] 🎯 ConcurrencyManager: Queuing anthropic haiku-3 completion (active: 1/3)
[1] 🚀 ConcurrencyManager: Starting anthropic haiku-3 completion (waited 0ms, active: 2/3)
[1] 🎯 ConcurrencyManager: Queuing anthropic haiku-3 completion (active: 2/3)
[1] 🚀 ConcurrencyManager: Starting anthropic haiku-3 completion (waited 0ms, active: 3/3)
[1] IPC: board:get-state for board main
[1] IPC: board:get-state for board main
[1] IPC: board:get-state for board main
[1] LLM request failed for anthropic: Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] ❌ ConcurrencyManager: Failed anthropic haiku-3 completion after 154ms: Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] Error occurred in handler for 'llm:callLLM': Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] Successfully moved card card-1749783857055-v2nc9iwtt from column-1 to column-3
[1] 🎯 ConcurrencyManager: Queuing anthropic haiku-3 completion (active: 2/3)
[1] 🚀 ConcurrencyManager: Starting anthropic haiku-3 completion (waited 0ms, active: 3/3)
[1] IPC: board:get-state for board main
[1] Card card-1749783857055-v2nc9iwtt not found in specified source column column-1, searching all columns...
[1] Found card card-1749783857055-v2nc9iwtt in column column-3 instead of column-1
[1] Card card-1749783857055-v2nc9iwtt is already in destination column column-3, no move needed
[1] IPC: board:get-state for board main
[1] 🎯 ConcurrencyManager: Queuing anthropic haiku-3 completion (active: 3/3)
[1] Successfully moved card card-1749783857058-j9fdwtdkj from column-1 to column-3
[1] 🎯 ConcurrencyManager: Queuing anthropic haiku-3 completion (active: 3/3)
[1] IPC: board:get-state for board main
[1] Card card-1749783857058-j9fdwtdkj not found in specified source column column-1, searching all columns...
[1] Found card card-1749783857058-j9fdwtdkj in column column-3 instead of column-1
[1] Card card-1749783857058-j9fdwtdkj is already in destination column column-3, no move needed
[1] IPC: board:get-state for board main
[1] IPC: board:get-state for board main
[1] IPC: board:get-state for board main
[1] IPC: board:get-state for board main
[1] IPC: board:get-state for board main
[1] IPC: board:get-state for board main
[1] IPC: board:get-state for board main
[1] IPC: board:get-state for board main
[1] LLM request failed for anthropic: Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] ❌ ConcurrencyManager: Failed anthropic haiku-3 completion after 231ms: Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] Error occurred in handler for 'llm:callLLM': Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] 🚀 ConcurrencyManager: Starting anthropic haiku-3 completion (waited 108ms, active: 3/3)
[1] 🚀 ConcurrencyManager: Starting anthropic haiku-3 completion (waited 105ms, active: 3/3)
[1] LLM request failed for anthropic: Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] ❌ ConcurrencyManager: Failed anthropic haiku-3 completion after 152ms: Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] Error occurred in handler for 'llm:callLLM': Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] IPC: board:get-state for board main
[1] 🎯 ConcurrencyManager: Queuing anthropic haiku-3 completion (active: 3/3)
[1] 🎯 ConcurrencyManager: Queuing anthropic haiku-3 completion (active: 3/3)
[1] IPC: board:get-state for board main
[1] IPC: board:get-state for board main
[1] IPC: board:get-state for board main
[1] Successfully moved card card-1749783857030-q8tx2nb7k from column-1 to column-3
[dev]  ✓ Compiled in 618ms (2645 modules)
[1] LLM request failed for anthropic: Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] ❌ ConcurrencyManager: Failed anthropic haiku-3 completion after 405ms: Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] Error occurred in handler for 'llm:callLLM': Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] 🚀 ConcurrencyManager: Starting anthropic haiku-3 completion (waited 155ms, active: 3/3)
[1] LLM request failed for anthropic: Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] ❌ ConcurrencyManager: Failed anthropic haiku-3 completion after 174ms: Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] 🚀 ConcurrencyManager: Starting anthropic haiku-3 completion (waited 155ms, active: 3/3)
[1] Error occurred in handler for 'llm:callLLM': Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] LLM request failed for anthropic: Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] ❌ ConcurrencyManager: Failed anthropic haiku-3 completion after 173ms: Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] Error occurred in handler for 'llm:callLLM': Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[dev]  GET / 200 in 12ms
[1] 🎯 ConcurrencyManager: Queuing anthropic haiku-3 completion (active: 2/3)
[1] 🚀 ConcurrencyManager: Starting anthropic haiku-3 completion (waited 0ms, active: 3/3)
[1] Successfully moved card card-1749783857055-v2nc9iwtt from column-1 to column-6
[1] Successfully moved card card-1749783857058-j9fdwtdkj from column-1 to column-6
[1] Card card-1749783857058-j9fdwtdkj not found in specified source column column-1, searching all columns...
[1] Found card card-1749783857058-j9fdwtdkj in column column-6 instead of column-1
[1] Card card-1749783857058-j9fdwtdkj is already in destination column column-6, no move needed
[1] Successfully moved card card-1749783857030-q8tx2nb7k from column-1 to column-6
[1] Card card-1749783857055-v2nc9iwtt not found in specified source column column-1, searching all columns...
[1] Found card card-1749783857055-v2nc9iwtt in column column-6 instead of column-1
[1] Card card-1749783857055-v2nc9iwtt is already in destination column column-6, no move needed
[1] LLM request failed for anthropic: Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] ❌ ConcurrencyManager: Failed anthropic haiku-3 completion after 192ms: Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] Error occurred in handler for 'llm:callLLM': Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] LLM request failed for anthropic: Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] ❌ ConcurrencyManager: Failed anthropic haiku-3 completion after 199ms: Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] Error occurred in handler for 'llm:callLLM': Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] IPC: board:get-state for board main
[1] 🎯 ConcurrencyManager: Queuing anthropic haiku-3 completion (active: 1/3)
[1] 🚀 ConcurrencyManager: Starting anthropic haiku-3 completion (waited 0ms, active: 2/3)
[1] 🎯 ConcurrencyManager: Queuing anthropic haiku-3 completion (active: 2/3)
[1] 🚀 ConcurrencyManager: Starting anthropic haiku-3 completion (waited 0ms, active: 3/3)
[1] Successfully moved card card-1749783857065-mlqmai701 from column-1 to column-3
[1] Found card card-1749783857065-mlqmai701 in column column-3 instead of column-1
[1] Card card-1749783857065-mlqmai701 is already in destination column column-3, no move needed
[1] Card card-1749783857065-mlqmai701 not found in specified source column column-1, searching all columns...
[1] Successfully moved card card-1749783857055-v2nc9iwtt from column-1 to column-3
[1] Card card-1749783857055-v2nc9iwtt not found in specified source column column-1, searching all columns...
[1] Found card card-1749783857055-v2nc9iwtt in column column-3 instead of column-1
[1] Card card-1749783857055-v2nc9iwtt is already in destination column column-3, no move needed
[1] LLM request failed for anthropic: Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] ❌ ConcurrencyManager: Failed anthropic haiku-3 completion after 208ms: Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] Error occurred in handler for 'llm:callLLM': Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] 🎯 ConcurrencyManager: Queuing anthropic haiku-3 completion (active: 2/3)
[1] 🚀 ConcurrencyManager: Starting anthropic haiku-3 completion (waited 0ms, active: 3/3)
[1] Successfully moved card card-1749783857065-mlqmai701 from column-1 to column-6
[1] Card card-1749783857065-mlqmai701 not found in specified source column column-1, searching all columns...
[1] Found card card-1749783857065-mlqmai701 in column column-6 instead of column-1
[1] Card card-1749783857065-mlqmai701 is already in destination column column-6, no move needed
[1] 🎯 ConcurrencyManager: Queuing anthropic haiku-3 completion (active: 3/3)
[1] 🎯 ConcurrencyManager: Queuing anthropic haiku-3 completion (active: 3/3)
[dev]  ✓ Compiled in 244ms (2645 modules)
[1] LLM request failed for anthropic: Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] ❌ ConcurrencyManager: Failed anthropic haiku-3 completion after 130ms: Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] Error occurred in handler for 'llm:callLLM': Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] 🚀 ConcurrencyManager: Starting anthropic haiku-3 completion (waited 85ms, active: 3/3)
[1] LLM request failed for anthropic: Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] ❌ ConcurrencyManager: Failed anthropic haiku-3 completion after 150ms: Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] Error occurred in handler for 'llm:callLLM': Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] 🚀 ConcurrencyManager: Starting anthropic haiku-3 completion (waited 106ms, active: 3/3)
[1] LLM request failed for anthropic: Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] ❌ ConcurrencyManager: Failed anthropic haiku-3 completion after 142ms: Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] Error occurred in handler for 'llm:callLLM': Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] 🎯 ConcurrencyManager: Queuing anthropic haiku-3 completion (active: 2/3)
[1] 🚀 ConcurrencyManager: Starting anthropic haiku-3 completion (waited 0ms, active: 3/3)
[1] 🎯 ConcurrencyManager: Queuing anthropic haiku-3 completion (active: 3/3)
[1] Successfully moved card card-1749783857048-zbml9pdo8 from column-1 to column-6
[1] Successfully moved card card-1749783857072-h1264zmzc from column-1 to column-3
[1] 🎯 ConcurrencyManager: Queuing anthropic haiku-3 completion (active: 3/3)
[1] LLM request failed for anthropic: Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] ❌ ConcurrencyManager: Failed anthropic haiku-3 completion after 160ms: Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] 🚀 ConcurrencyManager: Starting anthropic haiku-3 completion (waited 104ms, active: 3/3)
[1] Error occurred in handler for 'llm:callLLM': Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] Successfully moved card card-1749783857055-v2nc9iwtt from column-1 to column-6
[1] LLM request failed for anthropic: Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] ❌ ConcurrencyManager: Failed anthropic haiku-3 completion after 143ms: Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] 🚀 ConcurrencyManager: Starting anthropic haiku-3 completion (waited 33ms, active: 3/3)
[1] Error occurred in handler for 'llm:callLLM': Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] Card card-1749783857055-v2nc9iwtt not found in specified source column column-1, searching all columns...
[1] Found card card-1749783857055-v2nc9iwtt in column column-6 instead of column-1
[1] Card card-1749783857055-v2nc9iwtt is already in destination column column-6, no move needed
[dev]  ✓ Compiled in 232ms (2645 modules)
[1] LLM request failed for anthropic: Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] ❌ ConcurrencyManager: Failed anthropic haiku-3 completion after 309ms: Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] Error occurred in handler for 'llm:callLLM': Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] LLM request failed for anthropic: Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] ❌ ConcurrencyManager: Failed anthropic haiku-3 completion after 193ms: Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] Error occurred in handler for 'llm:callLLM': Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] LLM request failed for anthropic: Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] ❌ ConcurrencyManager: Failed anthropic haiku-3 completion after 191ms: Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] Error occurred in handler for 'llm:callLLM': Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] IPC: board:get-state for board main
[1] 🎯 ConcurrencyManager: Queuing anthropic haiku-3 completion (active: 0/3)
[1] 🚀 ConcurrencyManager: Starting anthropic haiku-3 completion (waited 0ms, active: 1/3)
[1] 🎯 ConcurrencyManager: Queuing anthropic haiku-3 completion (active: 1/3)
[1] 🚀 ConcurrencyManager: Starting anthropic haiku-3 completion (waited 0ms, active: 2/3)
[1] Successfully moved card card-1749783857031-pb9p8vpf2 from column-1 to column-6
[1] Successfully moved card card-1749783857048-zbml9pdo8 from column-1 to column-3
[1] 🎯 ConcurrencyManager: Queuing anthropic haiku-3 completion (active: 2/3)
[1] 🚀 ConcurrencyManager: Starting anthropic haiku-3 completion (waited 1ms, active: 3/3)
[dev]  ✓ Compiled in 206ms (2645 modules)
[1] LLM request failed for anthropic: Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] ❌ ConcurrencyManager: Failed anthropic haiku-3 completion after 328ms: Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] Error occurred in handler for 'llm:callLLM': Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] 🎯 ConcurrencyManager: Queuing anthropic haiku-3 completion (active: 2/3)
[1] 🚀 ConcurrencyManager: Starting anthropic haiku-3 completion (waited 0ms, active: 3/3)
[1] LLM request failed for anthropic: Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] ❌ ConcurrencyManager: Failed anthropic haiku-3 completion after 518ms: Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] Error occurred in handler for 'llm:callLLM': Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] LLM request failed for anthropic: Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] ❌ ConcurrencyManager: Failed anthropic haiku-3 completion after 495ms: Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] Error occurred in handler for 'llm:callLLM': Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] IPC: board:get-state for board main
[1] Successfully moved card card-1749783857031-pb9p8vpf2 from column-1 to column-6
[1] Successfully moved card card-1749783857048-zbml9pdo8 from column-1 to column-6
[1] LLM request failed for anthropic: Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] ❌ ConcurrencyManager: Failed anthropic haiku-3 completion after 226ms: Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] Error occurred in handler for 'llm:callLLM': Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] Successfully moved card card-1749783857072-h1264zmzc from column-1 to column-6
[1] Successfully moved card card-1749783857072-h1264zmzc from column-1 to column-3
[1] 🎯 ConcurrencyManager: Queuing anthropic haiku-3 completion (active: 0/3)
[1] 🚀 ConcurrencyManager: Starting anthropic haiku-3 completion (waited 0ms, active: 1/3)
[dev]  ✓ Compiled in 249ms (2645 modules)
[1] LLM request failed for anthropic: Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] ❌ ConcurrencyManager: Failed anthropic haiku-3 completion after 143ms: Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] Error occurred in handler for 'llm:callLLM': Error: HTTP 404: {"type":"error","error":{"type":"not_found_error","message":"model: haiku-3"}}
[1]     at LLMService.callLLM (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/services/llm-service.js:425:23)
[1]     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[1]     at async withTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/timeout.js:26:24)
[1]     at async ConcurrencyManager.executeWithTimeout (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:121:28)
[1]     at async ConcurrencyManager.executeTask (/Volumes/Extreme SSD/- Development/synapse/file-explorer/dist-electron/utils/concurrency-manager.js:86:26)
[1] Successfully moved card card-1749783857072-h1264zmzc from column-1 to column-6
[dev]  ✓ Compiled in 222ms (2645 modules)