

  // Configure API keys and settings
  async configureTaskmaster(
    projectPath: string,
    anthropicApiKey: string,
    perplexityApiKey: string,
    config?: Partial<TaskmasterConfig>
  ): Promise<{ success: boolean; error?: string }>

  // Initialize project with Taskmaster
  async initializeProject(projectPath: string, projectName: string): Promise<TaskmasterInitResult>

  // Check if Taskmaster is installed
  async checkInstallation(): Promise<{ installed: boolean; version?: string; error?: string }>
}
```

---

## 🎯 2. Electron Taskmaster Service

### **File**: `electron/services/claude-taskmaster-service.ts`

#### **Core Parsing Logic**
```typescript
async parsePRD(projectPath: string, prdContent: string, prdFileName = 'prd.txt') {
  // 1. Setup file paths
  const scriptsPrdPath = path.join(projectPath, 'scripts', prdFileName);
  const docsDir = path.join(projectPath, '.taskmaster', 'docs');
  const tasksDir = path.join(projectPath, '.taskmaster', 'tasks');
  const tasksPath = path.join(tasksDir, 'tasks.json');

  // 2. Create directory structure
  await fs.mkdir(docsDir, { recursive: true });
  await fs.mkdir(tasksDir, { recursive: true });

  // 3. Write PRD content
  await fs.writeFile(prdPath, prdContent, 'utf8');

  // 4. Load API keys from .env
  const envPath = path.join(projectPath, '.env');
  const envContent = await fs.readFile(envPath, 'utf8');
  
  // 5. Execute Taskmaster CLI
  const commands = [
    `task-master parse-prd ${relativePrdPath}`,
    `task-master parse-prd "${relativePrdPath}"`,
    `npx task-master-ai parse-prd ${relativePrdPath}`
  ];

  // 6. Wait for tasks.json creation
  await this.waitForFile(tasksPath, timeout);

  // 7. Validate and return results
  const taskContent = await fs.readFile(tasksPath, 'utf8');
  const parsed = JSON.parse(taskContent);
  return { success: true, tasksGenerated: parsed.length };
}
```

#### **File Waiting Logic**
```typescript
private async waitForFile(filePath: string, timeout = 60000): Promise<void> {
  const pollInterval = timeout <= 3000 ? 100 : 250;
  let elapsed = 0;

  return new Promise((resolve, reject) => {
    const interval = setInterval(async () => {
      try {
        const content = await fs.readFile(filePath, 'utf-8');
        JSON.parse(content); // Ensures valid JSON
        clearInterval(interval);
        resolve();
      } catch (err) {
        elapsed += pollInterval;
        if (elapsed >= timeout) {
          clearInterval(interval);
          reject(new Error(`Timed out waiting for ${filePath}`));
        }
      }
    }, pollInterval);
  });
}
```

---

## 🎯 3. Create Project Wizard

### **File**: `components/project/create-project-wizard.tsx`

#### **Wizard State Management**
```typescript
type WizardStep = 'name' | 'apikeys' | 'prd' | 'taskmaster' | 'orchestration' | 'complete';

interface ProjectCreationState {
  step: WizardStep;
  projectName: string;
  projectPath: string | null;
  folderPath: string | null;
  taskmasterInitialized: boolean;
  apiKeysConfigured: boolean;
  prdValidated: boolean;
  prdUploaded: boolean;
  prdParsed: boolean;
  tasksGenerated: boolean;
  electronAPIAvailable: boolean | null;
}
```

#### **Step Flow Logic**
```typescript
// Step progression through wizard
const createProject = async (): Promise<void> => {
  // 1. Validate inputs
  if (!validateInputs()) return;
  
  // 2. Select folder
  const selectedPath = await selectProjectFolder();
  if (!selectedPath) return;
  
  // 3. Create directory
  const projectPath = await createProjectDirectory(selectedPath, state.projectName);
  
  // 4. Register project
  await registerProject(projectPath, state.projectName);
  
  // 5. Initialize Taskmaster
  const taskmasterResult = await initializeTaskmaster(projectPath, state.projectName);
  
  // 6. Advance to API keys
  advanceToApiKeys();
};
```

#### **API Key Configuration**
```typescript
const handleApiKeysNext = async (): Promise<void> => {
  // Validate API keys are provided
  if (!apiKeys.anthropic.trim() || !apiKeys.perplexity.trim()) {
    setLastError('Both Anthropic and Perplexity API keys are required');
    return;
  }

  // Check validation status
  const anthropicValid = apiKeyValidation.anthropic.isValid === true;
  const perplexityValid = apiKeyValidation.perplexity.isValid === true;

  if (!anthropicValid || !perplexityValid) {
    setLastError('Please validate your API keys before proceeding');
    return;
  }

  // Save to settings manager
  settingsManager.setApiKey('anthropic', apiKeys.anthropic);
  settingsManager.setApiKey('perplexity', apiKeys.perplexity);

  // Advance to PRD step
  setState(prev => ({ ...prev, apiKeysConfigured: true, step: 'prd' }));
};
```

---

## 🎯 4. Task Generation UI

### **File**: `components/project/taskmaster-task-generation-ui.tsx`

#### **Component Interface**
```typescript
interface TaskmasterTaskGenerationUIProps {
  projectPath: string;
  prdContent: string;
  anthropicApiKey: string;
  perplexityApiKey: string;
  onTasksGenerated: (result: PRDParseResult) => void;
  onCancel: () => void;
}
```

#### **Generation Process**
```typescript
const TaskmasterTaskGenerationUI: React.FC<Props> = ({
  projectPath,
  prdContent,
  anthropicApiKey,
  perplexityApiKey,
  onTasksGenerated
}) => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [progress, setProgress] = useState(0);

  const generateTasks = async () => {
    setIsGenerating(true);
    setProgress(0);

    try {
      // 1. Configure Taskmaster with API keys
      setProgress(20);
      const configResult = await claudeTaskmasterService.configureTaskmaster(
        projectPath,
        anthropicApiKey,
        perplexityApiKey
      );

      if (!configResult.success) {
        throw new Error(configResult.error);
      }

      // 2. Parse PRD and generate tasks
      setProgress(50);
      const parseResult = await claudeTaskmasterService.parsePRD(
        projectPath,
        prdContent,
        'prd.txt'
      );

      setProgress(100);
      onTasksGenerated(parseResult);

    } catch (error) {
      onTasksGenerated({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="space-y-4">
      <div className="text-center">
        <Button
          onClick={generateTasks}
          disabled={isGenerating}
          className="w-full"
        >
          {isGenerating ? 'Generating Tasks...' : 'Generate Tasks with Claude Taskmaster'}
        </Button>
      </div>
      
      {isGenerating && (
        <div className="space-y-2">
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${progress}%` }}
            />
          </div>
          <p className="text-sm text-center text-muted-foreground">
            {progress < 30 ? 'Configuring Taskmaster...' :
             progress < 70 ? 'Parsing PRD with AI...' :
             'Finalizing task structure...'}
          </p>
        </div>
      )}
    </div>
  );
};
```

---

## 🎯 5. Orchestration UI

### **File**: `components/orchestrators/taskmaster-orchestration-ui.tsx`

#### **Component Structure**
```typescript
interface TaskmasterOrchestrationUIProps {
  onOrchestrationComplete: (result: OrchestrationResult) => void;
  onCancel: () => void;
}

type OrchestrationState = 'loading' | 'ready' | 'orchestrating' | 'complete' | 'error';
```

#### **Task Loading Logic**
```typescript
const TaskmasterOrchestrationUI: React.FC<Props> = ({ onOrchestrationComplete }) => {
  const [tasks, setTasks] = useState<TaskmasterTask[]>([]);
  const [orchestrationState, setOrchestrationState] = useState<OrchestrationState>('loading');

  useEffect(() => {
    loadTasksFromFile();
  }, []);

  const loadTasksFromFile = async () => {
    try {
      const { activeProjectService } = await import('../../services/active-project-service');
      const activeProject = activeProjectService.getActiveProject();
      
      if (!activeProject) {
        throw new Error('No active project found');
      }

      // Try multiple possible locations for tasks.json
      const possiblePaths = [
        `${activeProject.path}/.taskmaster/tasks/tasks.json`,
        `${activeProject.path}/.taskmaster/tasks.json`,
        `${activeProject.path}/tasks.json`
      ];

      let tasksData = null;
      for (const tasksPath of possiblePaths) {
        try {
          const result = await window.electronAPI.readFile(tasksPath);
          if (result.success) {
            tasksData = JSON.parse(result.content);
            break;
          }
        } catch (error) {
          continue; // Try next path
        }
      }

      if (!tasksData) {
        throw new Error('No tasks.json file found');
      }

      // Handle different task.json formats
      const taskArray = Array.isArray(tasksData) ? tasksData :
                       tasksData.tasks ? tasksData.tasks :
                       Object.values(tasksData);

      setTasks(taskArray);
      setOrchestrationState('ready');

    } catch (error) {
      console.error('Failed to load tasks:', error);
      setOrchestrationState('error');
    }
  };
};
```

#### **Orchestration Execution**
```typescript
const executeOrchestration = async () => {
  setOrchestrationState('orchestrating');

  try {
    // Import orchestrator
    const { KanbanTaskOrchestrator } = await import('./kanban-task-orchestrator');
    const orchestrator = new KanbanTaskOrchestrator();

    // Execute orchestration
    const result = await orchestrator.orchestrateTasks(tasks);

    setOrchestrationState('complete');
    onOrchestrationComplete(result);

  } catch (error) {
    setOrchestrationState('error');
    onOrchestrationComplete({
      success: false,
      error: error instanceof Error ? error.message : 'Orchestration failed'
    });
  }
};
```

---

## 🎯 6. Kanban Task Orchestrator

### **File**: `components/orchestrators/kanban-task-orchestrator.ts`

#### **Core Orchestrator Class**
```typescript
export interface OrchestrationResult {
  success: boolean;
  error?: string;
  boardsCreated?: number;
  cardsCreated?: number;
  structure?: KanbanStructure;
}

export class KanbanTaskOrchestrator {
  async orchestrateTasks(tasks: TaskmasterTask[]): Promise<OrchestrationResult> {
    try {
      // 1. Analyze task structure
      const analysis = this.analyzeTaskStructure(tasks);
      
      // 2. Create board structure
      const boards = this.createBoardStructure(analysis);
      
      // 3. Assign tasks to agents
      const assignedTasks = this.assignTasksToAgents(tasks);
      
      // 4. Create Kanban structure
      const kanbanStructure = this.createKanbanStructure(boards, assignedTasks);
      
      return {
        success: true,
        boardsCreated: boards.length,
        cardsCreated: assignedTasks.length,
        structure: kanbanStructure
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown orchestration error'
      };
    }
  }
}
```

#### **Task Analysis**
```typescript
private analyzeTaskStructure(tasks: TaskmasterTask[]): TaskAnalysis {
  const modules = new Set<string>();
  const milestones = new Set<string>();
  const priorities = { high: 0, medium: 0, low: 0 };
  const agents = new Set<string>();

  tasks.forEach(task => {
    // Extract modules from task metadata
    if (task.module) modules.add(task.module);
    if (task.milestone) milestones.add(task.milestone);
    if (task.agent) agents.add(task.agent);
    
    // Count priorities
    priorities[task.priority]++;
  });

  return {
    totalTasks: tasks.length,
    modules: Array.from(modules),
    milestones: Array.from(milestones),
    priorities,
    agents: Array.from(agents),
    dependencies: this.analyzeDependencies(tasks)
  };
}
```

#### **Board Structure Creation**
```typescript
private createBoardStructure(analysis: TaskAnalysis): KanbanBoard[] {
  const boards: KanbanBoard[] = [];

  // Create boards by module
  analysis.modules.forEach(module => {
    const board: KanbanBoard = {
      id: `board-${module}`,
      title: `${module.charAt(0).toUpperCase() + module.slice(1)} Module`,
      description: `Tasks related to ${module} functionality`,
      lanes: this.createLanes(),
      columns: this.createColumns(),
      cards: []
    };
    boards.push(board);
  });

  // If no modules, create a default board
  if (boards.length === 0) {
    boards.push({
      id: 'board-main',
      title: 'Main Project Board',
      description: 'All project tasks',
      lanes: this.createLanes(),
      columns: this.createColumns(),
      cards: []
    });
  }

  return boards;
}
```

#### **Agent Assignment Logic**
```typescript
private assignTasksToAgents(tasks: TaskmasterTask[]): TaskmasterTask[] {
  return tasks.map(task => {
    // If task already has agent assigned, keep it
    if (task.agent) return task;

    // Analyze task complexity
    const complexity = this.analyzeTaskComplexity(task);
    
    // Assign based on complexity and type
    const agent = this.selectBestAgent(complexity, task);
    
    return { ...task, agent };
  });
}

private selectBestAgent(complexity: number, task: TaskmasterTask): string {
  // High complexity or architectural tasks
  if (complexity >= 8 || task.title.toLowerCase().includes('architecture')) {
    return 'architect';
  }
  
  // Complex implementation tasks
  if (complexity >= 6 || task.