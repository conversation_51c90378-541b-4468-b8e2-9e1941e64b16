# 🎯 Complete Taskmaster Kanban Board Generation Flow

> **⚠️ ARCHITECTURE RESTORATION NOTICE**
> This document describes the LEGACY implementation that violated Synapse architecture principles.
> The system has been RESTORED to proper Synapse architecture where:
> - **Micromanager** is the ONLY entity allowed to perform task orchestration and agent assignment
> - **Taskmaster** is limited to PRD parsing and task definition generation ONLY
> - **Kanban** is a passive UI visualization tool with NO business logic
> - All tasks flow through: Taskmaster → Micromanager → Task Classifier → Context Prefetcher → Agent
>
> See `Documentation/SYNAPSE-ARCHITECTURE-RESTORATION-COMPLETE.md` for current implementation.

## 📋 Overview

This document explains the complete process of how Claude Taskmaster generates structured tasks from a PRD (Product Requirements Document) and converts them into organized Kanban boards with lanes, columns, and cards.

---

## 🔄 Complete Flow Diagram

```
PRD Upload → Taskmaster Parsing → Task Generation → Orchestration → Kanban Boards
     ↓              ↓                ↓               ↓              ↓
1. User uploads  2. Claude <PERSON>    3. tasks.json   4. Agent       5. Visual
   PRD file         parses PRD      created        assignment     boards
                                                                  created
```

---

## 🎬 Step-by-Step Process

### **Step 1: PRD Upload & Validation**
**Location**: `create-project-wizard.tsx` → PRD step
**Purpose**: User uploads their Project Requirements Document

```typescript
// User uploads PRD through PRDUploadUI component
<PRDUploadUI
  onPRDUploaded={handlePRDUploaded}
  onValidationChange={handlePRDValidationChange}
  projectPath={state.projectPath}
  disableParsing={true}
/>
```

### **Step 2: Claude Taskmaster Task Generation**
**Location**: `claude-taskmaster-service.ts` → `parsePRD()` method
**Purpose**: AI parses PRD and generates structured tasks

```typescript
// Core parsing logic in claude-taskmaster-service.ts
async parsePRD(projectPath: string, prdContent: string, prdFileName = 'prd.txt') {
  // 1. Save PRD to .taskmaster/docs/prd.txt
  await fs.writeFile(prdPath, prdContent, 'utf8');
  
  // 2. Execute Claude Taskmaster CLI command
  const command = `task-master parse-prd ${relativePrdPath}`;
  const result = await execAsync(command, {
    cwd: projectPath,
    env: envVars, // Contains API keys
    timeout: 120000
  });
  
  // 3. Wait for tasks.json to be created
  await this.waitForFile(tasksPath, timeout);
  
  // 4. Validate and return task count
  const taskContent = await fs.readFile(tasksPath, 'utf8');
  const parsed = JSON.parse(taskContent);
  return { success: true, tasksGenerated: parsed.length };
}
```

### **Step 3: Task Structure Creation**
**Location**: `.taskmaster/tasks/tasks.json` (generated by Claude AI)
**Purpose**: Structured task breakdown with dependencies and priorities

**Example Generated Structure**:
```json
{
  "tasks": [
    {
      "id": "task-001",
      "title": "Setup Project Infrastructure",
      "description": "Initialize project with basic structure",
      "status": "pending",
      "priority": "high",
      "dependencies": [],
      "agent": "architect",
      "estimatedHours": 4,
      "module": "infrastructure",
      "milestone": "foundation"
    },
    {
      "id": "task-002",
      "title": "Implement User Authentication",
      "description": "Create login/signup functionality",
      "status": "pending",
      "priority": "medium",
      "dependencies": ["task-001"],
      "agent": "senior",
      "estimatedHours": 8,
      "module": "auth",
      "milestone": "core-features"
    }
  ]
}
```

### **Step 4: Orchestration Initialization**
**Location**: `taskmaster-orchestration-ui.tsx`
**Purpose**: Load tasks and prepare for board creation

```typescript
// TaskmasterOrchestrationUI component initialization
const TaskmasterOrchestrationUI: React.FC<Props> = ({ onOrchestrationComplete }) => {
  const [tasks, setTasks] = useState<TaskmasterTask[]>([]);
  const [orchestrationState, setOrchestrationState] = useState<OrchestrationState>('loading');

  useEffect(() => {
    loadTasksFromFile();
  }, []);

  const loadTasksFromFile = async () => {
    // Read tasks.json from .taskmaster directory
    const tasksData = await readTasksFile();
    setTasks(tasksData);
    setOrchestrationState('ready');
  };
};
```

### **Step 5: Kanban Board Generation**
**Location**: `kanban-task-orchestrator.ts`
**Purpose**: Convert tasks into organized Kanban structure

```typescript
// ARCHITECTURE RESTORATION: This orchestration logic has been REMOVED
// All task orchestration now flows through Micromanager for proper Synapse architecture

// NEW: Micromanager delegation logic
export class MicromanagerAgent {
  async handleTaskmasterIngestion(context: AgentContext): Promise<AgentResponse> {
    // 1. Classify tasks through Task Classifier
    const classification = await this.classifyTaskmasterTask(context, taskmasterData);

    // 2. Prefetch context through Context Prefetcher
    const optimizedContext = await this.prefetchTaskContext(context, classification);

    // 3. Decompose tasks through Micromanager
    const decomposition = await this.decomposeTask(optimizedContext, classification);

    // 4. Create passive Kanban visualization (UI only)
    await this.createKanbanVisualization(taskId, decomposition, executionPlan);

    return {
      success: true,
      tasksSubmitted: decomposition.subtasks.length,
      message: 'Tasks processed through proper Synapse architecture pipeline'
    };
  }
}
```

---

## 🏗️ Kanban Structure Creation Details

### **Board Creation Logic**
```typescript
createBoardStructure(analysis: TaskAnalysis): KanbanBoard[] {
  const boards: KanbanBoard[] = [];
  
  // Create boards by module or milestone
  analysis.modules.forEach(module => {
    const board: KanbanBoard = {
      id: `board-${module}`,
      title: `${module.charAt(0).toUpperCase() + module.slice(1)} Module`,
      lanes: this.createLanes(module),
      columns: this.createColumns()
    };
    boards.push(board);
  });
  
  return boards;
}
```

### **Lane Creation (Agent-based Swimlanes)**
```typescript
createLanes(module: string): KanbanLane[] {
  return [
    { id: 'lane-architect', title: 'Architect', agent: 'architect' },
    { id: 'lane-senior', title: 'Senior Developer', agent: 'senior' },
    { id: 'lane-midlevel', title: 'Mid-Level Developer', agent: 'midlevel' },
    { id: 'lane-junior', title: 'Junior Developer', agent: 'junior' },
    { id: 'lane-intern', title: 'Intern', agent: 'intern' }
  ];
}
```

### **Column Creation (Status-based)**
```typescript
createColumns(): KanbanColumn[] {
  return [
    { id: 'col-backlog', title: 'Backlog', status: 'pending' },
    { id: 'col-todo', title: 'To Do', status: 'ready' },
    { id: 'col-progress', title: 'In Progress', status: 'in-progress' },
    { id: 'col-review', title: 'Review', status: 'review' },
    { id: 'col-done', title: 'Done', status: 'done' }
  ];
}
```

### **Card Creation (Task-based)**
```typescript
createKanbanCards(tasks: TaskmasterTask[]): KanbanCard[] {
  return tasks.map(task => ({
    id: `card-${task.id}`,
    title: task.title,
    description: task.description,
    priority: task.priority,
    estimatedHours: task.estimatedHours,
    dependencies: task.dependencies,
    assignedAgent: task.agent,
    status: task.status,
    tags: [task.module, task.milestone],
    laneId: `lane-${task.agent}`,
    columnId: this.getColumnByStatus(task.status)
  }));
}
```

---

## 📁 Complete File Structure

### **Core Service Files**
```
file-explorer/
├── services/
│   ├── claude-taskmaster-service.ts          # Main Taskmaster integration
│   └── active-project-service.ts             # Project state management
├── electron/services/
│   └── claude-taskmaster-service.ts          # Electron-specific Taskmaster service
└── components/
    ├── project/
    │   ├── create-project-wizard.tsx          # Main wizard orchestrating the flow
    │   └── taskmaster-task-generation-ui.tsx  # Task generation UI
    ├── orchestrators/
    │   └── taskmaster-orchestration-ui.tsx    # Micromanager delegation interface
    ├── kanban/
    │   └── kanban-visualizer.ts               # Passive Kanban visualization (UI only)
    └── intake/
        ├── prd-upload-ui.tsx                  # PRD upload interface
        └── prd-intake-service.ts              # PRD processing service
```

### **Generated Project Files**
```
project-directory/
├── .taskmaster/
│   ├── docs/
│   │   └── prd.txt                           # Original PRD content
│   ├── tasks/
│   │   ├── tasks.json                        # Generated task structure
│   │   ├── task-001.md                       # Individual task files
│   │   └── task-002.md
│   └── config.json                           # Taskmaster configuration
├── .env                                      # API keys for Taskmaster
└── README.md                                 # Project documentation
```

---

## 🎯 Agent Assignment Logic

### **Agent Capability Mapping**
```typescript
const AGENT_CAPABILITIES = {
  architect: ['system_design', 'technical_strategy', 'high_complexity'],
  senior: ['complex_system_implementation', 'architectural_decisions'],
  midlevel: ['multi_file_implementation', 'component_integration'],
  junior: ['single_file_implementation', 'moderate_complexity'],
  intern: ['simple_tasks', 'boilerplate_generation'],
  researcher: ['codebase_analysis', 'pattern_recognition'],
  designer: ['ui_design', 'component_styling'],
  tester: ['test_generation', 'quality_assurance']
};
```

### **Task Assignment Algorithm**
```typescript
assignTasksToAgents(tasks: TaskmasterTask[]): TaskmasterTask[] {
  return tasks.map(task => {
    // 1. Analyze task complexity
    const complexity = this.analyzeTaskComplexity(task);
    
    // 2. Check dependencies
    const dependencyLevel = this.calculateDependencyLevel(task);
    
    // 3. Match to appropriate agent
    const agent = this.selectBestAgent(complexity, dependencyLevel, task.type);
    
    return { ...task, agent };
  });
}
```

---

## 🔧 Configuration & Settings

### **Taskmaster Configuration**
```typescript
// Default configuration in claude-taskmaster-service.ts
getDefaultConfig(projectName: string): TaskmasterConfig {
  return {
    models: {
      main: {
        provider: 'anthropic',
        modelId: 'claude-3-5-sonnet-20241022',
        maxTokens: 64000,
        temperature: 0.2
      },
      research: {
        provider: 'perplexity',
        modelId: 'sonar-pro',
        maxTokens: 8700,
        temperature: 0.1
      }
    },
    global: {
      logLevel: 'info',
      debug: false,
      defaultSubtasks: 5,
      defaultPriority: 'medium',
      projectName: projectName
    }
  };
}
```

### **API Key Management**
```typescript
// API keys are managed through settings-manager.ts
settingsManager.setApiKey('anthropic', anthropicKey);
settingsManager.setApiKey('perplexity', perplexityKey);

// Keys are saved to .env file in project directory
const envContent = `ANTHROPIC_API_KEY=${anthropicApiKey}\nPERPLEXITY_API_KEY=${perplexityApiKey}\n`;
await fs.writeFile(envPath, envContent);
```

---

## 🚀 Execution Flow Summary

### **1. Project Creation Wizard Flow**
```typescript
// create-project-wizard.tsx - Complete flow
'name' → 'apikeys' → 'prd' → 'taskmaster' → 'orchestration' → 'complete'
```

### **2. Task Generation Process**
```typescript
// Taskmaster execution sequence
PRD Content → Claude AI Analysis → Structured Tasks → JSON Output
```

### **3. Kanban Board Creation**
```typescript
// Orchestration sequence
Tasks.json → Agent Assignment → Board Structure → Lane/Column Creation → Card Population
```

### **4. Final Result**
- **Multiple Kanban Boards**: Organized by module/milestone
- **Agent-based Swimlanes**: Tasks assigned to appropriate skill levels
- **Status Columns**: Backlog → To Do → In Progress → Review → Done
- **Task Cards**: Complete with dependencies, priorities, and estimates

---

## 🎉 End Result

The complete process transforms a simple PRD document into a fully structured project management system with:

- ✅ **AI-Generated Tasks**: Intelligent breakdown of requirements
- ✅ **Agent Assignment**: Tasks matched to appropriate skill levels
- ✅ **Kanban Visualization**: Clear project structure and workflow
- ✅ **Dependency Management**: Proper task ordering and relationships
- ✅ **Progress Tracking**: Visual status management across all tasks

This creates a comprehensive, AI-driven project management workflow that takes users from initial requirements to executable development tasks with clear organization and assignment.