# Sequential Workflow Technical Specification

## Overview

This document provides detailed technical specifications for the controlled sequential workflow system implemented in the Synapse project creation and development automation platform.

## Core Services

### 1. Sequential Execution Controller

**File**: `file-explorer/services/task-state-service.ts`

#### Interface
```typescript
export class SequentialExecutionController {
  // Core Methods
  public async activateSingleAgent(agentId: string, taskId: string): Promise<boolean>
  public async deactivateAllAgents(): Promise<void>
  public getCurrentActiveAgent(): string | null
  public getCurrentActiveTask(): string | null
  public isActive(): boolean
  
  // Queue Management
  public queueTask(taskId: string, agentId: string, context: any): void
  public getNextQueuedTask(): { taskId: string; agentId: string; context: any } | null
  public clearQueue(): void
  
  // Status and Events
  public getQueueStatus(): { active: boolean; currentAgent: string | null; queueLength: number }
  public addListener(listener: (event: { type: string; data: any }) => void): () => void
}
```

#### Key Features
- **Single-Agent Policy**: Enforces only one active agent at a time
- **Queue Management**: FIFO task queue with context preservation
- **Event System**: Real-time notifications for state changes
- **Thread Safety**: Atomic operations for state management

### 2. Completion Verification Service

**File**: `file-explorer/services/completion-verification-service.ts`

#### Interface
```typescript
export class CompletionVerificationService {
  // File Validation
  public async validateFileOutput(taskId: string, expectedFiles: string[]): Promise<ValidationResult>
  public async verifyCodeQuality(generatedFiles: string[]): Promise<QualityReport>
  
  // Objective Verification
  public async confirmTaskObjectivesMet(taskId: string): Promise<ObjectiveValidation>
  public async generateDeliverableReport(taskId: string): Promise<DeliverableReport>
}

export interface ValidationResult {
  isValid: boolean
  reason?: string
  details?: string[]
  filesValidated: string[]
  missingFiles: string[]
}

export interface QualityReport {
  score: number // 0-100
  issues: QualityIssue[]
  recommendations: string[]
  passesMinimumStandards: boolean
}
```

#### Validation Criteria
- **File Existence**: Verifies files are actually created
- **Content Quality**: Checks for meaningful content (not placeholders)
- **Code Structure**: Basic syntax and structure validation
- **Quality Scoring**: 0-100 score based on multiple factors

### 3. Automatic Execution Service

**File**: `file-explorer/services/automatic-execution-service.ts`

#### Interface
```typescript
export class AutomaticExecutionService {
  // Configuration
  public configure(config: Partial<AutoExecutionConfig>): void
  
  // Execution Control
  public async startAutomaticExecution(): Promise<{ success: boolean; message: string }>
  public async stopAutomaticExecution(): Promise<{ success: boolean; message: string }>
  public async resumeAfterUserIntervention(): Promise<{ success: boolean; message: string }>
  
  // Status Monitoring
  public getStatus(): AutoExecutionStatus
  public addStatusListener(listener: (status: AutoExecutionStatus) => void): () => void
}

export interface AutoExecutionConfig {
  enabled: boolean
  autoApproveThreshold: number // Quality score threshold (0-100)
  maxConsecutiveTasks: number // Safety limit
  timeoutPerTask: number // Milliseconds
  requireUserApprovalFor: ('low_quality' | 'file_errors' | 'validation_failures')[]
}
```

#### Safety Mechanisms
- **Quality Thresholds**: Auto-approval only for high-quality output (80%+ default)
- **Consecutive Limits**: Prevents runaway execution (3 tasks default)
- **Timeout Protection**: Task execution time limits (5 minutes default)
- **User Intervention**: Automatic pause for quality issues

### 4. Live Coding Service

**File**: `file-explorer/services/live-coding-service.ts`

#### Interface
```typescript
export class LiveCodingService {
  // Monaco Integration
  public setMonacoIntegration(integration: MonacoEditorIntegration): void
  
  // Real-time Display
  public displayActiveWork(agentId: string, taskId: string, filePath: string, content: string): void
  public streamCodeGeneration(agentId: string, taskId: string, filePath: string, content: string, progress: number): void
  public highlightWorkArea(agentId: string, taskId: string, filePath: string, lineRange: [number, number]): void
  
  // Progress Tracking
  public showProgress(agentId: string, taskId: string, percentage: number, description: string): void
  public completeWork(agentId: string, taskId: string, filePath: string): void
  
  // State Management
  public getActiveFiles(): Array<{ filePath: string; content: string; lastUpdate: number; progress?: number }>
  public clearActiveWork(): void
}
```

#### Real-time Features
- **Live Code Streaming**: Real-time content updates in Monaco editor
- **Progress Visualization**: Visual progress indicators and status updates
- **Work Area Highlighting**: Highlight current editing areas
- **File State Tracking**: Monitor active files and their progress

## Enhanced Components

### 1. Micromanager Agent Enhancements

**File**: `file-explorer/components/agents/micromanager-agent.ts`

#### New Methods
```typescript
// Sequential Workflow Management
public async initializeSequentialWorkflow(tasks: any[]): Promise<{ success: boolean; message: string }>
public async startNextSequentialTask(): Promise<{ success: boolean; taskStarted?: any; message: string }>
public async completeCurrentTask(taskId: string, expectedFiles: string[]): Promise<{ success: boolean; report?: any; message: string }>
public getSequentialWorkflowStatus(): { active: boolean; currentAgent: string | null; currentTask: string | null; queueLength: number; canStartNext: boolean }
```

#### Enhanced Capabilities
- **Workflow Initialization**: Sets up task queue and sequential execution
- **Task Progression**: Controlled advancement through task sequence
- **Completion Validation**: Integrates with verification service
- **Status Monitoring**: Real-time workflow state information

### 2. Agent Execution Service Enhancements

**File**: `file-explorer/components/agents/agent-execution-service.ts`

#### New Interfaces
```typescript
export interface StreamingUpdate {
  type: 'file_progress' | 'code_generation' | 'validation' | 'completion'
  agentId: string
  taskId: string
  data: {
    filePath?: string
    content?: string
    progress?: number
    message?: string
    timestamp: number
  }
}

export interface MonacoIntegration {
  displayActiveWork: (agentId: string, filePath: string, content: string) => void
  streamCodeGeneration: (filePath: string, content: string, progress: number) => void
  highlightWorkArea: (filePath: string, lineRange: [number, number]) => void
  showProgress: (agentId: string, percentage: number, description: string) => void
}
```

#### Enhanced Features
- **Real-time Streaming**: Live updates during file operations
- **Monaco Integration**: Direct integration with code editor
- **Progress Tracking**: Detailed progress monitoring and reporting
- **Completion Verification**: Enhanced validation using verification service

### 3. Agent Manager Enhancements

**File**: `file-explorer/components/agents/agent-manager-complete.ts`

#### New Methods
```typescript
// Sequential Execution Controls
public async terminateAgent(agentId: string, reason: string): Promise<boolean>
public async handoffToNextAgent(): Promise<{ success: boolean; nextAgent?: string; message: string }>
public async validateTaskCompletion(taskId: string, expectedFiles: string[]): Promise<{ valid: boolean; report?: any; message: string }>
```

#### Enhanced Capabilities
- **Agent Termination**: Clean shutdown with resource cleanup
- **Handoff Management**: Controlled transitions between agents
- **Validation Integration**: Task completion verification
- **Resource Management**: Proper lifecycle and state management

## User Interface Components

### 1. User Confirmation Dialog

**File**: `file-explorer/components/workflow/user-confirmation-dialog.tsx`

#### Interface
```typescript
export interface TaskCompletionReport {
  taskId: string
  agentId: string
  title: string
  description: string
  filesCreated: string[]
  filesModified: string[]
  filesDeleted: string[]
  executionTime: number
  tokensUsed: number
  success: boolean
  validationResults: {
    isValid: boolean
    reason?: string
    details?: string[]
  }
  qualityScore: number
  timestamp: number
}

export interface UserDecision {
  action: 'proceed' | 'retry' | 'modify' | 'cancel'
  feedback?: string
  modifications?: string[]
}
```

#### Features
- **Comprehensive Reporting**: Detailed task execution summaries
- **Multiple Decision Options**: Proceed, retry, modify, or cancel
- **Feedback Collection**: Optional user feedback for improvements
- **Visual Indicators**: Clear status and quality indicators

### 2. Enhanced Orchestration UI

**File**: `file-explorer/components/orchestrators/taskmaster-orchestration-ui.tsx`

#### Key Features
- **Sequential Workflow Controls**: Manual and automatic execution options
- **Real-time Status Display**: Live workflow state and progress
- **Agent Lane Management**: Dynamic agent visibility and task assignment
- **Automatic Execution Toggle**: Enable/disable hands-off execution

#### Control Flow
1. **Manual Mode**: User-controlled task progression with confirmation dialogs
2. **Automatic Mode**: Quality-based auto-approval with safety limits
3. **Hybrid Mode**: Automatic execution with manual override capabilities

## Data Flow Architecture

### Sequential Execution Flow
```
Project Creation
       ↓
Task Queue Setup (Micromanager)
       ↓
Sequential Execution Controller
       ↓
Agent Activation (Single Agent)
       ↓
Real-time Monitoring (Live Coding Service)
       ↓
Task Completion
       ↓
Validation (Completion Verification Service)
       ↓
User Confirmation (if required)
       ↓
Agent Termination & Handoff
       ↓
Next Task (repeat until queue empty)
```

### Event System
```typescript
// Event Types
type WorkflowEvent = 
  | { type: 'agent_activated'; data: { agentId: string; taskId: string } }
  | { type: 'agent_deactivated'; data: { agentId: string; taskId: string } }
  | { type: 'task_completed'; data: { taskId: string; success: boolean } }
  | { type: 'validation_result'; data: { taskId: string; isValid: boolean } }
  | { type: 'user_decision'; data: { taskId: string; action: string } }
```

## Configuration

### Default Settings
```typescript
// Sequential Execution Controller
const defaultSequentialConfig = {
  maxQueueSize: 100,
  timeoutPerTask: 300000, // 5 minutes
  retryAttempts: 3
}

// Automatic Execution Service
const defaultAutoConfig = {
  enabled: true,
  autoApproveThreshold: 80, // 80% quality score
  maxConsecutiveTasks: 3,
  timeoutPerTask: 300000, // 5 minutes
  requireUserApprovalFor: ['low_quality', 'validation_failures']
}

// Completion Verification Service
const defaultValidationConfig = {
  minimumQualityScore: 70,
  requireFileValidation: true,
  placeholderDetection: true,
  syntaxValidation: true
}
```

### Environment Variables
- `SEQUENTIAL_WORKFLOW_ENABLED`: Enable/disable sequential workflow (default: true)
- `AUTO_EXECUTION_THRESHOLD`: Quality threshold for auto-approval (default: 80)
- `MAX_CONSECUTIVE_TASKS`: Safety limit for automatic execution (default: 3)
- `TASK_TIMEOUT_MS`: Maximum task execution time (default: 300000)

## Error Handling

### Error Categories
1. **Validation Errors**: File validation or quality check failures
2. **Execution Errors**: Agent execution or timeout failures
3. **System Errors**: Service initialization or communication failures
4. **User Errors**: Invalid input or configuration errors

### Recovery Mechanisms
- **Graceful Degradation**: Fall back to manual control on automatic execution failure
- **Retry Logic**: Configurable retry attempts for transient failures
- **User Intervention**: Pause automatic execution for manual resolution
- **State Recovery**: Restore workflow state after system restart

## Performance Considerations

### Optimization Strategies
- **Lazy Loading**: Load services only when needed
- **Event Debouncing**: Prevent excessive event firing
- **Memory Management**: Clean up resources after task completion
- **Caching**: Cache validation results and quality scores

### Monitoring
- **Execution Time**: Track task execution duration
- **Memory Usage**: Monitor service memory consumption
- **Event Frequency**: Track event system performance
- **User Interaction**: Monitor user decision patterns

## Security Considerations

### Access Control
- **Agent Isolation**: Prevent agents from interfering with each other
- **File System Protection**: Validate file paths and permissions
- **API Key Security**: Secure storage and transmission of API keys
- **User Input Validation**: Sanitize all user inputs

### Data Protection
- **Sensitive Data**: Avoid logging sensitive information
- **Temporary Files**: Clean up temporary files after use
- **Error Messages**: Sanitize error messages to prevent information leakage
- **Audit Trail**: Maintain logs for security auditing

This technical specification provides the foundation for understanding, maintaining, and extending the controlled sequential workflow system.
