{\rtf1\ansi\ansicpg1252\cocoartf2761
\cocoatextscaling0\cocoaplatform0{\fonttbl\f0\fswiss\fcharset0 Helvetica;\f1\fnil\fcharset134 PingFangSC-Regular;\f2\fnil\fcharset0 AppleColorEmoji;
\f3\fnil\fcharset0 LucidaGrande;}
{\colortbl;\red255\green255\blue255;}
{\*\expandedcolortbl;;}
\paperw11900\paperh16840\margl1440\margr1440\vieww21740\viewh16840\viewkind1
\pard\tx720\tx1440\tx2160\tx2880\tx3600\tx4320\tx5040\tx5760\tx6480\tx7200\tx7920\tx8640\pardirnatural\partightenfactor0

\f0\fs28 \cf0 # Task Master Tutorial\
\
This tutorial will guide you through setting up and using Task Master for AI-driven development.\
\
## Initial Setup\
\
There are two ways to set up Task Master: using MCP (recommended) or via npm installation.\
\
### Option 1: Using MCP (Recommended)\
\
MCP (Model Control Protocol) provides the easiest way to get started with Task Master directly in your editor.\
\
1. **Install the package**\
\
```bash\
npm i -g task-master-ai\
```\
\
2. **Add the MCP config to your IDE/MCP Client** (Cursor is recommended, but it works with other clients):\
\
```json\
\{\
  "mcpServers": \{\
    "taskmaster-ai": \{\
      "command": "npx",\
      "args": ["-y", "--package=task-master-ai", "task-master-ai"],\
      "env": \{\
        "ANTHROPIC_API_KEY": "YOUR_ANTHROPIC_API_KEY_HERE",\
        "PERPLEXITY_API_KEY": "YOUR_PERPLEXITY_API_KEY_HERE",\
        "OPENAI_API_KEY": "YOUR_OPENAI_KEY_HERE",\
        "GOOGLE_API_KEY": "YOUR_GOOGLE_KEY_HERE",\
        "MISTRAL_API_KEY": "YOUR_MISTRAL_KEY_HERE",\
        "OPENROUTER_API_KEY": "YOUR_OPENROUTER_KEY_HERE",\
        "XAI_API_KEY": "YOUR_XAI_KEY_HERE",\
        "AZURE_OPENAI_API_KEY": "YOUR_AZURE_KEY_HERE"\
      \}\
    \}\
  \}\
\}\
```\
\
**IMPORTANT:** An API key is _required_ for each AI provider you plan on using. Run the `task-master models` command to see your selected models and the status of your API keys across .env and mcp.json\
\
**To use AI commands in CLI** you MUST have API keys in the .env file\
**To use AI commands in MCP** you MUST have API keys in the .mcp.json file (or MCP config equivalent)\
\
We recommend having keys in both places and adding mcp.json to your gitignore so your API keys aren't checked into git.\
\
3. **Enable the MCP** in your editor settings\
\
4. **Prompt the AI** to initialize Task Master:\
\
```\
Can you please initialize taskmaster-ai into my project?\
```\
\
The AI will:\
\
- Create necessary project structure\
- Set up initial configuration files\
- Guide you through the rest of the process\
\
5. Place your PRD document in the `.taskmaster/docs/` directory (e.g., `.taskmaster/docs/prd.txt`)\
\
6. **Use natural language commands** to interact with Task Master:\
\
```\
Can you parse my PRD at .taskmaster/docs/prd.txt?\
What's the next task I should work on?\
Can you help me implement task 3?\
```\
\
### Option 2: Manual Installation\
\
If you prefer to use the command line interface directly:\
\
```bash\
# Install globally\
npm install -g task-master-ai\
\
# OR install locally within your project\
npm install task-master-ai\
```\
\
Initialize a new project:\
\
```bash\
# If installed globally\
task-master init\
\
# If installed locally\
npx task-master init\
```\
\
This will prompt you for project details and set up a new project with the necessary files and structure.\
\
## Common Commands\
\
After setting up Task Master, you can use these commands (either via AI prompts or CLI):\
\
```bash\
# Parse a PRD and generate tasks\
task-master parse-prd your-prd.txt\
\
# List all tasks\
task-master list\
\
# Show the next task to work on\
task-master next\
\
# Generate task files\
task-master generate\
```\
\
## Setting up Cursor AI Integration\
\
Task Master is designed to work seamlessly with [Cursor AI](https://www.cursor.so/), providing a structured workflow for AI-driven development.\
\
### Using Cursor with MCP (Recommended)\
\
If you've already set up Task Master with MCP in Cursor, the integration is automatic. You can simply use natural language to interact with Task Master:\
\
```\
What tasks are available to work on next?\
Can you analyze the complexity of our tasks?\
I'd like to implement task 4. What does it involve?\
```\
\
### Manual Cursor Setup\
\
If you're not using MCP, you can still set up Cursor integration:\
\
1. After initializing your project, open it in Cursor\
2. The `.cursor/rules/dev_workflow.mdc` file is automatically loaded by Cursor, providing the AI with knowledge about the task management system\
3. Place your PRD document in the `.taskmaster/docs/` directory (e.g., `.taskmaster/docs/prd.txt`)\
4. Open Cursor's AI chat and switch to Agent mode\
\
### Alternative MCP Setup in Cursor\
\
You can also set up the MCP server in Cursor settings:\
\
1. Go to Cursor settings\
2. Navigate to the MCP section\
3. Click on "Add New MCP Server"\
4. Configure with the following details:\
   - Name: "Task Master"\
   - Type: "Command"\
   - Command: "npx -y --package=task-master-ai task-master-ai"\
5. Save the settings\
\
Once configured, you can interact with Task Master's task management commands directly through Cursor's interface, providing a more integrated experience.\
\
## Initial Task Generation\
\
In Cursor's AI chat, instruct the agent to generate tasks from your PRD:\
\
```\
Please use the task-master parse-prd command to generate tasks from my PRD. The PRD is located at .taskmaster/docs/prd.txt.\
```\
\
The agent will execute:\
\
```bash\
task-master parse-prd .taskmaster/docs/prd.txt\
```\
\
This will:\
\
- Parse your PRD document\
- Generate a structured `tasks.json` file with tasks, dependencies, priorities, and test strategies\
- The agent will understand this process due to the Cursor rules\
\
### Generate Individual Task Files\
\
Next, ask the agent to generate individual task files:\
\
```\
Please generate individual task files from tasks.json\
```\
\
The agent will execute:\
\
```bash\
task-master generate\
```\
\
This creates individual task files in the `tasks/` directory (e.g., `task_001.txt`, `task_002.txt`), making it easier to reference specific tasks.\
\
## AI-Driven Development Workflow\
\
The Cursor agent is pre-configured (via the rules file) to follow this workflow:\
\
### 1. Task Discovery and Selection\
\
Ask the agent to list available tasks:\
\
```\
What tasks are available to work on next?\
```\
\
The agent will:\
\
- Run `task-master list` to see all tasks\
- Run `task-master next` to determine the next task to work on\
- Analyze dependencies to determine which tasks are ready to be worked on\
- Prioritize tasks based on priority level and ID order\
- Suggest the next task(s) to implement\
\
### 2. Task Implementation\
\
When implementing a task, the agent will:\
\
- Reference the task's details section for implementation specifics\
- Consider dependencies on previous tasks\
- Follow the project's coding standards\
- Create appropriate tests based on the task's testStrategy\
\
You can ask:\
\
```\
Let's implement task 3. What does it involve?\
```\
\
### 3. Task Verification\
\
Before marking a task as complete, verify it according to:\
\
- The task's specified testStrategy\
- Any automated tests in the codebase\
- Manual verification if required\
\
### 4. Task Completion\
\
When a task is completed, tell the agent:\
\
```\
Task 3 is now complete. Please update its status.\
```\
\
The agent will execute:\
\
```bash\
task-master set-status --id=3 --status=done\
```\
\
### 5. Handling Implementation Drift\
\
If during implementation, you discover that:\
\
- The current approach differs significantly from what was planned\
- Future tasks need to be modified due to current implementation choices\
- New dependencies or requirements have emerged\
\
Tell the agent:\
\
```\
We've decided to use MongoDB instead of PostgreSQL. Can you update all future tasks (from ID 4) to reflect this change?\
```\
\
The agent will execute:\
\
```bash\
task-master update --from=4 --prompt="Now we are using MongoDB instead of PostgreSQL."\
\
# OR, if research is needed to find best practices for MongoDB:\
task-master update --from=4 --prompt="Update to use MongoDB, researching best practices" --research\
```\
\
This will rewrite or re-scope subsequent tasks in tasks.json while preserving completed work.\
\
### 6. Reorganizing Tasks\
\
If you need to reorganize your task structure:\
\
```\
I think subtask 5.2 would fit better as part of task 7 instead. Can you move it there?\
```\
\
The agent will execute:\
\
```bash\
task-master move --from=5.2 --to=7.3\
```\
\
You can reorganize tasks in various ways:\
\
- Moving a standalone task to become a subtask: `--from=5 --to=7`\
- Moving a subtask to become a standalone task: `--from=5.2 --to=7`\
- Moving a subtask to a different parent: `--from=5.2 --to=7.3`\
- Reordering subtasks within the same parent: `--from=5.2 --to=5.4`\
- Moving a task to a new ID position: `--from=5 --to=25` (even if task 25 doesn't exist yet)\
- Moving multiple tasks at once: `--from=10,11,12 --to=16,17,18` (must have same number of IDs, Taskmaster will look through each position)\
\
When moving tasks to new IDs:\
\
- The system automatically creates placeholder tasks for non-existent destination IDs\
- This prevents accidental data loss during reorganization\
- Any tasks that depend on moved tasks will have their dependencies updated\
- When moving a parent task, all its subtasks are automatically moved with it and renumbered\
\
This is particularly useful as your project understanding evolves and you need to refine your task structure.\
\
### 7. Resolving Merge Conflicts with Tasks\
\
When working with a team, you might encounter merge conflicts in your tasks.json file if multiple team members create tasks on different branches. The move command makes resolving these conflicts straightforward:\
\
```\
I just merged the main branch and there's a conflict with tasks.json. My teammates created tasks 10-15 while I created tasks 10-12 on my branch. Can you help me resolve this?\
```\
\
The agent will help you:\
\
1. Keep your teammates' tasks (10-15)\
2. Move your tasks to new positions to avoid conflicts:\
\
```bash\
# Move your tasks to new positions (e.g., 16-18)\
task-master move --from=10 --to=16\
task-master move --from=11 --to=17\
task-master move --from=12 --to=18\
```\
\
This approach preserves everyone's work while maintaining a clean task structure, making it much easier to handle task conflicts than trying to manually merge JSON files.\
\
### 8. Breaking Down Complex Tasks\
\
For complex tasks that need more granularity:\
\
```\
Task 5 seems complex. Can you break it down into subtasks?\
```\
\
The agent will execute:\
\
```bash\
task-master expand --id=5 --num=3\
```\
\
You can provide additional context:\
\
```\
Please break down task 5 with a focus on security considerations.\
```\
\
The agent will execute:\
\
```bash\
task-master expand --id=5 --prompt="Focus on security aspects"\
```\
\
You can also expand all pending tasks:\
\
```\
Please break down all pending tasks into subtasks.\
```\
\
The agent will execute:\
\
```bash\
task-master expand --all\
```\
\
For research-backed subtask generation using the configured research model:\
\
```\
Please break down task 5 using research-backed generation.\
```\
\
The agent will execute:\
\
```bash\
task-master expand --id=5 --research\
```\
\
## Example Cursor AI Interactions\
\
### Starting a new project\
\
```\
I've just initialized a new project with Claude Task Master. I have a PRD at .taskmaster/docs/prd.txt.\
Can you help me parse it and set up the initial tasks?\
```\
\
### Working on tasks\
\
```\
What's the next task I should work on? Please consider dependencies and priorities.\
```\
\
### Implementing a specific task\
\
```\
I'd like to implement task 4. Can you help me understand what needs to be done and how to approach it?\
```\
\
### Managing subtasks\
\
```\
I need to regenerate the subtasks for task 3 with a different approach. Can you help me clear and regenerate them?\
```\
\
### Handling changes\
\
```\
We've decided to use MongoDB instead of PostgreSQL. Can you update all future tasks to reflect this change?\
```\
\
### Completing work\
\
```\
I've finished implementing the authentication system described in task 2. All tests are passing.\
Please mark it as complete and tell me what I should work on next.\
```\
\
### Analyzing complexity\
\
```\
Can you analyze the complexity of our tasks to help me understand which ones need to be broken down further?\
```\
\
### Viewing complexity report\
\
```\
Can you show me the complexity report in a more readable format?\
```\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
# Task Master Command Reference\
\
Here's a comprehensive reference of all available commands:\
\
## Parse PRD\
\
```bash\
# Parse a PRD file and generate tasks\
task-master parse-prd <prd-file.txt>\
\
# Limit the number of tasks generated\
task-master parse-prd <prd-file.txt> --num-tasks=10\
```\
\
## List Tasks\
\
```bash\
# List all tasks\
task-master list\
\
# List tasks with a specific status\
task-master list --status=<status>\
\
# List tasks with subtasks\
task-master list --with-subtasks\
\
# List tasks with a specific status and include subtasks\
task-master list --status=<status> --with-subtasks\
```\
\
## Show Next Task\
\
```bash\
# Show the next task to work on based on dependencies and status\
task-master next\
```\
\
## Show Specific Task\
\
```bash\
# Show details of a specific task\
task-master show <id>\
# or\
task-master show --id=<id>\
\
# View a specific subtask (e.g., subtask 2 of task 1)\
task-master show 1.2\
```\
\
## Update Tasks\
\
```bash\
# Update tasks from a specific ID and provide context\
task-master update --from=<id> --prompt="<prompt>"\
\
# Update tasks using research role\
task-master update --from=<id> --prompt="<prompt>" --research\
```\
\
## Update a Specific Task\
\
```bash\
# Update a single task by ID with new information\
task-master update-task --id=<id> --prompt="<prompt>"\
\
# Use research-backed updates\
task-master update-task --id=<id> --prompt="<prompt>" --research\
```\
\
## Update a Subtask\
\
```bash\
# Append additional information to a specific subtask\
task-master update-subtask --id=<parentId.subtaskId> --prompt="<prompt>"\
\
# Example: Add details about API rate limiting to subtask 2 of task 5\
task-master update-subtask --id=5.2 --prompt="Add rate limiting of 100 requests per minute"\
\
# Use research-backed updates\
task-master update-subtask --id=<parentId.subtaskId> --prompt="<prompt>" --research\
```\
\
Unlike the `update-task` command which replaces task information, the `update-subtask` command _appends_ new information to the existing subtask details, marking it with a timestamp. This is useful for iteratively enhancing subtasks while preserving the original content.\
\
## Generate Task Files\
\
```bash\
# Generate individual task files from tasks.json\
task-master generate\
```\
\
## Set Task Status\
\
```bash\
# Set status of a single task\
task-master set-status --id=<id> --status=<status>\
\
# Set status for multiple tasks\
task-master set-status --id=1,2,3 --status=<status>\
\
# Set status for subtasks\
task-master set-status --id=1.1,1.2 --status=<status>\
```\
\
When marking a task as "done", all of its subtasks will automatically be marked as "done" as well.\
\
## Expand Tasks\
\
```bash\
# Expand a specific task with subtasks\
task-master expand --id=<id> --num=<number>\
\
# Expand with additional context\
task-master expand --id=<id> --prompt="<context>"\
\
# Expand all pending tasks\
task-master expand --all\
\
# Force regeneration of subtasks for tasks that already have them\
task-master expand --all --force\
\
# Research-backed subtask generation for a specific task\
task-master expand --id=<id> --research\
\
# Research-backed generation for all tasks\
task-master expand --all --research\
```\
\
## Clear Subtasks\
\
```bash\
# Clear subtasks from a specific task\
task-master clear-subtasks --id=<id>\
\
# Clear subtasks from multiple tasks\
task-master clear-subtasks --id=1,2,3\
\
# Clear subtasks from all tasks\
task-master clear-subtasks --all\
```\
\
## Analyze Task Complexity\
\
```bash\
# Analyze complexity of all tasks\
task-master analyze-complexity\
\
# Save report to a custom location\
task-master analyze-complexity --output=my-report.json\
\
# Use a specific LLM model\
task-master analyze-complexity --model=claude-3-opus-20240229\
\
# Set a custom complexity threshold (1-10)\
task-master analyze-complexity --threshold=6\
\
# Use an alternative tasks file\
task-master analyze-complexity --file=custom-tasks.json\
\
# Use Perplexity AI for research-backed complexity analysis\
task-master analyze-complexity --research\
```\
\
## View Complexity Report\
\
```bash\
# Display the task complexity analysis report\
task-master complexity-report\
\
# View a report at a custom location\
task-master complexity-report --file=my-report.json\
```\
\
## Managing Task Dependencies\
\
```bash\
# Add a dependency to a task\
task-master add-dependency --id=<id> --depends-on=<id>\
\
# Remove a dependency from a task\
task-master remove-dependency --id=<id> --depends-on=<id>\
\
# Validate dependencies without fixing them\
task-master validate-dependencies\
\
# Find and fix invalid dependencies automatically\
task-master fix-dependencies\
```\
\
## Move Tasks\
\
```bash\
# Move a task or subtask to a new position\
task-master move --from=<id> --to=<id>\
\
# Examples:\
# Move task to become a subtask\
task-master move --from=5 --to=7\
\
# Move subtask to become a standalone task\
task-master move --from=5.2 --to=7\
\
# Move subtask to a different parent\
task-master move --from=5.2 --to=7.3\
\
# Reorder subtasks within the same parent\
task-master move --from=5.2 --to=5.4\
\
# Move a task to a new ID position (creates placeholder if doesn't exist)\
task-master move --from=5 --to=25\
\
# Move multiple tasks at once (must have the same number of IDs)\
task-master move --from=10,11,12 --to=16,17,18\
```\
\
## Add a New Task\
\
```bash\
# Add a new task using AI (main role)\
task-master add-task --prompt="Description of the new task"\
\
# Add a new task using AI (research role)\
task-master add-task --prompt="Description of the new task" --research\
\
# Add a task with dependencies\
task-master add-task --prompt="Description" --dependencies=1,2,3\
\
# Add a task with priority\
task-master add-task --prompt="Description" --priority=high\
```\
\
## Initialize a Project\
\
```bash\
# Initialize a new project with Task Master structure\
task-master init\
```\
\
## Configure AI Models\
\
```bash\
# View current AI model configuration and API key status\
task-master models\
\
# Set the primary model for generation/updates (provider inferred if known)\
task-master models --set-main=claude-3-opus-20240229\
\
# Set the research model\
task-master models --set-research=sonar-pro\
\
# Set the fallback model\
task-master models --set-fallback=claude-3-haiku-20240307\
\
# Set a custom Ollama model for the main role\
task-master models --set-main=my-local-llama --ollama\
\
# Set a custom OpenRouter model for the research role\
task-master models --set-research=google/gemini-pro --openrouter\
\
# Run interactive setup to configure models, including custom ones\
task-master models --setup\
```\
\
Configuration is stored in `.taskmasterconfig` in your project root. API keys are still managed via `.env` or MCP configuration. Use `task-master models` without flags to see available built-in models. Use `--setup` for a guided experience.\
\uc0\u8232 \
\
\
\
\
\
\
\
\
\
\
# Configuration\
\
Taskmaster uses two primary methods for configuration:\
\
1.  **`.taskmaster/config.json` File (Recommended - New Structure)**\
\
    - This JSON file stores most configuration settings, including AI model selections, parameters, logging levels, and project defaults.\
    - **Location:** This file is created in the `.taskmaster/` directory when you run the `task-master models --setup` interactive setup or initialize a new project with `task-master init`.\
    - **Migration:** Existing projects with `.taskmasterconfig` in the root will continue to work, but should be migrated to the new structure using `task-master migrate`.\
    - **Management:** Use the `task-master models --setup` command (or `models` MCP tool) to interactively create and manage this file. You can also set specific models directly using `task-master models --set-<role>=<model_id>`, adding `--ollama` or `--openrouter` flags for custom models. Manual editing is possible but not recommended unless you understand the structure.\
    - **Example Structure:**\
      ```json\
      \{\
        "models": \{\
          "main": \{\
            "provider": "anthropic",\
            "modelId": "claude-3-7-sonnet-20250219",\
            "maxTokens": 64000,\
            "temperature": 0.2,\
            "baseURL": "https://api.anthropic.com/v1"\
          \},\
          "research": \{\
            "provider": "perplexity",\
            "modelId": "sonar-pro",\
            "maxTokens": 8700,\
            "temperature": 0.1,\
            "baseURL": "https://api.perplexity.ai/v1"\
          \},\
          "fallback": \{\
            "provider": "anthropic",\
            "modelId": "claude-3-5-sonnet",\
            "maxTokens": 64000,\
            "temperature": 0.2\
          \}\
        \},\
        "global": \{\
          "logLevel": "info",\
          "debug": false,\
          "defaultSubtasks": 5,\
          "defaultPriority": "medium",\
          "projectName": "Your Project Name",\
          "ollamaBaseURL": "http://localhost:11434/api",\
          "azureBaseURL": "https://your-endpoint.azure.com/",\
          "vertexProjectId": "your-gcp-project-id",\
          "vertexLocation": "us-central1"\
        \}\
      \}\
      ```\
\
2.  **Legacy `.taskmasterconfig` File (Backward Compatibility)**\
\
    - For projects that haven't migrated to the new structure yet.\
    - **Location:** Project root directory.\
    - **Migration:** Use `task-master migrate` to move this to `.taskmaster/config.json`.\
    - **Deprecation:** While still supported, you'll see warnings encouraging migration to the new structure.\
\
## Environment Variables (`.env` file or MCP `env` block - For API Keys Only)\
\
- Used **exclusively** for sensitive API keys and specific endpoint URLs.\
- **Location:**\
  - For CLI usage: Create a `.env` file in your project root.\
  - For MCP/Cursor usage: Configure keys in the `env` section of your `.cursor/mcp.json` file.\
- **Required API Keys (Depending on configured providers):**\
  - `ANTHROPIC_API_KEY`: Your Anthropic API key.\
  - `PERPLEXITY_API_KEY`: Your Perplexity API key.\
  - `OPENAI_API_KEY`: Your OpenAI API key.\
  - `GOOGLE_API_KEY`: Your Google API key (also used for Vertex AI provider).\
  - `MISTRAL_API_KEY`: Your Mistral API key.\
  - `AZURE_OPENAI_API_KEY`: Your Azure OpenAI API key (also requires `AZURE_OPENAI_ENDPOINT`).\
  - `OPENROUTER_API_KEY`: Your OpenRouter API key.\
  - `XAI_API_KEY`: Your X-AI API key.\
- **Optional Endpoint Overrides:**\
  - **Per-role `baseURL` in `.taskmasterconfig`:** You can add a `baseURL` property to any model role (`main`, `research`, `fallback`) to override the default API endpoint for that provider. If omitted, the provider's standard endpoint is used.\
  - `AZURE_OPENAI_ENDPOINT`: Required if using Azure OpenAI key (can also be set as `baseURL` for the Azure model role).\
  - `OLLAMA_BASE_URL`: Override the default Ollama API URL (Default: `http://localhost:11434/api`).\
  - `VERTEX_PROJECT_ID`: Your Google Cloud project ID for Vertex AI. Required when using the 'vertex' provider.\
  - `VERTEX_LOCATION`: Google Cloud region for Vertex AI (e.g., 'us-central1'). Default is 'us-central1'.\
  - `GOOGLE_APPLICATION_CREDENTIALS`: Path to service account credentials JSON file for Google Cloud auth (alternative to API key for Vertex AI).\
\
**Important:** Settings like model ID selections (`main`, `research`, `fallback`), `maxTokens`, `temperature`, `logLevel`, `defaultSubtasks`, `defaultPriority`, and `projectName` are **managed in `.taskmaster/config.json`** (or `.taskmasterconfig` for unmigrated projects), not environment variables.\
\
## Example `.env` File (for API Keys)\
\
```\
# Required API keys for providers configured in .taskmasterconfig\
ANTHROPIC_API_KEY=sk-ant-api03-your-key-here\
PERPLEXITY_API_KEY=pplx-your-key-here\
# OPENAI_API_KEY=sk-your-key-here\
# GOOGLE_API_KEY=AIzaSy...\
# etc.\
\
# Optional Endpoint Overrides\
# AZURE_OPENAI_ENDPOINT=https://your-azure-endpoint.openai.azure.com/\
# OLLAMA_BASE_URL=http://custom-ollama-host:11434/api\
\
# Google Vertex AI Configuration (Required if using 'vertex' provider)\
# VERTEX_PROJECT_ID=your-gcp-project-id\
# VERTEX_LOCATION=us-central1\
# GOOGLE_APPLICATION_CREDENTIALS=/path/to/service-account-credentials.json\
```\
\
## Troubleshooting\
\
### Configuration Errors\
\
- If Task Master reports errors about missing configuration or cannot find the config file, run `task-master models --setup` in your project root to create or repair the file.\
- For new projects, config will be created at `.taskmaster/config.json`. For legacy projects, you may want to use `task-master migrate` to move to the new structure.\
- Ensure API keys are correctly placed in your `.env` file (for CLI) or `.cursor/mcp.json` (for MCP) and are valid for the providers selected in your config file.\
\
### If `task-master init` doesn't respond:\
\
Try running it with Node directly:\
\
```bash\
node node_modules/claude-task-master/scripts/init.js\
```\
\
Or clone the repository and run:\
\
```bash\
git clone https://github.com/eyaltoledano/claude-task-master.git\
cd claude-task-master\
node scripts/init.js\
```\
\
## Provider-Specific Configuration\
\
### Google Vertex AI Configuration\
\
Google Vertex AI is Google Cloud's enterprise AI platform and requires specific configuration:\
\
1. **Prerequisites**:\
   - A Google Cloud account with Vertex AI API enabled\
   - Either a Google API key with Vertex AI permissions OR a service account with appropriate roles\
   - A Google Cloud project ID\
2. **Authentication Options**:\
   - **API Key**: Set the `GOOGLE_API_KEY` environment variable\
   - **Service Account**: Set `GOOGLE_APPLICATION_CREDENTIALS` to point to your service account JSON file\
3. **Required Configuration**:\
   - Set `VERTEX_PROJECT_ID` to your Google Cloud project ID\
   - Set `VERTEX_LOCATION` to your preferred Google Cloud region (default: us-central1)\
4. **Example Setup**:\
\
   ```bash\
   # In .env file\
   GOOGLE_API_KEY=AIzaSyXXXXXXXXXXXXXXXXXXXXXXXXX\
   VERTEX_PROJECT_ID=my-gcp-project-123\
   VERTEX_LOCATION=us-central1\
   ```\
\
   Or using service account:\
\
   ```bash\
   # In .env file\
   GOOGLE_APPLICATION_CREDENTIALS=/path/to/service-account.json\
   VERTEX_PROJECT_ID=my-gcp-project-123\
   VERTEX_LOCATION=us-central1\
   ```\
\
5. **In .taskmasterconfig**:\
   ```json\
   "global": \{\
     "vertexProjectId": "my-gcp-project-123",\
     "vertexLocation": "us-central1"\
   \}\
   ```\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
# Example Cursor AI Interactions\
\
Here are some common interactions with Cursor AI when using Task Master:\
\
## Starting a new project\
\
```\
I've just initialized a new project with Claude Task Master. I have a PRD at .taskmaster/docs/prd.txt.\
Can you help me parse it and set up the initial tasks?\
```\
\
## Working on tasks\
\
```\
What's the next task I should work on? Please consider dependencies and priorities.\
```\
\
## Implementing a specific task\
\
```\
I'd like to implement task 4. Can you help me understand what needs to be done and how to approach it?\
```\
\
## Managing subtasks\
\
```\
I need to regenerate the subtasks for task 3 with a different approach. Can you help me clear and regenerate them?\
```\
\
## Handling changes\
\
```\
I've decided to use MongoDB instead of PostgreSQL. Can you update all future tasks to reflect this change?\
```\
\
## Completing work\
\
```\
I've finished implementing the authentication system described in task 2. All tests are passing.\
Please mark it as complete and tell me what I should work on next.\
```\
\
## Reorganizing tasks\
\
```\
I think subtask 5.2 would fit better as part of task 7. Can you move it there?\
```\
\
(Agent runs: `task-master move --from=5.2 --to=7.3`)\
\
```\
Task 8 should actually be a subtask of task 4. Can you reorganize this?\
```\
\
(Agent runs: `task-master move --from=8 --to=4.1`)\
\
```\
I just merged the main branch and there's a conflict in tasks.json. My teammates created tasks 10-15 on their branch while I created tasks 10-12 on my branch. Can you help me resolve this by moving my tasks?\
```\
\
(Agent runs:\
\
```bash\
task-master move --from=10 --to=16\
task-master move --from=11 --to=17\
task-master move --from=12 --to=18\
```\
\
)\
\
## Analyzing complexity\
\
```\
Can you analyze the complexity of our tasks to help me understand which ones need to be broken down further?\
```\
\
## Viewing complexity report\
\
```\
Can you show me the complexity report in a more readable format?\
```\
\
### Breaking Down Complex Tasks\
\
```\
Task 5 seems complex. Can you break it down into subtasks?\
```\
\
(Agent runs: `task-master expand --id=5`)\
\
```\
Please break down task 5 using research-backed generation.\
```\
\
(Agent runs: `task-master expand --id=5 --research`)\
\
### Updating Tasks with Research\
\
```\
We need to update task 15 based on the latest React Query v5 changes. Can you research this and update the task?\
```\
\
(Agent runs: `task-master update-task --id=15 --prompt="Update based on React Query v5 changes" --research`)\
\
### Adding Tasks with Research\
\
```\
Please add a new task to implement user profile image uploads using Cloudinary, research the best approach.\
```\
\
(Agent runs: `task-master add-task --prompt="Implement user profile image uploads using Cloudinary" --research`)\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
# Migration Guide: New .taskmaster Directory Structure\
\
## Overview\
\
Task Master v0.16.0 introduces a new `.taskmaster/` directory structure to keep your project directories clean and organized. This guide explains the benefits of the new structure and how to migrate existing projects.\
\
## What's New\
\
### Before (Legacy Structure)\
\
```\
your-project/\

\f1 \'a9\'c0
\f0 \uc0\u9472 \u9472  tasks/                    # Task files\
\uc0\u9474    
\f1 \'a9\'c0
\f0 \uc0\u9472 \u9472  tasks.json\
\uc0\u9474    
\f1 \'a9\'c0
\f0 \uc0\u9472 \u9472  task-1.txt\
\uc0\u9474    \u9492 \u9472 \u9472  task-2.txt\

\f1 \'a9\'c0
\f0 \uc0\u9472 \u9472  scripts/                  # PRD and reports\
\uc0\u9474    
\f1 \'a9\'c0
\f0 \uc0\u9472 \u9472  prd.txt\
\uc0\u9474    
\f1 \'a9\'c0
\f0 \uc0\u9472 \u9472  example_prd.txt\
\uc0\u9474    \u9492 \u9472 \u9472  task-complexity-report.json\

\f1 \'a9\'c0
\f0 \uc0\u9472 \u9472  .taskmasterconfig         # Configuration\
\uc0\u9492 \u9472 \u9472  ... (your project files)\
```\
\
### After (New Structure)\
\
```\
your-project/\

\f1 \'a9\'c0
\f0 \uc0\u9472 \u9472  .taskmaster/              # Consolidated Task Master files\
\uc0\u9474    
\f1 \'a9\'c0
\f0 \uc0\u9472 \u9472  config.json          # Configuration (was .taskmasterconfig)\
\uc0\u9474    
\f1 \'a9\'c0
\f0 \uc0\u9472 \u9472  tasks/               # Task files\
\uc0\u9474    \u9474    
\f1 \'a9\'c0
\f0 \uc0\u9472 \u9472  tasks.json\
\uc0\u9474    \u9474    
\f1 \'a9\'c0
\f0 \uc0\u9472 \u9472  task-1.txt\
\uc0\u9474    \u9474    \u9492 \u9472 \u9472  task-2.txt\
\uc0\u9474    
\f1 \'a9\'c0
\f0 \uc0\u9472 \u9472  docs/                # Project documentation\
\uc0\u9474    \u9474    \u9492 \u9472 \u9472  prd.txt\
\uc0\u9474    
\f1 \'a9\'c0
\f0 \uc0\u9472 \u9472  reports/             # Generated reports\
\uc0\u9474    \u9474    \u9492 \u9472 \u9472  task-complexity-report.json\
\uc0\u9474    \u9492 \u9472 \u9472  templates/           # Example/template files\
\uc0\u9474        \u9492 \u9472 \u9472  example_prd.txt\
\uc0\u9492 \u9472 \u9472  ... (your project files)\
```\
\
## Benefits of the New Structure\
\

\f2 \uc0\u9989 
\f0  **Cleaner Project Root**: No more scattered Task Master files  \

\f2 \uc0\u9989 
\f0  **Better Organization**: Logical separation of tasks, docs, reports, and templates  \

\f2 \uc0\u9989 
\f0  **Hidden by Default**: `.taskmaster/` directory is hidden from most file browsers  \

\f2 \uc0\u9989 
\f0  **Future-Proof**: Centralized location for Task Master extensions  \

\f2 \uc0\u9989 
\f0  **Backward Compatible**: Existing projects continue to work until migrated\
\
## Migration Options\
\
### Option 1: Automatic Migration (Recommended)\
\
Task Master provides a built-in migration command that handles everything automatically:\
\
#### CLI Migration\
\
```bash\
# Dry run to see what would be migrated\
task-master migrate --dry-run\
\
# Perform the migration with backup\
task-master migrate --backup\
\
# Force migration (overwrites existing files)\
task-master migrate --force\
\
# Clean up legacy files after migration\
task-master migrate --cleanup\
```\
\
#### MCP Migration (Cursor/AI Editors)\
\
Ask your AI assistant:\
\
```\
Please migrate my Task Master project to the new .taskmaster directory structure\
```\
\
### Option 2: Manual Migration\
\
If you prefer to migrate manually:\
\
1. **Create the new directory structure:**\
\
   ```bash\
   mkdir -p .taskmaster/\{tasks,docs,reports,templates\}\
   ```\
\
2. **Move your files:**\
\
   ```bash\
   # Move tasks\
   mv tasks/* .taskmaster/tasks/\
\
   # Move configuration\
   mv .taskmasterconfig .taskmaster/config.json\
\
   # Move PRD and documentation\
   mv scripts/prd.txt .taskmaster/docs/\
   mv scripts/example_prd.txt .taskmaster/templates/\
\
   # Move reports (if they exist)\
   mv scripts/task-complexity-report.json .taskmaster/reports/ 2>/dev/null || true\
   ```\
\
3. **Clean up empty directories:**\
   ```bash\
   rmdir tasks scripts 2>/dev/null || true\
   ```\
\
## What Gets Migrated\
\
The migration process handles these file types:\
\
### Tasks Directory 
\f3 \uc0\u8594 
\f0  `.taskmaster/tasks/`\
\
- `tasks.json`\
- Individual task text files (`.txt`)\
\
### Scripts Directory 
\f3 \uc0\u8594 
\f0  Multiple Destinations\
\
- **PRD files** 
\f3 \uc0\u8594 
\f0  `.taskmaster/docs/`\
  - `prd.txt`, `requirements.txt`, etc.\
- **Example/Template files** 
\f3 \uc0\u8594 
\f0  `.taskmaster/templates/`\
  - `example_prd.txt`, template files\
- **Reports** 
\f3 \uc0\u8594 
\f0  `.taskmaster/reports/`\
  - `task-complexity-report.json`\
\
### Configuration\
\
- `.taskmasterconfig` 
\f3 \uc0\u8594 
\f0  `.taskmaster/config.json`\
\
## After Migration\
\
Once migrated, Task Master will:\
\

\f2 \uc0\u9989 
\f0  **Automatically use** the new directory structure  \

\f2 \uc0\u9989 
\f0  **Show deprecation warnings** when legacy files are detected  \

\f2 \uc0\u9989 
\f0  **Create new files** in the proper locations  \

\f2 \uc0\u9989 
\f0  **Fall back gracefully** to legacy locations if new ones don't exist\
\
### Verification\
\
After migration, verify everything works:\
\
1. **List your tasks:**\
\
   ```bash\
   task-master list\
   ```\
\
2. **Check your configuration:**\
\
   ```bash\
   task-master models\
   ```\
\
3. **Generate new task files:**\
   ```bash\
   task-master generate\
   ```\
\
## Troubleshooting\
\
### Migration Issues\
\
**Q: Migration says "no files to migrate"**  \
A: Your project may already be using the new structure or have no Task Master files to migrate.\
\
**Q: Migration fails with permission errors**  \
A: Ensure you have write permissions in your project directory.\
\
**Q: Some files weren't migrated**  \
A: Check the migration output - some files may not match the expected patterns. You can migrate these manually.\
\
### Working with Legacy Projects\
\
If you're working with an older project that hasn't been migrated:\
\
- Task Master will continue to work with the old structure\
- You'll see deprecation warnings in the output\
- New files will still be created in legacy locations\
- Use the migration command when ready to upgrade\
\
### New Project Initialization\
\
New projects automatically use the new structure:\
\
```bash\
task-master init  # Creates .taskmaster/ structure\
```\
\
## Path Changes for Developers\
\
If you're developing tools or scripts that interact with Task Master files:\
\
### Configuration File\
\
- **Old:** `.taskmasterconfig`\
- **New:** `.taskmaster/config.json`\
- **Fallback:** Task Master checks both locations\
\
### Tasks File\
\
- **Old:** `tasks/tasks.json`\
- **New:** `.taskmaster/tasks/tasks.json`\
- **Fallback:** Task Master checks both locations\
\
### Reports\
\
- **Old:** `scripts/task-complexity-report.json`\
- **New:** `.taskmaster/reports/task-complexity-report.json`\
- **Fallback:** Task Master checks both locations\
\
### PRD Files\
\
- **Old:** `scripts/prd.txt`\
- **New:** `.taskmaster/docs/prd.txt`\
- **Fallback:** Task Master checks both locations\
\
## Need Help?\
\
If you encounter issues during migration:\
\
1. **Check the logs:** Add `--debug` flag for detailed output\
2. **Backup first:** Always use `--backup` option for safety\
3. **Test with dry-run:** Use `--dry-run` to preview changes\
4. **Ask for help:** Use our Discord community or GitHub issues\
\
---\
\
_This migration guide applies to Task Master v3.x and later. For older versions, please upgrade to the latest version first._\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
# Available Models as of May 27, 2025\
\
## Main Models\
\
| Provider   | Model Name                                     | SWE Score | Input Cost | Output Cost |\
| ---------- | ---------------------------------------------- | --------- | ---------- | ----------- |\
| anthropic  | claude-sonnet-4-20250514                       | 0.727     | 3          | 15          |\
| anthropic  | claude-opus-4-20250514                         | 0.725     | 15         | 75          |\
| anthropic  | claude-3-7-sonnet-20250219                     | 0.623     | 3          | 15          |\
| anthropic  | claude-3-5-sonnet-20241022                     | 0.49      | 3          | 15          |\
| openai     | gpt-4o                                         | 0.332     | 2.5        | 10          |\
| openai     | o1                                             | 0.489     | 15         | 60          |\
| openai     | o3                                             | 0.5       | 10         | 40          |\
| openai     | o3-mini                                        | 0.493     | 1.1        | 4.4         |\
| openai     | o4-mini                                        | 0.45      | 1.1        | 4.4         |\
| openai     | o1-mini                                        | 0.4       | 1.1        | 4.4         |\
| openai     | o1-pro                                         | \'97         | 150        | 600         |\
| openai     | gpt-4-5-preview                                | 0.38      | 75         | 150         |\
| openai     | gpt-4-1-mini                                   | \'97         | 0.4        | 1.6         |\
| openai     | gpt-4-1-nano                                   | \'97         | 0.1        | 0.4         |\
| openai     | gpt-4o-mini                                    | 0.3       | 0.15       | 0.6         |\
| google     | gemini-2.5-pro-preview-05-06                   | 0.638     | \'97          | \'97           |\
| google     | gemini-2.5-pro-preview-03-25                   | 0.638     | \'97          | \'97           |\
| google     | gemini-2.5-flash-preview-04-17                 | \'97         | \'97          | \'97           |\
| google     | gemini-2.0-flash                               | 0.754     | 0.15       | 0.6         |\
| google     | gemini-2.0-flash-lite                          | \'97         | \'97          | \'97           |\
| perplexity | sonar-reasoning-pro                            | 0.211     | 2          | 8           |\
| perplexity | sonar-reasoning                                | 0.211     | 1          | 5           |\
| xai        | grok-3                                         | \'97         | 3          | 15          |\
| xai        | grok-3-fast                                    | \'97         | 5          | 25          |\
| ollama     | devstral:latest                                | \'97         | 0          | 0           |\
| ollama     | qwen3:latest                                   | \'97         | 0          | 0           |\
| ollama     | qwen3:14b                                      | \'97         | 0          | 0           |\
| ollama     | qwen3:32b                                      | \'97         | 0          | 0           |\
| ollama     | mistral-small3.1:latest                        | \'97         | 0          | 0           |\
| ollama     | llama3.3:latest                                | \'97         | 0          | 0           |\
| ollama     | phi4:latest                                    | \'97         | 0          | 0           |\
| openrouter | google/gemini-2.5-flash-preview-05-20          | \'97         | 0.15       | 0.6         |\
| openrouter | google/gemini-2.5-flash-preview-05-20:thinking | \'97         | 0.15       | 3.5         |\
| openrouter | google/gemini-2.5-pro-exp-03-25                | \'97         | 0          | 0           |\
| openrouter | deepseek/deepseek-chat-v3-0324:free            | \'97         | 0          | 0           |\
| openrouter | deepseek/deepseek-chat-v3-0324                 | \'97         | 0.27       | 1.1         |\
| openrouter | openai/gpt-4.1                                 | \'97         | 2          | 8           |\
| openrouter | openai/gpt-4.1-mini                            | \'97         | 0.4        | 1.6         |\
| openrouter | openai/gpt-4.1-nano                            | \'97         | 0.1        | 0.4         |\
| openrouter | openai/o3                                      | \'97         | 10         | 40          |\
| openrouter | openai/codex-mini                              | \'97         | 1.5        | 6           |\
| openrouter | openai/gpt-4o-mini                             | \'97         | 0.15       | 0.6         |\
| openrouter | openai/o4-mini                                 | 0.45      | 1.1        | 4.4         |\
| openrouter | openai/o4-mini-high                            | \'97         | 1.1        | 4.4         |\
| openrouter | openai/o1-pro                                  | \'97         | 150        | 600         |\
| openrouter | meta-llama/llama-3.3-70b-instruct              | \'97         | 120        | 600         |\
| openrouter | meta-llama/llama-4-maverick                    | \'97         | 0.18       | 0.6         |\
| openrouter | meta-llama/llama-4-scout                       | \'97         | 0.08       | 0.3         |\
| openrouter | qwen/qwen-max                                  | \'97         | 1.6        | 6.4         |\
| openrouter | qwen/qwen-turbo                                | \'97         | 0.05       | 0.2         |\
| openrouter | qwen/qwen3-235b-a22b                           | \'97         | 0.14       | 2           |\
| openrouter | mistralai/mistral-small-3.1-24b-instruct:free  | \'97         | 0          | 0           |\
| openrouter | mistralai/mistral-small-3.1-24b-instruct       | \'97         | 0.1        | 0.3         |\
| openrouter | mistralai/devstral-small                       | \'97         | 0.1        | 0.3         |\
| openrouter | mistralai/mistral-nemo                         | \'97         | 0.03       | 0.07        |\
| openrouter | thudm/glm-4-32b:free                           | \'97         | 0          | 0           |\
\
## Research Models\
\
| Provider   | Model Name                 | SWE Score | Input Cost | Output Cost |\
| ---------- | -------------------------- | --------- | ---------- | ----------- |\
| openai     | gpt-4o-search-preview      | 0.33      | 2.5        | 10          |\
| openai     | gpt-4o-mini-search-preview | 0.3       | 0.15       | 0.6         |\
| perplexity | sonar-pro                  | \'97         | 3          | 15          |\
| perplexity | sonar                      | \'97         | 1          | 1           |\
| perplexity | deep-research              | 0.211     | 2          | 8           |\
| xai        | grok-3                     | \'97         | 3          | 15          |\
| xai        | grok-3-fast                | \'97         | 5          | 25          |\
\
## Fallback Models\
\
| Provider   | Model Name                                     | SWE Score | Input Cost | Output Cost |\
| ---------- | ---------------------------------------------- | --------- | ---------- | ----------- |\
| anthropic  | claude-sonnet-4-20250514                       | 0.727     | 3          | 15          |\
| anthropic  | claude-opus-4-20250514                         | 0.725     | 15         | 75          |\
| anthropic  | claude-3-7-sonnet-20250219                     | 0.623     | 3          | 15          |\
| anthropic  | claude-3-5-sonnet-20241022                     | 0.49      | 3          | 15          |\
| openai     | gpt-4o                                         | 0.332     | 2.5        | 10          |\
| openai     | o3                                             | 0.5       | 10         | 40          |\
| openai     | o4-mini                                        | 0.45      | 1.1        | 4.4         |\
| google     | gemini-2.5-pro-preview-05-06                   | 0.638     | \'97          | \'97           |\
| google     | gemini-2.5-pro-preview-03-25                   | 0.638     | \'97          | \'97           |\
| google     | gemini-2.5-flash-preview-04-17                 | \'97         | \'97          | \'97           |\
| google     | gemini-2.0-flash                               | 0.754     | 0.15       | 0.6         |\
| google     | gemini-2.0-flash-lite                          | \'97         | \'97          | \'97           |\
| perplexity | sonar-reasoning-pro                            | 0.211     | 2          | 8           |\
| perplexity | sonar-reasoning                                | 0.211     | 1          | 5           |\
| xai        | grok-3                                         | \'97         | 3          | 15          |\
| xai        | grok-3-fast                                    | \'97         | 5          | 25          |\
| ollama     | devstral:latest                                | \'97         | 0          | 0           |\
| ollama     | qwen3:latest                                   | \'97         | 0          | 0           |\
| ollama     | qwen3:14b                                      | \'97         | 0          | 0           |\
| ollama     | qwen3:32b                                      | \'97         | 0          | 0           |\
| ollama     | mistral-small3.1:latest                        | \'97         | 0          | 0           |\
| ollama     | llama3.3:latest                                | \'97         | 0          | 0           |\
| ollama     | phi4:latest                                    | \'97         | 0          | 0           |\
| openrouter | google/gemini-2.5-flash-preview-05-20          | \'97         | 0.15       | 0.6         |\
| openrouter | google/gemini-2.5-flash-preview-05-20:thinking | \'97         | 0.15       | 3.5         |\
| openrouter | google/gemini-2.5-pro-exp-03-25                | \'97         | 0          | 0           |\
| openrouter | deepseek/deepseek-chat-v3-0324:free            | \'97         | 0          | 0           |\
| openrouter | openai/gpt-4.1                                 | \'97         | 2          | 8           |\
| openrouter | openai/gpt-4.1-mini                            | \'97         | 0.4        | 1.6         |\
| openrouter | openai/gpt-4.1-nano                            | \'97         | 0.1        | 0.4         |\
| openrouter | openai/o3                                      | \'97         | 10         | 40          |\
| openrouter | openai/codex-mini                              | \'97         | 1.5        | 6           |\
| openrouter | openai/gpt-4o-mini                             | \'97         | 0.15       | 0.6         |\
| openrouter | openai/o4-mini                                 | 0.45      | 1.1        | 4.4         |\
| openrouter | openai/o4-mini-high                            | \'97         | 1.1        | 4.4         |\
| openrouter | openai/o1-pro                                  | \'97         | 150        | 600         |\
| openrouter | meta-llama/llama-3.3-70b-instruct              | \'97         | 120        | 600         |\
| openrouter | meta-llama/llama-4-maverick                    | \'97         | 0.18       | 0.6         |\
| openrouter | meta-llama/llama-4-scout                       | \'97         | 0.08       | 0.3         |\
| openrouter | qwen/qwen-max                                  | \'97         | 1.6        | 6.4         |\
| openrouter | qwen/qwen-turbo                                | \'97         | 0.05       | 0.2         |\
| openrouter | qwen/qwen3-235b-a22b                           | \'97         | 0.14       | 2           |\
| openrouter | mistralai/mistral-small-3.1-24b-instruct:free  | \'97         | 0          | 0           |\
| openrouter | mistralai/mistral-small-3.1-24b-instruct       | \'97         | 0.1        | 0.3         |\
| openrouter | mistralai/mistral-nemo                         | \'97         | 0.03       | 0.07        |\
| openrouter | thudm/glm-4-32b:free                           | \'97         | 0          | 0           |\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
# Task Structure\
\
Tasks in Task Master follow a specific format designed to provide comprehensive information for both humans and AI assistants.\
\
## Task Fields in tasks.json\
\
Tasks in tasks.json have the following structure:\
\
- `id`: Unique identifier for the task (Example: `1`)\
- `title`: Brief, descriptive title of the task (Example: `"Initialize Repo"`)\
- `description`: Concise description of what the task involves (Example: `"Create a new repository, set up initial structure."`)\
- `status`: Current state of the task (Example: `"pending"`, `"done"`, `"deferred"`)\
- `dependencies`: IDs of tasks that must be completed before this task (Example: `[1, 2]`)\
  - Dependencies are displayed with status indicators (
\f2 \uc0\u9989 
\f0  for completed, 
\f2 \uc0\u9201 \u65039 
\f0  for pending)\
  - This helps quickly identify which prerequisite tasks are blocking work\
- `priority`: Importance level of the task (Example: `"high"`, `"medium"`, `"low"`)\
- `details`: In-depth implementation instructions (Example: `"Use GitHub client ID/secret, handle callback, set session token."`)\
- `testStrategy`: Verification approach (Example: `"Deploy and call endpoint to confirm 'Hello World' response."`)\
- `subtasks`: List of smaller, more specific tasks that make up the main task (Example: `[\{"id": 1, "title": "Configure OAuth", ...\}]`)\
\
## Task File Format\
\
Individual task files follow this format:\
\
```\
# Task ID: <id>\
# Title: <title>\
# Status: <status>\
# Dependencies: <comma-separated list of dependency IDs>\
# Priority: <priority>\
# Description: <brief description>\
# Details:\
<detailed implementation notes>\
\
# Test Strategy:\
<verification approach>\
```\
\
## Features in Detail\
\
### Analyzing Task Complexity\
\
The `analyze-complexity` command:\
\
- Analyzes each task using AI to assess its complexity on a scale of 1-10\
- Recommends optimal number of subtasks based on configured DEFAULT_SUBTASKS\
- Generates tailored prompts for expanding each task\
- Creates a comprehensive JSON report with ready-to-use commands\
- Saves the report to scripts/task-complexity-report.json by default\
\
The generated report contains:\
\
- Complexity analysis for each task (scored 1-10)\
- Recommended number of subtasks based on complexity\
- AI-generated expansion prompts customized for each task\
- Ready-to-run expansion commands directly within each task analysis\
\
### Viewing Complexity Report\
\
The `complexity-report` command:\
\
- Displays a formatted, easy-to-read version of the complexity analysis report\
- Shows tasks organized by complexity score (highest to lowest)\
- Provides complexity distribution statistics (low, medium, high)\
- Highlights tasks recommended for expansion based on threshold score\
- Includes ready-to-use expansion commands for each complex task\
- If no report exists, offers to generate one on the spot\
\
### Smart Task Expansion\
\
The `expand` command automatically checks for and uses the complexity report:\
\
When a complexity report exists:\
\
- Tasks are automatically expanded using the recommended subtask count and prompts\
- When expanding all tasks, they're processed in order of complexity (highest first)\
- Research-backed generation is preserved from the complexity analysis\
- You can still override recommendations with explicit command-line options\
\
Example workflow:\
\
```bash\
# Generate the complexity analysis report with research capabilities\
task-master analyze-complexity --research\
\
# Review the report in a readable format\
task-master complexity-report\
\
# Expand tasks using the optimized recommendations\
task-master expand --id=8\
# or expand all tasks\
task-master expand --all\
```\
\
### Finding the Next Task\
\
The `next` command:\
\
- Identifies tasks that are pending/in-progress and have all dependencies satisfied\
- Prioritizes tasks by priority level, dependency count, and task ID\
- Displays comprehensive information about the selected task:\
  - Basic task details (ID, title, priority, dependencies)\
  - Implementation details\
  - Subtasks (if they exist)\
- Provides contextual suggested actions:\
  - Command to mark the task as in-progress\
  - Command to mark the task as done\
  - Commands for working with subtasks\
\
### Viewing Specific Task Details\
\
The `show` command:\
\
- Displays comprehensive details about a specific task or subtask\
- Shows task status, priority, dependencies, and detailed implementation notes\
- For parent tasks, displays all subtasks and their status\
- For subtasks, shows parent task relationship\
- Provides contextual action suggestions based on the task's state\
- Works with both regular tasks and subtasks (using the format taskId.subtaskId)\
\
## Best Practices for AI-Driven Development\
\
1. **Start with a detailed PRD**: The more detailed your PRD, the better the generated tasks will be.\
\
2. **Review generated tasks**: After parsing the PRD, review the tasks to ensure they make sense and have appropriate dependencies.\
\
3. **Analyze task complexity**: Use the complexity analysis feature to identify which tasks should be broken down further.\
\
4. **Follow the dependency chain**: Always respect task dependencies - the Cursor agent will help with this.\
\
5. **Update as you go**: If your implementation diverges from the plan, use the update command to keep future tasks aligned with your current approach.\
\
6. **Break down complex tasks**: Use the expand command to break down complex tasks into manageable subtasks.\
\
7. **Regenerate task files**: After any updates to tasks.json, regenerate the task files to keep them in sync.\
\
8. **Communicate context to the agent**: When asking the Cursor agent to help with a task, provide context about what you're trying to achieve.\
\
9. **Validate dependencies**: Periodically run the validate-dependencies command to check for invalid or circular dependencies.\
\
\
\
\
\
\
\
\
# Licensing\
\
Task Master is licensed under the MIT License with Commons Clause. This means you can:\
\
## 
\f2 \uc0\u9989 
\f0  Allowed:\
\
- Use Task Master for any purpose (personal, commercial, academic)\
- Modify the code\
- Distribute copies\
- Create and sell products built using Task Master\
\
## 
\f2 \uc0\u10060 
\f0  Not Allowed:\
\
- Sell Task Master itself\
- Offer Task Master as a hosted service\
- Create competing products based on Task Master\
\
See the [LICENSE](../LICENSE) file for the complete license text.\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
\
docs/scripts/models-json-to-markdown.js\
\
import fs from 'fs';\
import path from 'path';\
import \{ fileURLToPath \} from 'url';\
\
const __filename = fileURLToPath(import.meta.url);\
const __dirname = path.dirname(__filename);\
\
const supportedModelsPath = path.join(\
	__dirname,\
	'..',\
	'modules',\
	'supported-models.json'\
);\
const outputMarkdownPath = path.join(\
	__dirname,\
	'..',\
	'..',\
	'docs',\
	'models.md'\
);\
\
function formatCost(cost) \{\
	if (cost === null || cost === undefined) \{\
		return '\'97';\
	\}\
	return cost;\
\}\
\
function formatSweScore(score) \{\
	if (score === null || score === undefined || score === 0) \{\
		return '\'97';\
	\}\
	return score.toString();\
\}\
\
function generateMarkdownTable(title, models) \{\
	if (!models || models.length === 0) \{\
		return `## $\{title\}\\n\\nNo models in this category.\\n\\n`;\
	\}\
	let table = `## $\{title\}\\n\\n`;\
	table += '| Provider | Model Name | SWE Score | Input Cost | Output Cost |\\n';\
	table += '|---|---|---|---|---|\\n';\
	models.forEach((model) => \{\
		table += `| $\{model.provider\} | $\{model.modelName\} | $\{formatSweScore(model.sweScore)\} | $\{formatCost(model.inputCost)\} | $\{formatCost(model.outputCost)\} |\\n`;\
	\});\
	table += '\\n';\
	return table;\
\}\
\
function main() \{\
	try \{\
		const correctSupportedModelsPath = path.join(\
			__dirname,\
			'..',\
			'..',\
			'scripts',\
			'modules',\
			'supported-models.json'\
		);\
		const correctOutputMarkdownPath = path.join(__dirname, '..', 'models.md');\
\
		const supportedModelsContent = fs.readFileSync(\
			correctSupportedModelsPath,\
			'utf8'\
		);\
		const supportedModels = JSON.parse(supportedModelsContent);\
\
		const mainModels = [];\
		const researchModels = [];\
		const fallbackModels = [];\
\
		for (const provider in supportedModels) \{\
			if (Object.hasOwnProperty.call(supportedModels, provider)) \{\
				const models = supportedModels[provider];\
				models.forEach((model) => \{\
					const modelEntry = \{\
						provider: provider,\
						modelName: model.id,\
						sweScore: model.swe_score,\
						inputCost: model.cost_per_1m_tokens\
							? model.cost_per_1m_tokens.input\
							: null,\
						outputCost: model.cost_per_1m_tokens\
							? model.cost_per_1m_tokens.output\
							: null\
					\};\
\
					if (model.allowed_roles.includes('main')) \{\
						mainModels.push(modelEntry);\
					\}\
					if (model.allowed_roles.includes('research')) \{\
						researchModels.push(modelEntry);\
					\}\
					if (model.allowed_roles.includes('fallback')) \{\
						fallbackModels.push(modelEntry);\
					\}\
				\});\
			\}\
		\}\
\
		const date = new Date();\
		const monthNames = [\
			'January',\
			'February',\
			'March',\
			'April',\
			'May',\
			'June',\
			'July',\
			'August',\
			'September',\
			'October',\
			'November',\
			'December'\
		];\
		const formattedDate = `$\{monthNames[date.getMonth()]\} $\{date.getDate()\}, $\{date.getFullYear()\}`;\
\
		let markdownContent = `# Available Models as of $\{formattedDate\}\\n\\n`;\
		markdownContent += generateMarkdownTable('Main Models', mainModels);\
		markdownContent += generateMarkdownTable('Research Models', researchModels);\
		markdownContent += generateMarkdownTable('Fallback Models', fallbackModels);\
\
		fs.writeFileSync(correctOutputMarkdownPath, markdownContent, 'utf8');\
		console.log(`Successfully updated $\{correctOutputMarkdownPath\}`);\
	\} catch (error) \{\
		console.error('Error transforming models.json to models.md:', error);\
		process.exit(1);\
	\}\
\}\
\
main();}