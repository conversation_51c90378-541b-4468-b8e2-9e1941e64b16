# Sequential Workflow User Guide

## Overview

The Sequential Workflow system provides controlled, validated development automation that ensures actual code generation with user oversight and quality control. This guide explains how to use the system effectively.

## Getting Started

### What is Sequential Workflow?

Sequential Workflow is a controlled execution system where:
- **One agent works at a time** (no parallel execution chaos)
- **Each task is validated** before marking as complete
- **You control the progression** with confirmation checkpoints
- **Real code files are generated** and verified
- **Quality is automatically assessed** with scoring

### Key Benefits

✅ **Reliable Code Generation**: Actual files created and validated  
✅ **User Control**: You approve each task before proceeding  
✅ **Quality Assurance**: Automatic quality scoring and validation  
✅ **Real-time Visibility**: Watch agents work in live coding view  
✅ **No Resource Conflicts**: One agent at a time prevents issues  

## Creating a Project with Sequential Workflow

### Step 1: Start Project Creation
1. Click **"Create New Project"** in the main interface
2. Enter your project name and details
3. The system will automatically detect if you're using the desktop app

### Step 2: Configure API Keys
1. Enter your **Anthropic API key** (required for agents)
2. Enter your **Perplexity API key** (optional, for enhanced capabilities)
3. Keys are automatically validated when entered
4. ✅ Green checkmark = valid key
5. ❌ Red X = invalid key

### Step 3: Upload PRD (Project Requirements Document)
1. Click **"Upload PRD"** or drag and drop your file
2. Supported formats: `.txt`, `.md`, `.pdf`, `.docx`
3. The system will validate and parse your PRD
4. Review the parsed content for accuracy

### Step 4: Generate Tasks with Taskmaster
1. Click **"Generate Tasks"** to process your PRD
2. Claude Taskmaster will analyze your requirements
3. Tasks are automatically broken down into manageable pieces
4. Each task is assigned to the most appropriate agent

### Step 5: Initialize Sequential Workflow
1. The system automatically sets up sequential execution
2. All tasks are queued for controlled progression
3. Agent swimlanes are created in the Kanban view
4. You'll see confirmation that sequential workflow is ready

### Step 6: Project Ready
🎯 **Sequential Workflow Activated!** Your project is now ready with:
- ✅ Controlled execution with user confirmation checkpoints
- ✅ Real-time code generation monitoring
- ✅ Automatic quality validation and approval
- ✅ One agent at a time execution

## Using the Sequential Workflow Control Panel

### Location
After project creation, find the **Sequential Workflow Control** panel in the orchestration interface. It has a green border and shows "🎮 Sequential Workflow Control (ACTIVE)".

### Control Options

#### Manual Mode (Default)
**Best for**: Learning the system, complex projects, quality-critical work

**How to use**:
1. Click **"Start Next Task"** to begin the next task in the queue
2. Watch the agent work in real-time in the Monaco editor
3. When the task appears complete, click **"Complete Current Task"**
4. Review the completion report with file validation results
5. Choose your action: **Proceed**, **Retry**, **Modify**, or **Cancel**
6. Repeat for each task in the sequence

#### Automatic Mode
**Best for**: Well-defined projects, trusted workflows, hands-off development

**How to enable**:
1. Check the **"Enable Auto-Execution"** checkbox
2. Click **"Start Auto-Execution"** to begin hands-off execution
3. The system will automatically approve tasks meeting quality thresholds
4. You'll be notified when user intervention is required

**Safety features**:
- Auto-approval only for tasks scoring 80%+ quality
- Maximum 3 consecutive tasks before requiring user check-in
- Automatic pause for validation failures or low quality
- Easy pause and manual override at any time

### Status Indicators

#### Workflow Status Display
- **Execution Mode**: Manual or Sequential
- **Current Agent**: Which agent is currently active (or "None")
- **Queue Length**: Number of tasks remaining
- **Can Start Next**: Whether you can start the next task

#### Current Task Information
When a task is running, you'll see:
- **Task ID**: Unique identifier for the current task
- **Agent**: Which agent is working on the task
- **Progress**: Real-time progress indicators

## Understanding the User Confirmation Dialog

### When It Appears
The confirmation dialog appears when:
- You click "Complete Current Task" in manual mode
- Automatic execution encounters a task requiring user approval
- A task fails validation or has quality issues

### Information Provided

#### Task Summary
- **Task Title and Description**: What the task was supposed to accomplish
- **Agent**: Which agent worked on the task
- **Execution Time**: How long the task took to complete
- **Quality Score**: 0-100 quality assessment

#### Validation Results
- ✅ **Validation Passed**: All checks successful
- ❌ **Validation Failed**: Issues found that need attention
- **Details**: Specific validation results and recommendations

#### File Operations
- **Created**: New files generated by the agent
- **Modified**: Existing files that were updated
- **Deleted**: Files that were removed (if any)

### Decision Options

#### ✅ Proceed to Next Task
- **When to use**: Task completed successfully, you're satisfied with the results
- **What happens**: Current task marked complete, next task becomes available
- **Recommendation**: Use when validation passed and quality score is good

#### 🔄 Retry This Task
- **When to use**: Task had issues but you want the same agent to try again
- **What happens**: Task is reset and can be restarted
- **Recommendation**: Use for temporary issues or when you want to give the agent another chance

#### ✏️ Request Modifications
- **When to use**: Task partially completed but needs specific changes
- **What happens**: Task remains active, you can provide specific feedback
- **Recommendation**: Use when the output is close but needs refinement

#### ❌ Cancel Workflow
- **When to use**: Serious issues or you want to stop the entire workflow
- **What happens**: All agents deactivated, workflow stops
- **Recommendation**: Use sparingly, only for major issues

### Providing Feedback
- **Optional but valuable**: Help improve future task execution
- **Be specific**: Mention what worked well and what could be improved
- **Examples**: "Good file structure but needs more comments" or "Perfect implementation, exactly what I wanted"

## Real-time Monitoring

### Monaco Editor Integration
- **Live Code Display**: Watch agents write code in real-time
- **File Switching**: See which files are being worked on
- **Progress Indicators**: Visual progress bars and status updates
- **Syntax Highlighting**: Full code highlighting and formatting

### Progress Tracking
- **Task Progress**: Overall task completion percentage
- **File Progress**: Individual file generation progress
- **Agent Status**: Current agent activity and focus area
- **Queue Status**: Remaining tasks and estimated completion

## Quality Assurance

### Automatic Quality Scoring
The system automatically evaluates:
- **File Creation**: Were the expected files actually created?
- **Content Quality**: Is the content meaningful (not placeholder text)?
- **Code Structure**: Does the code follow basic structural patterns?
- **Completeness**: Does the output match the task requirements?

### Quality Score Interpretation
- **90-100**: Excellent quality, ready for production
- **80-89**: Good quality, minor improvements possible
- **70-79**: Acceptable quality, some issues to address
- **60-69**: Below standard, significant improvements needed
- **Below 60**: Poor quality, requires major revision

### Validation Checks
- ✅ **File Existence**: Confirms files were actually created
- ✅ **Content Validation**: Ensures meaningful content (no placeholders)
- ✅ **Syntax Checking**: Basic syntax validation for code files
- ✅ **Structure Analysis**: Checks for proper code organization

## Troubleshooting

### Common Issues

#### "No tasks in queue"
- **Cause**: All tasks completed or workflow not initialized
- **Solution**: Check if project creation completed successfully, restart if needed

#### "Agent activation failed"
- **Cause**: Another agent still active or system conflict
- **Solution**: Try "Stop Auto-Execution" then restart, or refresh the page

#### "Validation failed"
- **Cause**: Generated files don't meet quality standards
- **Solution**: Review the validation details, choose "Retry" or "Request Modifications"

#### "Task timeout"
- **Cause**: Task took longer than 5 minutes to complete
- **Solution**: Check agent status, may need to retry or modify task scope

### Getting Help
1. **Check Console Logs**: Browser developer tools show detailed execution logs
2. **Review Validation Details**: Completion dialog provides specific issue information
3. **Use Manual Mode**: Switch from automatic to manual for more control
4. **Restart Workflow**: Sometimes a fresh start resolves issues

## Best Practices

### For Best Results
1. **Start with Manual Mode**: Learn how the system works before using automatic mode
2. **Review Completion Reports**: Always check validation results and quality scores
3. **Provide Feedback**: Help the system learn and improve
4. **Use Appropriate Mode**: Manual for complex/critical work, automatic for routine tasks
5. **Monitor Progress**: Keep an eye on real-time execution in Monaco editor

### Quality Tips
- **Clear Requirements**: Better PRDs lead to better task generation
- **Appropriate Scope**: Break large features into smaller, manageable tasks
- **Validation Attention**: Pay attention to validation failures and quality scores
- **Iterative Improvement**: Use feedback to refine subsequent tasks

### Workflow Optimization
- **Batch Similar Tasks**: Group related tasks for more efficient execution
- **Quality Thresholds**: Adjust auto-approval thresholds based on your quality requirements
- **Regular Check-ins**: Don't let automatic mode run too long without oversight
- **Save Progress**: The system automatically saves progress, but manual saves are good practice

## Advanced Features

### Agent Lane Management
- **Toggle Visibility**: Show/hide specific agent swimlanes
- **Task Reassignment**: Move tasks between agents if needed
- **Agent Performance**: Monitor which agents perform best for different task types

### Automatic Execution Configuration
- **Quality Thresholds**: Adjust the minimum quality score for auto-approval
- **Consecutive Limits**: Change how many tasks run automatically before user check-in
- **Timeout Settings**: Modify how long tasks can run before timing out
- **Approval Requirements**: Customize when user approval is required

This user guide provides everything you need to effectively use the Sequential Workflow system for reliable, controlled development automation.
