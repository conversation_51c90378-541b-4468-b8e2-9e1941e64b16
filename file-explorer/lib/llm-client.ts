// lib/llm-client.ts
"use client"

import { useSystemSettings } from '../components/settings/settings-context';
import { useTimeout } from './utils/use-timeout';
import { isTimeoutError } from './utils/timeout';

export interface LLMRequest {
  messages: Array<{ role: string; content: string }>;
  model: string;
  temperature?: number;
  maxTokens?: number;
  stream?: boolean;
}

export interface LLMResponse {
  content: string;
  tokensUsed: {
    input: number;
    output: number;
    total: number;
  };
  model: string;
  finishReason: string;
}

export type LLMProvider = 'openai' | 'anthropic' | 'openrouter' | 'azure' | 'google' | 'deepseek' | 'fireworks' | 'perplexity';

/**
 * ✅ Client-side LLM service with timeout support
 * Uses SystemSettings.defaultTimeout for all operations
 */
export class LLMClient {
  private systemSettings: any;
  private timeoutUtils: any;

  constructor(systemSettings: any, timeoutUtils: any) {
    this.systemSettings = systemSettings;
    this.timeoutUtils = timeoutUtils;
  }

  /**
   * ✅ Validate API key with timeout
   */
  async validateApiKey(provider: LLMProvider, apiKey: string): Promise<boolean> {
    try {
      if (typeof window !== 'undefined' && window.electronAPI?.ipc) {
        return await this.timeoutUtils.withDefaultTimeout(
          window.electronAPI.ipc.invoke('llm:validateApiKey', provider, apiKey, this.systemSettings.defaultTimeout),
          `${provider} API key validation`
        );
      }
      throw new Error('Electron API not available');
    } catch (error) {
      if (isTimeoutError(error)) {
        console.error(`API key validation timed out for ${provider} after ${this.systemSettings.defaultTimeout}ms`);
        throw new Error(`API key validation timed out. Please check your connection or increase the timeout setting.`);
      }
      throw error;
    }
  }

  /**
   * ✅ Call LLM with timeout
   */
  async callLLM(provider: LLMProvider, request: LLMRequest, apiKey: string): Promise<LLMResponse> {
    try {
      if (typeof window !== 'undefined' && window.electronAPI?.ipc) {
        return await this.timeoutUtils.withDefaultTimeout(
          window.electronAPI.ipc.invoke('llm:callLLM', provider, request, apiKey, this.systemSettings.defaultTimeout),
          `${provider} ${request.model} completion`
        );
      }
      throw new Error('Electron API not available');
    } catch (error) {
      if (isTimeoutError(error)) {
        console.error(`LLM call timed out for ${provider} after ${this.systemSettings.defaultTimeout}ms`);
        throw new Error(`LLM request timed out. The model may be overloaded or your timeout setting is too low.`);
      }
      throw error;
    }
  }

  /**
   * ✅ Fetch models with timeout
   */
  async fetchModels(provider: LLMProvider, apiKey: string): Promise<string[]> {
    try {
      if (typeof window !== 'undefined' && window.electronAPI?.ipc) {
        return await this.timeoutUtils.withDefaultTimeout(
          window.electronAPI.ipc.invoke('llm:fetchModels', provider, apiKey, this.systemSettings.defaultTimeout),
          `${provider} model fetching`
        );
      }
      throw new Error('Electron API not available');
    } catch (error) {
      if (isTimeoutError(error)) {
        console.error(`Model fetching timed out for ${provider} after ${this.systemSettings.defaultTimeout}ms`);
        throw new Error(`Model fetching timed out. Please check your connection or increase the timeout setting.`);
      }
      throw error;
    }
  }
}

/**
 * ✅ React hook for LLM operations with timeout support
 */
export function useLLMClient() {
  const { systemSettings } = useSystemSettings();
  const timeoutUtils = useTimeout();

  const llmClient = new LLMClient(systemSettings, timeoutUtils);

  /**
   * ✅ Validate API key with user-friendly error handling
   */
  const validateApiKey = async (provider: LLMProvider, apiKey: string): Promise<{ success: boolean; error?: string }> => {
    try {
      const isValid = await llmClient.validateApiKey(provider, apiKey);
      return { success: isValid };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  };

  /**
   * ✅ Call LLM with user-friendly error handling
   */
  const callLLM = async (
    provider: LLMProvider, 
    request: LLMRequest, 
    apiKey: string
  ): Promise<{ success: boolean; data?: LLMResponse; error?: string }> => {
    try {
      const response = await llmClient.callLLM(provider, request, apiKey);
      return { success: true, data: response };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  };

  /**
   * ✅ Fetch models with user-friendly error handling
   */
  const fetchModels = async (
    provider: LLMProvider, 
    apiKey: string
  ): Promise<{ success: boolean; data?: string[]; error?: string }> => {
    try {
      const models = await llmClient.fetchModels(provider, apiKey);
      return { success: true, data: models };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  };

  return {
    validateApiKey,
    callLLM,
    fetchModels,
    defaultTimeout: systemSettings.defaultTimeout,
    isTimeoutError
  };
}

/**
 * ✅ Hook for agent-specific LLM operations
 */
export function useAgentLLM() {
  const { callLLM, defaultTimeout } = useLLMClient();

  /**
   * ✅ Execute agent LLM call with timeout
   */
  const executeAgentCall = async (
    agentId: string,
    provider: LLMProvider,
    request: LLMRequest,
    apiKey: string
  ): Promise<{ success: boolean; data?: LLMResponse; error?: string }> => {
    console.log(`🤖 Agent ${agentId}: Starting LLM call to ${provider} (timeout: ${defaultTimeout}ms)`);
    
    const startTime = Date.now();
    const result = await callLLM(provider, request, apiKey);
    const duration = Date.now() - startTime;
    
    if (result.success) {
      console.log(`✅ Agent ${agentId}: LLM call completed in ${duration}ms`);
    } else {
      console.error(`❌ Agent ${agentId}: LLM call failed after ${duration}ms:`, result.error);
    }
    
    return result;
  };

  return {
    executeAgentCall,
    defaultTimeout
  };
}

/**
 * ✅ Utility function to format timeout errors for UI display
 */
export function formatLLMError(error: string): string {
  if (error.includes('timed out')) {
    return 'Request timed out. Try increasing the timeout in settings or check your internet connection.';
  }
  if (error.includes('API key')) {
    return 'Invalid API key. Please check your API key in settings.';
  }
  if (error.includes('rate limit')) {
    return 'Rate limit exceeded. Please wait a moment before trying again.';
  }
  if (error.includes('quota')) {
    return 'API quota exceeded. Please check your account limits.';
  }
  return error;
}
