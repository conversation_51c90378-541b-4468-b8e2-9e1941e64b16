#!/bin/bash

# Electron Development Runner
# This script starts Next.js and Elec<PERSON> in development mode

set -e

# Get the absolute path of the script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

echo "🚀 Starting Electron Development Environment"
echo "📁 Working directory: $SCRIPT_DIR"

# Kill any existing processes on port 4444
echo "🔄 Killing any existing processes on port 4444..."
lsof -ti:4444 | xargs kill -9 2>/dev/null || true

# Compile Electron TypeScript
echo "🔨 Compiling Electron TypeScript..."
npx tsc -p tsconfig.electron.json

# Copy preload script
echo "📋 Copying preload script..."
cp electron/preload.js dist-electron/

# Start Next.js in background
echo "🌐 Starting Next.js development server..."
npx next dev -p 4444 &
NEXTJS_PID=$!

# Wait for Next.js to be ready
echo "⏳ Waiting for Next.js to be ready..."
npx wait-on http://localhost:4444

# Give it a moment to fully initialize
sleep 2

# Start Electron
echo "⚡ Starting Electron..."
npx electron . --dev

# Cleanup function
cleanup() {
    echo "🧹 Cleaning up..."
    kill $NEXTJS_PID 2>/dev/null || true
    lsof -ti:4444 | xargs kill -9 2>/dev/null || true
}

# Set trap to cleanup on exit
trap cleanup EXIT
