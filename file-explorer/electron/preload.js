// ✅ ULTRA-CRITICAL FIX: Absolute prevention of browser execution
// This script should NEVER run in browser environments
// Use multiple layers of protection to prevent any execution

// Layer 1: Immediate environment check with hard exit
if (typeof window !== 'undefined' && typeof document !== 'undefined') {
  // We're in a browser-like environment
  // Check if we're actually in Electron
  const isRealElectron = (
    typeof process !== 'undefined' &&
    process.versions &&
    process.versions.electron &&
    typeof process.type !== 'undefined' &&
    (process.type === 'renderer' || process.type === 'browser') &&
    typeof require !== 'undefined'
  );

  if (!isRealElectron) {
    // We're in a browser, not Electron - STOP ALL EXECUTION
    // Use a syntax error to completely halt script parsing if needed
    if (typeof window.location !== 'undefined' && window.location.protocol.startsWith('http')) {
      // Definitely in a browser - exit immediately
      return; // This should stop execution in most cases
    }
  }
}

// Layer 2: Additional safety check for webpack/bundler environments
if (typeof __webpack_require__ !== 'undefined' ||
    typeof webpackChunkName !== 'undefined' ||
    (typeof process !== 'undefined' && process.browser === true)) {
  // This is being executed in a webpack/browser bundle - STOP
  return;
}

// Layer 3: Final Electron validation
const isElectronEnvironment = (
  typeof process !== 'undefined' &&
  process.versions &&
  process.versions.electron &&
  typeof require !== 'undefined' &&
  typeof window !== 'undefined' &&
  typeof process.type !== 'undefined' &&
  (process.type === 'renderer' || process.type === 'browser')
);

if (!isElectronEnvironment) {
  // Final safety: prevent any further execution
  return;
}

// ✅ CRITICAL FIX: Safe require with error handling
let contextBridge, ipcRenderer;
try {
  const electron = require('electron');
  contextBridge = electron.contextBridge;
  ipcRenderer = electron.ipcRenderer;

  // Verify required APIs are available
  if (!contextBridge || !ipcRenderer) {
    console.error('❌ [Preload] Required Electron APIs not available');
    return;
  }
} catch (error) {
  console.error('❌ [Preload] Failed to load Electron APIs:', error.message);
  return;
}

// ✅ Task 100 Step 3: Set up terminal log event forwarding
ipcRenderer.on('terminal:log', (event, logData) => {
  // Dispatch custom event to window for React components to listen
  if (typeof window !== 'undefined') {
    window.dispatchEvent(new CustomEvent('terminal-log', { detail: logData }));
  }
});

// ✅ CRITICAL FIX: Wrap API exposure in try-catch
try {
  // Expose protected methods that allow the renderer process to use
  // the ipcRenderer without exposing the entire object
  contextBridge.exposeInMainWorld('electronAPI', {
  selectFolder: () => ipcRenderer.invoke('select-folder'),
  readDirectory: (dirPath) => ipcRenderer.invoke('read-directory', dirPath),
  readFile: (filePath) => ipcRenderer.invoke('read-file', filePath),
  saveFile: (filePath, content) => ipcRenderer.invoke('save-file', filePath, content),
  createFile: (filePath, content) => ipcRenderer.invoke('create-file', filePath, content),
  deleteFile: (filePath) => ipcRenderer.invoke('delete-file', filePath),

  // ✅ Logging System API
  appendFile: (filePath, content) => ipcRenderer.invoke('append-file', filePath, content),
  ensureDirectory: (dirPath) => ipcRenderer.invoke('ensure-directory', dirPath),
  executeCommand: (command, workingDirectory) => ipcRenderer.invoke('execute-command', command, workingDirectory),
  executeCommandWithEnv: (command, workingDirectory, envVars) => ipcRenderer.invoke('execute-command-with-env', command, workingDirectory, envVars),

  openKanbanWindow: (boardId) => ipcRenderer.send('open-kanban-window', boardId),
  openAgentSystemWindow: () => ipcRenderer.send('open-agent-system-window'),
  openEditorWindow: (filePath) => ipcRenderer.send('open-editor-window', filePath),
  openExplorerWindow: () => ipcRenderer.send('open-explorer-window'),
  openChatWindow: () => ipcRenderer.send('open-chat-window'),
  openTimelineWindow: () => ipcRenderer.send('open-timeline-window'),
  openTerminalWindow: () => ipcRenderer.send('open-terminal-window'),
  openSettingsWindow: () => ipcRenderer.send('open-settings-window'),

  // ✅ Auto-Save API
  saveAgentStates: (agentStates) => ipcRenderer.invoke('save-agent-states', agentStates),
  saveBoardState: (boardState) => ipcRenderer.invoke('save-board-state', boardState),

  // TODO: migrate to Claude-task-master - Taskmaster API exposure removed as part of legacy system purge.

  // New: LLM API for AI provider integration
  llm: {
    validateApiKey: (provider, apiKey) => ipcRenderer.invoke('llm:validateApiKey', provider, apiKey),
    callLLM: (provider, request, apiKey) => ipcRenderer.invoke('llm:callLLM', provider, request, apiKey),
    fetchModels: (provider, apiKey) => ipcRenderer.invoke('llm:fetchModels', provider, apiKey),
  },

  // ✅ MCP Protocol API
  mcp: {
    initializeConnection: (serverId, config) => ipcRenderer.invoke('mcp:initializeConnection', serverId, config),
    sendTask: (serverId, request) => ipcRenderer.invoke('mcp:sendTask', serverId, request),
    syncAgentState: (serverId, agentId, state) => ipcRenderer.invoke('mcp:syncAgentState', serverId, agentId, state),
    testConnection: (serverId) => ipcRenderer.invoke('mcp:testConnection', serverId),
    disconnectServer: (serverId) => ipcRenderer.invoke('mcp:disconnectServer', serverId),
    getConnectedServers: () => ipcRenderer.invoke('mcp:getConnectedServers'),
  },

  // ✅ Claude Taskmaster API
  taskmaster: {
    checkInstallation: () => ipcRenderer.invoke('taskmaster:checkInstallation'),
    install: () => ipcRenderer.invoke('taskmaster:install'),
    validateCLI: (projectPath) => ipcRenderer.invoke('taskmaster:validateCLI', projectPath),
    init: (projectPath, projectName) => ipcRenderer.invoke('taskmaster:init', projectPath, projectName),
    configure: (projectPath, anthropicApiKey, perplexityApiKey, config) => ipcRenderer.invoke('taskmaster:configure', projectPath, anthropicApiKey, perplexityApiKey, config),
    parsePRD: (projectPath, prdContent, prdFileName) => ipcRenderer.invoke('taskmaster:parsePRD', projectPath, prdContent, prdFileName),
    generate: (projectPath) => ipcRenderer.invoke('taskmaster:generate', projectPath),
    list: (projectPath) => ipcRenderer.invoke('taskmaster:list', projectPath),
    next: (projectPath) => ipcRenderer.invoke('taskmaster:next', projectPath),
  },

  // ✅ Terminal PTY API
  terminal: {
    create: (terminalSettings) => ipcRenderer.invoke('terminal:create', terminalSettings),
    write: (id, data) => ipcRenderer.send('terminal:input', { id, data }),
    resize: (id, cols, rows) => ipcRenderer.invoke('terminal:resize', { id, cols, rows }),
    listen: (id, callback) => {
      ipcRenderer.send('terminal:listen', id);
      const dataListener = (event, data) => callback(data);
      const exitListener = (event, { exitCode, signal }) => callback(null, { exitCode, signal });

      ipcRenderer.on(`terminal:data:${id}`, dataListener);
      ipcRenderer.on(`terminal:exit:${id}`, exitListener);

      // Return cleanup function
      return () => {
        ipcRenderer.removeListener(`terminal:data:${id}`, dataListener);
        ipcRenderer.removeListener(`terminal:exit:${id}`, exitListener);
      };
    },
    dispose: (id) => ipcRenderer.send('terminal:dispose', id),
    // ✅ Task 99: Enhanced agent command with session support
    agentCommand: ({ command, agentId, sessionId, timeout, workingDirectory, environment }) =>
      ipcRenderer.invoke('terminal:agent-command', { command, agentId, sessionId, timeout, workingDirectory, environment }),

    // ✅ Task 100 Step 3: Terminal log event handling
    onLog: (callback) => {
      const handleLog = (event, logData) => {
        callback(logData);
      };
      ipcRenderer.on('terminal:log', handleLog);

      // Return cleanup function
      return () => {
        ipcRenderer.removeListener('terminal:log', handleLog);
      };
    },

    // ✅ Task 101 Step 3: TerminalSessionManager API
    createSession: (sessionId, agentId, options) =>
      ipcRenderer.invoke('terminal:create-session', sessionId, agentId, options),

    writeToSession: (sessionId, data) =>
      ipcRenderer.invoke('terminal:write-session', sessionId, data),

    destroySession: (sessionId) =>
      ipcRenderer.invoke('terminal:destroy-session', sessionId),

    listSessions: (agentId) =>
      ipcRenderer.invoke('terminal:list-sessions', agentId),

    getSessionInfo: (sessionId) =>
      ipcRenderer.invoke('terminal:get-session-info', sessionId),

    resizeSession: (sessionId, cols, rows) =>
      ipcRenderer.invoke('terminal:resize-session', sessionId, cols, rows),

    getSessionStats: () =>
      ipcRenderer.invoke('terminal:get-session-stats'),

    cleanupAgentSessions: (agentId) =>
      ipcRenderer.invoke('terminal:cleanup-agent-sessions', agentId),

    // Session event listeners
    onSessionData: (sessionId, callback) => {
      const handleData = (event, data) => callback(data);
      ipcRenderer.on(`terminal:session-data:${sessionId}`, handleData);

      return () => {
        ipcRenderer.removeListener(`terminal:session-data:${sessionId}`, handleData);
      };
    },

    onSessionExit: (sessionId, callback) => {
      const handleExit = (event, exitInfo) => callback(exitInfo);
      ipcRenderer.on(`terminal:session-exit:${sessionId}`, handleExit);

      return () => {
        ipcRenderer.removeListener(`terminal:session-exit:${sessionId}`, handleExit);
      };
    },

    // ✅ Task 104 Step 2: User Terminal Session API
    createUserSession: (shell) => ipcRenderer.invoke('terminal:create-user-session', shell),
    disposeUserSession: (sessionId) => ipcRenderer.invoke('terminal:dispose-user-session', sessionId),
    writeUserSession: (sessionId, data) => ipcRenderer.invoke('terminal:write-user-session', sessionId, data),
    resizeUserSession: (sessionId, cols, rows) => ipcRenderer.invoke('terminal:resize-user-session', sessionId, cols, rows),
    listUserSessions: () => ipcRenderer.invoke('terminal:list-user-sessions'),

    // User session event listeners
    onUserSessionData: (sessionId, callback) => {
      const handleData = (event, data) => callback(data);
      ipcRenderer.on(`terminal:user-session-data:${sessionId}`, handleData);

      return () => {
        ipcRenderer.removeListener(`terminal:user-session-data:${sessionId}`, handleData);
      };
    },

    onUserSessionExit: (sessionId, callback) => {
      const handleExit = (event, exitInfo) => callback(exitInfo);
      ipcRenderer.on(`terminal:user-session-exit:${sessionId}`, handleExit);

      return () => {
        ipcRenderer.removeListener(`terminal:user-session-exit:${sessionId}`, handleExit);
      };
    },
  },



  // New: Generic IPC methods for bridges like BoardIPCBridge
  ipc: {
    send: (channel, ...args) => ipcRenderer.send(channel, ...args),
    invoke: (channel, ...args) => ipcRenderer.invoke(channel, ...args),
    on: (channel, listener) => {
      // ipcRenderer.on returns a cleanup function itself if needed by removing the listener.
      // However, the typical pattern is to provide a way to remove listeners.
      // For simplicity, we wrap the listener.
      const wrappedListener = (event, ...args) => listener(...args);
      ipcRenderer.on(channel, wrappedListener);
      // Return a function to remove this specific listener
      return () => ipcRenderer.removeListener(channel, wrappedListener);
    },
    removeAllListeners: (channel) => ipcRenderer.removeAllListeners(channel),
  }
});

  // Log when the API is exposed with enhanced debugging
  console.log('🚀 [Preload] Electron API exposed to renderer process (including generic IPC)');

} catch (error) {
  console.error('❌ [Preload] Failed to expose electronAPI:', error.message);
  // Don't throw - just log and continue
}


// ✅ CRITICAL FIX: Only verify API in Electron environment
// This verification should only run when we're actually in Electron
// Use the same robust check as above
const isElectronForVerification = (
  typeof process !== 'undefined' &&
  process.versions &&
  process.versions.electron &&
  typeof process.type !== 'undefined' &&
  (process.type === 'renderer' || process.type === 'browser') &&
  typeof contextBridge !== 'undefined' &&
  typeof ipcRenderer !== 'undefined'
);

if (isElectronForVerification) {
  // Verify the API is actually available on window after DOM loads
  window.addEventListener('DOMContentLoaded', () => {
    setTimeout(() => {
      if (typeof window !== 'undefined' && window.electronAPI) {
        console.log('✅ [Preload] Verification: electronAPI is available on window');
        console.log('🔍 [Preload] Available API methods:', Object.keys(window.electronAPI));
      } else {
        console.error('❌ [Preload] Verification failed: electronAPI not found on window');
        console.log('🔍 [Preload] Debug - window object keys:', typeof window !== 'undefined' ? Object.keys(window) : 'window undefined');
      }
    }, 100);
  });
}

// ✅ CRITICAL FIX: Only run version replacement in Electron environment
// All of the Node.js APIs are available in the preload process.
// It has the same sandbox as a Chrome extension.
const isElectronForVersions = (
  typeof process !== 'undefined' &&
  process.versions &&
  process.versions.electron &&
  typeof process.type !== 'undefined' &&
  (process.type === 'renderer' || process.type === 'browser')
);

if (isElectronForVersions) {
  window.addEventListener('DOMContentLoaded', () => {
    const replaceText = (selector, text) => {
      const element = document.getElementById(selector)
      if (element) element.innerText = text
    }

    for (const type of ['chrome', 'node', 'electron']) {
      replaceText(`${type}-version`, process.versions[type])
    }
  })
}