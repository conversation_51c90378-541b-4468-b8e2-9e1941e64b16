// electron/services/mcp-service.ts
// ✅ Electron main process MCP service using real MCP SDK
import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';
import { spawn, ChildProcess } from 'child_process';

export interface MCPServerConfig {
  command: string;
  args: string[];
  timeout: number;
  maxRetries: number;
}

export interface MCPConnection {
  serverId: string;
  client: Client;
  transport: StdioClientTransport;
  process?: ChildProcess;
  connected: boolean;
  serverInfo?: {
    name: string;
    version: string;
    capabilities: string[];
  };
  lastConnected?: number;
  connectionAttempts?: number;
}

export interface MCPTaskRequest {
  task: string;
  context: any;
  agentId: string;
  messages: Array<{
    role: string;
    content: string;
  }>;
  metadata?: {
    maxTokens?: number;
    temperature?: number;
    systemPrompt?: string;
  };
}

export interface MCPTaskResponse {
  success: boolean;
  content?: string;
  tokensUsed?: {
    prompt: number;
    completion: number;
    total: number;
  };
  model?: string;
  finishReason?: string;
  error?: string;
  toolsInvoked?: string[];
}

export class MCPService {
  private connections: Map<string, MCPConnection> = new Map();

  /**
   * ✅ Initialize MCP connection to a server
   */
  public async initializeConnection(serverId: string, config: MCPServerConfig): Promise<{
    success: boolean;
    error?: string;
    serverInfo?: {
      name: string;
      version: string;
      capabilities: string[];
    };
  }> {
    try {
      console.log(`🔌 MCP Service: Initializing connection to ${serverId}...`);

      // Create stdio transport for the MCP server
      const transport = new StdioClientTransport({
        command: config.command,
        args: config.args
      });

      // Create MCP client
      const client = new Client(
        {
          name: 'synapse-file-explorer',
          version: '1.0.0'
        },
        {
          capabilities: {
            sampling: {},
            tools: {},
            resources: {}
          }
        }
      );

      // Connect to the server
      await client.connect(transport);

      // Get server capabilities
      const serverCapabilities = await client.getServerCapabilities();

      const serverInfo = {
        name: serverId,
        version: String(serverCapabilities?.protocolVersion || '1.0.0'),
        capabilities: Object.keys(serverCapabilities?.capabilities || {})
      };

      // Store connection
      const connection: MCPConnection = {
        serverId,
        client,
        transport,
        connected: true,
        serverInfo,
        lastConnected: Date.now(),
        connectionAttempts: 1
      };

      this.connections.set(serverId, connection);

      console.log(`✅ MCP Service: Connected to ${serverId}`, {
        capabilities: serverInfo.capabilities
      });

      return {
        success: true,
        serverInfo
      };

    } catch (error) {
      console.error(`❌ MCP Service: Failed to connect to ${serverId}:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown connection error'
      };
    }
  }

  /**
   * ✅ Send task to MCP server for processing
   */
  public async sendTask(serverId: string, request: MCPTaskRequest): Promise<MCPTaskResponse> {
    try {
      const connection = this.connections.get(serverId);
      if (!connection || !connection.connected) {
        throw new Error(`MCP server ${serverId} is not connected`);
      }

      console.log(`🚀 MCP Service: Sending task to ${serverId} for agent ${request.agentId}`);

      // Convert messages to MCP format
      const mcpMessages = request.messages.map(msg => ({
        role: msg.role,
        content: {
          type: 'text',
          text: msg.content
        }
      }));

      // Prepare sampling request
      const samplingRequest = {
        messages: mcpMessages,
        maxTokens: request.metadata?.maxTokens || 4000,
        temperature: request.metadata?.temperature || 0.7,
        systemPrompt: request.metadata?.systemPrompt,
        includeContext: 'allServers' as const
      };

      // Send sampling request to MCP server
      // TODO: Implement proper MCP sampling API when available
      // const response = await connection.client.sampling.createMessage(samplingRequest);

      // Intentionally not implemented - MCP sampling API integration pending
      console.log(`❌ MCP Service: Sampling API not implemented for ${serverId}`);
      return {
        success: false,
        error: 'MCP sampling API integration pending - feature not yet available'
      };

    } catch (error) {
      console.error(`❌ MCP Service: Task failed for ${serverId}:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown task error'
      };
    }
  }

  /**
   * ✅ Sync agent state with MCP server
   */
  public async syncAgentState(
    serverId: string,
    agentId: string,
    state: {
      currentTask?: string;
      status: 'idle' | 'working' | 'error';
      lastUpdate: number;
      metadata?: Record<string, any>;
    }
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const connection = this.connections.get(serverId);
      if (!connection || !connection.connected) {
        return {
          success: false,
          error: `MCP server ${serverId} is not connected`
        };
      }

      console.log(`🔄 MCP Service: Syncing agent ${agentId} state with ${serverId}`);

      // In a real MCP implementation, this would use server-specific state sync
      // For now, we'll log the state sync attempt
      console.log(`📊 MCP Service: Agent ${agentId} state:`, state);

      return { success: true };

    } catch (error) {
      console.error(`❌ MCP Service: State sync failed for ${serverId}:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'State sync error'
      };
    }
  }

  /**
   * ✅ Test connection to MCP server
   */
  public async testConnection(serverId: string): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      const connection = this.connections.get(serverId);
      if (!connection || !connection.connected) {
        return {
          success: false,
          error: `Server ${serverId} is not connected`
        };
      }

      // Send a simple test message
      const testRequest: MCPTaskRequest = {
        task: 'Test connection',
        context: { task: 'test' },
        agentId: 'test-agent',
        messages: [{
          role: 'user',
          content: 'Hello, this is a connection test.'
        }],
        metadata: { maxTokens: 100, temperature: 0.1 }
      };

      const response = await this.sendTask(serverId, testRequest);

      return {
        success: response.success,
        error: response.error
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown test error'
      };
    }
  }

  /**
   * ✅ Disconnect from MCP server
   */
  public async disconnectServer(serverId: string): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      const connection = this.connections.get(serverId);
      if (!connection) {
        return { success: true }; // Already disconnected
      }

      console.log(`🔌 MCP Service: Disconnecting from ${serverId}...`);

      if (connection.connected && connection.client) {
        await connection.client.close();
      }

      // Kill process if it exists
      if (connection.process && !connection.process.killed) {
        connection.process.kill();
      }

      this.connections.delete(serverId);
      console.log(`✅ MCP Service: Disconnected from ${serverId}`);

      return { success: true };

    } catch (error) {
      console.error(`❌ MCP Service: Failed to disconnect from ${serverId}:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Disconnect error'
      };
    }
  }

  /**
   * ✅ Get list of connected servers
   */
  public getConnectedServers(): string[] {
    return Array.from(this.connections.keys()).filter(serverId =>
      this.connections.get(serverId)?.connected
    );
  }

  /**
   * ✅ Check if server is connected
   */
  public isServerConnected(serverId: string): boolean {
    const connection = this.connections.get(serverId);
    return connection?.connected || false;
  }

  /**
   * ✅ Disconnect all servers
   */
  public async disconnectAll(): Promise<void> {
    const serverIds = Array.from(this.connections.keys());

    for (const serverId of serverIds) {
      await this.disconnectServer(serverId);
    }

    console.log('🔌 MCP Service: All servers disconnected');
  }
}
