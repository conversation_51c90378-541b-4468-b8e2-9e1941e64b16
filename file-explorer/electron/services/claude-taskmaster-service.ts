// electron/services/claude-taskmaster-service.ts
import { exec, spawn } from 'child_process';
import { promisify } from 'util';
import * as path from 'path';
import * as fs from 'fs/promises';
import * as fsSync from 'fs';
import { ipcMain } from 'electron';

const execAsync = promisify(exec);

export interface TaskmasterConfig {
  models: {
    main: {
      provider: string;
      modelId: string;
      maxTokens: number;
      temperature: number;
      baseURL?: string;
    };
    research: {
      provider: string;
      modelId: string;
      maxTokens: number;
      temperature: number;
      baseURL?: string;
    };
    fallback: {
      provider: string;
      modelId: string;
      maxTokens: number;
      temperature: number;
      baseURL?: string;
    };
  };
  global: {
    logLevel: string;
    debug: boolean;
    defaultSubtasks: number;
    defaultPriority: string;
    projectName: string;
  };
}

export class ClaudeTaskmasterElectronService {
  private static _instance: ClaudeTaskmasterElectronService | null = null;
  private handlersRegistered = false;

  static getInstance(): ClaudeTaskmasterElectronService {
    if (!ClaudeTaskmasterElectronService._instance) {
      ClaudeTaskmasterElectronService._instance = new ClaudeTaskmasterElectronService();
    }
    return ClaudeTaskmasterElectronService._instance;
  }

  constructor() {
    if (ClaudeTaskmasterElectronService._instance) {
      return ClaudeTaskmasterElectronService._instance;
    }

    this.registerIPCHandlers();
    ClaudeTaskmasterElectronService._instance = this;
  }

  private registerIPCHandlers() {
    if (this.handlersRegistered) {
      console.log('Claude Taskmaster IPC handlers already registered, skipping...');
      return;
    }
    // Check if Claude Taskmaster is installed
    ipcMain.handle('taskmaster:checkInstallation', async () => {
      return this.checkInstallation();
    });

    // Install Claude Taskmaster
    ipcMain.handle('taskmaster:install', async () => {
      return this.installTaskmaster();
    });

    // Validate CLI functionality
    ipcMain.handle('taskmaster:validateCLI', async (_, projectPath: string) => {
      return this.validateCLIFunctionality(projectPath);
    });

    // Initialize project with Claude Taskmaster
    ipcMain.handle('taskmaster:init', async (_, projectPath: string, projectName: string) => {
      return this.initializeProject(projectPath, projectName);
    });

    // Configure Claude Taskmaster
    ipcMain.handle('taskmaster:configure', async (_, projectPath: string, anthropicApiKey: string, perplexityApiKey: string, config?: Partial<TaskmasterConfig>) => {
      return this.configureTaskmaster(projectPath, anthropicApiKey, perplexityApiKey, config);
    });

    // Parse PRD with Claude Taskmaster
    ipcMain.handle('taskmaster:parsePRD', async (_, projectPath: string, prdContent: string, prdFileName: string) => {
      return this.parsePRD(projectPath, prdContent, prdFileName);
    });

    // Generate task files
    ipcMain.handle('taskmaster:generate', async (_, projectPath: string) => {
      return this.generateTaskFiles(projectPath);
    });

    // List tasks
    ipcMain.handle('taskmaster:list', async (_, projectPath: string) => {
      return this.listTasks(projectPath);
    });

    // Get next task
    ipcMain.handle('taskmaster:next', async (_, projectPath: string) => {
      return this.getNextTask(projectPath);
    });

    this.handlersRegistered = true;
    console.log('✅ Claude Taskmaster IPC handlers registered successfully');
  }

  async checkInstallation(): Promise<{ installed: boolean; version?: string; error?: string }> {
    console.log('🔍 Checking Claude Taskmaster installation...');

    // Method 1: Try npx first (faster and more reliable)
    try {
      console.log('🔍 Checking via npx...');
      const result = await Promise.race([
        execAsync('task-master --version', { timeout: 8000 }), // Increased timeout
        new Promise<never>((_, reject) => setTimeout(() => reject(new Error('npx check timeout')), 8000))
      ]);

      const versionMatch = result.stdout.match(/(\d+\.\d+\.\d+)/);
      console.log('✅ Found via npx:', versionMatch?.[1] || 'unknown version');
      return {
        installed: true,
        version: versionMatch ? versionMatch[1] : 'available via npx'
      };
    } catch (npxError) {
      console.log('⚠️ npx check failed:', npxError instanceof Error ? npxError.message : 'unknown error');
    }

    // Method 2: Try global npm list (with shorter timeout)
    try {
      console.log('🔍 Checking global npm installation...');
      const result = await Promise.race([
        execAsync('npm list -g task-master-ai --depth=0', { timeout: 3000 }),
        new Promise<never>((_, reject) => setTimeout(() => reject(new Error('npm list timeout')), 3000))
      ]);

      const versionMatch = result.stdout.match(/task-master-ai@(\d+\.\d+\.\d+)/);
      console.log('✅ Found globally installed:', versionMatch?.[1] || 'unknown version');
      return {
        installed: true,
        version: versionMatch ? versionMatch[1] : 'unknown'
      };
    } catch (npmError) {
      console.log('⚠️ npm list check failed:', npmError instanceof Error ? npmError.message : 'unknown error');
    }

    // Method 3: Try which/where command (fastest)
    try {
      console.log('🔍 Checking PATH...');
      const command = process.platform === 'win32' ? 'where task-master-ai' : 'which task-master-ai';
      await Promise.race([
        execAsync(command, { timeout: 2000 }),
        new Promise<never>((_, reject) => setTimeout(() => reject(new Error('which/where timeout')), 2000))
      ]);

      console.log('✅ Found in PATH');
      return { installed: true, version: 'found in PATH' };
    } catch (pathError) {
      console.log('⚠️ PATH check failed:', pathError instanceof Error ? pathError.message : 'unknown error');
    }

    console.log('❌ Claude Taskmaster not found via any method');
    return {
      installed: false,
      error: 'Claude Taskmaster not found. Please install with: npm install -g task-master-ai'
    };
  }

  async installTaskmaster(): Promise<{ success: boolean; error?: string }> {
    console.log('📦 Installing Claude Taskmaster...');

    try {
      // ✅ ENHANCED: Try npm install with longer timeout for better reliability
      console.log('📦 Starting npm install with extended timeout...');
      const result = await Promise.race([
        execAsync('npm install -g task-master-ai', { timeout: 120000 }), // Increased to 2 minutes
        new Promise<never>((_, reject) => setTimeout(() => reject(new Error('Installation timeout after 2 minutes')), 120000))
      ]);

      console.log('📦 Installation output:', result.stdout);
      if (result.stderr) console.warn('📦 Installation warnings:', result.stderr);

      // Verify installation worked
      const verifyResult = await this.checkInstallation();
      if (verifyResult.installed) {
        console.log('✅ Installation verified successfully');
        return { success: true };
      } else {
        console.warn('⚠️ Installation completed but verification failed');
        return {
          success: false,
          error: 'Installation completed but Claude Taskmaster is not available. Try restarting the application.'
        };
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to install Claude Taskmaster';
      console.error('❌ Installation failed:', errorMessage);

      // Provide helpful error messages based on common issues
      if (errorMessage.includes('EACCES') || errorMessage.includes('permission denied')) {
        return {
          success: false,
          error: 'Permission denied. Please install manually:\n\n1. Open Terminal\n2. Run: sudo npm install -g task-master-ai\n3. Enter your password when prompted\n4. Restart this application\n\nAlternatively, try: npm install -g task-master-ai --unsafe-perm'
        };
      } else if (errorMessage.includes('timeout')) {
        return {
          success: false,
          error: 'Installation timed out. Please try manual installation:\n\n1. Open Terminal\n2. Run: npm install -g task-master-ai\n3. Restart this application'
        };
      } else if (errorMessage.includes('ENOTFOUND') || errorMessage.includes('network')) {
        return {
          success: false,
          error: 'Network error. Please check your internet connection and try manual installation:\n\n1. Open Terminal\n2. Run: npm install -g task-master-ai\n3. Restart this application'
        };
      } else {
        return {
          success: false,
          error: `Installation failed: ${errorMessage}\n\nPlease try manual installation:\n1. Open Terminal\n2. Run: npm install -g task-master-ai\n3. Restart this application`
        };
      }
    }
  }

  async validateCLIFunctionality(projectPath: string): Promise<{ isWorking: boolean; error?: string }> {
    try {
      console.log('[CLI Validation] Testing Claude Taskmaster CLI functionality...');

      // Test basic help command with correct command name
      const { stdout, stderr } = await execAsync('task-master --help', {
        cwd: projectPath,
        timeout: 15000
      });

      if (stdout.includes('task-master') || stdout.includes('parse-prd')) {
        console.log('[CLI Validation] ✅ CLI is functional');
        return { isWorking: true };
      } else {
        console.log('[CLI Validation] ❌ CLI output unexpected:', stdout);
        return {
          isWorking: false,
          error: `CLI output unexpected: ${stdout}`
        };
      }
    } catch (error) {
      console.error('[CLI Validation] ❌ CLI test failed:', error);
      return {
        isWorking: false,
        error: error instanceof Error ? error.message : 'CLI test failed'
      };
    }
  }

  async initializeProject(projectPath: string, projectName: string): Promise<{ success: boolean; error?: string; configPath?: string }> {
    try {
      console.log(`🔄 Initializing Taskmaster project at: ${projectPath}`);

      // ✅ ENHANCED: Change to project directory and run init with timeout
      const { stdout, stderr } = await Promise.race([
        execAsync('task-master init --yes', { // Add --yes flag to skip prompts
          cwd: projectPath,
          env: { ...process.env },
          timeout: 45000 // 45 second timeout
        }),
        new Promise<never>((_, reject) =>
          setTimeout(() => reject(new Error('Project initialization timeout after 45 seconds')), 45000)
        )
      ]);

      console.log('✅ Taskmaster init output:', stdout);
      if (stderr) console.warn('⚠️ Taskmaster init warnings:', stderr);

      const configPath = path.join(projectPath, '.taskmaster', 'config.json');

      // Verify config was created
      if (!fsSync.existsSync(configPath)) {
        console.warn('⚠️ Config file not found after init, but continuing...');
      }

      return {
        success: true,
        configPath
      };
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'Failed to initialize Claude Taskmaster';
      console.error('❌ Taskmaster initialization failed:', errorMsg);
      return {
        success: false,
        error: errorMsg
      };
    }
  }

  async configureTaskmaster(
    projectPath: string,
    anthropicApiKey: string,
    perplexityApiKey: string,
    config?: Partial<TaskmasterConfig>
  ): Promise<{ success: boolean; error?: string }> {
    try {
      console.log('[Configure Taskmaster] Starting configuration...');
      console.log('[Configure Taskmaster] Project path:', projectPath);
      console.log('[Configure Taskmaster] API keys received:', {
        anthropic: anthropicApiKey ? '***' + anthropicApiKey.slice(-4) : 'missing',
        perplexity: perplexityApiKey ? '***' + perplexityApiKey.slice(-4) : 'missing'
      });

      // Validate API keys
      if (!anthropicApiKey || anthropicApiKey.trim() === '') {
        return {
          success: false,
          error: 'Anthropic API key is required but was not provided'
        };
      }

      if (!perplexityApiKey || perplexityApiKey.trim() === '') {
        return {
          success: false,
          error: 'Perplexity API key is required but was not provided'
        };
      }

      // Create .env file with API keys
      const envContent = `ANTHROPIC_API_KEY=${anthropicApiKey}\nPERPLEXITY_API_KEY=${perplexityApiKey}\n`;
      const envPath = path.join(projectPath, '.env');
      await fs.writeFile(envPath, envContent);
      console.log('[Configure Taskmaster] ✅ .env file created at:', envPath);

      // Verify the .env file was written correctly
      try {
        const writtenContent = await fs.readFile(envPath, 'utf8');
        console.log('[Configure Taskmaster] ✅ .env file verification successful');
        console.log('[Configure Taskmaster] .env content preview:', writtenContent.split('\n').map(line =>
          line.includes('=') ? line.split('=')[0] + '=***' + line.split('=')[1].slice(-4) : line
        ).join('\n'));
      } catch (verifyError) {
        console.error('[Configure Taskmaster] ❌ Failed to verify .env file:', verifyError);
      }

      // Create or update config file if provided
      if (config) {
        const configPath = path.join(projectPath, '.taskmaster', 'config.json');
        await fs.mkdir(path.dirname(configPath), { recursive: true });
        await fs.writeFile(configPath, JSON.stringify(config, null, 2));
        console.log('[Configure Taskmaster] ✅ Config file created at:', configPath);
      }

      console.log('[Configure Taskmaster] ✅ Configuration completed successfully');
      return { success: true };
    } catch (error) {
      console.error('[Configure Taskmaster] ❌ Configuration failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to configure Claude Taskmaster'
      };
    }
  }

  /**
   * Waits for a file to exist and be valid JSON
   * @param filePath Path to the file to wait for
   * @param timeout Maximum time to wait in milliseconds (default: 60000)
   * @returns Promise that resolves when file exists and is valid JSON
   */
  private async waitForFile(filePath: string, timeout = 60000): Promise<void> {
    // ✅ SUPER OPTIMIZED: Use very frequent polling for ultra-fast detection
    const pollInterval = timeout <= 3000 ? 100 : 250; // Use 100ms for short timeouts, 250ms for longer ones
    let elapsed = 0;

    return new Promise((resolve, reject) => {
      const interval = setInterval(async () => {
        try {
          const content = await fs.readFile(filePath, 'utf-8');
          JSON.parse(content); // Ensures valid JSON
          clearInterval(interval);
          console.log(`[waitForFile] ✅ File ready: ${filePath}`);
          return resolve();
        } catch (err) {
          elapsed += pollInterval;
          if (elapsed >= timeout) {
            clearInterval(interval);
            console.error(`[waitForFile] ❌ Timeout waiting for ${filePath} after ${timeout}ms`);
            return reject(new Error(`Timed out waiting for ${filePath} after ${timeout}ms`));
          }
          // Continue polling
        }
      }, pollInterval);
    });
  }

  async parsePRD(
    projectPath: string,
    prdContent: string,
    prdFileName = 'prd.txt'
  ): Promise<{
    success: boolean;
    error?: string;
    tasksGenerated?: number;
    tasksFilePath?: string;
  }> {
    // Check if PRD file already exists in scripts directory (preferred location)
    const scriptsPrdPath = path.join(projectPath, 'scripts', prdFileName);
    const docsDir = path.join(projectPath, '.taskmaster', 'docs');
    const tasksDir = path.join(projectPath, '.taskmaster', 'tasks');

    let prdPath = scriptsPrdPath;
    let useScriptsLocation = false;

    // Updated path for new directory structure
    const tasksPath = path.join(tasksDir, 'tasks.json');
    const legacyTasksPath = path.join(projectPath, '.taskmaster', 'tasks.json');

    try {
      console.log('[PRD Parser] Starting PRD parsing process...');
      console.log('[PRD Parser] Project path:', projectPath);

      // 1. Check if PRD file exists in scripts directory first
      try {
        await fs.access(scriptsPrdPath);
        console.log('[PRD Parser] Found existing PRD file in scripts directory:', scriptsPrdPath);
        prdPath = scriptsPrdPath;
        useScriptsLocation = true;
      } catch {
        // PRD file doesn't exist in scripts, use .taskmaster/docs location
        console.log('[PRD Parser] PRD file not found in scripts, using .taskmaster/docs location');
        prdPath = path.join(docsDir, prdFileName);
        useScriptsLocation = false;
      }

      console.log('[PRD Parser] PRD file path:', prdPath);
      console.log('[PRD Parser] Expected tasks.json:', tasksPath);
      console.log('[PRD Parser] Legacy tasks.json fallback:', legacyTasksPath);

      // 2. Ensure .taskmaster directory structure exists
      await fs.mkdir(docsDir, { recursive: true });
      await fs.mkdir(tasksDir, { recursive: true });

      // 3. Write PRD content to file if not using scripts location
      if (!useScriptsLocation) {
        await fs.writeFile(prdPath, prdContent, 'utf8');
        console.log('[PRD Parser] PRD file written to .taskmaster/docs');
      } else {
        console.log('[PRD Parser] Using existing PRD file from scripts directory');
      }

      // 3. Check if project needs initialization
      const configPath = path.join(projectPath, '.taskmaster', 'config.json');
      const legacyConfigPath = path.join(projectPath, '.taskmasterconfig');

      let needsInit = true;
      try {
        await fs.access(configPath);
        needsInit = false;
        console.log('[PRD Parser] Found existing config.json');
      } catch {
        try {
          await fs.access(legacyConfigPath);
          needsInit = false;
          console.log('[PRD Parser] Found legacy .taskmasterconfig');
        } catch {
          console.log('[PRD Parser] No configuration found, will initialize project');
        }
      }

      // 4. Read API keys from .env file in project directory
      let envVars = { ...process.env };
      try {
        const envPath = path.join(projectPath, '.env');
        console.log('[PRD Parser] Reading .env file from:', envPath);
        const envContent = await fs.readFile(envPath, 'utf8');
        console.log('[PRD Parser] .env file content preview:', envContent.split('\n').map(line =>
          line.includes('=') ? line.split('=')[0] + '=***' + line.split('=')[1].slice(-4) : line
        ).join('\n'));

        const envLines = envContent.split('\n');
        for (const line of envLines) {
          const [key, value] = line.split('=');
          if (key && value) {
            envVars[key.trim()] = value.trim();
          }
        }
        console.log('[PRD Parser] ✅ Loaded API keys from .env file');
        console.log('[PRD Parser] Environment variables after loading:', {
          ANTHROPIC_API_KEY: envVars.ANTHROPIC_API_KEY ? '***' + envVars.ANTHROPIC_API_KEY.slice(-4) : 'missing',
          PERPLEXITY_API_KEY: envVars.PERPLEXITY_API_KEY ? '***' + envVars.PERPLEXITY_API_KEY.slice(-4) : 'missing'
        });
      } catch (envError) {
        console.error('[PRD Parser] ❌ Could not read .env file:', envError);

        // Check if API keys are available in the environment
        if (!envVars.ANTHROPIC_API_KEY && !envVars.PERPLEXITY_API_KEY) {
          return {
            success: false,
            error: 'API keys not found. Please ensure Anthropic and Perplexity API keys are configured in .env file or environment variables.'
          };
        } else {
          console.log('[PRD Parser] Using API keys from environment variables');
        }
      }

      // Validate that we have at least one required API key
      if (!envVars.ANTHROPIC_API_KEY) {
        console.error('[PRD Parser] ❌ ANTHROPIC_API_KEY is missing from environment variables');
        return {
          success: false,
          error: 'ANTHROPIC_API_KEY is required for Claude Taskmaster to function.'
        };
      }

      console.log('[PRD Parser] ✅ API key validation passed');

      // 5. Initialize project if needed
      if (needsInit) {
        console.log('[PRD Parser] Initializing Claude Taskmaster project...');
        try {
          const initCommand = 'task-master init --yes';
          const { stdout: initStdout, stderr: initStderr } = await execAsync(initCommand, {
            cwd: projectPath,
            env: envVars,
            timeout: 30000 // 30 second timeout
          });
          console.log('[PRD Parser] Init stdout:', initStdout);
          if (initStderr) console.warn('[PRD Parser] Init stderr:', initStderr);
        } catch (initError) {
          console.warn('[PRD Parser] Init failed, continuing anyway:', initError);
          // Continue with parsing even if init fails
        }
      } else {
        console.log('[PRD Parser] Project already initialized, skipping init');
      }

      // ✅ CRITICAL: Always ensure .env file exists with API keys
      const envFilePath = path.join(projectPath, '.env');
      try {
        // Check if .env exists and has our keys
        let envContent = '';
        try {
          envContent = await fs.readFile(envFilePath, 'utf8');
        } catch {
          // .env doesn't exist, create it
        }

        // Ensure our API keys are in the .env file
        const envLines = envContent.split('\n').filter(line => line.trim());
        const envMap = new Map<string, string>();

        // Parse existing .env
        for (const line of envLines) {
          const [key, ...valueParts] = line.split('=');
          if (key && valueParts.length > 0) {
            envMap.set(key.trim(), valueParts.join('=').trim());
          }
        }

        // ✅ FIXED: API keys should already be in .env from configureTaskmaster step
        // If they're missing, we'll rely on the existing environment variables
        if (!envMap.has('ANTHROPIC_API_KEY') && envVars.ANTHROPIC_API_KEY) {
          envMap.set('ANTHROPIC_API_KEY', envVars.ANTHROPIC_API_KEY);
        }
        if (!envMap.has('PERPLEXITY_API_KEY') && envVars.PERPLEXITY_API_KEY) {
          envMap.set('PERPLEXITY_API_KEY', envVars.PERPLEXITY_API_KEY);
        }

        // Write back to .env
        const newEnvContent = Array.from(envMap.entries())
          .map(([key, value]) => `${key}=${value}`)
          .join('\n');

        await fs.writeFile(envFilePath, newEnvContent, 'utf8');
        console.log('[PRD Parser] ✅ Updated .env file with API keys');
      } catch (envError) {
        console.warn('[PRD Parser] Failed to update .env file:', envError);
      }

      // 6. Execute the parse command with correct syntax and API keys
      const relativePrdPath = useScriptsLocation ? `scripts/${prdFileName}` : `.taskmaster/docs/${prdFileName}`;

      // ✅ ENHANCED: Try multiple command variations to ensure compatibility
      const commands = [
        `task-master parse-prd ${relativePrdPath}`,
        `task-master parse-prd "${relativePrdPath}"`,
        `task-master parse-prd ./${relativePrdPath}`,
        `npx task-master-ai parse-prd ${relativePrdPath}`
      ];

      console.log('[PRD Parser] Will try commands:', commands);
      console.log('[PRD Parser] Working directory:', projectPath);
      console.log('[PRD Parser] Using PRD file:', relativePrdPath);
      console.log('[PRD Parser] Full PRD path:', prdPath);
      console.log('[PRD Parser] Environment includes API keys:', 'ANTHROPIC_API_KEY' in envVars, 'PERPLEXITY_API_KEY' in envVars);

      // ✅ CRITICAL: Verify PRD file exists before parsing
      try {
        await fs.access(prdPath);
        const prdStats = await fs.stat(prdPath);
        console.log('[PRD Parser] PRD file verified - size:', prdStats.size, 'bytes');

        // Also verify the file has content
        if (prdStats.size === 0) {
          console.error('[PRD Parser] ❌ PRD file is empty');
          return {
            success: false,
            error: `PRD file is empty: ${prdPath}`
          };
        }
      } catch (prdError) {
        console.error('[PRD Parser] ❌ PRD file not accessible:', prdPath, prdError);
        return {
          success: false,
          error: `PRD file not found at ${prdPath}: ${prdError instanceof Error ? prdError.message : 'Unknown error'}`
        };
      }

      let lastError: string = '';
      let stdout = '';
      let stderr = '';

      // ✅ ENHANCED: First check if task-master is working at all
      try {
        console.log('[PRD Parser] Testing task-master CLI availability...');
        const versionResult = await execAsync('task-master --version', {
          cwd: projectPath,
          env: envVars,
          timeout: 10000
        });
        console.log('[PRD Parser] CLI version check:', versionResult.stdout);
      } catch (versionError) {
        console.warn('[PRD Parser] CLI version check failed:', versionError instanceof Error ? versionError.message : 'Unknown error');
        // Try with npx
        try {
          const npxVersionResult = await execAsync('npx task-master-ai --version', {
            cwd: projectPath,
            env: envVars,
            timeout: 15000
          });
          console.log('[PRD Parser] NPX CLI version check:', npxVersionResult.stdout);
        } catch (npxError) {
          console.error('[PRD Parser] Both CLI and NPX version checks failed');
          return {
            success: false,
            error: `Claude Taskmaster CLI not accessible. Direct error: ${versionError instanceof Error ? versionError.message : 'Unknown error'}. NPX error: ${npxError instanceof Error ? npxError.message : 'Unknown error'}`
          };
        }
      }

      // Try each command until one succeeds
      let commandSucceeded = false;
      for (let i = 0; i < commands.length; i++) {
        const command = commands[i];
        console.log(`[PRD Parser] Attempt ${i + 1}/${commands.length}: ${command}`);

        try {
          const result = await execAsync(command, {
            cwd: projectPath,
            env: envVars,
            timeout: 120000 // 2 minute timeout for parsing
          });

          stdout = result.stdout;
          stderr = result.stderr;

          console.log('[PRD Parser] ✅ Command completed successfully');
          console.log('[PRD Parser] stdout:', stdout);
          if (stderr) console.warn('[PRD Parser] stderr:', stderr);

          // Check if command indicated success
          if (stderr && stderr.toLowerCase().includes('error')) {
            lastError = `CLI reported error: ${stderr}`;
            console.warn(`[PRD Parser] Command ${i + 1} had stderr errors, trying next...`);
            continue;
          }

          // Command succeeded, mark it and break out of loop
          commandSucceeded = true;
          break;

        } catch (cmdError) {
          lastError = cmdError instanceof Error ? cmdError.message : 'Unknown command error';
          console.warn(`[PRD Parser] Command ${i + 1} failed:`, lastError);

          // ✅ ENHANCED: Log detailed error information
          if (cmdError instanceof Error) {
            console.error(`[PRD Parser] Command ${i + 1} detailed error:`, {
              message: cmdError.message,
              stack: cmdError.stack,
              name: cmdError.name
            });
          }

          // If this is the last command, we'll return the error
          if (i === commands.length - 1) {
            console.error('[PRD Parser] ❌ All commands failed');
            console.error('[PRD Parser] Final error details:', {
              lastError,
              commandsTried: commands,
              workingDirectory: projectPath,
              prdPath,
              envHasKeys: {
                ANTHROPIC_API_KEY: !!envVars.ANTHROPIC_API_KEY,
                PERPLEXITY_API_KEY: !!envVars.PERPLEXITY_API_KEY
              }
            });
            return {
              success: false,
              error: `All parse commands failed. Last error: ${lastError}. Commands tried: ${commands.join(', ')}`
            };
          }
        }
      }

      // If no command succeeded, return error
      if (!commandSucceeded) {
        return {
          success: false,
          error: `All parse commands failed. Last error: ${lastError}`
        };
      }

      // 7. 🕒 CRITICAL: Block until tasks.json exists and is valid
      console.log('[PRD Parser] 🕒 Waiting for tasks.json to be created...');

      // ✅ OPTIMIZATION: Brief pause to allow file system sync after CLI completion
      console.log('[PRD Parser] ⏱️ Allowing brief file system sync delay...');
      await new Promise(resolve => setTimeout(resolve, 500)); // 500ms delay

      const projectRootTasksPath = path.join(projectPath, 'tasks.json');
      const taskmasterRootTasksPath = path.join(projectPath, '.taskmaster', 'tasks.json');
      const taskmasterTasksTasksPath = path.join(projectPath, '.taskmaster', 'tasks', 'tasks.json');
      const outputJsonPath = path.join(projectPath, 'output.json'); // Sometimes Claude Taskmaster uses this

      // ✅ SMART STRATEGY: Check primary location first with immediate polling, then fallback
      console.log('[PRD Parser] 🚀 OPTIMIZED: Checking primary location first...');

      let finalTasksPath: string | null = null;
      let taskContent: string = '';

      // ✅ STEP 1: Quick check of primary location (.taskmaster/tasks/tasks.json)
      try {
        console.log(`[PRD Parser] 🕒 Quick check of primary location: ${taskmasterTasksTasksPath}`);
        await this.waitForFile(taskmasterTasksTasksPath, 3000); // Very short timeout for primary
        taskContent = await fs.readFile(taskmasterTasksTasksPath, 'utf8');
        finalTasksPath = taskmasterTasksTasksPath;
        console.log(`[PRD Parser] ✅ Found tasks file in primary location: ${taskmasterTasksTasksPath}`);
      } catch (primaryError) {
        console.log(`[PRD Parser] ⚠️ Primary location not ready yet, checking fallback locations...`);

        // ✅ STEP 2: Fallback to other locations with reduced timeouts
        const fallbackLocations = [
          { path: taskmasterRootTasksPath, name: '.taskmaster root', timeout: 2000 },
          { path: legacyTasksPath, name: 'legacy', timeout: 2000 },
          { path: tasksPath, name: 'tasks dir', timeout: 2000 },
          { path: outputJsonPath, name: 'output.json', timeout: 1000 },
          { path: projectRootTasksPath, name: 'project root', timeout: 1000 }
        ];

        console.log('[PRD Parser] Will check these fallback locations:');
        fallbackLocations.forEach((loc, i) => {
          console.log(`[PRD Parser] Fallback ${i + 1} (${loc.name}): ${loc.path} (timeout: ${loc.timeout}ms)`);
        });

        // ✅ STEP 3: Try fallback locations
        for (const location of fallbackLocations) {
          try {
            console.log(`[PRD Parser] 🕒 Checking fallback ${location.name}: ${location.path}`);
            await this.waitForFile(location.path, location.timeout);
            taskContent = await fs.readFile(location.path, 'utf8');
            finalTasksPath = location.path;
            console.log(`[PRD Parser] ✅ Found tasks file in fallback ${location.name}: ${location.path}`);
            break;
          } catch (error) {
            console.log(`[PRD Parser] ❌ Fallback ${location.name} timeout: ${error instanceof Error ? error.message : 'Unknown error'}`);
            // Continue to next fallback location
          }
        }
      }

      if (!finalTasksPath) {
        // ✅ ENHANCED: List actual directory contents for debugging
        console.log('[PRD Parser] 🔍 Debugging: Listing directory contents...');
        try {
          const projectFiles = await fs.readdir(projectPath);
          console.log('[PRD Parser] Project root files:', projectFiles);

          const taskmasterDir = path.join(projectPath, '.taskmaster');
          try {
            const taskmasterFiles = await fs.readdir(taskmasterDir);
            console.log('[PRD Parser] .taskmaster directory files:', taskmasterFiles);

            // Check subdirectories
            for (const file of taskmasterFiles) {
              const subPath = path.join(taskmasterDir, file);
              try {
                const stat = await fs.stat(subPath);
                if (stat.isDirectory()) {
                  const subFiles = await fs.readdir(subPath);
                  console.log(`[PRD Parser] .taskmaster/${file} files:`, subFiles);
                }
              } catch (subError) {
                console.log(`[PRD Parser] Could not read .taskmaster/${file}:`, subError instanceof Error ? subError.message : 'Unknown error');
              }
            }
          } catch (dirError) {
            console.log('[PRD Parser] .taskmaster directory not accessible:', dirError instanceof Error ? dirError.message : 'Unknown error');
          }
        } catch (listError) {
          console.log('[PRD Parser] Could not list directory contents:', listError instanceof Error ? listError.message : 'Unknown error');
        }

        console.error('[PRD Parser] ❌ tasks.json not created within timeout period');
        console.error('[PRD Parser] Checked primary location:', taskmasterTasksTasksPath);
        console.error('[PRD Parser] CLI stdout:', stdout);
        console.error('[PRD Parser] CLI stderr:', stderr);

        // Calculate total timeout used (3s primary + fallback timeouts)
        const totalTimeout = 3000 + 2000 + 2000 + 2000 + 1000 + 1000; // 11 seconds total

        return {
          success: false,
          error: `Claude Taskmaster failed to create tasks.json within ${totalTimeout}ms total timeout. CLI completed but file not found in expected locations. CLI output: ${stdout || stderr || 'No output'}`
        };
      }

      // 8. ✅ FINAL VALIDATION: Parse and validate tasks.json content
      try {
        console.log('[PRD Parser] 🔍 Validating tasks.json content...');
        const parsed = JSON.parse(taskContent);
        let count = 0;

        // Handle different task.json formats
        if (Array.isArray(parsed)) {
          count = parsed.length;
        } else if (parsed.tasks && Array.isArray(parsed.tasks)) {
          count = parsed.tasks.length;
        } else if (typeof parsed === 'object') {
          count = Object.keys(parsed).length;
        }

        console.log('[PRD Parser] ✅ Successfully created', count, 'tasks in', finalTasksPath);
        console.log('[PRD Parser] ✅ PRD parsing completed successfully - tasks.json exists and is valid');

        return {
          success: true,
          tasksGenerated: count,
          tasksFilePath: finalTasksPath
        };
      } catch (parseError) {
        console.error('[PRD Parser] ❌ Failed to parse tasks.json content:', parseError);
        console.error('[PRD Parser] ❌ Raw content:', taskContent.substring(0, 500));
        return {
          success: false,
          error: `Invalid tasks.json format: ${parseError instanceof Error ? parseError.message : 'Unknown parse error'}`
        };
      }
    } catch (err) {
      const msg = err instanceof Error ? err.message : 'Unknown error';
      console.error('[PRD Parser] FAILED:', msg);
      return {
        success: false,
        error: `Taskmaster failed to generate tasks.json: ${msg}`
      };
    }
  }

  async generateTaskFiles(projectPath: string): Promise<{ success: boolean; error?: string }> {
    try {
      const { stdout, stderr } = await execAsync('task-master generate', {
        cwd: projectPath,
        env: { ...process.env }
      });

      console.log('Taskmaster generate output:', stdout);
      if (stderr) console.warn('Taskmaster generate warnings:', stderr);

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to generate task files'
      };
    }
  }

  async listTasks(projectPath: string): Promise<{ success: boolean; tasks?: any[]; error?: string }> {
    try {
      const { stdout } = await execAsync('task-master list --json', {
        cwd: projectPath,
        env: { ...process.env }
      });

      const tasks = JSON.parse(stdout);
      return {
        success: true,
        tasks
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to list tasks'
      };
    }
  }

  async getNextTask(projectPath: string): Promise<{ success: boolean; task?: any; error?: string }> {
    try {
      const { stdout } = await execAsync('task-master next --json', {
        cwd: projectPath,
        env: { ...process.env }
      });

      const task = JSON.parse(stdout);
      return {
        success: true,
        task
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get next task'
      };
    }
  }
}

// Export singleton getter function
export const getClaudeTaskmasterElectronService = (): ClaudeTaskmasterElectronService => {
  return ClaudeTaskmasterElectronService.getInstance();
};
