/**
 * Electron Main Process - Streamlined Shell
 * 
 * Reduced from 1523 lines to under 200 lines by extracting functionality into modular components.
 * This serves as the main orchestration interface that delegates to specialized modules.
 */

import fs from 'fs';
import path from 'path';

// ✅ Import all modular components
import {
  // Constants and configuration
  isDev,
  debugConfig,
  LOG_CONFIG,
  
  // App lifecycle
  initializeApp,
  
  // IPC handlers
  registerAllIPCHandlers,
  
  // Services (imported for terminal session manager integration)
  getServices
} from './bootstrap';

// ✅ CRITICAL DEBUG: Log immediately to see if script is running
console.log('🚀 Main process starting...');
console.log('Process argv:', process.argv);
console.log('Process cwd:', process.cwd());
console.log('__dirname:', __dirname);

// ✅ CRITICAL DEBUG: File-based logging to debug Electron startup
const logToFile = (message: string) => {
  try {
    fs.appendFileSync(LOG_CONFIG.file, `${new Date().toISOString()} - ${message}\n`);
  } catch (error) {
    // Ignore file write errors
  }
};

logToFile('🚀 Main process starting...');
logToFile(`Process argv: ${JSON.stringify(process.argv)}`);
logToFile(`Process cwd: ${process.cwd()}`);
logToFile(`__dirname: ${__dirname}`);

// ✅ Create a safer console.log that doesn't throw when streams are closed
const safeConsole = {
  log: (...args: any[]) => {
    try {
      console.log(...args);
    } catch (error) {
      // Silently ignore write errors
    }
  },
  error: (...args: any[]) => {
    try {
      console.error(...args);
    } catch (error) {
      // Silently ignore write errors
    }
  },
  warn: (...args: any[]) => {
    try {
      console.warn(...args);
    } catch (error) {
      // Silently ignore write errors
    }
  }
};

// ✅ Log configuration
if (debugConfig.strictMode) {
  safeConsole.log('🔍 Debug strict mode enabled - enhanced error detection and logging');
}

safeConsole.log('📦 Development mode:', isDev);
safeConsole.log('🔧 Debug config:', debugConfig);

// ✅ Initialize the Electron application
try {
  logToFile('🔄 Initializing Electron app...');
  safeConsole.log('🔄 Initializing Electron app...');
  
  // Initialize all app configurations and lifecycle handlers
  initializeApp();
  
  logToFile('✅ Electron app initialization completed');
  safeConsole.log('✅ Electron app initialization completed');
} catch (error) {
  logToFile(`❌ Electron app initialization failed: ${error}`);
  safeConsole.error('❌ Electron app initialization failed:', error);
  process.exit(1);
}

// ✅ Register all IPC handlers
try {
  logToFile('🔄 Registering IPC handlers...');
  safeConsole.log('🔄 Registering IPC handlers...');
  
  registerAllIPCHandlers();
  
  logToFile('✅ IPC handlers registration completed');
  safeConsole.log('✅ IPC handlers registration completed');
} catch (error) {
  logToFile(`❌ IPC handlers registration failed: ${error}`);
  safeConsole.error('❌ IPC handlers registration failed:', error);
  // Continue anyway to allow basic functionality
}

// ✅ Additional terminal session manager IPC handlers (if needed)
try {
  const { terminalSessionManager } = getServices();

  if (terminalSessionManager) {
    const { ipcMain } = require('electron');
    const crypto = require('crypto');

    // Terminal session manager handlers
    ipcMain.handle('terminal:create-session', async (event: any, sessionId: string, agentId: string, options?: any) => {
      try {
        const ptyProcess = terminalSessionManager.createSession(sessionId, agentId, options);

        // Set up real-time data and exit listeners
        ptyProcess.onData((data: string) => {
          event.sender.send(`terminal:session-data:${sessionId}`, data);
        });

        ptyProcess.onExit((e: { exitCode: number; signal?: number }) => {
          safeConsole.log(`✅ Terminal session ${sessionId} exited with code: ${e.exitCode}`);
          event.sender.send(`terminal:session-exit:${sessionId}`, { exitCode: e.exitCode, signal: e.signal });
        });

        return { success: true, sessionId, agentId };
      } catch (error) {
        safeConsole.error(`❌ Terminal session creation error:`, error);
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        };
      }
    });

    ipcMain.handle('terminal:write-session', async (event: any, sessionId: string, data: string) => {
      try {
        const result = terminalSessionManager.writeToSession(sessionId, data);
        return { success: true, result };
      } catch (error) {
        safeConsole.error(`❌ Terminal session write error:`, error);
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        };
      }
    });

    ipcMain.handle('terminal:destroy-session', async (event: any, sessionId: string) => {
      try {
        const result = terminalSessionManager.destroySession(sessionId);
        return { success: true, result };
      } catch (error) {
        safeConsole.error(`❌ Terminal session destroy error:`, error);
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        };
      }
    });

    ipcMain.handle('terminal:list-sessions', async (event: any, agentId?: string) => {
      try {
        const allSessions = terminalSessionManager.listSessions();
        const sessions = agentId
          ? allSessions.filter(session => session.agentId === agentId)
          : allSessions;
        return { success: true, sessions };
      } catch (error) {
        safeConsole.error(`❌ Terminal session list error:`, error);
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        };
      }
    });

    ipcMain.handle('terminal:get-session-info', async (event: any, sessionId: string) => {
      try {
        const sessionInfo = terminalSessionManager.getSessionInfo(sessionId);
        return { success: true, sessionInfo };
      } catch (error) {
        safeConsole.error(`❌ Terminal session info error:`, error);
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        };
      }
    });

    ipcMain.handle('terminal:resize-session', async (event: any, sessionId: string, cols: number, rows: number) => {
      try {
        const result = terminalSessionManager.resizeSession(sessionId, cols, rows);
        return { success: true, result };
      } catch (error) {
        safeConsole.error(`❌ Terminal session resize error:`, error);
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        };
      }
    });

    ipcMain.handle('terminal:get-session-stats', async () => {
      try {
        const stats = terminalSessionManager.getStats();
        return { success: true, stats };
      } catch (error) {
        safeConsole.error(`❌ Terminal session stats error:`, error);
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        };
      }
    });

    ipcMain.handle('terminal:cleanup-agent-sessions', async (event: any, agentId: string) => {
      try {
        const result = terminalSessionManager.destroyAgentSessions(agentId);
        return { success: true, result };
      } catch (error) {
        safeConsole.error(`❌ Terminal agent cleanup error:`, error);
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        };
      }
    });

    safeConsole.log('✅ Terminal session manager IPC handlers registered');
  }
} catch (error) {
  safeConsole.error('❌ Terminal session manager IPC setup failed:', error);
  // Continue anyway
}

// ✅ Final startup message
logToFile('🚀 Electron main process startup completed');
safeConsole.log('🚀 Electron main process startup completed');

// ✅ Export for testing purposes
export { safeConsole, logToFile };
