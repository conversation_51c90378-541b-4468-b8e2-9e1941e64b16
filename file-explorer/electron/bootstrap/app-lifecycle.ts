/**
 * App Lifecycle Events - Application Event Listeners
 * 
 * Extracted from main.ts to handle app lifecycle events, quitting, and suspend.
 */

import { app } from 'electron';
import fs from 'fs';
import { createMainWindow, getMainWindow } from './create-main-window';
import { initializeServices, cleanupServices } from './initialize-services';
import { cleanupTerminalSessions } from './register-ipc-handlers';

// ✅ Safe console for logging
const safeConsole = {
  log: (...args: any[]) => {
    try {
      console.log(...args);
    } catch (error) {
      // Silently ignore write errors
    }
  },
  error: (...args: any[]) => {
    try {
      console.error(...args);
    } catch (error) {
      // Silently ignore write errors
    }
  },
  warn: (...args: any[]) => {
    try {
      console.warn(...args);
    } catch (error) {
      // Silently ignore write errors
    }
  }
};

// ✅ File-based logging
const logToFile = (message: string) => {
  try {
    fs.appendFileSync(require('path').join(process.cwd(), 'electron-debug.log'), 
      `${new Date().toISOString()} - ${message}\n`);
  } catch (error) {
    // Ignore file write errors
  }
};

/**
 * Register app ready event handler
 */
export function registerAppReadyHandler(): void {
  app.on('ready', async () => {
    logToFile('🚀 Electron app ready event fired');
    safeConsole.log('🚀 Electron app ready event fired');

    // Initialize all services before creating windows
    try {
      await initializeServices();
      logToFile('✅ All services initialized successfully');
      safeConsole.log('✅ All services initialized successfully');
    } catch (error) {
      logToFile(`❌ Service initialization failed: ${error}`);
      safeConsole.error('❌ Service initialization failed:', error);
      // Continue anyway to allow basic functionality
    }

    logToFile('Creating main window');
    safeConsole.log('Creating main window');
    createMainWindow();

    // ✅ FIXED: Window IPC handlers now registered in main.ts with other handlers
    // No need to register here to avoid double registration
  });
}

/**
 * Register window all closed event handler
 */
export function registerWindowAllClosedHandler(): void {
  app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
      app.quit();
    }
  });
}

/**
 * Register activate event handler (macOS)
 */
export function registerActivateHandler(): void {
  app.on('activate', () => {
    const mainWindow = getMainWindow();
    if (mainWindow === null) {
      createMainWindow();
    }
  });
}

/**
 * Register before quit event handler
 */
export function registerBeforeQuitHandler(): void {
  app.on('before-quit', () => {
    safeConsole.log('🧹 Cleaning up before quit...');

    // Clean up terminal sessions
    cleanupTerminalSessions();

    // Clean up services
    cleanupServices();

    safeConsole.log('✅ Cleanup completed');
  });
}

/**
 * Register will quit event handler
 */
export function registerWillQuitHandler(): void {
  app.on('will-quit', (event) => {
    safeConsole.log('🔄 App will quit...');
    // Additional cleanup if needed
  });
}

/**
 * Register window created event handler
 */
export function registerWindowCreatedHandler(): void {
  app.on('browser-window-created', (event, window) => {
    safeConsole.log('🪟 Browser window created:', window.getTitle());
  });
}

/**
 * Register web contents created event handler
 */
export function registerWebContentsCreatedHandler(): void {
  app.on('web-contents-created', (event, contents) => {
    safeConsole.log('🌐 Web contents created');
    
    // Security: Prevent new window creation
    contents.setWindowOpenHandler(({ url }) => {
      safeConsole.log('🔒 Blocked window open:', url);
      return { action: 'deny' };
    });
  });
}

/**
 * Register certificate error handler
 */
export function registerCertificateErrorHandler(): void {
  app.on('certificate-error', (event, webContents, url, error, certificate, callback) => {
    // In development, ignore certificate errors for localhost
    if (url.startsWith('https://localhost') || url.startsWith('https://127.0.0.1')) {
      event.preventDefault();
      callback(true);
    } else {
      callback(false);
    }
  });
}

/**
 * Register all app lifecycle event handlers
 */
export function registerAllLifecycleHandlers(): void {
  safeConsole.log('🔄 Registering app lifecycle handlers...');

  registerAppReadyHandler();
  registerWindowAllClosedHandler();
  registerActivateHandler();
  registerBeforeQuitHandler();
  registerWillQuitHandler();
  registerWindowCreatedHandler();
  registerWebContentsCreatedHandler();
  registerCertificateErrorHandler();

  safeConsole.log('✅ All app lifecycle handlers registered');
}

/**
 * Setup macOS specific configurations
 */
export function setupMacOSConfig(): void {
  if (process.platform === 'darwin') {
    try {
      app.setAboutPanelOptions({
        applicationName: 'CodeFusion',
        applicationVersion: '1.0.0',
        copyright: 'Copyright © 2024 CodeFusion'
      });
      
      // Disable automatic state restoration to avoid the secure coding warning
      app.commandLine.appendSwitch('disable-features', 'VizDisplayCompositor');
      
      safeConsole.log('✅ macOS configuration applied');
    } catch (error) {
      safeConsole.error('❌ macOS configuration failed:', error);
    }
  }
}

/**
 * Setup security configurations
 */
export function setupSecurityConfig(): void {
  // Disable node integration in renderer processes by default
  app.on('web-contents-created', (event, contents) => {
    contents.setWindowOpenHandler(({ url }) => {
      // Prevent new window creation
      safeConsole.log('🔒 Blocked window open:', url);
      return { action: 'deny' };
    });
  });

  safeConsole.log('✅ Security configuration applied');
}

/**
 * Initialize all app configurations and handlers
 */
export function initializeApp(): void {
  safeConsole.log('🚀 Initializing Electron app...');

  setupSecurityConfig();
  setupMacOSConfig();
  registerAllLifecycleHandlers();

  safeConsole.log('✅ Electron app initialization completed');
}
