/**
 * Main Window Creation - Window Lifecycle Management
 * 
 * Extracted from main.ts to handle main window creation and lifecycle.
 */

import { BrowserWindow, shell, app } from 'electron';
import fs from 'fs';
import { WINDOW_CONFIG, PATHS, WEB_PREFERENCES, isDev, buildUrl } from './main-constants';

// ✅ Safe console for logging
const safeConsole = {
  log: (...args: any[]) => {
    try {
      console.log(...args);
    } catch (error) {
      // Silently ignore write errors
    }
  },
  error: (...args: any[]) => {
    try {
      console.error(...args);
    } catch (error) {
      // Silently ignore write errors
    }
  },
  warn: (...args: any[]) => {
    try {
      console.warn(...args);
    } catch (error) {
      // Silently ignore write errors
    }
  }
};

// ✅ File-based logging
const logToFile = (message: string) => {
  try {
    fs.appendFileSync(require('path').join(process.cwd(), 'electron-debug.log'), 
      `${new Date().toISOString()} - ${message}\n`);
  } catch (error) {
    // Ignore file write errors
  }
};

// ✅ Main window reference
let mainWindow: BrowserWindow | null = null;

/**
 * Create the main application window
 */
export function createMainWindow(): BrowserWindow {
  logToFile('🪟 Creating main window...');
  safeConsole.log('🪟 Creating main window...');

  mainWindow = new BrowserWindow({
    width: WINDOW_CONFIG.main.width,
    height: WINDOW_CONFIG.main.height,
    webPreferences: WEB_PREFERENCES,
    icon: PATHS.icon,
    title: WINDOW_CONFIG.main.title
  });

  // Determine the correct path to load
  const appPath = app.getAppPath();
  safeConsole.log('App path:', appPath);

  if (isDev || process.argv.includes('--dev')) {
    // In development, load from the Next.js dev server
    safeConsole.log('Loading from dev server');
    
    // Add error handling for URL loading
    mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription, validatedURL) => {
      logToFile(`Failed to load URL: ${validatedURL}, Error: ${errorCode} ${errorDescription}`);
      safeConsole.error('Failed to load URL:', validatedURL, 'Error:', errorCode, errorDescription);
    });

    mainWindow.webContents.on('did-finish-load', () => {
      logToFile('Successfully loaded main window');
      safeConsole.log('Successfully loaded main window');
    });

    mainWindow.webContents.on('dom-ready', () => {
      logToFile('DOM ready for main window');
      safeConsole.log('DOM ready for main window');
    });
    
    logToFile('Loading URL: http://localhost:4444');
    mainWindow.loadURL(PATHS.devServer);
    mainWindow.webContents.openDevTools();
    logToFile('Window creation completed');
  } else {
    // In production, load the statically exported Next.js app
    try {
      // Use a direct path to the index.html file
      const indexFile = require('path').join(__dirname, '../../out/index.html');
      safeConsole.log('Checking for index.html at:', indexFile);
      
      mainWindow.loadFile(indexFile);
      
      // Only open DevTools in development or when explicitly requested
      if (process.argv.includes('--devtools')) {
        mainWindow.webContents.openDevTools();
      }
    } catch (error) {
      safeConsole.error('Error loading index.html:', error);
      
      // Show error in window
      mainWindow.loadURL(`data:text/html,<html><body><h1>Error</h1><p>${error}</p></body></html>`);
      
      // Always open DevTools when there's an error
      mainWindow.webContents.openDevTools();
    }
  }

  // Open external links in the default browser
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url);
    return { action: 'deny' };
  });

  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  return mainWindow;
}

/**
 * Get the current main window instance
 */
export function getMainWindow(): BrowserWindow | null {
  return mainWindow;
}

/**
 * Focus the main window if it exists
 */
export function focusMainWindow(): boolean {
  if (mainWindow) {
    mainWindow.focus();
    return true;
  }
  return false;
}

/**
 * Close the main window
 */
export function closeMainWindow(): void {
  if (mainWindow) {
    mainWindow.close();
    mainWindow = null;
  }
}

/**
 * Check if main window exists and is not destroyed
 */
export function isMainWindowValid(): boolean {
  return mainWindow !== null && !mainWindow.isDestroyed();
}

/**
 * Register main window with services
 */
export function registerMainWindowWithServices(
  boardStateService?: any,
  agentStateService?: any
): void {
  if (!mainWindow) return;

  if (boardStateService) {
    boardStateService.registerWindow(mainWindow);
  }
  if (agentStateService) {
    agentStateService.registerWindow(mainWindow);
  }
}
