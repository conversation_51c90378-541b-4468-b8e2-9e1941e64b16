/**
 * Main Constants - Platform Detection & Paths
 * 
 * Extracted from main.ts to provide centralized configuration and path management.
 */

import path from 'path';
import os from 'os';

// ✅ Platform detection
export const PLATFORM = {
  isDarwin: process.platform === 'darwin',
  isWin32: process.platform === 'win32',
  isLinux: process.platform === 'linux'
};

// ✅ Development mode detection
export const isDev = (() => {
  try {
    return require('electron-is-dev');
  } catch (error) {
    // Fallback: check if we're in development mode manually
    return process.env.NODE_ENV === 'development' || process.argv.includes('--dev');
  }
})();

// ✅ Debug configuration
export const debugConfig = {
  strictMode: process.argv.includes('--strict') || process.env.DEBUG_STRICT === 'true',
  enableServiceLogging: isDev || process.argv.includes('--debug-services'),
  enableIPCLogging: isDev || process.argv.includes('--debug-ipc')
};

// ✅ Window configuration constants
export const WINDOW_CONFIG = {
  main: {
    width: 1200,
    height: 800,
    title: 'CodeFusion - Modern Code Editor'
  },
  kanban: {
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    title: 'Kanban Board - CodeFusion'
  },
  agentSystem: {
    width: 1000,
    height: 700,
    minWidth: 600,
    minHeight: 400,
    title: 'Agent System - CodeFusion'
  },
  chat: {
    width: 400,
    height: 600,
    minWidth: 300,
    minHeight: 400,
    title: 'AI Chat - CodeFusion'
  },
  editor: {
    width: 1000,
    height: 700,
    minWidth: 600,
    minHeight: 400,
    title: 'Editor - CodeFusion'
  },
  explorer: {
    width: 400,
    height: 600,
    minWidth: 300,
    minHeight: 400,
    title: 'Explorer - CodeFusion'
  },
  timeline: {
    width: 800,
    height: 600,
    minWidth: 600,
    minHeight: 400,
    title: 'Timeline - CodeFusion'
  },
  terminal: {
    width: 800,
    height: 600,
    minWidth: 600,
    minHeight: 400,
    title: 'Terminal - CodeFusion'
  },
  settings: {
    width: 800,
    height: 600,
    minWidth: 600,
    minHeight: 400,
    title: 'Settings - CodeFusion'
  }
};

// ✅ Path constants
export const PATHS = {
  preload: path.join(__dirname, '../preload.js'),
  icon: path.join(__dirname, '../../public/placeholder-logo.svg'),
  devServer: 'http://localhost:4444',
  outDir: path.join(__dirname, '../../out')
};

// ✅ Terminal configuration
export const TERMINAL_CONFIG = {
  throttleMs: 300,
  defaultShell: os.platform() === 'win32' ? 'powershell.exe' : 'bash',
  defaultCols: 80,
  defaultRows: 30,
  enhancedEnv: {
    TERM: 'xterm-256color',
    COLORTERM: 'truecolor',
    PS1: '\\[\\033[01;32m\\]\\u@\\h\\[\\033[00m\\]:\\[\\033[01;34m\\]\\w\\[\\033[00m\\]\\$ ',
    HISTSIZE: '1000',
    HISTFILESIZE: '2000',
    HISTCONTROL: 'ignoredups:erasedups',
    LANG: process.env.LANG || 'en_US.UTF-8',
    LC_ALL: process.env.LC_ALL || 'en_US.UTF-8'
  },
  shellArgs: {
    bash: ['--login', '-i'],
    zsh: ['--login', '-i'],
    powershell: []
  }
};

// ✅ Service configuration
export const SERVICE_CONFIG = {
  initTimeout: 30000,
  retryAttempts: 3,
  retryDelay: 1000
};

// ✅ IPC throttling configuration
export const IPC_CONFIG = {
  boardStateThrottleMs: 300
};

// ✅ Logging configuration
export const LOG_CONFIG = {
  file: path.join(process.cwd(), 'electron-debug.log'),
  maxSize: 10 * 1024 * 1024, // 10MB
  maxFiles: 5
};

// ✅ WebPreferences template
export const WEB_PREFERENCES = {
  nodeIntegration: false,
  contextIsolation: true,
  preload: PATHS.preload,
  devTools: true,
  webSecurity: true
};

// ✅ URL builders
export const buildUrl = (route: string, params?: Record<string, string>) => {
  if (isDev || process.argv.includes('--dev')) {
    const url = new URL(route, PATHS.devServer);
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        url.searchParams.set(key, value);
      });
    }
    return url.toString();
  } else {
    return path.join(PATHS.outDir, route, 'index.html');
  }
};

// ✅ Shell detection helper
export const getShellArgs = (shell: string): string[] => {
  if (shell.includes('bash')) return TERMINAL_CONFIG.shellArgs.bash;
  if (shell.includes('zsh')) return TERMINAL_CONFIG.shellArgs.zsh;
  if (shell.includes('powershell')) return TERMINAL_CONFIG.shellArgs.powershell;
  return [];
};

// ✅ Environment enhancement helper
export const getEnhancedEnv = (additionalEnv?: Record<string, string>) => ({
  ...process.env,
  ...TERMINAL_CONFIG.enhancedEnv,
  ...additionalEnv
});
