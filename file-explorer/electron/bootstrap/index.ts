/**
 * Electron Bootstrap - Barrel Export
 * 
 * Unified export surface for all modular Electron bootstrap components.
 * This provides a clean import interface for the main.ts shell.
 */

// ✅ Constants and configuration
export * from './main-constants';

// ✅ Window management
export * from './create-main-window';
export * from './register-window-handlers';

// ✅ Service initialization
export * from './initialize-services';

// ✅ IPC handlers
export * from './register-ipc-handlers';

// ✅ App lifecycle
export * from './app-lifecycle';

// ✅ Convenience function to initialize everything
export function initializeElectronApp(): void {
  const { initializeApp } = require('./app-lifecycle');
  initializeApp();
}
