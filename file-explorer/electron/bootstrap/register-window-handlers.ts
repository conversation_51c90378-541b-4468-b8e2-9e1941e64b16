/**
 * Window Management Handlers - Multi-Window IPC Registration
 * 
 * Extracted from main.ts to handle window creation and management IPC handlers.
 */

import { ipc<PERSON>ain, BrowserWindow } from 'electron';
import { WINDOW_CONFIG, WEB_PREFERENCES, buildUrl } from './main-constants';
import { getServices } from './initialize-services';

// ✅ Safe console for logging
const safeConsole = {
  log: (...args: any[]) => {
    try {
      console.log(...args);
    } catch (error) {
      // Silently ignore write errors
    }
  },
  error: (...args: any[]) => {
    try {
      console.error(...args);
    } catch (error) {
      // Silently ignore write errors
    }
  },
  warn: (...args: any[]) => {
    try {
      console.warn(...args);
    } catch (error) {
      // Silently ignore write errors
    }
  }
};

// ✅ Window references
let kanbanWindow: BrowserWindow | null = null;
let agentSystemWindow: BrowserWindow | null = null;
let editorWindow: BrowserWindow | null = null;
let explorerWindow: BrowserWindow | null = null;
let chatWindow: BrowserWindow | null = null;
let timelineWindow: BrowserWindow | null = null;
let terminalWindow: BrowserWindow | null = null;
let settingsWindow: BrowserWindow | null = null;

/**
 * Create kanban window
 */
function createKanbanWindow(boardId: string): void {
  if (kanbanWindow) {
    kanbanWindow.focus();
    return;
  }

  kanbanWindow = new BrowserWindow({
    ...WINDOW_CONFIG.kanban,
    webPreferences: WEB_PREFERENCES,
    show: false
  });

  const kanbanUrl = buildUrl(`kanban/${boardId}`);
  kanbanWindow.loadURL(kanbanUrl);

  kanbanWindow.once('ready-to-show', () => {
    kanbanWindow?.show();
    if (process.argv.includes('--devtools')) {
      kanbanWindow?.webContents.openDevTools();
    }
  });

  kanbanWindow.on('closed', () => {
    kanbanWindow = null;
  });

  // Register with services
  const { boardStateService, agentStateService } = getServices();
  if (boardStateService) {
    boardStateService.registerWindow(kanbanWindow);
  }
  if (agentStateService) {
    agentStateService.registerWindow(kanbanWindow);
  }
}

/**
 * Create agent system window
 */
function createAgentSystemWindow(): void {
  if (agentSystemWindow) {
    agentSystemWindow.focus();
    return;
  }

  agentSystemWindow = new BrowserWindow({
    ...WINDOW_CONFIG.agentSystem,
    webPreferences: WEB_PREFERENCES,
    show: false
  });

  const agentUrl = buildUrl('agent-system');
  agentSystemWindow.loadURL(agentUrl);

  agentSystemWindow.once('ready-to-show', () => {
    agentSystemWindow?.show();
    if (process.argv.includes('--devtools')) {
      agentSystemWindow?.webContents.openDevTools();
    }
  });

  agentSystemWindow.on('closed', () => {
    agentSystemWindow = null;
  });

  // Register with services
  const { agentStateService } = getServices();
  if (agentStateService) {
    agentStateService.registerWindow(agentSystemWindow);
  }
}

/**
 * Create chat window
 */
function createChatWindow(): void {
  if (chatWindow) {
    chatWindow.focus();
    return;
  }

  chatWindow = new BrowserWindow({
    ...WINDOW_CONFIG.chat,
    webPreferences: WEB_PREFERENCES,
    show: false
  });

  const chatUrl = buildUrl('chat');
  chatWindow.loadURL(chatUrl);

  chatWindow.once('ready-to-show', () => {
    chatWindow?.show();
    if (process.argv.includes('--devtools')) {
      chatWindow?.webContents.openDevTools();
    }
  });

  chatWindow.on('closed', () => {
    chatWindow = null;
  });
}

/**
 * Create editor window
 */
function createEditorWindow(filePath?: string): void {
  if (editorWindow) {
    editorWindow.focus();
    return;
  }

  editorWindow = new BrowserWindow({
    ...WINDOW_CONFIG.editor,
    webPreferences: WEB_PREFERENCES,
    show: false
  });

  const editorUrl = buildUrl('editor', filePath ? { file: filePath } : undefined);
  editorWindow.loadURL(editorUrl);

  editorWindow.once('ready-to-show', () => {
    editorWindow?.show();
    if (process.argv.includes('--devtools')) {
      editorWindow?.webContents.openDevTools();
    }
  });

  editorWindow.on('closed', () => {
    editorWindow = null;
  });
}

/**
 * Create explorer window
 */
function createExplorerWindow(): void {
  if (explorerWindow) {
    explorerWindow.focus();
    return;
  }

  explorerWindow = new BrowserWindow({
    ...WINDOW_CONFIG.explorer,
    webPreferences: WEB_PREFERENCES,
    show: false
  });

  const explorerUrl = buildUrl('explorer');
  explorerWindow.loadURL(explorerUrl);

  explorerWindow.once('ready-to-show', () => {
    explorerWindow?.show();
    if (process.argv.includes('--devtools')) {
      explorerWindow?.webContents.openDevTools();
    }
  });

  explorerWindow.on('closed', () => {
    explorerWindow = null;
  });
}

/**
 * Create timeline window
 */
function createTimelineWindow(): void {
  if (timelineWindow) {
    timelineWindow.focus();
    return;
  }

  timelineWindow = new BrowserWindow({
    ...WINDOW_CONFIG.timeline,
    webPreferences: WEB_PREFERENCES,
    show: false
  });

  const timelineUrl = buildUrl('timeline');
  timelineWindow.loadURL(timelineUrl);

  timelineWindow.once('ready-to-show', () => {
    timelineWindow?.show();
    if (process.argv.includes('--devtools')) {
      timelineWindow?.webContents.openDevTools();
    }
  });

  timelineWindow.on('closed', () => {
    timelineWindow = null;
  });
}

/**
 * Create terminal window
 */
function createTerminalWindow(): void {
  if (terminalWindow) {
    terminalWindow.focus();
    return;
  }

  terminalWindow = new BrowserWindow({
    ...WINDOW_CONFIG.terminal,
    webPreferences: WEB_PREFERENCES,
    show: false
  });

  const terminalUrl = buildUrl('terminal');
  terminalWindow.loadURL(terminalUrl);

  terminalWindow.once('ready-to-show', () => {
    terminalWindow?.show();
    if (process.argv.includes('--devtools')) {
      terminalWindow?.webContents.openDevTools();
    }
  });

  terminalWindow.on('closed', () => {
    terminalWindow = null;
  });
}

/**
 * Create settings window
 */
function createSettingsWindow(): void {
  if (settingsWindow) {
    settingsWindow.focus();
    return;
  }

  settingsWindow = new BrowserWindow({
    ...WINDOW_CONFIG.settings,
    webPreferences: WEB_PREFERENCES,
    show: false
  });

  const settingsUrl = buildUrl('settings');
  settingsWindow.loadURL(settingsUrl);

  settingsWindow.once('ready-to-show', () => {
    settingsWindow?.show();
    if (process.argv.includes('--devtools')) {
      settingsWindow?.webContents.openDevTools();
    }
  });

  settingsWindow.on('closed', () => {
    settingsWindow = null;
  });
}

/**
 * Register window management IPC handlers
 */
export function registerWindowIPCHandlers(): void {
  // Register IPC handlers for window management
  ipcMain.on('open-kanban-window', (event, boardId: string) => {
    createKanbanWindow(boardId);
  });

  ipcMain.on('open-agent-system-window', () => {
    createAgentSystemWindow();
  });

  ipcMain.on('open-editor-window', (event, filePath?: string) => {
    createEditorWindow(filePath);
  });

  ipcMain.on('open-explorer-window', () => {
    createExplorerWindow();
  });

  ipcMain.on('open-chat-window', () => {
    createChatWindow();
  });

  ipcMain.on('open-timeline-window', () => {
    createTimelineWindow();
  });

  ipcMain.on('open-terminal-window', () => {
    createTerminalWindow();
  });

  ipcMain.on('open-settings-window', () => {
    createSettingsWindow();
  });

  safeConsole.log('✅ Window management IPC handlers registered');
}

/**
 * Get all window references
 */
export function getAllWindows() {
  return {
    kanbanWindow,
    agentSystemWindow,
    editorWindow,
    explorerWindow,
    chatWindow,
    timelineWindow,
    terminalWindow,
    settingsWindow
  };
}

/**
 * Close all secondary windows
 */
export function closeAllSecondaryWindows(): void {
  const windows = getAllWindows();
  Object.entries(windows).forEach(([name, window]) => {
    if (window && !window.isDestroyed()) {
      window.close();
    }
  });
}
