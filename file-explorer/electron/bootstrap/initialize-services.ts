/**
 * Service Initialization - Background Service Setup
 * 
 * Extracted from main.ts to handle service initialization with proper sequencing and error handling.
 */

import { BoardStateService } from '../services/board-state-service';
import { AgentStateService } from '../services/agent-state-service';
import { LLMService } from '../services/llm-service';
import { MCPService } from '../services/mcp-service';
import { getClaudeTaskmasterElectronService } from '../services/claude-taskmaster-service';
import { TerminalSessionManager } from '../services/TerminalSessionManager';
import { debugConfig } from './main-constants';

// ✅ Safe console for logging
const safeConsole = {
  log: (...args: any[]) => {
    try {
      console.log(...args);
    } catch (error) {
      // Silently ignore write errors
    }
  },
  error: (...args: any[]) => {
    try {
      console.error(...args);
    } catch (error) {
      // Silently ignore write errors
    }
  },
  warn: (...args: any[]) => {
    try {
      console.warn(...args);
    } catch (error) {
      // Silently ignore write errors
    }
  }
};

// ✅ Service instances
let boardStateService: BoardStateService | null = null;
let agentStateService: AgentStateService | null = null;
let llmService: LLMService | null = null;
let mcpService: MCPService | null = null;
let terminalSessionManager: TerminalSessionManager | null = null;

// ✅ Service initialization result interface
interface ServiceInitResult {
  name: string;
  success: boolean;
  error?: string;
  duration: number;
}

/**
 * Initialize all Electron services with proper sequencing and error handling
 */
export async function initializeServices(): Promise<void> {
  const startTime = Date.now();
  safeConsole.log('🔄 Initializing Electron services...');

  if (debugConfig.enableServiceLogging) {
    safeConsole.log('🔍 Service initialization debug logging enabled');
  }

  try {
    const serviceInitResults: ServiceInitResult[] = [];

    // Initialize BoardStateService
    if (!boardStateService) {
      const serviceStart = Date.now();
      try {
        if (debugConfig.enableServiceLogging) {
          safeConsole.log('🔄 Initializing BoardStateService...');
        }
        boardStateService = new BoardStateService();
        const duration = Date.now() - serviceStart;
        serviceInitResults.push({ name: 'BoardStateService', success: true, duration });
        safeConsole.log(`✅ BoardStateService initialized (${duration}ms)`);
      } catch (error) {
        const duration = Date.now() - serviceStart;
        const errorMsg = error instanceof Error ? error.message : 'Unknown error';
        serviceInitResults.push({ name: 'BoardStateService', success: false, error: errorMsg, duration });
        safeConsole.error(`❌ BoardStateService failed to initialize (${duration}ms):`, error);
        if (debugConfig.strictMode) throw error;
      }
    }

    // Initialize AgentStateService
    if (!agentStateService) {
      const serviceStart = Date.now();
      try {
        if (debugConfig.enableServiceLogging) {
          safeConsole.log('🔄 Initializing AgentStateService...');
        }
        agentStateService = new AgentStateService();
        const duration = Date.now() - serviceStart;
        serviceInitResults.push({ name: 'AgentStateService', success: true, duration });
        safeConsole.log(`✅ AgentStateService initialized (${duration}ms)`);
      } catch (error) {
        const duration = Date.now() - serviceStart;
        const errorMsg = error instanceof Error ? error.message : 'Unknown error';
        serviceInitResults.push({ name: 'AgentStateService', success: false, error: errorMsg, duration });
        safeConsole.error(`❌ AgentStateService failed to initialize (${duration}ms):`, error);
        if (debugConfig.strictMode) throw error;
      }
    }

    // Initialize LLMService
    if (!llmService) {
      const serviceStart = Date.now();
      try {
        if (debugConfig.enableServiceLogging) {
          safeConsole.log('🔄 Initializing LLMService...');
        }
        llmService = new LLMService();
        const duration = Date.now() - serviceStart;
        serviceInitResults.push({ name: 'LLMService', success: true, duration });
        safeConsole.log(`✅ LLMService initialized (${duration}ms)`);
      } catch (error) {
        const duration = Date.now() - serviceStart;
        const errorMsg = error instanceof Error ? error.message : 'Unknown error';
        serviceInitResults.push({ name: 'LLMService', success: false, error: errorMsg, duration });
        safeConsole.error(`❌ LLMService failed to initialize (${duration}ms):`, error);
        if (debugConfig.strictMode) throw error;
      }
    }

    // Initialize MCPService
    if (!mcpService) {
      const serviceStart = Date.now();
      try {
        if (debugConfig.enableServiceLogging) {
          safeConsole.log('🔄 Initializing MCPService...');
        }
        mcpService = new MCPService();
        const duration = Date.now() - serviceStart;
        serviceInitResults.push({ name: 'MCPService', success: true, duration });
        safeConsole.log(`✅ MCPService initialized (${duration}ms)`);
      } catch (error) {
        const duration = Date.now() - serviceStart;
        const errorMsg = error instanceof Error ? error.message : 'Unknown error';
        serviceInitResults.push({ name: 'MCPService', success: false, error: errorMsg, duration });
        safeConsole.error(`❌ MCPService failed to initialize (${duration}ms):`, error);
        if (debugConfig.strictMode) throw error;
      }
    }

    // Initialize Claude Taskmaster service (singleton)
    const taskmasterStart = Date.now();
    try {
      if (debugConfig.enableServiceLogging) {
        safeConsole.log('🔄 Initializing Claude Taskmaster service...');
      }
      getClaudeTaskmasterElectronService();
      const duration = Date.now() - taskmasterStart;
      serviceInitResults.push({ name: 'ClaudeTaskmasterService', success: true, duration });
      safeConsole.log(`✅ Claude Taskmaster service initialized (${duration}ms)`);
    } catch (error) {
      const duration = Date.now() - taskmasterStart;
      const errorMsg = error instanceof Error ? error.message : 'Unknown error';
      serviceInitResults.push({ name: 'ClaudeTaskmasterService', success: false, error: errorMsg, duration });
      safeConsole.error(`❌ Claude Taskmaster service failed to initialize (${duration}ms):`, error);
      if (debugConfig.strictMode) throw error;
    }

    // Initialize Terminal Session Manager
    if (!terminalSessionManager) {
      const serviceStart = Date.now();
      try {
        if (debugConfig.enableServiceLogging) {
          safeConsole.log('🔄 Initializing TerminalSessionManager...');
        }
        terminalSessionManager = new TerminalSessionManager();
        const duration = Date.now() - serviceStart;
        serviceInitResults.push({ name: 'TerminalSessionManager', success: true, duration });
        safeConsole.log(`✅ TerminalSessionManager initialized (${duration}ms)`);
      } catch (error) {
        const duration = Date.now() - serviceStart;
        const errorMsg = error instanceof Error ? error.message : 'Unknown error';
        serviceInitResults.push({ name: 'TerminalSessionManager', success: false, error: errorMsg, duration });
        safeConsole.error(`❌ TerminalSessionManager failed to initialize (${duration}ms):`, error);
        if (debugConfig.strictMode) throw error;
      }
    }

    const elapsed = Date.now() - startTime;
    const successCount = serviceInitResults.filter(r => r.success).length;
    const totalCount = serviceInitResults.length;

    if (debugConfig.enableServiceLogging || successCount < totalCount) {
      safeConsole.log('📊 Service initialization summary:', {
        total: totalCount,
        successful: successCount,
        failed: totalCount - successCount,
        totalTime: elapsed,
        results: serviceInitResults
      });
    }

    if (successCount === totalCount) {
      safeConsole.log(`✅ All ${totalCount} services initialized successfully in ${elapsed}ms`);
    } else {
      safeConsole.warn(`⚠️ ${successCount}/${totalCount} services initialized successfully in ${elapsed}ms`);
      if (debugConfig.strictMode) {
        throw new Error(`Service initialization incomplete: ${totalCount - successCount} services failed`);
      }
    }

  } catch (error) {
    const elapsed = Date.now() - startTime;
    safeConsole.error(`❌ Service initialization failed after ${elapsed}ms:`, error);
    throw error;
  }
}

/**
 * Get service instances
 */
export function getServices() {
  return {
    boardStateService,
    agentStateService,
    llmService,
    mcpService,
    terminalSessionManager
  };
}

/**
 * Cleanup all services
 */
export function cleanupServices(): void {
  safeConsole.log('🧹 Cleaning up services...');

  try {
    if (terminalSessionManager) {
      terminalSessionManager.destroyAllSessions();
    }
  } catch (error) {
    safeConsole.error('Error cleaning up terminal session manager:', error);
  }

  // Reset service instances
  boardStateService = null;
  agentStateService = null;
  llmService = null;
  mcpService = null;
  terminalSessionManager = null;

  safeConsole.log('✅ Service cleanup completed');
}
