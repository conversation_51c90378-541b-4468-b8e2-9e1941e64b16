import { app, BrowserWindow, shell, ipc<PERSON>ain, dialog } from 'electron';
import path from 'path';
import fs from 'fs';
import os from 'os';
import crypto from 'crypto'; // ✅ Import crypto for UUID generation
import { spawn } from 'node-pty'; // ✅ Import node-pty for terminal functionality
import { BoardStateService } from './services/board-state-service';
import { AgentStateService } from './services/agent-state-service';
import { LLMService } from './services/llm-service';
import { MCPService } from './services/mcp-service';
import { getClaudeTaskmasterElectronService } from './services/claude-taskmaster-service';
import { TerminalSessionManager } from './services/TerminalSessionManager';

// ✅ CRITICAL DEBUG: Log immediately to see if script is running
console.log('🚀 Main process starting...');
console.log('Process argv:', process.argv);
console.log('Process cwd:', process.cwd());
console.log('__dirname:', __dirname);

// ✅ CRITICAL DEBUG: File-based logging to debug Electron startup
const logFile = path.join(process.cwd(), 'electron-debug.log');
const logToFile = (message: string) => {
  try {
    fs.appendFileSync(logFile, `${new Date().toISOString()} - ${message}\n`);
  } catch (error) {
    // Ignore file write errors
  }
};

logToFile('🚀 Main process starting...');
logToFile(`Process argv: ${JSON.stringify(process.argv)}`);
logToFile(`Process cwd: ${process.cwd()}`);
logToFile(`__dirname: ${__dirname}`);

// ✅ CRITICAL FIX: Safe import of electron-is-dev to prevent crashes
let isDev = false;
try {
  isDev = require('electron-is-dev');
} catch (error) {
  // Fallback: check if we're in development mode manually
  isDev = process.env.NODE_ENV === 'development' || process.argv.includes('--dev');
  console.log('electron-is-dev failed, using fallback:', isDev);
}

// ✅ RESTORATION: Debug configuration with strict mode
const debugConfig = {
  strictMode: process.argv.includes('--strict') || process.env.DEBUG_STRICT === 'true',
  enableServiceLogging: isDev || process.argv.includes('--debug-services'),
  enableIPCLogging: isDev || process.argv.includes('--debug-ipc')
};

// Note: safeConsole will be defined below
if (debugConfig.strictMode) {
  console.log('🔍 Debug strict mode enabled - enhanced error detection and logging');
}

// Create a safer console.log that doesn't throw when streams are closed
const safeConsole = {
  log: (...args: any[]) => {
    try {
      console.log(...args);
    } catch (error) {
      // Silently ignore write errors
    }
  },
  error: (...args: any[]) => {
    try {
      console.error(...args);
    } catch (error) {
      // Silently ignore write errors
    }
  },
  warn: (...args: any[]) => {
    try {
      console.warn(...args);
    } catch (error) {
      // Silently ignore write errors
    }
  }
};

// ✅ Debug mode already logged above with console.log

let mainWindow: BrowserWindow | null;

// ✅ RESTORATION: Service instances for full functionality
let kanbanWindow: BrowserWindow | null = null;
let agentSystemWindow: BrowserWindow | null = null;
let editorWindow: BrowserWindow | null = null;
let explorerWindow: BrowserWindow | null = null;
let chatWindow: BrowserWindow | null = null;
let timelineWindow: BrowserWindow | null = null;
let terminalWindow: BrowserWindow | null = null;
let settingsWindow: BrowserWindow | null = null;

// ✅ RESTORATION: Service instances
let boardStateService: BoardStateService | null = null;
let agentStateService: AgentStateService | null = null;
let llmService: LLMService | null = null;
let mcpService: MCPService | null = null;

// ✅ Terminal session storage
const terminals: { [id: string]: any } = {};
const userTerminalSessions = new Map<string, {
  ptyProcess: any;
  shell: string;
  createdAt: number;
}>();

// ✅ Terminal Session Manager for agent sessions
const terminalSessionManager = new TerminalSessionManager();

function createWindow() {
  logToFile('🪟 Creating main window...');
  safeConsole.log('🪟 Creating main window...');

  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: false, // Best practice for security
      contextIsolation: true, // Best practice for security
      preload: path.join(__dirname, 'preload.js'), // ✅ FIXED: Re-enable preload script for IPC
      devTools: true, // Always enable DevTools for debugging
      webSecurity: true, // Enable web security
    },
    icon: path.join(__dirname, '../public/placeholder-logo.svg') // Adjust path if needed
  });

  // Set a default title
  mainWindow.setTitle('CodeFusion - Modern Code Editor');

  // Determine the correct path to load
  const appPath = app.getAppPath();
  safeConsole.log('App path:', appPath);

  if (isDev || process.argv.includes('--dev')) {
    // In development, load from the Next.js dev server
    safeConsole.log('Loading from dev server');
    
    // Add error handling for URL loading
    mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription, validatedURL) => {
      logToFile(`Failed to load URL: ${validatedURL}, Error: ${errorCode} ${errorDescription}`);
      safeConsole.error('Failed to load URL:', validatedURL, 'Error:', errorCode, errorDescription);
    });

    mainWindow.webContents.on('did-finish-load', () => {
      logToFile('Successfully loaded main window');
      safeConsole.log('Successfully loaded main window');
    });

    mainWindow.webContents.on('dom-ready', () => {
      logToFile('DOM ready for main window');
      safeConsole.log('DOM ready for main window');
    });
    
    logToFile('Loading URL: http://localhost:4444');
    mainWindow.loadURL('http://localhost:4444');
    mainWindow.webContents.openDevTools();
    logToFile('Window creation completed');
  } else {
    // In production, load the statically exported Next.js app
    try {
      // Use a direct path to the index.html file
      const indexFile = path.join(__dirname, '../out/index.html');
      safeConsole.log('Checking for index.html at:', indexFile);
      
      mainWindow.loadFile(indexFile);
      
      // Only open DevTools in development or when explicitly requested
      if (process.argv.includes('--devtools')) {
        mainWindow.webContents.openDevTools();
      }
    } catch (error) {
      safeConsole.error('Error loading index.html:', error);
      
      // Show error in window
      mainWindow.loadURL(`data:text/html,<html><body><h1>Error</h1><p>${error}</p></body></html>`);
      
      // Always open DevTools when there's an error
      mainWindow.webContents.openDevTools();
    }
  }

  // Open external links in the default browser
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url);
    return { action: 'deny' };
  });

  // ✅ RESTORATION: Register main window with services
  if (boardStateService && mainWindow) {
    boardStateService.registerWindow(mainWindow);
  }
  if (agentStateService && mainWindow) {
    agentStateService.registerWindow(mainWindow);
  }

  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

// ✅ RESTORATION: Window creation functions
function createKanbanWindow(boardId: string) {
  if (kanbanWindow) {
    kanbanWindow.focus();
    return;
  }

  kanbanWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    frame: true,
    show: false,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js'),
      devTools: true,
      webSecurity: true,
    },
    title: 'Kanban Board - CodeFusion',
    icon: path.join(__dirname, '../public/placeholder-logo.svg')
  });

  const kanbanUrl = isDev || process.argv.includes('--dev')
    ? `http://localhost:4444/kanban/${boardId}`
    : path.join(__dirname, `../out/kanban/${boardId}/index.html`);

  kanbanWindow.loadURL(kanbanUrl);

  kanbanWindow.once('ready-to-show', () => {
    kanbanWindow?.show();
    if (process.argv.includes('--devtools')) {
      kanbanWindow?.webContents.openDevTools();
    }
  });

  kanbanWindow.on('closed', () => {
    kanbanWindow = null;
  });

  // ✅ Register with services
  if (boardStateService) {
    boardStateService.registerWindow(kanbanWindow);
  }
  if (agentStateService) {
    agentStateService.registerWindow(kanbanWindow);
  }
}

function createAgentSystemWindow() {
  if (agentSystemWindow) {
    agentSystemWindow.focus();
    return;
  }

  agentSystemWindow = new BrowserWindow({
    width: 1000,
    height: 700,
    minWidth: 600,
    minHeight: 400,
    frame: true,
    show: false,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js'),
      devTools: true,
      webSecurity: true,
    },
    title: 'Agent System - CodeFusion',
    icon: path.join(__dirname, '../public/placeholder-logo.svg')
  });

  const agentUrl = isDev || process.argv.includes('--dev')
    ? 'http://localhost:4444/agent-system'
    : path.join(__dirname, '../out/agent-system/index.html');

  agentSystemWindow.loadURL(agentUrl);

  agentSystemWindow.once('ready-to-show', () => {
    agentSystemWindow?.show();
    if (process.argv.includes('--devtools')) {
      agentSystemWindow?.webContents.openDevTools();
    }
  });

  agentSystemWindow.on('closed', () => {
    agentSystemWindow = null;
  });

  // ✅ Register with services
  if (agentStateService) {
    agentStateService.registerWindow(agentSystemWindow);
  }
}

function createChatWindow() {
  if (chatWindow) {
    chatWindow.focus();
    return;
  }

  chatWindow = new BrowserWindow({
    width: 400,
    height: 600,
    minWidth: 300,
    minHeight: 400,
    frame: true,
    show: false,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js'),
      devTools: true,
      webSecurity: true,
    },
    title: 'AI Chat - CodeFusion',
    icon: path.join(__dirname, '../public/placeholder-logo.svg')
  });

  const chatUrl = isDev || process.argv.includes('--dev')
    ? 'http://localhost:4444/chat'
    : path.join(__dirname, '../out/chat/index.html');

  chatWindow.loadURL(chatUrl);

  chatWindow.once('ready-to-show', () => {
    chatWindow?.show();
    if (process.argv.includes('--devtools')) {
      chatWindow?.webContents.openDevTools();
    }
  });

  chatWindow.on('closed', () => {
    chatWindow = null;
  });
}

function createEditorWindow(filePath?: string) {
  if (editorWindow) {
    editorWindow.focus();
    return;
  }

  editorWindow = new BrowserWindow({
    width: 1000,
    height: 700,
    minWidth: 600,
    minHeight: 400,
    frame: true,
    show: false,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js'),
      devTools: true,
      webSecurity: true,
    },
    title: 'Editor - CodeFusion',
    icon: path.join(__dirname, '../public/placeholder-logo.svg')
  });

  const editorUrl = isDev || process.argv.includes('--dev')
    ? `http://localhost:4444/editor${filePath ? `?file=${encodeURIComponent(filePath)}` : ''}`
    : path.join(__dirname, '../out/editor/index.html');

  editorWindow.loadURL(editorUrl);

  editorWindow.once('ready-to-show', () => {
    editorWindow?.show();
    if (process.argv.includes('--devtools')) {
      editorWindow?.webContents.openDevTools();
    }
  });

  editorWindow.on('closed', () => {
    editorWindow = null;
  });
}

function createExplorerWindow() {
  if (explorerWindow) {
    explorerWindow.focus();
    return;
  }

  explorerWindow = new BrowserWindow({
    width: 400,
    height: 600,
    minWidth: 300,
    minHeight: 400,
    frame: true,
    show: false,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js'),
      devTools: true,
      webSecurity: true,
    },
    title: 'Explorer - CodeFusion',
    icon: path.join(__dirname, '../public/placeholder-logo.svg')
  });

  const explorerUrl = isDev || process.argv.includes('--dev')
    ? 'http://localhost:4444/explorer'
    : path.join(__dirname, '../out/explorer/index.html');

  explorerWindow.loadURL(explorerUrl);

  explorerWindow.once('ready-to-show', () => {
    explorerWindow?.show();
    if (process.argv.includes('--devtools')) {
      explorerWindow?.webContents.openDevTools();
    }
  });

  explorerWindow.on('closed', () => {
    explorerWindow = null;
  });
}

function createTimelineWindow() {
  if (timelineWindow) {
    timelineWindow.focus();
    return;
  }

  timelineWindow = new BrowserWindow({
    width: 800,
    height: 600,
    minWidth: 600,
    minHeight: 400,
    frame: true,
    show: false,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js'),
      devTools: true,
      webSecurity: true,
    },
    title: 'Timeline - CodeFusion',
    icon: path.join(__dirname, '../public/placeholder-logo.svg')
  });

  const timelineUrl = isDev || process.argv.includes('--dev')
    ? 'http://localhost:4444/timeline'
    : path.join(__dirname, '../out/timeline/index.html');

  timelineWindow.loadURL(timelineUrl);

  timelineWindow.once('ready-to-show', () => {
    timelineWindow?.show();
    if (process.argv.includes('--devtools')) {
      timelineWindow?.webContents.openDevTools();
    }
  });

  timelineWindow.on('closed', () => {
    timelineWindow = null;
  });
}

function createTerminalWindow() {
  if (terminalWindow) {
    terminalWindow.focus();
    return;
  }

  terminalWindow = new BrowserWindow({
    width: 800,
    height: 600,
    minWidth: 600,
    minHeight: 400,
    frame: true,
    show: false,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js'),
      devTools: true,
      webSecurity: true,
    },
    title: 'Terminal - CodeFusion',
    icon: path.join(__dirname, '../public/placeholder-logo.svg')
  });

  const terminalUrl = isDev || process.argv.includes('--dev')
    ? 'http://localhost:4444/terminal'
    : path.join(__dirname, '../out/terminal/index.html');

  terminalWindow.loadURL(terminalUrl);

  terminalWindow.once('ready-to-show', () => {
    terminalWindow?.show();
    if (process.argv.includes('--devtools')) {
      terminalWindow?.webContents.openDevTools();
    }
  });

  terminalWindow.on('closed', () => {
    terminalWindow = null;
  });
}

function createSettingsWindow() {
  if (settingsWindow) {
    settingsWindow.focus();
    return;
  }

  settingsWindow = new BrowserWindow({
    width: 800,
    height: 600,
    minWidth: 600,
    minHeight: 400,
    frame: true,
    show: false,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js'),
      devTools: true,
      webSecurity: true,
    },
    title: 'Settings - CodeFusion',
    icon: path.join(__dirname, '../public/placeholder-logo.svg')
  });

  const settingsUrl = isDev || process.argv.includes('--dev')
    ? 'http://localhost:4444/settings'
    : path.join(__dirname, '../out/settings/index.html');

  settingsWindow.loadURL(settingsUrl);

  settingsWindow.once('ready-to-show', () => {
    settingsWindow?.show();
    if (process.argv.includes('--devtools')) {
      settingsWindow?.webContents.openDevTools();
    }
  });

  settingsWindow.on('closed', () => {
    settingsWindow = null;
  });
}

// ✅ RESTORATION: Window management IPC handlers
function registerWindowIPCHandlers() {
  // Register IPC handlers for window management
  ipcMain.on('open-kanban-window', (event, boardId: string) => {
    createKanbanWindow(boardId);
  });

  ipcMain.on('open-agent-system-window', () => {
    createAgentSystemWindow();
  });

  ipcMain.on('open-editor-window', (event, filePath?: string) => {
    createEditorWindow(filePath);
  });

  ipcMain.on('open-explorer-window', () => {
    createExplorerWindow();
  });

  ipcMain.on('open-chat-window', () => {
    createChatWindow();
  });

  ipcMain.on('open-timeline-window', () => {
    createTimelineWindow();
  });

  ipcMain.on('open-terminal-window', () => {
    createTerminalWindow();
  });

  ipcMain.on('open-settings-window', () => {
    createSettingsWindow();
  });

  safeConsole.log('✅ Window management IPC handlers registered');
}

// ✅ RESTORATION: Service initialization with proper sequencing and debug logging
async function initializeServices(): Promise<void> {
  const startTime = Date.now();
  safeConsole.log('🔄 Initializing Electron services...');

  if (debugConfig.enableServiceLogging) {
    safeConsole.log('🔍 Service initialization debug logging enabled');
  }

  try {
    // ✅ Initialize services in proper order with enhanced error handling
    const serviceInitResults: { name: string; success: boolean; error?: string; duration: number }[] = [];

    // Initialize BoardStateService
    if (!boardStateService) {
      const serviceStart = Date.now();
      try {
        if (debugConfig.enableServiceLogging) {
          safeConsole.log('🔄 Initializing BoardStateService...');
        }
        boardStateService = new BoardStateService();
        const duration = Date.now() - serviceStart;
        serviceInitResults.push({ name: 'BoardStateService', success: true, duration });
        safeConsole.log(`✅ BoardStateService initialized (${duration}ms)`);
      } catch (error) {
        const duration = Date.now() - serviceStart;
        const errorMsg = error instanceof Error ? error.message : 'Unknown error';
        serviceInitResults.push({ name: 'BoardStateService', success: false, error: errorMsg, duration });
        safeConsole.error(`❌ BoardStateService failed to initialize (${duration}ms):`, error);
        if (debugConfig.strictMode) throw error;
      }
    }

    // Initialize AgentStateService
    if (!agentStateService) {
      const serviceStart = Date.now();
      try {
        if (debugConfig.enableServiceLogging) {
          safeConsole.log('🔄 Initializing AgentStateService...');
        }
        agentStateService = new AgentStateService();
        const duration = Date.now() - serviceStart;
        serviceInitResults.push({ name: 'AgentStateService', success: true, duration });
        safeConsole.log(`✅ AgentStateService initialized (${duration}ms)`);
      } catch (error) {
        const duration = Date.now() - serviceStart;
        const errorMsg = error instanceof Error ? error.message : 'Unknown error';
        serviceInitResults.push({ name: 'AgentStateService', success: false, error: errorMsg, duration });
        safeConsole.error(`❌ AgentStateService failed to initialize (${duration}ms):`, error);
        if (debugConfig.strictMode) throw error;
      }
    }

    // Initialize LLMService
    if (!llmService) {
      const serviceStart = Date.now();
      try {
        if (debugConfig.enableServiceLogging) {
          safeConsole.log('🔄 Initializing LLMService...');
        }
        llmService = new LLMService();
        const duration = Date.now() - serviceStart;
        serviceInitResults.push({ name: 'LLMService', success: true, duration });
        safeConsole.log(`✅ LLMService initialized (${duration}ms)`);
      } catch (error) {
        const duration = Date.now() - serviceStart;
        const errorMsg = error instanceof Error ? error.message : 'Unknown error';
        serviceInitResults.push({ name: 'LLMService', success: false, error: errorMsg, duration });
        safeConsole.error(`❌ LLMService failed to initialize (${duration}ms):`, error);
        if (debugConfig.strictMode) throw error;
      }
    }

    // Initialize MCPService
    if (!mcpService) {
      const serviceStart = Date.now();
      try {
        if (debugConfig.enableServiceLogging) {
          safeConsole.log('🔄 Initializing MCPService...');
        }
        mcpService = new MCPService();
        const duration = Date.now() - serviceStart;
        serviceInitResults.push({ name: 'MCPService', success: true, duration });
        safeConsole.log(`✅ MCPService initialized (${duration}ms)`);
      } catch (error) {
        const duration = Date.now() - serviceStart;
        const errorMsg = error instanceof Error ? error.message : 'Unknown error';
        serviceInitResults.push({ name: 'MCPService', success: false, error: errorMsg, duration });
        safeConsole.error(`❌ MCPService failed to initialize (${duration}ms):`, error);
        if (debugConfig.strictMode) throw error;
      }
    }

    // Initialize Claude Taskmaster service (singleton)
    const taskmasterStart = Date.now();
    try {
      if (debugConfig.enableServiceLogging) {
        safeConsole.log('🔄 Initializing Claude Taskmaster service...');
      }
      getClaudeTaskmasterElectronService();
      const duration = Date.now() - taskmasterStart;
      serviceInitResults.push({ name: 'ClaudeTaskmasterService', success: true, duration });
      safeConsole.log(`✅ Claude Taskmaster service initialized (${duration}ms)`);
    } catch (error) {
      const duration = Date.now() - taskmasterStart;
      const errorMsg = error instanceof Error ? error.message : 'Unknown error';
      serviceInitResults.push({ name: 'ClaudeTaskmasterService', success: false, error: errorMsg, duration });
      safeConsole.error(`❌ Claude Taskmaster service failed to initialize (${duration}ms):`, error);
      if (debugConfig.strictMode) throw error;
    }

    const elapsed = Date.now() - startTime;
    const successCount = serviceInitResults.filter(r => r.success).length;
    const totalCount = serviceInitResults.length;

    if (debugConfig.enableServiceLogging || successCount < totalCount) {
      safeConsole.log('📊 Service initialization summary:', {
        total: totalCount,
        successful: successCount,
        failed: totalCount - successCount,
        totalTime: elapsed,
        results: serviceInitResults
      });
    }

    if (successCount === totalCount) {
      safeConsole.log(`✅ All ${totalCount} services initialized successfully in ${elapsed}ms`);
    } else {
      safeConsole.warn(`⚠️ ${successCount}/${totalCount} services initialized successfully in ${elapsed}ms`);
      if (debugConfig.strictMode) {
        throw new Error(`Service initialization incomplete: ${totalCount - successCount} services failed`);
      }
    }

  } catch (error) {
    const elapsed = Date.now() - startTime;
    safeConsole.error(`❌ Service initialization failed after ${elapsed}ms:`, error);
    throw error;
  }
}

// ✅ TEMPORARY: Comment out macOS setup to test
// Fix macOS secure coding warning by disabling state restoration
// if (process.platform === 'darwin') {
//   app.setAboutPanelOptions({
//     applicationName: 'CodeFusion',
//     applicationVersion: '1.0.0',
//     copyright: 'Copyright © 2024 CodeFusion'
//   });
//   // Disable automatic state restoration to avoid the secure coding warning
//   app.commandLine.appendSwitch('disable-features', 'VizDisplayCompositor');
// }

app.on('ready', async () => {
  logToFile('🚀 Electron app ready event fired');
  safeConsole.log('🚀 Electron app ready event fired');

  // ✅ RESTORATION: Initialize all services before creating windows
  try {
    await initializeServices();
    logToFile('✅ All services initialized successfully');
    safeConsole.log('✅ All services initialized successfully');
  } catch (error) {
    logToFile(`❌ Service initialization failed: ${error}`);
    safeConsole.error('❌ Service initialization failed:', error);
    // Continue anyway to allow basic functionality
  }

  logToFile('Creating main window');
  safeConsole.log('Creating main window');
  createWindow();

  // ✅ RESTORATION: Register IPC handlers for window management
  registerWindowIPCHandlers();
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (mainWindow === null) {
    createWindow();
  }
});

// ✅ Cleanup terminal sessions on app quit
app.on('before-quit', () => {
  safeConsole.log('🧹 Cleaning up terminal sessions before quit...');

  // Clean up regular terminals
  Object.keys(terminals).forEach(id => {
    try {
      terminals[id].kill();
      delete terminals[id];
    } catch (error) {
      safeConsole.error(`Error cleaning up terminal ${id}:`, error);
    }
  });

  // Clean up user terminal sessions
  userTerminalSessions.forEach((session, sessionId) => {
    try {
      session.ptyProcess.kill();
    } catch (error) {
      safeConsole.error(`Error cleaning up user session ${sessionId}:`, error);
    }
  });
  userTerminalSessions.clear();

  // Clean up terminal session manager
  try {
    terminalSessionManager.destroyAllSessions();
  } catch (error) {
    safeConsole.error(`Error cleaning up terminal session manager:`, error);
  }

  safeConsole.log('✅ Terminal cleanup completed');
});

// ✅ CRITICAL FIX: Add essential IPC handlers for Create Project Wizard
// IPC handlers for file system operations
ipcMain.handle('select-folder', async () => {
  const result = await dialog.showOpenDialog(mainWindow!, {
    properties: ['openDirectory'],
    title: 'Select Project Folder'
  });

  if (!result.canceled && result.filePaths.length > 0) {
    const folderPath = result.filePaths[0];
    return {
      success: true,
      path: folderPath,
      name: path.basename(folderPath)
    };
  }

  return { success: false };
});

ipcMain.handle('read-directory', async (event, dirPath: string) => {
  try {
    if (!dirPath || typeof dirPath !== 'string') {
      return { success: false, error: 'Invalid directory path provided' };
    }

    if (!fs.existsSync(dirPath)) {
      return { success: false, error: `Directory does not exist: ${dirPath}` };
    }

    const stats = fs.statSync(dirPath);
    if (!stats.isDirectory()) {
      return { success: false, error: `Path is not a directory: ${dirPath}` };
    }

    const items = fs.readdirSync(dirPath);
    const itemsWithStats = items.map(item => {
      const itemPath = path.join(dirPath, item);
      const itemStats = fs.statSync(itemPath);
      return {
        name: item,
        path: itemPath,
        isDirectory: itemStats.isDirectory(),
        size: itemStats.size,
        modified: itemStats.mtime
      };
    });

    return { success: true, items: itemsWithStats };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error reading directory'
    };
  }
});

ipcMain.handle('read-file', async (event, filePath: string) => {
  try {
    if (!filePath || typeof filePath !== 'string') {
      return { success: false, error: 'Invalid file path provided' };
    }

    if (!fs.existsSync(filePath)) {
      return { success: false, error: `File does not exist: ${filePath}` };
    }

    const content = fs.readFileSync(filePath, 'utf8');
    return { success: true, content };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error reading file'
    };
  }
});

ipcMain.handle('save-file', async (event, filePath: string, content: string) => {
  try {
    if (!filePath || typeof filePath !== 'string') {
      return { success: false, error: 'Invalid file path provided' };
    }

    // Ensure directory exists
    const dir = path.dirname(filePath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    fs.writeFileSync(filePath, content, 'utf8');
    return { success: true };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error saving file'
    };
  }
});

ipcMain.handle('create-file', async (event, filePath: string, content: string = '') => {
  try {
    if (!filePath || typeof filePath !== 'string') {
      return { success: false, error: 'Invalid file path provided' };
    }

    // Check if file already exists
    if (fs.existsSync(filePath)) {
      return { success: false, error: 'File already exists' };
    }

    // Ensure directory exists
    const dir = path.dirname(filePath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    fs.writeFileSync(filePath, content, 'utf8');
    return { success: true };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error creating file'
    };
  }
});

ipcMain.handle('delete-file', async (event, filePath: string) => {
  try {
    if (!filePath || typeof filePath !== 'string') {
      return { success: false, error: 'Invalid file path provided' };
    }

    if (!fs.existsSync(filePath)) {
      return { success: false, error: `File does not exist: ${filePath}` };
    }

    fs.unlinkSync(filePath);
    return { success: true };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error deleting file'
    };
  }
});

ipcMain.handle('ensure-directory', async (event, dirPath: string) => {
  try {
    if (!dirPath || typeof dirPath !== 'string') {
      return { success: false, error: 'Invalid directory path provided' };
    }

    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
    }
    return { success: true };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error creating directory'
    };
  }
});

ipcMain.handle('append-file', async (event, filePath: string, content: string) => {
  try {
    if (!filePath || typeof filePath !== 'string') {
      return { success: false, error: 'Invalid file path provided' };
    }

    // Ensure directory exists
    const dir = path.dirname(filePath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    fs.appendFileSync(filePath, content, 'utf8');
    return { success: true };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error appending to file'
    };
  }
});

// ✅ RESTORATION: Board state IPC handlers with throttling
let boardStateThrottle: { [key: string]: { timer: NodeJS.Timeout | null; lastCall: number } } = {};
const BOARD_STATE_THROTTLE_MS = 300; // 300ms throttle for board state calls

// ✅ RESTORATION: Board state IPC handler with throttling
// Note: BoardStateService registers its own IPC handlers, so we don't need to duplicate them
// The throttling will be handled by the BoardIPCBridge on the client side

// ✅ RESTORATION: Agent state IPC handlers
// Note: AgentStateService registers its own IPC handlers, so we don't need to duplicate them

// ✅ Terminal PTY IPC Handlers
ipcMain.handle('terminal:create', (event, terminalSettings?: {
  shell?: string;
  cols?: number;
  rows?: number;
}) => {
  try {
    // Use settings or fallback to defaults
    const shell = terminalSettings?.shell || (os.platform() === 'win32' ? 'powershell.exe' : 'bash');
    const cols = terminalSettings?.cols || 80;
    const rows = terminalSettings?.rows || 30;

    // Generate unique terminal ID
    const terminalId = crypto.randomUUID();

    // Enhanced environment for better terminal experience
    const enhancedEnv = {
      ...process.env,
      TERM: 'xterm-256color',
      COLORTERM: 'truecolor',
      // Set up proper bash prompt with current directory and colors
      PS1: '\\[\\033[01;32m\\]\\u@\\h\\[\\033[00m\\]:\\[\\033[01;34m\\]\\w\\[\\033[00m\\]\\$ ',
      // Enable command history and completion
      HISTSIZE: '1000',
      HISTFILESIZE: '2000',
      HISTCONTROL: 'ignoredups:erasedups',
      // Set proper locale
      LANG: process.env.LANG || 'en_US.UTF-8',
      LC_ALL: process.env.LC_ALL || 'en_US.UTF-8',
    };

    // Shell-specific arguments for interactive experience
    let shellArgs: string[] = [];
    if (shell.includes('bash')) {
      shellArgs = ['--login', '-i'];
    } else if (shell.includes('zsh')) {
      shellArgs = ['--login', '-i'];
    }

    // Create PTY process
    const ptyProcess = spawn(shell, shellArgs, {
      name: 'xterm-256color',
      cols,
      rows,
      cwd: process.env.HOME || process.cwd(),
      env: enhancedEnv,
      encoding: 'utf8',
    });

    // Store terminal reference
    terminals[terminalId] = ptyProcess;

    safeConsole.log(`✅ Terminal created: ${terminalId} (shell: ${shell})`);

    // Return just the terminalId as expected by frontend
    return terminalId;
  } catch (error) {
    safeConsole.error(`❌ Terminal creation error:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
});

ipcMain.handle('terminal:resize', (event, { id, cols, rows }: { id: string; cols: number; rows: number }) => {
  try {
    const terminal = terminals[id];
    if (!terminal) {
      return { success: false, error: `Terminal not found: ${id}` };
    }

    terminal.resize(cols, rows);
    safeConsole.log(`✅ Terminal ${id} resized to ${cols}x${rows}`);

    return { success: true };
  } catch (error) {
    safeConsole.error(`❌ Terminal resize error:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
});

ipcMain.on('terminal:input', (event, { id, data }: { id: string; data: string }) => {
  try {
    const terminal = terminals[id];
    if (!terminal) {
      safeConsole.error(`❌ Terminal not found for input: ${id}`);
      return;
    }

    terminal.write(data);
    safeConsole.log(`✅ Terminal ${id} input: ${data.replace(/\r?\n/g, '\\n')}`);
  } catch (error) {
    safeConsole.error(`❌ Terminal input error:`, error);
  }
});

ipcMain.on('terminal:listen', (event, id) => {
  try {
    const terminal = terminals[id];
    if (!terminal) {
      safeConsole.error(`❌ Terminal not found for listening: ${id}`);
      return;
    }

    terminal.onData((data: string) => {
      event.sender.send(`terminal:data:${id}`, data);
    });

    terminal.onExit((e: { exitCode: number; signal?: number }) => {
      safeConsole.log(`✅ Terminal ${id} exited with code: ${e.exitCode}, signal: ${e.signal}`);
      event.sender.send(`terminal:exit:${id}`, { exitCode: e.exitCode, signal: e.signal });
      delete terminals[id];
    });

    safeConsole.log(`✅ Terminal ${id} listeners attached`);
  } catch (error) {
    safeConsole.error(`❌ Terminal listen error:`, error);
  }
});

ipcMain.on('terminal:dispose', (event, id) => {
  try {
    const terminal = terminals[id];
    if (!terminal) {
      safeConsole.error(`❌ Terminal not found for disposal: ${id}`);
      return;
    }

    terminal.kill();
    delete terminals[id];
    safeConsole.log(`✅ Terminal ${id} disposed`);
  } catch (error) {
    safeConsole.error(`❌ Terminal disposal error:`, error);
  }
});

// ✅ User Terminal Session Handlers
ipcMain.handle('terminal:create-user-session', (event, shell = '/bin/bash') => {
  try {
    const sessionId = crypto.randomUUID();
    const effectiveShell = shell || (os.platform() === 'win32' ? 'powershell.exe' : 'bash');

    // Enhanced environment for user terminal sessions
    const enhancedEnv = {
      ...process.env,
      TERM: 'xterm-256color',
      COLORTERM: 'truecolor',
      // Set up proper bash prompt with current directory and colors
      PS1: '\\[\\033[01;32m\\]\\u@\\h\\[\\033[00m\\]:\\[\\033[01;34m\\]\\w\\[\\033[00m\\]\\$ ',
      // Enable command history and completion
      HISTSIZE: '1000',
      HISTFILESIZE: '2000',
      HISTCONTROL: 'ignoredups:erasedups',
      // Set proper locale
      LANG: process.env.LANG || 'en_US.UTF-8',
      LC_ALL: process.env.LC_ALL || 'en_US.UTF-8',
    };

    // Shell-specific arguments for interactive experience
    let shellArgs: string[] = [];
    if (effectiveShell.includes('bash')) {
      shellArgs = ['--login', '-i'];
    } else if (effectiveShell.includes('zsh')) {
      shellArgs = ['--login', '-i'];
    }

    const ptyProcess = spawn(effectiveShell, shellArgs, {
      name: 'xterm-256color',
      cols: 80,
      rows: 24,
      cwd: process.env.HOME || process.cwd(),
      env: enhancedEnv,
      encoding: 'utf8',
    });

    userTerminalSessions.set(sessionId, {
      ptyProcess,
      shell: effectiveShell,
      createdAt: Date.now()
    });

    // Set up data listener
    ptyProcess.onData((data: string) => {
      event.sender.send(`terminal:user-session-data:${sessionId}`, data);
    });

    // Set up exit listener
    ptyProcess.onExit((e: { exitCode: number; signal?: number }) => {
      safeConsole.log(`✅ User terminal session ${sessionId} exited with code: ${e.exitCode}`);
      event.sender.send(`terminal:user-session-exit:${sessionId}`, { exitCode: e.exitCode, signal: e.signal });
      userTerminalSessions.delete(sessionId);
    });

    safeConsole.log(`✅ User terminal session created: ${sessionId} (shell: ${effectiveShell})`);

    return {
      success: true,
      sessionId,
      shell: effectiveShell
    };
  } catch (error) {
    safeConsole.error(`❌ User terminal session creation error:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
});

ipcMain.handle('terminal:dispose-user-session', (event, sessionId: string) => {
  try {
    const session = userTerminalSessions.get(sessionId);
    if (!session) {
      return { success: false, error: `Session not found: ${sessionId}` };
    }

    session.ptyProcess.kill();
    userTerminalSessions.delete(sessionId);
    safeConsole.log(`✅ User terminal session disposed: ${sessionId}`);

    return { success: true };
  } catch (error) {
    safeConsole.error(`❌ User terminal session disposal error:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
});

ipcMain.handle('terminal:write-user-session', (event, sessionId: string, data: string) => {
  try {
    const session = userTerminalSessions.get(sessionId);
    if (!session) {
      return { success: false, error: `Session not found: ${sessionId}` };
    }

    session.ptyProcess.write(data);
    return { success: true };
  } catch (error) {
    safeConsole.error(`❌ User terminal session write error:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
});

ipcMain.handle('terminal:resize-user-session', (event, sessionId: string, cols: number, rows: number) => {
  try {
    const session = userTerminalSessions.get(sessionId);
    if (!session) {
      return { success: false, error: `Session not found: ${sessionId}` };
    }

    session.ptyProcess.resize(cols, rows);
    return { success: true };
  } catch (error) {
    safeConsole.error(`❌ User terminal session resize error:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
});

ipcMain.handle('terminal:list-user-sessions', () => {
  try {
    const sessions = Array.from(userTerminalSessions.entries()).map(([sessionId, session]) => ({
      sessionId,
      shell: session.shell,
      createdAt: session.createdAt
    }));

    return { success: true, sessions };
  } catch (error) {
    safeConsole.error(`❌ User terminal session list error:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
});

// ✅ Agent Command Handler
ipcMain.handle('terminal:agent-command', async (event, { command, agentId, sessionId, timeout, workingDirectory, environment }: {
  command: string;
  agentId: string;
  sessionId?: string;
  timeout?: number;
  workingDirectory?: string;
  environment?: Record<string, string>;
}) => {
  try {
    const startTime = Date.now();
    const effectiveTimeout = timeout || 30000;
    const effectiveWorkingDirectory = workingDirectory || process.cwd();
    const effectiveSessionId = sessionId || crypto.randomUUID();

    // Enhanced environment for agent commands
    const enhancedEnv = {
      ...process.env,
      ...environment,
      TERM: 'xterm-256color',
      COLORTERM: 'truecolor',
    };

    // Create PTY process for command execution
    const shell = os.platform() === 'win32' ? 'powershell.exe' : 'bash';
    const ptyProcess = spawn(shell, [], {
      name: 'xterm-256color',
      cols: 80,
      rows: 30,
      cwd: effectiveWorkingDirectory,
      env: enhancedEnv,
      encoding: 'utf8',
    });

    let output = '';
    let hasExited = false;
    let exitCode = 0;

    // Set up data listener
    ptyProcess.onData((data: string) => {
      output += data;
    });

    // Set up exit listener
    ptyProcess.onExit((e: { exitCode: number; signal?: number }) => {
      hasExited = true;
      exitCode = e.exitCode;
    });

    // Send command to PTY
    ptyProcess.write(command + '\n');

    // Wait for command completion or timeout
    const waitForCompletion = () => {
      return new Promise<void>((resolve) => {
        const checkInterval = setInterval(() => {
          if (hasExited || Date.now() - startTime > effectiveTimeout) {
            clearInterval(checkInterval);
            resolve();
          }
        }, 100);
      });
    };

    await waitForCompletion();

    // Clean up PTY process
    if (!hasExited) {
      ptyProcess.kill();
    }

    const executionTime = Date.now() - startTime;
    const success = exitCode === 0 && !output.includes('command not found') && !output.includes('error');

    safeConsole.log(`✅ Agent command executed: ${command} (agent: ${agentId}, time: ${executionTime}ms, success: ${success})`);

    return {
      success,
      output: output.trim(),
      exitCode,
      executionTime,
      sessionId: effectiveSessionId,
      agentId,
      command
    };
  } catch (error) {
    safeConsole.error(`❌ Agent command error:`, error);
    return {
      success: false,
      output: '',
      error: error instanceof Error ? error.message : 'Unknown error',
      exitCode: -1,
      executionTime: 0,
      sessionId: sessionId || crypto.randomUUID(),
      agentId,
      command
    };
  }
});

// ✅ TerminalSessionManager IPC Handlers
ipcMain.handle('terminal:create-session', async (event, sessionId: string, agentId: string, options?: {
  shell?: string;
  workingDirectory?: string;
  environment?: Record<string, string>;
  cols?: number;
  rows?: number;
}) => {
  try {
    const ptyProcess = terminalSessionManager.createSession(sessionId, agentId, options);

    // Set up real-time data and exit listeners
    ptyProcess.onData((data: string) => {
      event.sender.send(`terminal:session-data:${sessionId}`, data);
    });

    ptyProcess.onExit((e: { exitCode: number; signal?: number }) => {
      safeConsole.log(`✅ Terminal session ${sessionId} exited with code: ${e.exitCode}`);
      event.sender.send(`terminal:session-exit:${sessionId}`, { exitCode: e.exitCode, signal: e.signal });
    });

    return { success: true, sessionId, agentId };
  } catch (error) {
    safeConsole.error(`❌ Terminal session creation error:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
});

ipcMain.handle('terminal:write-session', async (event, sessionId: string, data: string) => {
  try {
    const result = terminalSessionManager.writeToSession(sessionId, data);
    return { success: true, result };
  } catch (error) {
    safeConsole.error(`❌ Terminal session write error:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
});

ipcMain.handle('terminal:destroy-session', async (event, sessionId: string) => {
  try {
    const result = terminalSessionManager.destroySession(sessionId);
    return { success: true, result };
  } catch (error) {
    safeConsole.error(`❌ Terminal session destroy error:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
});

ipcMain.handle('terminal:list-sessions', async (event, agentId?: string) => {
  try {
    const allSessions = terminalSessionManager.listSessions();
    const sessions = agentId
      ? allSessions.filter(session => session.agentId === agentId)
      : allSessions;
    return { success: true, sessions };
  } catch (error) {
    safeConsole.error(`❌ Terminal session list error:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
});

ipcMain.handle('terminal:get-session-info', async (event, sessionId: string) => {
  try {
    const sessionInfo = terminalSessionManager.getSessionInfo(sessionId);
    return { success: true, sessionInfo };
  } catch (error) {
    safeConsole.error(`❌ Terminal session info error:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
});

ipcMain.handle('terminal:resize-session', async (event, sessionId: string, cols: number, rows: number) => {
  try {
    const result = terminalSessionManager.resizeSession(sessionId, cols, rows);
    return { success: true, result };
  } catch (error) {
    safeConsole.error(`❌ Terminal session resize error:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
});

ipcMain.handle('terminal:get-session-stats', async () => {
  try {
    const stats = terminalSessionManager.getStats();
    return { success: true, stats };
  } catch (error) {
    safeConsole.error(`❌ Terminal session stats error:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
});

ipcMain.handle('terminal:cleanup-agent-sessions', async (event, agentId: string) => {
  try {
    const result = terminalSessionManager.destroyAgentSessions(agentId);
    return { success: true, result };
  } catch (error) {
    safeConsole.error(`❌ Terminal agent cleanup error:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
});
