"use client"

import React, { useEffect, useState } from 'react';
import SettingsCenter from '@/components/settings/SettingsCenter';
import { getGlobalSettingsManager } from '@/components/settings/global-settings';
import { CompleteAgentManager } from '@/components/agents/agent-manager-complete';
import { ClientSettingsWrapper } from '@/components/settings/client-settings-wrapper';
import { getGlobalSettingsSyncService, cleanupGlobalSettingsSyncService } from '@/components/settings/settings-sync-service';

/**
 * ✅ Floating Settings Window Page
 * This page is loaded in the floating settings window and provides
 * real-time synchronization with the main window settings.
 */
export default function FloatingSettingsPage() {
  const [settingsManager, setSettingsManager] = useState(getGlobalSettingsManager());
  const [agentManager, setAgentManager] = useState<CompleteAgentManager | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    const initializeServices = async () => {
      try {
        // Initialize agent manager
        const agentMgr = CompleteAgentManager.getInstance(); // ✅ CRITICAL FIX: Use singleton
        // Use waitForInitialization instead of initialize
        await agentMgr.waitForInitialization();
        setAgentManager(agentMgr);

        // Initialize settings sync service
        const syncService = getGlobalSettingsSyncService(settingsManager);

        // Request initial state from other windows
        syncService.requestInitialState();

        setIsInitialized(true);
      } catch (error) {
        console.error('❌ Failed to initialize floating settings:', error);
        setIsInitialized(true); // Still show UI even if some services fail
      }
    };

    initializeServices();

    // Cleanup on unmount
    return () => {
      try {
        cleanupGlobalSettingsSyncService();
      } catch (error) {
        console.error('❌ Failed to cleanup settings sync service:', error);
      }
    };
  }, [settingsManager]);

  // The settings sync service handles broadcasting changes automatically

  if (!isInitialized) {
    return (
      <div className="flex items-center justify-center h-screen bg-background">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Initializing floating settings...</p>
        </div>
      </div>
    );
  }

  return (
    <ClientSettingsWrapper>
      <div className="h-screen bg-background">
        <SettingsCenter
          settingsManager={settingsManager}
          agentManager={agentManager}
          onClose={() => {
            // Close the floating window
            if (typeof window !== 'undefined' && window.electronAPI?.closeWindow) {
              window.electronAPI.closeWindow();
            } else {
              window.close();
            }
          }}
        />
      </div>
    </ClientSettingsWrapper>
  );
}
