"use client"

import React from 'react';
import dynamic from 'next/dynamic';
import { ThemeProvider } from "@/components/theme-provider";
import { Toaster } from "@/components/ui/toaster";
import { BoardProvider } from "@/components/kanban/board-context";
import { SearchProvider } from "@/components/kanban/search-provider";
import { AgentBoardControllerProvider } from "@/components/kanban/agent-board-controller";
import { SettingsProvider } from "@/components/settings/settings-context";
import { ThemeBridge } from "@/components/settings/theme-bridge";
import { getGlobalSettingsManager } from "@/components/settings/global-settings";

const KanbanBoard = dynamic(() => import('@/components/kanban/kanban-board'), {
  ssr: false,
  loading: () => <div className="p-4 text-center">Loading Kanban Board...</div>
});

interface KanbanWindowClientProps {
  boardId: string;
}

function ErrorBoundary({ children, fallback }: { children: React.ReactNode; fallback: React.ReactNode }) {
  const [hasError, setHasError] = React.useState(false);

  React.useEffect(() => {
    const handleError = (error: ErrorEvent) => {
      console.error('Kanban Error:', error);
      setHasError(true);
    };

    window.addEventListener('error', handleError);
    return () => window.removeEventListener('error', handleError);
  }, []);

  if (hasError) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}

export function KanbanWindowClient({ boardId }: KanbanWindowClientProps) {
  const [settingsManager, setSettingsManager] = React.useState<any>(null);

  React.useEffect(() => {
    // ✅ SURGICAL FIX: Initialize SettingsManager for theme synchronization
    const manager = getGlobalSettingsManager();
    setSettingsManager(manager);
  }, []);

  if (!boardId) {
    return <div className="p-8 text-center text-red-500">Board ID not provided.</div>;
  }

  // ✅ SURGICAL FIX: Don't render until SettingsManager is ready
  if (!settingsManager) {
    return (
      <div className="flex items-center justify-center h-screen bg-background">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Initializing theme...</p>
        </div>
      </div>
    );
  }

  return (
    <ThemeProvider attribute="class" defaultTheme="system" enableSystem disableTransitionOnChange>
      <SettingsProvider settingsManager={settingsManager}>
        <ThemeBridge />
        <BoardProvider>
          <AgentBoardControllerProvider>
            <SearchProvider>
              <div className="flex flex-col h-screen bg-background text-foreground">
                {/* ✅ SURGICAL FIX: Use theme-aware background classes */}
                <ErrorBoundary fallback={<div className="p-4 text-center text-red-500">Failed to load Kanban Board</div>}>
                  <KanbanBoard boardId={boardId} />
                </ErrorBoundary>
              </div>
            </SearchProvider>
          </AgentBoardControllerProvider>
        </BoardProvider>
        <Toaster />
      </SettingsProvider>
    </ThemeProvider>
  );
}
