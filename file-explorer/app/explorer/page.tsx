"use client"

import React from "react";
import { ThemeProvider } from "@/components/theme-provider";
import { Toaster } from "@/components/ui/toaster";
import FileSidebar from "@/components/file-sidebar";
import { FileSidebarProvider } from "@/components/file-sidebar/useFileSidebarStore";
import { SettingsProvider } from "@/components/settings/settings-context";
import { ThemeBridge } from "@/components/settings/theme-bridge";
import { getGlobalSettingsManager } from "@/components/settings/global-settings";

function ErrorBoundary({ children, fallback }: { children: React.ReactNode; fallback: React.ReactNode }) {
  const [hasError, setHasError] = React.useState(false);

  React.useEffect(() => {
    const handleError = (error: ErrorEvent) => {
      console.error('Explorer Error:', error);
      setHasError(true);
    };

    window.addEventListener('error', handleError);
    return () => window.removeEventListener('error', handleError);
  }, []);

  if (hasError) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}

export default function ExplorerWindowPage() {
  const [settingsManager, setSettingsManager] = React.useState<any>(null);

  React.useEffect(() => {
    // ✅ SURGICAL FIX: Initialize SettingsManager for theme synchronization
    const manager = getGlobalSettingsManager();
    setSettingsManager(manager);
  }, []);

  // ✅ SURGICAL FIX: Don't render until SettingsManager is ready
  if (!settingsManager) {
    return (
      <div className="flex items-center justify-center h-screen bg-background">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Initializing theme...</p>
        </div>
      </div>
    );
  }

  return (
    <ThemeProvider attribute="class" defaultTheme="system" enableSystem disableTransitionOnChange>
      <SettingsProvider settingsManager={settingsManager}>
        <ThemeBridge />
        <div className="h-screen w-full bg-background text-foreground">
          <div className="h-full flex flex-col">
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b border-border bg-background">
              <h1 className="text-lg font-semibold">File Explorer</h1>
            </div>

            {/* Explorer Content */}
            <div className="flex-1 overflow-hidden">
              <ErrorBoundary fallback={<div className="p-4 text-center text-red-500">Failed to load File Explorer</div>}>
                <FileSidebarProvider>
                  <FileSidebar onFileSelect={() => {}} />
                </FileSidebarProvider>
              </ErrorBoundary>
            </div>
          </div>
          <Toaster />
        </div>
      </SettingsProvider>
    </ThemeProvider>
  );
}
