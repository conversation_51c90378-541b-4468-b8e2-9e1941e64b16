# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules

# next.js
/.next/
/out/

# production
/build
/dist-electron

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*
*.log
dev.log
electron-dev.log
output.log

# env files
.env*

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# test files and logs
/logs/
test-*.js
*.test.log

# development only files
test-prd.txt
**/test-orchestration.*