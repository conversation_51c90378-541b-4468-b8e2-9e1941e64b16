/** @type {import('next').NextConfig} */
const nextConfig = {
  // output: 'export', // Commented out for development - enable for production build
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  experimental: {
    typedRoutes: false,
  },
  images: {
    unoptimized: true,
  },
  // Disable server components for static export
  serverExternalPackages: [],
  // Disable source maps in production
  productionBrowserSourceMaps: false,
  // Disable React strict mode for compatibility
  reactStrictMode: false,
  // Configure webpack for Monaco Editor
  webpack: (config, { isServer, webpack }) => {
    // Only modify client-side webpack config
    if (!isServer) {
      // Make sure workers are properly handled
      config.output.globalObject = 'self';

      // Ensure Monaco Editor's workers are properly handled
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        path: false,
        os: false,
      };

      // ✅ CRITICAL FIX: Exclude Electron preload script from browser bundle
      config.externals = config.externals || [];
      if (Array.isArray(config.externals)) {
        config.externals.push(/^electron$/);
        config.externals.push(/^.*\/electron\/preload\.js$/);
        config.externals.push(/^.*\/dist-electron\/preload\.js$/);
      }

      // ✅ Add ignore plugin to completely exclude electron files
      // Use webpack parameter provided by Next.js
      config.plugins = config.plugins || [];
      config.plugins.push(
        new webpack.IgnorePlugin({
          resourceRegExp: /^electron$/,
        }),
        new webpack.IgnorePlugin({
          resourceRegExp: /\/electron\/preload\.js$/,
        }),
        new webpack.IgnorePlugin({
          resourceRegExp: /\/dist-electron\/preload\.js$/,
        })
      );
    }

    // Important: Return the modified config
    return config;
  },
}

export default nextConfig