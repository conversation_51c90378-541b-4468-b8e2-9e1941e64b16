/**
 * SYNAPSE LOGGING SERVICE
 * Comprehensive and persistent logging system for Agent System and Kanban Board lifecycle
 * 
 * Core Requirements:
 * - Multi-level logging (INFO, DEBUG, ERROR)
 * - Terminal and file output
 * - Structured JSON format
 * - Real-time transparency
 * - Modular and replaceable
 */

export type LogLevel = 'INFO' | 'DEBUG' | 'ERROR';

export interface LogEntry {
  timestamp: string;
  component: string;
  event: string;
  data: Record<string, any>;
  level: LogLevel;
}

export interface LoggerConfig {
  enableConsole: boolean;
  enableFile: boolean;
  logLevel: LogLevel;
  maxFileSize: number; // in MB
  logDirectory: string;
}

/**
 * Synapse Activity Logger
 * Handles all Agent System and Kanban operations logging
 */
export class SynapseLogger {
  private static instance: SynapseLogger;
  private config: LoggerConfig;
  private logBuffer: LogEntry[] = [];
  private isInitialized = false;

  private constructor() {
    this.config = {
      enableConsole: true,
      enableFile: true,
      logLevel: 'INFO',
      maxFileSize: 5, // 5MB
      logDirectory: 'logs'
    };
  }

  public static getInstance(): SynapseLogger {
    if (!SynapseLogger.instance) {
      SynapseLogger.instance = new SynapseLogger();
    }
    return SynapseLogger.instance;
  }

  /**
   * Initialize the logger with configuration
   */
  public async initialize(config?: Partial<LoggerConfig>): Promise<void> {
    if (this.isInitialized) return;

    if (config) {
      this.config = { ...this.config, ...config };
    }

    // Create logs directory if file logging is enabled
    if (this.config.enableFile) {
      await this.ensureLogDirectory();
    }

    this.isInitialized = true;
    this.logInfo('SynapseLogger', 'LoggerInitialized', {
      config: this.config,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Log INFO level events - major system actions
   */
  public logInfo(component: string, event: string, data: Record<string, any> = {}): void {
    this.log('INFO', component, event, data);
  }

  /**
   * Log DEBUG level events - deep diagnostic output
   */
  public logDebug(component: string, event: string, data: Record<string, any> = {}): void {
    this.log('DEBUG', component, event, data);
  }

  /**
   * Log ERROR level events - failures or critical issues
   */
  public logError(component: string, event: string, data: Record<string, any> = {}): void {
    this.log('ERROR', component, event, data);
  }

  /**
   * Core logging method
   */
  private log(level: LogLevel, component: string, event: string, data: Record<string, any>): void {
    // Check if this log level should be processed
    if (!this.shouldLog(level)) return;

    const logEntry: LogEntry = {
      timestamp: new Date().toISOString(),
      component,
      event,
      data: this.sanitizeData(data),
      level
    };

    // Console logging with color coding
    if (this.config.enableConsole) {
      this.logToConsole(logEntry);
    }

    // File logging
    if (this.config.enableFile) {
      this.logToFile(logEntry);
    }

    // Buffer for potential UI overlay (future phase)
    this.logBuffer.push(logEntry);
    this.trimBuffer();
  }

  /**
   * Console logging with color coding
   */
  private logToConsole(entry: LogEntry): void {
    const colors = {
      INFO: '\x1b[36m',    // Cyan
      DEBUG: '\x1b[90m',   // Gray
      ERROR: '\x1b[31m',   // Red
      RESET: '\x1b[0m'
    };

    const color = colors[entry.level];
    const prefix = `${color}[${entry.level}]${colors.RESET}`;
    const timestamp = `\x1b[90m${entry.timestamp}\x1b[0m`;
    const component = `\x1b[33m${entry.component}\x1b[0m`;
    const event = `\x1b[32m${entry.event}\x1b[0m`;

    console.log(`${prefix} ${timestamp} ${component} ${event}`);
    
    // Log data if present and not too large
    if (Object.keys(entry.data).length > 0) {
      const dataStr = JSON.stringify(entry.data, null, 2);
      if (dataStr.length < 500) {
        console.log(`${color}  Data:${colors.RESET}`, entry.data);
      } else {
        console.log(`${color}  Data: [Large object - ${Object.keys(entry.data).length} keys]${colors.RESET}`);
      }
    }
  }

  /**
   * File logging in JSON Lines format
   */
  private async logToFile(entry: LogEntry): Promise<void> {
    try {
      // Check if we're in an Electron environment
      if (typeof window !== 'undefined' && window.electronAPI && window.electronAPI.appendFile) {
        const logLine = JSON.stringify(entry) + '\n';
        const logFilePath = `${this.config.logDirectory}/synapse-activity.log`;
        
        await window.electronAPI.appendFile(logFilePath, logLine);
      } else {
        // Fallback: store in memory buffer for later processing
        console.warn('File logging not available - storing in memory buffer');
      }
    } catch (error) {
      console.error('Failed to write log to file:', error);
    }
  }

  /**
   * Ensure log directory exists
   */
  private async ensureLogDirectory(): Promise<void> {
    try {
      if (typeof window !== 'undefined' && window.electronAPI && window.electronAPI.ensureDirectory) {
        await window.electronAPI.ensureDirectory(this.config.logDirectory);
      }
    } catch (error) {
      console.warn('Could not create log directory:', error);
    }
  }

  /**
   * Check if log level should be processed
   */
  private shouldLog(level: LogLevel): boolean {
    const levels = { ERROR: 0, INFO: 1, DEBUG: 2 };
    return levels[level] <= levels[this.config.logLevel];
  }

  /**
   * Sanitize data to prevent circular references and large objects
   */
  private sanitizeData(data: Record<string, any>): Record<string, any> {
    try {
      // Create a clean copy and handle circular references
      return JSON.parse(JSON.stringify(data, (_key, value) => {
        // Skip functions
        if (typeof value === 'function') return '[Function]';
        
        // Truncate very long strings
        if (typeof value === 'string' && value.length > 1000) {
          return value.substring(0, 1000) + '... [truncated]';
        }
        
        // Handle large arrays
        if (Array.isArray(value) && value.length > 50) {
          return `[Array with ${value.length} items - truncated]`;
        }
        
        return value;
      }));
    } catch (error) {
      return { error: 'Failed to sanitize data', originalKeys: Object.keys(data) };
    }
  }

  /**
   * Trim buffer to prevent memory leaks
   */
  private trimBuffer(): void {
    const maxBufferSize = 1000;
    if (this.logBuffer.length > maxBufferSize) {
      this.logBuffer = this.logBuffer.slice(-maxBufferSize);
    }
  }

  /**
   * Get recent log entries (for future UI overlay)
   */
  public getRecentLogs(count: number = 100): LogEntry[] {
    return this.logBuffer.slice(-count);
  }

  /**
   * Update logger configuration
   */
  public updateConfig(config: Partial<LoggerConfig>): void {
    this.config = { ...this.config, ...config };
    this.logInfo('SynapseLogger', 'ConfigUpdated', { newConfig: this.config });
  }

  /**
   * Clear log buffer
   */
  public clearBuffer(): void {
    this.logBuffer = [];
    this.logInfo('SynapseLogger', 'BufferCleared', {});
  }
}

// Export singleton instance
export const synapseLogger = SynapseLogger.getInstance();

// Convenience functions for direct import
export const logInfo = (component: string, event: string, data?: Record<string, any>) => 
  synapseLogger.logInfo(component, event, data);

export const logDebug = (component: string, event: string, data?: Record<string, any>) => 
  synapseLogger.logDebug(component, event, data);

export const logError = (component: string, event: string, data?: Record<string, any>) =>
  synapseLogger.logError(component, event, data);

/**
 * SPECIALIZED LOGGING METHODS FOR SYNAPSE COMPONENTS
 */

// Micromanager logging helpers
export const logMicromanagerEvent = (event: string, data: Record<string, any>) =>
  synapseLogger.logInfo('Micromanager', event, data);

export const logMicromanagerDebug = (event: string, data: Record<string, any>) =>
  synapseLogger.logDebug('Micromanager', event, data);

export const logMicromanagerError = (event: string, data: Record<string, any>) =>
  synapseLogger.logError('Micromanager', event, data);

// Taskmaster logging helpers
export const logTaskmasterEvent = (event: string, data: Record<string, any>) =>
  synapseLogger.logInfo('Taskmaster', event, data);

export const logTaskmasterDebug = (event: string, data: Record<string, any>) =>
  synapseLogger.logDebug('Taskmaster', event, data);

export const logTaskmasterError = (event: string, data: Record<string, any>) =>
  synapseLogger.logError('Taskmaster', event, data);

// Kanban logging helpers
export const logKanbanEvent = (event: string, data: Record<string, any>) =>
  synapseLogger.logInfo('KanbanVisualizer', event, data);

export const logKanbanDebug = (event: string, data: Record<string, any>) =>
  synapseLogger.logDebug('KanbanVisualizer', event, data);

export const logKanbanError = (event: string, data: Record<string, any>) =>
  synapseLogger.logError('KanbanVisualizer', event, data);

// ✅ MANDATORY UPGRADE: Kanban Card Movement Reason Logging
export const logCardMovementWithReason = (data: {
  cardId: string;
  taskId?: string;
  fromColumn: string;
  toColumn: string;
  reason: string;
  triggeredBy: string;
  agentAction?: string;
  hasOutput?: boolean;
  outputFiles?: string[];
}) => synapseLogger.logInfo('KanbanVisualizer', 'CardMovedWithReason', data);

// Agent System logging helpers
export const logAgentEvent = (agentId: string, event: string, data: Record<string, any>) =>
  synapseLogger.logInfo(`Agent:${agentId}`, event, data);

export const logAgentDebug = (agentId: string, event: string, data: Record<string, any>) =>
  synapseLogger.logDebug(`Agent:${agentId}`, event, data);

export const logAgentError = (agentId: string, event: string, data: Record<string, any>) =>
  synapseLogger.logError(`Agent:${agentId}`, event, data);

// ✅ MANDATORY UPGRADE: Agent Execution Outcome Logging
export const logTaskExecutionSucceeded = (agentId: string, data: {
  taskId: string;
  generatedFiles?: string[];
  functionsCreated?: string[];
  diffStats?: { additions: number; deletions: number; modifications: number };
  outputPaths?: string[];
  executionTime: number;
  tokensUsed: number;
}) => synapseLogger.logInfo(`Agent:${agentId}`, 'TaskExecutionSucceeded', data);

export const logTaskExecutionFailed = (agentId: string, data: {
  taskId: string;
  error: string;
  reason: string;
  agentState: string;
  stack?: string;
  executionTime: number;
}) => synapseLogger.logError(`Agent:${agentId}`, 'TaskExecutionFailed', data);

export const logTaskExecutionSkipped = (agentId: string, data: {
  taskId: string;
  cause: string;
  reason: string;
  contextMissing?: boolean;
  invalidTask?: boolean;
}) => synapseLogger.logInfo(`Agent:${agentId}`, 'TaskExecutionSkipped', data);

export const logTaskOutputWritten = (agentId: string, data: {
  taskId: string;
  filePaths: string[];
  fileTypes: string[];
  totalFiles: number;
  totalSize?: number;
}) => synapseLogger.logInfo(`Agent:${agentId}`, 'TaskOutputWritten', data);

export const logSilentExecutionFailure = (data: {
  cardId: string;
  taskId: string;
  agentId: string;
  reason: string;
  columnTransition?: { from: string; to: string };
}) => synapseLogger.logError('System', 'SilentExecutionFailure', data);

export const logCardMarkedDoneWithoutOutput = (data: {
  cardId: string;
  taskId: string;
  agentId: string;
  columnId: string;
  timestamp: number;
}) => synapseLogger.logError('KanbanVisualizer', 'CardMarkedDoneWithoutOutput', data);

// ✅ MANDATORY UPGRADE: Execution Step Tracing (Optional but Recommended)
export const logPromptSentToModel = (agentId: string, data: {
  taskId: string;
  promptLength: number;
  model: string;
  provider: string;
  timestamp: number;
}) => synapseLogger.logDebug(`Agent:${agentId}`, 'PromptSentToModel', data);

export const logModelResponseReceived = (agentId: string, data: {
  taskId: string;
  responseLength: number;
  tokensUsed: number;
  responseTime: number;
  finishReason?: string;
}) => synapseLogger.logDebug(`Agent:${agentId}`, 'ModelResponseReceived', data);

export const logFileWriteAttempt = (agentId: string, data: {
  taskId: string;
  filePath: string;
  operation: 'create' | 'modify' | 'delete';
  success: boolean;
  error?: string;
}) => synapseLogger.logDebug(`Agent:${agentId}`, 'FileWriteAttempt', data);

export const logPostValidationCheck = (agentId: string, data: {
  taskId: string;
  validationType: string;
  passed: boolean;
  issues?: string[];
  autoFixApplied?: boolean;
}) => synapseLogger.logDebug(`Agent:${agentId}`, 'PostValidationCheck', data);

// Task Classifier logging helpers
export const logClassifierEvent = (event: string, data: Record<string, any>) =>
  synapseLogger.logInfo('TaskClassifier', event, data);

export const logClassifierDebug = (event: string, data: Record<string, any>) =>
  synapseLogger.logDebug('TaskClassifier', event, data);

export const logClassifierError = (event: string, data: Record<string, any>) =>
  synapseLogger.logError('TaskClassifier', event, data);

// Context Prefetcher logging helpers
export const logContextEvent = (event: string, data: Record<string, any>) =>
  synapseLogger.logInfo('ContextPrefetcher', event, data);

export const logContextDebug = (event: string, data: Record<string, any>) =>
  synapseLogger.logDebug('ContextPrefetcher', event, data);

export const logContextError = (event: string, data: Record<string, any>) =>
  synapseLogger.logError('ContextPrefetcher', event, data);
