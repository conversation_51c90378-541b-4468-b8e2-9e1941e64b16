// services/automatic-execution-service.ts
// ✅ PHASE 4: Automatic Sequential Execution Service

import { sequentialExecutionController } from './task-state-service';
import { completionVerificationService } from './completion-verification-service';
import { liveCodingService } from './live-coding-service';
import { micromanagerAgent } from '../components/agents/micromanager-agent';

export interface AutoExecutionConfig {
  enabled: boolean;
  autoApproveThreshold: number; // Quality score threshold for auto-approval (0-100)
  maxConsecutiveTasks: number; // Max tasks to run without user intervention
  timeoutPerTask: number; // Max time per task in milliseconds
  requireUserApprovalFor: ('low_quality' | 'file_errors' | 'validation_failures')[];
}

export interface AutoExecutionStatus {
  isRunning: boolean;
  currentTaskId: string | null;
  currentAgentId: string | null;
  tasksCompleted: number;
  tasksRemaining: number;
  consecutiveTasksRun: number;
  lastUserInteraction: number;
  autoApprovalEnabled: boolean;
}

export class AutomaticExecutionService {
  private static instance: AutomaticExecutionService;
  private config: AutoExecutionConfig = {
    enabled: false,
    autoApproveThreshold: 80,
    maxConsecutiveTasks: 5,
    timeoutPerTask: 300000, // 5 minutes
    requireUserApprovalFor: ['low_quality', 'validation_failures']
  };
  private status: AutoExecutionStatus = {
    isRunning: false,
    currentTaskId: null,
    currentAgentId: null,
    tasksCompleted: 0,
    tasksRemaining: 0,
    consecutiveTasksRun: 0,
    lastUserInteraction: Date.now(),
    autoApprovalEnabled: false
  };
  private executionTimer: NodeJS.Timeout | null = null;
  private listeners = new Set<(status: AutoExecutionStatus) => void>();

  private constructor() {
    console.log('AutomaticExecutionService: Initialized');
  }

  public static getInstance(): AutomaticExecutionService {
    if (!AutomaticExecutionService.instance) {
      AutomaticExecutionService.instance = new AutomaticExecutionService();
    }
    return AutomaticExecutionService.instance;
  }

  /**
   * ✅ PHASE 4: Configure automatic execution
   */
  public configure(config: Partial<AutoExecutionConfig>): void {
    this.config = { ...this.config, ...config };
    console.log('AutomaticExecutionService: Configuration updated:', this.config);
  }

  /**
   * ✅ PHASE 4: Add status listener
   */
  public addStatusListener(listener: (status: AutoExecutionStatus) => void): () => void {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  /**
   * ✅ PHASE 4: Start automatic execution
   */
  public async startAutomaticExecution(): Promise<{ success: boolean; message: string }> {
    if (this.status.isRunning) {
      return { success: false, message: 'Automatic execution already running' };
    }

    if (!this.config.enabled) {
      return { success: false, message: 'Automatic execution not enabled' };
    }

    console.log('AutomaticExecutionService: Starting automatic execution');
    
    this.status.isRunning = true;
    this.status.consecutiveTasksRun = 0;
    this.status.lastUserInteraction = Date.now();
    this.updateStatus();

    // Start execution loop
    this.executeNextTaskAutomatically();

    return { success: true, message: 'Automatic execution started' };
  }

  /**
   * ✅ PHASE 4: Stop automatic execution
   */
  public async stopAutomaticExecution(): Promise<{ success: boolean; message: string }> {
    console.log('AutomaticExecutionService: Stopping automatic execution');
    
    this.status.isRunning = false;
    this.status.lastUserInteraction = Date.now();
    
    if (this.executionTimer) {
      clearTimeout(this.executionTimer);
      this.executionTimer = null;
    }

    this.updateStatus();
    return { success: true, message: 'Automatic execution stopped' };
  }

  /**
   * ✅ PHASE 4: Execute next task automatically
   */
  private async executeNextTaskAutomatically(): Promise<void> {
    if (!this.status.isRunning || !this.config.enabled) {
      return;
    }

    try {
      // Check if we've hit the consecutive task limit
      if (this.status.consecutiveTasksRun >= this.config.maxConsecutiveTasks) {
        console.log('AutomaticExecutionService: Reached consecutive task limit, requiring user intervention');
        await this.pauseForUserIntervention('Consecutive task limit reached');
        return;
      }

      // Get workflow status
      const workflowStatus = micromanagerAgent.getSequentialWorkflowStatus();
      this.status.tasksRemaining = workflowStatus.queueLength;
      this.updateStatus();

      if (!workflowStatus.canStartNext) {
        if (workflowStatus.currentTask) {
          // Task is running, wait for completion
          this.scheduleNextCheck(5000); // Check again in 5 seconds
        } else {
          // No more tasks
          console.log('AutomaticExecutionService: No more tasks to execute');
          await this.stopAutomaticExecution();
        }
        return;
      }

      // Start next task
      console.log('AutomaticExecutionService: Starting next task automatically');
      const startResult = await micromanagerAgent.startNextSequentialTask();
      
      if (!startResult.success) {
        console.error('AutomaticExecutionService: Failed to start next task:', startResult.message);
        await this.pauseForUserIntervention(`Failed to start task: ${startResult.message}`);
        return;
      }

      // Update status
      this.status.currentTaskId = startResult.taskStarted?.taskId || null;
      this.status.currentAgentId = startResult.taskStarted?.agentId || null;
      this.updateStatus();

      // Initialize live coding
      if (startResult.taskStarted) {
        liveCodingService.showProgress(
          startResult.taskStarted.agentId,
          startResult.taskStarted.taskId,
          0,
          `Auto-executing task with ${startResult.taskStarted.agentId}`
        );
      }

      // Wait for task completion with timeout
      this.waitForTaskCompletion();

    } catch (error) {
      console.error('AutomaticExecutionService: Error in automatic execution:', error);
      await this.pauseForUserIntervention(`Execution error: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * ✅ PHASE 4: Wait for task completion and auto-approve if criteria met
   */
  private async waitForTaskCompletion(): Promise<void> {
    const startTime = Date.now();
    const checkInterval = 2000; // Check every 2 seconds

    const checkCompletion = async () => {
      if (!this.status.isRunning) return;

      const workflowStatus = micromanagerAgent.getSequentialWorkflowStatus();
      
      // Check timeout
      if (Date.now() - startTime > this.config.timeoutPerTask) {
        console.warn('AutomaticExecutionService: Task timeout reached');
        await this.pauseForUserIntervention('Task execution timeout');
        return;
      }

      // If task is still running, continue waiting
      if (workflowStatus.currentTask) {
        this.scheduleNextCheck(checkInterval, checkCompletion);
        return;
      }

      // Task completed, attempt auto-approval
      await this.attemptAutoApproval();
    };

    this.scheduleNextCheck(checkInterval, checkCompletion);
  }

  /**
   * ✅ PHASE 4: Attempt automatic approval based on quality criteria
   */
  private async attemptAutoApproval(): Promise<void> {
    if (!this.status.currentTaskId) {
      console.warn('AutomaticExecutionService: No current task for auto-approval');
      return;
    }

    try {
      console.log('AutomaticExecutionService: Attempting auto-approval for task:', this.status.currentTaskId);
      
      // Complete the task and get report
      const completionResult = await micromanagerAgent.completeCurrentTask(this.status.currentTaskId);
      
      if (!completionResult.success) {
        console.log('AutomaticExecutionService: Task completion failed, requiring user intervention');
        await this.pauseForUserIntervention(`Task completion failed: ${completionResult.message}`);
        return;
      }

      const report = completionResult.report;
      const qualityScore = report?.codeQuality?.score || 0;
      const hasValidationErrors = !report?.success;
      const hasFileErrors = report?.filesCreated?.length === 0 && report?.filesModified?.length === 0;

      // Check auto-approval criteria
      const shouldRequireApproval = 
        qualityScore < this.config.autoApproveThreshold ||
        (hasValidationErrors && this.config.requireUserApprovalFor.includes('validation_failures')) ||
        (hasFileErrors && this.config.requireUserApprovalFor.includes('file_errors')) ||
        (qualityScore < 70 && this.config.requireUserApprovalFor.includes('low_quality'));

      if (shouldRequireApproval) {
        console.log('AutomaticExecutionService: Task requires user approval due to quality/validation issues');
        await this.pauseForUserIntervention(`Task requires approval - Quality: ${qualityScore}/100`);
        return;
      }

      // Auto-approve and continue
      console.log('AutomaticExecutionService: Auto-approving task with quality score:', qualityScore);
      
      // Update status
      this.status.tasksCompleted++;
      this.status.consecutiveTasksRun++;
      this.status.currentTaskId = null;
      this.status.currentAgentId = null;
      this.updateStatus();

      // Complete live coding
      if (this.status.currentAgentId) {
        liveCodingService.completeWork(
          this.status.currentAgentId,
          this.status.currentTaskId || 'unknown',
          'auto-completed'
        );
      }

      // Continue to next task
      this.scheduleNextCheck(1000, () => this.executeNextTaskAutomatically());

    } catch (error) {
      console.error('AutomaticExecutionService: Error in auto-approval:', error);
      await this.pauseForUserIntervention(`Auto-approval error: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * ✅ PHASE 4: Pause for user intervention
   */
  private async pauseForUserIntervention(reason: string): Promise<void> {
    console.log('AutomaticExecutionService: Pausing for user intervention:', reason);
    
    this.status.isRunning = false;
    this.status.lastUserInteraction = Date.now();
    this.updateStatus();

    // Clear any pending timers
    if (this.executionTimer) {
      clearTimeout(this.executionTimer);
      this.executionTimer = null;
    }

    // Emit notification for UI
    this.notifyPauseForIntervention(reason);
  }

  /**
   * ✅ PHASE 4: Resume after user intervention
   */
  public async resumeAfterUserIntervention(): Promise<{ success: boolean; message: string }> {
    console.log('AutomaticExecutionService: Resuming after user intervention');
    
    this.status.isRunning = true;
    this.status.consecutiveTasksRun = 0; // Reset consecutive counter
    this.status.lastUserInteraction = Date.now();
    this.updateStatus();

    // Continue execution
    this.executeNextTaskAutomatically();

    return { success: true, message: 'Automatic execution resumed' };
  }

  /**
   * ✅ PHASE 4: Get current status
   */
  public getStatus(): AutoExecutionStatus {
    return { ...this.status };
  }

  /**
   * ✅ PHASE 4: Get current configuration
   */
  public getConfig(): AutoExecutionConfig {
    return { ...this.config };
  }

  /**
   * Private helper methods
   */
  private scheduleNextCheck(delay: number, callback?: () => void): void {
    if (this.executionTimer) {
      clearTimeout(this.executionTimer);
    }
    
    this.executionTimer = setTimeout(() => {
      if (callback) {
        callback();
      }
    }, delay);
  }

  private updateStatus(): void {
    this.listeners.forEach(listener => {
      try {
        listener({ ...this.status });
      } catch (error) {
        console.error('AutomaticExecutionService: Error in status listener:', error);
      }
    });
  }

  private notifyPauseForIntervention(reason: string): void {
    // This would integrate with a notification system
    console.log('🔔 AutomaticExecutionService: User intervention required:', reason);
  }
}

// Export singleton instance
export const automaticExecutionService = AutomaticExecutionService.getInstance();
