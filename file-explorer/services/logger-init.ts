/**
 * SYNAPSE LOGGER INITIALIZATION SERVICE
 * Handles logger setup at application boot
 */

import { synapseLogger, LoggerConfig } from './logger';

/**
 * Initialize the Synapse logging system
 * Should be called early in application lifecycle
 */
export async function initializeSynapseLogger(): Promise<void> {
  try {
    console.log('🔧 Initializing Synapse Logger...');

    // Determine environment and set appropriate config
    const isDevelopment = process.env.NODE_ENV === 'development';
    
    const config: Partial<LoggerConfig> = {
      enableConsole: true,
      enableFile: true,
      logLevel: isDevelopment ? 'DEBUG' : 'INFO',
      maxFileSize: 5, // 5MB
      logDirectory: 'logs'
    };

    // Initialize the logger
    await synapseLogger.initialize(config);

    console.log('✅ Synapse Logger initialized successfully');
    
    // Log the initialization
    synapseLogger.logInfo('System', 'LoggerInitialized', {
      environment: isDevelopment ? 'development' : 'production',
      config,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Failed to initialize Synapse Logger:', error);
    // Don't throw - logging should not break the application
  }
}

/**
 * Setup logger for specific environment
 */
export function setupLoggerForEnvironment(environment: 'development' | 'production' | 'test'): void {
  const configs = {
    development: {
      enableConsole: true,
      enableFile: true,
      logLevel: 'DEBUG' as const,
      maxFileSize: 5,
      logDirectory: 'logs'
    },
    production: {
      enableConsole: false,
      enableFile: true,
      logLevel: 'INFO' as const,
      maxFileSize: 10,
      logDirectory: 'logs'
    },
    test: {
      enableConsole: false,
      enableFile: false,
      logLevel: 'ERROR' as const,
      maxFileSize: 1,
      logDirectory: 'test-logs'
    }
  };

  synapseLogger.updateConfig(configs[environment]);
  synapseLogger.logInfo('System', 'LoggerConfigUpdated', { environment });
}

/**
 * Create logs directory structure
 */
export async function ensureLogDirectoryStructure(): Promise<void> {
  try {
    if (typeof window !== 'undefined' && window.electronAPI && window.electronAPI.ensureDirectory) {
      // Create main logs directory
      await window.electronAPI.ensureDirectory('logs');
      
      // Create subdirectories for different log types
      await window.electronAPI.ensureDirectory('logs/agents');
      await window.electronAPI.ensureDirectory('logs/kanban');
      await window.electronAPI.ensureDirectory('logs/system');
      
      console.log('✅ Log directory structure created');
    }
  } catch (error) {
    console.warn('⚠️ Could not create log directory structure:', error);
  }
}

/**
 * Log system startup information
 */
export function logSystemStartup(): void {
  synapseLogger.logInfo('System', 'ApplicationStartup', {
    timestamp: new Date().toISOString(),
    userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'Unknown',
    platform: typeof process !== 'undefined' ? process.platform : 'Unknown',
    nodeVersion: typeof process !== 'undefined' ? process.version : 'Unknown',
    electronAvailable: typeof window !== 'undefined' && !!window.electronAPI
  });
}

/**
 * Log system shutdown information
 */
export function logSystemShutdown(): void {
  synapseLogger.logInfo('System', 'ApplicationShutdown', {
    timestamp: new Date().toISOString(),
    uptime: typeof process !== 'undefined' ? process.uptime() : 'Unknown'
  });
}
