// services/active-project-service.ts
// Service for managing the currently active project

export interface ActiveProject {
  name: string;
  path: string;
  activatedAt: number;
  metadata?: {
    description?: string;
    version?: string;
    type?: string;
    lastModified?: number;
  };
}

class ActiveProjectService {
  private activeProject: ActiveProject | null = null;
  private listeners: Array<(project: ActiveProject | null) => void> = [];

  /**
   * Set the active project
   */
  setActiveProject(projectPath: string, projectName?: string): void {
    const name = projectName || this.extractProjectNameFromPath(projectPath);

    this.activeProject = {
      name,
      path: projectPath,
      activatedAt: Date.now(),
      metadata: {
        type: 'unknown',
        lastModified: Date.now()
      }
    };

    console.log(`✅ Active project set: ${name} (${projectPath})`);
    console.log(`🔍 Active project verification: ${JSON.stringify(this.activeProject)}`);

    // Persist active project state
    this.persistActiveProject();

    this.notifyListeners();
  }

  /**
   * Get the currently active project
   */
  getActiveProject(): ActiveProject | null {
    return this.activeProject;
  }

  /**
   * Clear the active project
   */
  clearActiveProject(): void {
    this.activeProject = null;
    console.log('✅ Active project cleared');

    // Persist cleared state
    this.persistActiveProject();

    this.notifyListeners();
  }

  /**
   * Check if a project is currently active
   */
  isProjectActive(projectPath: string): boolean {
    return this.activeProject?.path === projectPath;
  }

  /**
   * Get active project path
   */
  getActiveProjectPath(): string | null {
    return this.activeProject?.path || null;
  }

  /**
   * Get active project name
   */
  getActiveProjectName(): string | null {
    return this.activeProject?.name || null;
  }

  /**
   * ✅ DEBUG: Test method to verify active project service is working
   */
  testActiveProjectService(): void {
    console.log('🧪 TESTING Active Project Service:');
    console.log(`  - Active project: ${this.activeProject ? 'SET' : 'NOT SET'}`);
    if (this.activeProject) {
      console.log(`  - Name: ${this.activeProject.name}`);
      console.log(`  - Path: ${this.activeProject.path}`);
      console.log(`  - Activated at: ${new Date(this.activeProject.activatedAt).toISOString()}`);
    }
    console.log(`  - Listeners: ${this.listeners.length}`);

    // Test resolve method
    if (this.activeProject) {
      try {
        const testPath = this.resolve('src/test.js');
        console.log(`  - Resolve test: 'src/test.js' → '${testPath}' ✅`);
      } catch (error) {
        console.log(`  - Resolve test: FAILED - ${error} ❌`);
      }
    } else {
      console.log(`  - Resolve test: SKIPPED (no active project) ⚠️`);
    }
  }

  /**
   * ✅ CRITICAL FIX: Resolve relative path within active project
   */
  resolve(relativePath: string): string {
    console.log(`🔍 ActiveProjectService.resolve() called with: '${relativePath}'`);
    console.log(`🔍 Current active project: ${this.activeProject ? this.activeProject.name + ' (' + this.activeProject.path + ')' : 'NONE'}`);

    if (!this.activeProject) {
      const errorMsg = 'No active project set. Cannot resolve file path.';
      console.error(`❌ ${errorMsg}`);
      throw new Error(errorMsg);
    }

    // Clean the relative path (remove leading ./ and normalize)
    const cleanRelativePath = relativePath.replace(/^\.\//, '').replace(/\/+/g, '/');

    // Resolve to absolute path within project
    const resolvedPath = `${this.activeProject.path}/${cleanRelativePath}`;

    console.log(`✅ ActiveProjectService: Resolved '${relativePath}' to '${resolvedPath}'`);
    return resolvedPath;
  }

  /**
   * Update active project metadata
   */
  updateActiveProjectMetadata(metadata: Partial<ActiveProject['metadata']>): void {
    if (this.activeProject) {
      this.activeProject.metadata = {
        ...this.activeProject.metadata,
        ...metadata,
        lastModified: Date.now()
      };
      this.notifyListeners();
    }
  }

  /**
   * Add listener for active project changes
   */
  addListener(listener: (project: ActiveProject | null) => void): void {
    this.listeners.push(listener);
  }

  /**
   * Remove listener for active project changes
   */
  removeListener(listener: (project: ActiveProject | null) => void): void {
    const index = this.listeners.indexOf(listener);
    if (index >= 0) {
      this.listeners.splice(index, 1);
    }
  }

  /**
   * Subscribe to active project changes (returns unsubscribe function)
   */
  onActiveProjectChange(listener: (project: ActiveProject | null) => void): () => void {
    this.addListener(listener);
    return () => this.removeListener(listener);
  }

  /**
   * Notify all listeners of active project changes
   */
  private notifyListeners(): void {
    this.listeners.forEach(listener => {
      try {
        listener(this.activeProject);
      } catch (error) {
        console.warn('Error in active project listener:', error);
      }
    });
  }

  /**
   * Extract project name from path
   */
  private extractProjectNameFromPath(projectPath: string): string {
    const parts = projectPath.split(/[/\\]/);
    return parts[parts.length - 1] || 'Unknown Project';
  }

  /**
   * Get project statistics
   */
  getProjectStats(): {
    hasActiveProject: boolean;
    activeDuration: number;
    projectName: string | null;
    projectPath: string | null;
  } {
    if (!this.activeProject) {
      return {
        hasActiveProject: false,
        activeDuration: 0,
        projectName: null,
        projectPath: null
      };
    }

    return {
      hasActiveProject: true,
      activeDuration: Date.now() - this.activeProject.activatedAt,
      projectName: this.activeProject.name,
      projectPath: this.activeProject.path
    };
  }

  /**
   * Persist active project state to localStorage
   */
  private persistActiveProject(): void {
    try {
      if (typeof window !== 'undefined' && window.localStorage) {
        if (this.activeProject) {
          localStorage.setItem('activeProject', JSON.stringify(this.activeProject));
          console.log('✅ Active project persisted to localStorage');
        } else {
          localStorage.removeItem('activeProject');
          console.log('✅ Active project cleared from localStorage');
        }
      }
    } catch (error) {
      console.warn('⚠️ Failed to persist active project:', error);
    }
  }

  /**
   * Restore active project state from localStorage
   */
  private restoreActiveProject(): void {
    try {
      if (typeof window !== 'undefined' && window.localStorage) {
        const stored = localStorage.getItem('activeProject');
        if (stored) {
          const project = JSON.parse(stored) as ActiveProject;
          this.activeProject = project;
          console.log(`✅ Active project restored: ${project.name} (${project.path})`);
          this.notifyListeners();
        }
      }
    } catch (error) {
      console.warn('⚠️ Failed to restore active project:', error);
      // Clear corrupted data
      if (typeof window !== 'undefined' && window.localStorage) {
        localStorage.removeItem('activeProject');
      }
    }
  }

  /**
   * Initialize the service
   */
  initialize(): void {
    console.log('✅ Active project service initialized');
    // Restore active project from localStorage on initialization
    this.restoreActiveProject();
  }

  /**
   * Cleanup the service
   */
  cleanup(): void {
    this.activeProject = null;
    this.listeners = [];
    console.log('✅ Active project service cleaned up');
  }
}

// Export singleton instance
export const activeProjectService = new ActiveProjectService();

// ✅ DEBUG: Expose test function globally for debugging
if (typeof window !== 'undefined') {
  (window as any).testActiveProjectService = () => {
    activeProjectService.testActiveProjectService();
  };
}
activeProjectService.initialize();