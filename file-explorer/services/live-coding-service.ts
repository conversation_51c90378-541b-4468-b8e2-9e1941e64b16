// services/live-coding-service.ts
// ✅ PHASE 3: Live Coding Service for Real-time Monaco Editor Integration

export interface LiveCodingUpdate {
  type: 'file_open' | 'content_stream' | 'highlight' | 'progress' | 'completion';
  agentId: string;
  taskId: string;
  filePath: string;
  data: {
    content?: string;
    lineRange?: [number, number];
    progress?: number;
    message?: string;
    timestamp: number;
  };
}

export interface MonacoEditorIntegration {
  openFile: (filePath: string, content: string) => void;
  updateContent: (filePath: string, content: string, preserveCursor?: boolean) => void;
  highlightLines: (filePath: string, startLine: number, endLine: number) => void;
  showProgress: (message: string, percentage: number) => void;
  setReadOnly: (filePath: string, readOnly: boolean) => void;
  focusEditor: (filePath: string) => void;
}

export type LiveCodingListener = (update: LiveCodingUpdate) => void;

export class LiveCodingService {
  private static instance: LiveCodingService;
  private listeners = new Set<LiveCodingListener>();
  private monacoIntegration: MonacoEditorIntegration | null = null;
  private activeFiles = new Map<string, { content: string; lastUpdate: number }>();
  private streamingProgress = new Map<string, number>();

  private constructor() {
    console.log('LiveCodingService: Initialized');
  }

  public static getInstance(): LiveCodingService {
    if (!LiveCodingService.instance) {
      LiveCodingService.instance = new LiveCodingService();
    }
    return LiveCodingService.instance;
  }

  /**
   * ✅ PHASE 3: Set Monaco editor integration
   */
  public setMonacoIntegration(integration: MonacoEditorIntegration): void {
    this.monacoIntegration = integration;
    console.log('LiveCodingService: Monaco editor integration set');
  }

  /**
   * ✅ PHASE 3: Add live coding listener
   */
  public addListener(listener: LiveCodingListener): () => void {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  /**
   * ✅ PHASE 3: Display active agent work in Monaco editor
   */
  public displayActiveWork(agentId: string, taskId: string, filePath: string, content: string): void {
    console.log(`LiveCodingService: Displaying active work for agent ${agentId} - ${filePath}`);
    
    // Update active files tracking
    this.activeFiles.set(filePath, {
      content,
      lastUpdate: Date.now()
    });

    // Open file in Monaco editor
    if (this.monacoIntegration) {
      try {
        this.monacoIntegration.openFile(filePath, content);
        this.monacoIntegration.setReadOnly(filePath, true); // Read-only during agent work
        this.monacoIntegration.focusEditor(filePath);
      } catch (error) {
        console.error('LiveCodingService: Error opening file in Monaco:', error);
      }
    }

    // Emit update to listeners
    this.emitUpdate({
      type: 'file_open',
      agentId,
      taskId,
      filePath,
      data: {
        content,
        message: `Agent ${agentId} started working on ${filePath}`,
        timestamp: Date.now()
      }
    });
  }

  /**
   * ✅ PHASE 3: Stream code generation in real-time
   */
  public streamCodeGeneration(agentId: string, taskId: string, filePath: string, content: string, progress: number): void {
    console.log(`LiveCodingService: Streaming code generation for ${filePath} - ${progress}%`);
    
    // Update streaming progress
    this.streamingProgress.set(filePath, progress);
    
    // Update active file content
    this.activeFiles.set(filePath, {
      content,
      lastUpdate: Date.now()
    });

    // Update Monaco editor content
    if (this.monacoIntegration) {
      try {
        this.monacoIntegration.updateContent(filePath, content, true);
        this.monacoIntegration.showProgress(`Generating ${filePath}...`, progress);
      } catch (error) {
        console.error('LiveCodingService: Error updating Monaco content:', error);
      }
    }

    // Emit streaming update
    this.emitUpdate({
      type: 'content_stream',
      agentId,
      taskId,
      filePath,
      data: {
        content,
        progress,
        message: `Code generation ${progress}% complete`,
        timestamp: Date.now()
      }
    });
  }

  /**
   * ✅ PHASE 3: Highlight current work area
   */
  public highlightWorkArea(agentId: string, taskId: string, filePath: string, lineRange: [number, number]): void {
    console.log(`LiveCodingService: Highlighting work area in ${filePath} - lines ${lineRange[0]}-${lineRange[1]}`);
    
    // Highlight in Monaco editor
    if (this.monacoIntegration) {
      try {
        this.monacoIntegration.highlightLines(filePath, lineRange[0], lineRange[1]);
      } catch (error) {
        console.error('LiveCodingService: Error highlighting lines in Monaco:', error);
      }
    }

    // Emit highlight update
    this.emitUpdate({
      type: 'highlight',
      agentId,
      taskId,
      filePath,
      data: {
        lineRange,
        message: `Working on lines ${lineRange[0]}-${lineRange[1]}`,
        timestamp: Date.now()
      }
    });
  }

  /**
   * ✅ PHASE 3: Show agent progress
   */
  public showProgress(agentId: string, taskId: string, percentage: number, description: string): void {
    console.log(`LiveCodingService: Agent ${agentId} progress: ${percentage}% - ${description}`);
    
    // Show progress in Monaco editor
    if (this.monacoIntegration) {
      try {
        this.monacoIntegration.showProgress(description, percentage);
      } catch (error) {
        console.error('LiveCodingService: Error showing progress in Monaco:', error);
      }
    }

    // Emit progress update
    this.emitUpdate({
      type: 'progress',
      agentId,
      taskId,
      filePath: '', // No specific file for general progress
      data: {
        progress: percentage,
        message: description,
        timestamp: Date.now()
      }
    });
  }

  /**
   * ✅ PHASE 3: Complete agent work and make file editable
   */
  public completeWork(agentId: string, taskId: string, filePath: string): void {
    console.log(`LiveCodingService: Completing work for agent ${agentId} - ${filePath}`);
    
    // Remove from streaming progress
    this.streamingProgress.delete(filePath);
    
    // Make file editable in Monaco editor
    if (this.monacoIntegration) {
      try {
        this.monacoIntegration.setReadOnly(filePath, false);
        this.monacoIntegration.showProgress('Work completed', 100);
      } catch (error) {
        console.error('LiveCodingService: Error completing work in Monaco:', error);
      }
    }

    // Emit completion update
    this.emitUpdate({
      type: 'completion',
      agentId,
      taskId,
      filePath,
      data: {
        progress: 100,
        message: `Agent ${agentId} completed work on ${filePath}`,
        timestamp: Date.now()
      }
    });
  }

  /**
   * ✅ PHASE 3: Get active files being worked on
   */
  public getActiveFiles(): Array<{ filePath: string; content: string; lastUpdate: number; progress?: number }> {
    return Array.from(this.activeFiles.entries()).map(([filePath, fileData]) => ({
      filePath,
      content: fileData.content,
      lastUpdate: fileData.lastUpdate,
      progress: this.streamingProgress.get(filePath)
    }));
  }

  /**
   * ✅ PHASE 3: Get streaming progress for all files
   */
  public getStreamingProgress(): Record<string, number> {
    return Object.fromEntries(this.streamingProgress);
  }

  /**
   * ✅ PHASE 3: Clear all active work
   */
  public clearActiveWork(): void {
    this.activeFiles.clear();
    this.streamingProgress.clear();
    console.log('LiveCodingService: Cleared all active work');
  }

  /**
   * Private method to emit updates to listeners
   */
  private emitUpdate(update: LiveCodingUpdate): void {
    this.listeners.forEach(listener => {
      try {
        listener(update);
      } catch (error) {
        console.error('LiveCodingService: Error in listener:', error);
      }
    });
  }

  /**
   * ✅ PHASE 3: Check if file is currently being worked on
   */
  public isFileActive(filePath: string): boolean {
    return this.activeFiles.has(filePath);
  }

  /**
   * ✅ PHASE 3: Get file progress
   */
  public getFileProgress(filePath: string): number | null {
    return this.streamingProgress.get(filePath) || null;
  }
}

// Export singleton instance
export const liveCodingService = LiveCodingService.getInstance();
