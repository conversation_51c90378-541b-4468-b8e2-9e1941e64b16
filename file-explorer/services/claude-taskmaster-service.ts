// services/claude-taskmaster-service.ts

export interface TaskmasterConfig {
  models: {
    main: {
      provider: string;
      modelId: string;
      maxTokens: number;
      temperature: number;
      baseURL?: string;
    };
    research: {
      provider: string;
      modelId: string;
      maxTokens: number;
      temperature: number;
      baseURL?: string;
    };
    fallback: {
      provider: string;
      modelId: string;
      maxTokens: number;
      temperature: number;
      baseURL?: string;
    };
  };
  global: {
    logLevel: string;
    debug: boolean;
    defaultSubtasks: number;
    defaultPriority: string;
    projectName: string;
  };
}

export interface TaskmasterParseResult {
  success: boolean;
  tasksGenerated?: number;
  error?: string;
  tasksFilePath?: string;
}

export interface TaskmasterInitResult {
  success: boolean;
  error?: string;
  configPath?: string;
}

export interface TaskmasterTask {
  id: string;
  title: string;
  description: string;
  status: 'pending' | 'in-progress' | 'done' | 'blocked';
  priority: 'low' | 'medium' | 'high';
  dependencies: string[];
  subtasks?: TaskmasterTask[];
  testStrategy?: string;
}

class ClaudeTaskmasterService {
  private isElectronAvailable(): boolean {
    return typeof window !== 'undefined' && 
           window.electronAPI?.taskmaster !== undefined;
  }

  /**
   * Initialize Claude Taskmaster in a project directory
   */
  async initializeProject(projectPath: string, projectName: string): Promise<TaskmasterInitResult> {
    if (!this.isElectronAvailable()) {
      return {
        success: false,
        error: 'Electron API not available. Taskmaster initialization requires desktop app.'
      };
    }

    try {
      const result = await window.electronAPI.taskmaster.init(projectPath, projectName);
      return result;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error during initialization'
      };
    }
  }

  /**
   * Configure Claude Taskmaster with API keys and model settings
   */
  async configureTaskmaster(
    projectPath: string, 
    anthropicApiKey: string, 
    perplexityApiKey: string,
    config?: Partial<TaskmasterConfig>
  ): Promise<{ success: boolean; error?: string }> {
    if (!this.isElectronAvailable()) {
      return {
        success: false,
        error: 'Electron API not available. Taskmaster configuration requires desktop app.'
      };
    }

    try {
      const result = await window.electronAPI.taskmaster.configure(
        projectPath,
        anthropicApiKey,
        perplexityApiKey,
        config
      );
      return result;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error during configuration'
      };
    }
  }

  /**
   * Parse a PRD file and generate tasks using Claude Taskmaster
   */
  async parsePRD(
    projectPath: string, 
    prdContent: string, 
    prdFileName: string = 'prd.txt'
  ): Promise<TaskmasterParseResult> {
    if (!this.isElectronAvailable()) {
      return {
        success: false,
        error: 'Electron API not available. PRD parsing requires desktop app.'
      };
    }

    try {
      const result = await window.electronAPI.taskmaster.parsePRD(
        projectPath,
        prdContent,
        prdFileName
      );
      return result;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error during PRD parsing'
      };
    }
  }

  /**
   * Generate individual task files from tasks.json
   */
  async generateTaskFiles(projectPath: string): Promise<{ success: boolean; error?: string }> {
    if (!this.isElectronAvailable()) {
      return {
        success: false,
        error: 'Electron API not available. Task file generation requires desktop app.'
      };
    }

    try {
      const result = await window.electronAPI.taskmaster.generate(projectPath);
      return result;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error during task file generation'
      };
    }
  }

  /**
   * List all tasks from the project
   */
  async listTasks(projectPath: string): Promise<{ success: boolean; tasks?: TaskmasterTask[]; error?: string }> {
    if (!this.isElectronAvailable()) {
      return {
        success: false,
        error: 'Electron API not available. Task listing requires desktop app.'
      };
    }

    try {
      const result = await window.electronAPI.taskmaster.list(projectPath);
      return result;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error during task listing'
      };
    }
  }

  /**
   * Get the next task to work on
   */
  async getNextTask(projectPath: string): Promise<{ success: boolean; task?: TaskmasterTask; error?: string }> {
    if (!this.isElectronAvailable()) {
      return {
        success: false,
        error: 'Electron API not available. Next task retrieval requires desktop app.'
      };
    }

    try {
      const result = await window.electronAPI.taskmaster.next(projectPath);
      return result;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error during next task retrieval'
      };
    }
  }

  /**
   * Check if Claude Taskmaster is installed and available
   */
  async checkInstallation(): Promise<{ installed: boolean; version?: string; error?: string }> {
    if (!this.isElectronAvailable()) {
      return {
        installed: false,
        error: 'Electron API not available. Installation check requires desktop app.'
      };
    }

    try {
      const result = await window.electronAPI.taskmaster.checkInstallation();
      return result;
    } catch (error) {
      return {
        installed: false,
        error: error instanceof Error ? error.message : 'Unknown error during installation check'
      };
    }
  }

  /**
   * Install Claude Taskmaster globally
   */
  async installTaskmaster(): Promise<{ success: boolean; error?: string }> {
    if (!this.isElectronAvailable()) {
      return {
        success: false,
        error: 'Electron API not available. Installation requires desktop app.'
      };
    }

    try {
      const result = await window.electronAPI.taskmaster.install();
      return result;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error during installation'
      };
    }
  }

  /**
   * Validate Claude Taskmaster CLI functionality
   */
  async validateCLI(projectPath: string): Promise<{ isWorking: boolean; error?: string }> {
    if (!this.isElectronAvailable()) {
      return {
        isWorking: false,
        error: 'Electron API not available. CLI validation requires desktop app.'
      };
    }

    try {
      const result = await window.electronAPI.taskmaster.validateCLI(projectPath);
      return result;
    } catch (error) {
      return {
        isWorking: false,
        error: error instanceof Error ? error.message : 'Unknown error during CLI validation'
      };
    }
  }

  /**
   * Get default configuration for Claude Taskmaster
   */
  getDefaultConfig(projectName: string): TaskmasterConfig {
    return {
      models: {
        main: {
          provider: 'anthropic',
          modelId: 'claude-3-5-sonnet-20241022',
          maxTokens: 64000,
          temperature: 0.2,
          baseURL: 'https://api.anthropic.com/v1'
        },
        research: {
          provider: 'perplexity',
          modelId: 'sonar-pro',
          maxTokens: 8700,
          temperature: 0.1,
          baseURL: 'https://api.perplexity.ai/v1'
        },
        fallback: {
          provider: 'anthropic',
          modelId: 'claude-3-haiku-20240307',
          maxTokens: 64000,
          temperature: 0.2,
          baseURL: 'https://api.anthropic.com/v1'
        }
      },
      global: {
        logLevel: 'info',
        debug: false,
        defaultSubtasks: 5,
        defaultPriority: 'medium',
        projectName: projectName
      }
    };
  }
}

export const claudeTaskmasterService = new ClaudeTaskmasterService();
