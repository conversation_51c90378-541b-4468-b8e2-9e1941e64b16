// services/task-state-service.ts
// ✅ PART 2: Dynamic Agent Reassignment Service

import { kanbanTaskOrchestrator } from '../components/orchestrators/kanban-task-orchestrator';

export interface TaskState {
  id: string;
  title: string;
  description: string;
  agent: string;
  status: 'pending' | 'delegated' | 'running' | 'verifying' | 'finalizing' | 'completed' | 'failed';
  priority: 'low' | 'medium' | 'high';
  laneId: string;
  columnId: string;
  metadata: Record<string, any>;
  lastUpdated: number;
}

export interface TaskUpdateEvent {
  taskId: string;
  previousState: TaskState;
  newState: TaskState;
  changeType: 'agent' | 'status' | 'priority' | 'metadata';
  timestamp: number;
}

export type TaskStateListener = (event: TaskUpdateEvent) => void;

export class TaskStateService {
  private static instance: TaskStateService;
  private tasks = new Map<string, TaskState>();
  private listeners = new Set<TaskStateListener>();

  private constructor() {
    console.log('TaskStateService: Initialized');
  }

  public static getInstance(): TaskStateService {
    if (!TaskStateService.instance) {
      TaskStateService.instance = new TaskStateService();
    }
    return TaskStateService.instance;
  }

  /**
   * ✅ PART 2: Add task state listener
   */
  public addListener(listener: TaskStateListener): () => void {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  /**
   * ✅ PART 2: Update task state with reactive agent change detection
   */
  public async updateTask(taskId: string, updates: Partial<TaskState>): Promise<boolean> {
    const currentTask = this.tasks.get(taskId);
    if (!currentTask) {
      console.warn(`TaskStateService: Task ${taskId} not found`);
      return false;
    }

    const previousState = { ...currentTask };
    const newState: TaskState = {
      ...currentTask,
      ...updates,
      lastUpdated: Date.now()
    };

    // ✅ PART 2: Detect agent change and trigger lane reassignment
    if (previousState.agent !== newState.agent) {
      console.log(`🔄 TaskStateService: Agent change detected for task ${taskId}: ${previousState.agent} → ${newState.agent}`);
      
      // Update lane assignment
      const newLaneId = `lane-${newState.agent}`;
      newState.laneId = newLaneId;
      
      // Move card to new swimlane
      try {
        await kanbanTaskOrchestrator.updateCardLane(taskId, newState.agent);
        console.log(`✅ TaskStateService: Card ${taskId} moved to agent ${newState.agent} lane`);
      } catch (error) {
        console.error(`❌ TaskStateService: Failed to move card ${taskId} to new lane:`, error);
        return false;
      }
    }

    // Update stored state
    this.tasks.set(taskId, newState);

    // Determine change type
    let changeType: TaskUpdateEvent['changeType'] = 'metadata';
    if (previousState.agent !== newState.agent) {
      changeType = 'agent';
    } else if (previousState.status !== newState.status) {
      changeType = 'status';
    } else if (previousState.priority !== newState.priority) {
      changeType = 'priority';
    }

    // Notify listeners
    const event: TaskUpdateEvent = {
      taskId,
      previousState,
      newState,
      changeType,
      timestamp: Date.now()
    };

    this.notifyListeners(event);

    console.log(`TaskStateService: Updated task ${taskId} (${changeType} change)`);
    return true;
  }

  /**
   * ✅ PART 2: Set task state (for initial creation)
   */
  public setTask(task: TaskState): void {
    this.tasks.set(task.id, task);
    console.log(`TaskStateService: Set task ${task.id} with agent ${task.agent}`);
  }

  /**
   * ✅ PART 2: Get task state
   */
  public getTask(taskId: string): TaskState | undefined {
    return this.tasks.get(taskId);
  }

  /**
   * ✅ PART 2: Get all tasks
   */
  public getAllTasks(): TaskState[] {
    return Array.from(this.tasks.values());
  }

  /**
   * ✅ PART 2: Get tasks by agent
   */
  public getTasksByAgent(agentId: string): TaskState[] {
    return Array.from(this.tasks.values()).filter(task => task.agent === agentId);
  }

  /**
   * ✅ PART 2: Bulk update tasks (for orchestration initialization)
   */
  public initializeTasks(tasks: TaskState[]): void {
    this.tasks.clear();
    tasks.forEach(task => {
      this.tasks.set(task.id, task);
    });
    console.log(`TaskStateService: Initialized ${tasks.length} tasks`);
  }

  /**
   * ✅ PART 2: Reassign task to different agent
   */
  public async reassignTask(taskId: string, newAgentId: string): Promise<boolean> {
    const task = this.tasks.get(taskId);
    if (!task) {
      console.warn(`TaskStateService: Cannot reassign task ${taskId} - not found`);
      return false;
    }

    console.log(`🔄 TaskStateService: Reassigning task ${taskId} from ${task.agent} to ${newAgentId}`);
    
    return await this.updateTask(taskId, { agent: newAgentId });
  }

  /**
   * ✅ PART 2: Update task status
   */
  public async updateTaskStatus(taskId: string, status: TaskState['status']): Promise<boolean> {
    return await this.updateTask(taskId, { status });
  }

  /**
   * ✅ PART 2: Update task priority
   */
  public async updateTaskPriority(taskId: string, priority: TaskState['priority']): Promise<boolean> {
    return await this.updateTask(taskId, { priority });
  }

  /**
   * ✅ PART 2: Get tasks filtered by visible agents
   */
  public getVisibleTasks(visibleAgents: string[]): TaskState[] {
    return Array.from(this.tasks.values()).filter(task => visibleAgents.includes(task.agent));
  }

  /**
   * ✅ PART 2: Get unique agents from all tasks
   */
  public getUniqueAgents(): string[] {
    const agents = new Set<string>();
    this.tasks.forEach(task => {
      if (task.agent && task.agent !== 'unassigned') {
        agents.add(task.agent);
      }
    });
    return Array.from(agents);
  }

  /**
   * Private method to notify all listeners
   */
  private notifyListeners(event: TaskUpdateEvent): void {
    this.listeners.forEach(listener => {
      try {
        listener(event);
      } catch (error) {
        console.error('TaskStateService: Error in listener:', error);
      }
    });
  }

  /**
   * ✅ PART 2: Clear all tasks
   */
  public clearTasks(): void {
    this.tasks.clear();
    console.log('TaskStateService: Cleared all tasks');
  }

  /**
   * ✅ PART 2: Get task count by agent
   */
  public getTaskCountByAgent(): Record<string, number> {
    const counts: Record<string, number> = {};
    this.tasks.forEach(task => {
      if (task.agent && task.agent !== 'unassigned') {
        counts[task.agent] = (counts[task.agent] || 0) + 1;
      }
    });
    return counts;
  }
}

/**
 * ✅ PHASE 1: Sequential Execution Controller
 * Enforces single-agent activation and controlled workflow progression
 */
export class SequentialExecutionController {
  private static instance: SequentialExecutionController;
  private currentActiveAgent: string | null = null;
  private currentActiveTask: string | null = null;
  private executionQueue: Array<{ taskId: string; agentId: string; context: any }> = [];
  private isExecutionActive = false;
  private listeners = new Set<(event: { type: string; data: any }) => void>();

  private constructor() {
    console.log('SequentialExecutionController: Initialized');
  }

  public static getInstance(): SequentialExecutionController {
    if (!SequentialExecutionController.instance) {
      SequentialExecutionController.instance = new SequentialExecutionController();
    }
    return SequentialExecutionController.instance;
  }

  /**
   * ✅ PHASE 1: Single-Agent Activation Policy
   */
  public async activateSingleAgent(agentId: string, taskId: string): Promise<boolean> {
    if (this.currentActiveAgent && this.currentActiveAgent !== agentId) {
      console.warn(`SequentialExecutionController: Cannot activate ${agentId} - ${this.currentActiveAgent} is currently active`);
      return false;
    }

    this.currentActiveAgent = agentId;
    this.currentActiveTask = taskId;
    this.isExecutionActive = true;

    console.log(`✅ SequentialExecutionController: Activated agent ${agentId} for task ${taskId}`);

    this.notifyListeners({
      type: 'agent_activated',
      data: { agentId, taskId, timestamp: Date.now() }
    });

    return true;
  }

  /**
   * ✅ PHASE 1: Deactivate all agents
   */
  public async deactivateAllAgents(): Promise<void> {
    const previousAgent = this.currentActiveAgent;
    const previousTask = this.currentActiveTask;

    this.currentActiveAgent = null;
    this.currentActiveTask = null;
    this.isExecutionActive = false;

    if (previousAgent) {
      console.log(`✅ SequentialExecutionController: Deactivated agent ${previousAgent}`);

      this.notifyListeners({
        type: 'agent_deactivated',
        data: { agentId: previousAgent, taskId: previousTask, timestamp: Date.now() }
      });
    }
  }

  /**
   * ✅ PHASE 1: Get current active agent
   */
  public getCurrentActiveAgent(): string | null {
    return this.currentActiveAgent;
  }

  /**
   * ✅ PHASE 1: Get current active task
   */
  public getCurrentActiveTask(): string | null {
    return this.currentActiveTask;
  }

  /**
   * ✅ PHASE 1: Check if execution is active
   */
  public isActive(): boolean {
    return this.isExecutionActive;
  }

  /**
   * ✅ PHASE 1: Add execution listener
   */
  public addListener(listener: (event: { type: string; data: any }) => void): () => void {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  /**
   * ✅ PHASE 1: Queue task for sequential execution
   */
  public queueTask(taskId: string, agentId: string, context: any): void {
    this.executionQueue.push({ taskId, agentId, context });
    console.log(`SequentialExecutionController: Queued task ${taskId} for agent ${agentId}`);
  }

  /**
   * ✅ PHASE 1: Get next task in queue
   */
  public getNextQueuedTask(): { taskId: string; agentId: string; context: any } | null {
    return this.executionQueue.shift() || null;
  }

  /**
   * ✅ PHASE 1: Clear execution queue
   */
  public clearQueue(): void {
    this.executionQueue = [];
    console.log('SequentialExecutionController: Cleared execution queue');
  }

  /**
   * ✅ PHASE 1: Get queue status
   */
  public getQueueStatus(): { active: boolean; currentAgent: string | null; queueLength: number } {
    return {
      active: this.isExecutionActive,
      currentAgent: this.currentActiveAgent,
      queueLength: this.executionQueue.length
    };
  }

  /**
   * Private method to notify listeners
   */
  private notifyListeners(event: { type: string; data: any }): void {
    this.listeners.forEach(listener => {
      try {
        listener(event);
      } catch (error) {
        console.error('SequentialExecutionController: Error in listener:', error);
      }
    });
  }
}

// Export singleton instance
export const taskStateService = TaskStateService.getInstance();
export const sequentialExecutionController = SequentialExecutionController.getInstance();
