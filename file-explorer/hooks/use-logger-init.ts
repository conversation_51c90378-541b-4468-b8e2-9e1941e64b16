/**
 * SYNAPSE LOGGER INITIALIZATION HOOK
 * React hook to initialize the logging system at application startup
 */

import { useEffect, useState } from 'react';
import { initializeSynapseLogger, logSystemStartup, ensureLogDirectoryStructure } from '../services/logger-init';

export interface LoggerInitState {
  isInitialized: boolean;
  isInitializing: boolean;
  error: string | null;
}

/**
 * Hook to initialize the Synapse logging system
 * Should be used in the main application component
 */
export function useLoggerInit(): LoggerInitState {
  const [state, setState] = useState<LoggerInitState>({
    isInitialized: false,
    isInitializing: false,
    error: null
  });

  useEffect(() => {
    let isMounted = true;

    const initializeLogger = async () => {
      if (state.isInitialized || state.isInitializing) return;

      setState(prev => ({ ...prev, isInitializing: true, error: null }));

      try {
        // Ensure log directory structure exists
        await ensureLogDirectoryStructure();

        // Initialize the logger
        await initializeSynapseLogger();

        // Log system startup
        logSystemStartup();

        if (isMounted) {
          setState({
            isInitialized: true,
            isInitializing: false,
            error: null
          });
        }
      } catch (error) {
        console.error('Failed to initialize Synapse Logger:', error);
        
        if (isMounted) {
          setState({
            isInitialized: false,
            isInitializing: false,
            error: error instanceof Error ? error.message : 'Unknown initialization error'
          });
        }
      }
    };

    initializeLogger();

    return () => {
      isMounted = false;
    };
  }, []); // Empty dependency array - only run once

  return state;
}

/**
 * Hook to get logger initialization status without triggering initialization
 */
export function useLoggerStatus(): LoggerInitState {
  const [state, setState] = useState<LoggerInitState>({
    isInitialized: false,
    isInitializing: false,
    error: null
  });

  // This hook only provides status, doesn't initialize
  // The actual initialization should be done by useLoggerInit in the main app

  return state;
}
