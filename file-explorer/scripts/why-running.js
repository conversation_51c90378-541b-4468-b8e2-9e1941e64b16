#!/usr/bin/env node

/**
 * Async Resource Leak Detection Script
 * Uses why-is-node-running to detect potential memory leaks and hanging async operations
 */

const fs = require('fs');
const path = require('path');

// Check if why-is-node-running is available
let whyIsNodeRunning;
try {
    whyIsNodeRunning = require('why-is-node-running');
} catch (error) {
    console.log('⚠️  why-is-node-running not installed. Install with: npm install --save-dev why-is-node-running');
    process.exit(1);
}

console.log('🔍 Starting async resource leak detection...');

// Track initial handles
const initialHandles = process._getActiveHandles().length;
const initialRequests = process._getActiveRequests().length;

console.log(`📊 Initial state:`);
console.log(`   Active handles: ${initialHandles}`);
console.log(`   Active requests: ${initialRequests}`);

// Simulate some async operations that might be present in the Electron app
const simulateAsyncOperations = () => {
    // File system operations
    const testFile = path.join(__dirname, '../temp-test-file.txt');
    
    // Create a file operation
    fs.writeFile(testFile, 'test content', (err) => {
        if (err) {
            console.log('File write error (expected in testing):', err.message);
        } else {
            // Clean up
            fs.unlink(testFile, () => {});
        }
    });

    // Timer operations (common source of leaks)
    const timer1 = setTimeout(() => {
        console.log('Timer 1 executed');
    }, 100);

    const timer2 = setInterval(() => {
        console.log('Interval timer executed');
        clearInterval(timer2); // Clean up immediately
    }, 200);

    // Immediate operations
    setImmediate(() => {
        console.log('Immediate operation executed');
    });

    // Process next tick
    process.nextTick(() => {
        console.log('Next tick executed');
    });

    // Clear the first timer to show proper cleanup
    clearTimeout(timer1);
};

// Run simulation
simulateAsyncOperations();

// Wait a bit for operations to complete
setTimeout(() => {
    const currentHandles = process._getActiveHandles().length;
    const currentRequests = process._getActiveRequests().length;
    
    console.log(`\n📊 Current state after operations:`);
    console.log(`   Active handles: ${currentHandles}`);
    console.log(`   Active requests: ${currentRequests}`);
    
    const handleDiff = currentHandles - initialHandles;
    const requestDiff = currentRequests - initialRequests;
    
    console.log(`\n📈 Difference:`);
    console.log(`   Handle difference: ${handleDiff}`);
    console.log(`   Request difference: ${requestDiff}`);
    
    if (handleDiff > 0 || requestDiff > 0) {
        console.log('\n⚠️  Potential async resource leaks detected!');
        console.log('🔍 Analyzing what is keeping Node.js alive...\n');
        
        // Use why-is-node-running to analyze
        whyIsNodeRunning();
    } else {
        console.log('\n✅ No obvious async resource leaks detected.');
        console.log('🔍 Running why-is-node-running for detailed analysis...\n');
        
        // Still run the analysis for completeness
        whyIsNodeRunning();
    }
    
    // Force exit after analysis
    setTimeout(() => {
        console.log('\n🏁 Analysis complete. Exiting...');
        process.exit(0);
    }, 1000);
    
}, 1000);

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
    console.error('❌ Uncaught Exception:', error.message);
    whyIsNodeRunning();
    process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
    whyIsNodeRunning();
    process.exit(1);
});

// Graceful shutdown
process.on('SIGINT', () => {
    console.log('\n🛑 Received SIGINT. Running final analysis...');
    whyIsNodeRunning();
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\n🛑 Received SIGTERM. Running final analysis...');
    whyIsNodeRunning();
    process.exit(0);
});

console.log('⏳ Waiting for async operations to complete...');
console.log('💡 Press Ctrl+C to interrupt and see current state');
