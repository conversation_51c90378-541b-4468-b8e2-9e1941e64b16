#!/bin/bash

# Electron Performance Profiling Script
# This script initiates various profiling tools for the Electron application

set -e

echo "🔍 Starting Electron Performance Profiling..."

# Create profiling output directory
PROFILE_DIR="./reports/profiling-$(date +%Y%m%d-%H%M%S)"
mkdir -p "$PROFILE_DIR"

echo "📁 Profile results will be saved to: $PROFILE_DIR"

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to run profiling with timeout
run_with_timeout() {
    local timeout_duration=$1
    local command_name=$2
    shift 2
    
    echo "⏱️  Running $command_name (timeout: ${timeout_duration}s)..."
    timeout "$timeout_duration" "$@" || echo "⚠️  $command_name timed out or failed"
}

# 1. Memory Usage Baseline
echo "📊 Capturing memory baseline..."
if command_exists ps; then
    ps aux | grep -E "(electron|node)" | grep -v grep > "$PROFILE_DIR/memory-baseline.txt" || true
fi

# 2. Bundle Size Analysis
echo "📦 Analyzing bundle sizes..."
if [ -d "./out" ]; then
    du -sh ./out/* > "$PROFILE_DIR/bundle-sizes.txt" 2>/dev/null || true
fi

if [ -d "./dist-electron" ]; then
    du -sh ./dist-electron/* > "$PROFILE_DIR/electron-bundle-sizes.txt" 2>/dev/null || true
fi

# 3. Node.js Memory Leak Detection
echo "🔍 Checking for async leaks..."
if [ -f "./scripts/why-running.js" ]; then
    run_with_timeout 30 "why-is-node-running" node ./scripts/why-running.js > "$PROFILE_DIR/async-leaks.txt" 2>&1
fi

# 4. File System Analysis
echo "📁 Analyzing file system usage..."
find . -name "*.js" -o -name "*.ts" -o -name "*.tsx" | grep -v node_modules | xargs wc -l | sort -nr | head -20 > "$PROFILE_DIR/largest-source-files.txt"

# 5. Dependency Analysis
echo "📋 Analyzing dependencies..."
if [ -f "package.json" ]; then
    if command_exists jq; then
        jq '.dependencies | keys | length' package.json > "$PROFILE_DIR/dependency-count.txt" 2>/dev/null || true
        jq '.devDependencies | keys | length' package.json >> "$PROFILE_DIR/dependency-count.txt" 2>/dev/null || true
    fi
fi

# 6. Build Time Measurement
echo "⏱️  Measuring build time..."
if [ -f "package.json" ]; then
    echo "Starting build time measurement..." > "$PROFILE_DIR/build-time.txt"
    start_time=$(date +%s)
    
    # Clean build
    npm run clean >/dev/null 2>&1 || true
    
    # Measure compilation time
    { time npm run compile:electron; } 2>> "$PROFILE_DIR/build-time.txt" || true
    
    end_time=$(date +%s)
    build_duration=$((end_time - start_time))
    echo "Total build duration: ${build_duration} seconds" >> "$PROFILE_DIR/build-time.txt"
fi

# 7. Chrome DevTools CPU Profile (if running)
echo "🖥️  Checking for running Electron processes..."
if pgrep -f "electron" > /dev/null; then
    echo "Electron process detected. Manual DevTools profiling recommended." > "$PROFILE_DIR/devtools-instructions.txt"
    echo "1. Open DevTools in the Electron app" >> "$PROFILE_DIR/devtools-instructions.txt"
    echo "2. Go to Performance tab" >> "$PROFILE_DIR/devtools-instructions.txt"
    echo "3. Record for 30 seconds during normal usage" >> "$PROFILE_DIR/devtools-instructions.txt"
    echo "4. Save the profile as 'cpu-profile.json'" >> "$PROFILE_DIR/devtools-instructions.txt"
else
    echo "No running Electron process found." > "$PROFILE_DIR/devtools-instructions.txt"
fi

# 8. Generate summary report
echo "📋 Generating summary report..."
cat > "$PROFILE_DIR/profiling-summary.md" << EOF
# Electron Performance Profiling Summary

**Profile Date:** $(date)
**Profile Directory:** $PROFILE_DIR

## Files Generated

- \`memory-baseline.txt\` - Current memory usage by Electron/Node processes
- \`bundle-sizes.txt\` - Size analysis of output bundles
- \`electron-bundle-sizes.txt\` - Size analysis of Electron bundles
- \`async-leaks.txt\` - Async resource leak detection
- \`largest-source-files.txt\` - Top 20 largest source files
- \`dependency-count.txt\` - Dependency count analysis
- \`build-time.txt\` - Build time measurements
- \`devtools-instructions.txt\` - Manual profiling instructions

## Next Steps

1. Review the generated files for performance bottlenecks
2. Use Chrome DevTools for detailed CPU/memory profiling
3. Consider code splitting for large files (>1000 lines)
4. Analyze bundle sizes for optimization opportunities

## Baseline Metrics Template

Use the performance-baseline-template.md to record actual measurements.
EOF

echo "✅ Profiling complete! Results saved to: $PROFILE_DIR"
echo "📖 Review the summary at: $PROFILE_DIR/profiling-summary.md"
