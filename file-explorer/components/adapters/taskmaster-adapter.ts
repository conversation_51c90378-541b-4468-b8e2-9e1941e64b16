// components/adapters/taskmaster-adapter.ts
import { activeProjectService } from '../../services/active-project-service';

export interface AgentTask {
  id: string;
  title: string;
  description: string;
  assignedAgentId: string;
  dependencies: string[];
  priority: 'low' | 'medium' | 'high';
  // Additional metadata from Taskmaster
  module?: string;
  milestone?: string;
  phase?: string;
  estimatedHours?: number;
  complexity?: 'simple' | 'moderate' | 'complex';
  tags?: string[];
  acceptanceCriteria?: string[];
  subtasks?: string[];
  dueDate?: string;
  storyPoints?: number;
}

export interface TaskmasterData {
  tasks: AgentTask[];
  metadata?: {
    projectName?: string;
    version?: string;
    generatedAt?: string;
    totalTasks?: number;
    modules?: string[];
    milestones?: string[];
  };
}

export interface TaskLoadResult {
  success: boolean;
  data?: TaskmasterData;
  error?: string;
  filePath?: string;
}

export class TaskmasterAdapter {
  private static instance: TaskmasterAdapter;
  private cachedTasks: TaskmasterData | null = null;
  private lastLoadTime: number = 0;
  private readonly CACHE_DURATION = 30000; // 30 seconds

  private constructor() {}

  public static getInstance(): TaskmasterAdapter {
    if (!TaskmasterAdapter.instance) {
      TaskmasterAdapter.instance = new TaskmasterAdapter();
    }
    return TaskmasterAdapter.instance;
  }

  /**
   * ✅ Load and parse tasks.json from current project's .taskmaster directory
   */
  public async loadTasks(projectPath?: string, forceReload = false): Promise<TaskLoadResult> {
    try {
      // Use cache if available and not expired
      if (!forceReload && this.cachedTasks && (Date.now() - this.lastLoadTime) < this.CACHE_DURATION) {
        console.log('📋 TaskmasterAdapter: Using cached tasks');
        return {
          success: true,
          data: this.cachedTasks
        };
      }

      const targetProjectPath = projectPath || activeProjectService.getActiveProject()?.path;

      if (!targetProjectPath) {
        return {
          success: false,
          error: 'No active project selected. Cannot load Taskmaster tasks.'
        };
      }

      // ✅ FIXED: Check the correct location where Claude Taskmaster creates tasks.json
      const tasksFilePath = `${targetProjectPath}/.taskmaster/tasks/tasks.json`;

      if (typeof window !== 'undefined' && window.electronAPI) {
        console.log(`📋 TaskmasterAdapter: Loading tasks from ${tasksFilePath}`);

        const tasksFile = await window.electronAPI.readFile(tasksFilePath);

        if (!tasksFile.success) {
          return {
            success: false,
            error: `Tasks file not found: ${tasksFilePath}. Run Taskmaster CLI to generate tasks first.`,
            filePath: tasksFilePath
          };
        }

        try {
          const rawData = JSON.parse(tasksFile.content);
          const validatedData = this.validateAndNormalizeTasks(rawData);

          // Cache the validated data
          this.cachedTasks = validatedData;
          this.lastLoadTime = Date.now();

          console.log(`✅ TaskmasterAdapter: Loaded ${validatedData.tasks.length} tasks successfully`);

          return {
            success: true,
            data: validatedData,
            filePath: tasksFilePath
          };
        } catch (parseError) {
          return {
            success: false,
            error: `Invalid JSON in tasks file: ${parseError instanceof Error ? parseError.message : 'Parse error'}`,
            filePath: tasksFilePath
          };
        }
      } else {
        return {
          success: false,
          error: 'File system operations not available (Electron API required)'
        };
      }
    } catch (error) {
      console.error('TaskmasterAdapter: Error loading tasks:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * ✅ Validate and normalize tasks from Taskmaster JSON
   */
  private validateAndNormalizeTasks(rawData: any): TaskmasterData {
    if (!rawData || typeof rawData !== 'object') {
      throw new Error('Invalid tasks data: must be an object');
    }

    let tasks: any[] = [];
    let metadata: any = {};

    // Handle different JSON structures from Taskmaster
    if (Array.isArray(rawData)) {
      // Simple array of tasks
      tasks = rawData;
    } else if (rawData.tasks && Array.isArray(rawData.tasks)) {
      // Object with tasks array
      tasks = rawData.tasks;
      metadata = rawData.metadata || {};
    } else if (rawData.project && rawData.project.tasks) {
      // Nested project structure
      tasks = rawData.project.tasks;
      metadata = rawData.project.metadata || rawData.metadata || {};
    } else {
      // Try to extract tasks from object values
      const possibleTasks = Object.values(rawData).filter(item =>
        typeof item === 'object' && item !== null && 'title' in item
      );
      if (possibleTasks.length > 0) {
        tasks = possibleTasks;
      } else {
        throw new Error('No valid tasks found in JSON structure');
      }
    }

    if (!Array.isArray(tasks) || tasks.length === 0) {
      throw new Error('No tasks found in Taskmaster data');
    }

    // Validate and normalize each task
    const validatedTasks: AgentTask[] = tasks.map((task, index) => {
      return this.validateAndNormalizeTask(task, index);
    });

    // Extract metadata
    const normalizedMetadata = {
      projectName: metadata.projectName || metadata.name || 'Unknown Project',
      version: metadata.version || '1.0.0',
      generatedAt: metadata.generatedAt || metadata.timestamp || new Date().toISOString(),
      totalTasks: validatedTasks.length,
      modules: this.extractUniqueValues(validatedTasks, 'module'),
      milestones: this.extractUniqueValues(validatedTasks, 'milestone')
    };

    return {
      tasks: validatedTasks,
      metadata: normalizedMetadata
    };
  }

  /**
   * ✅ Validate and normalize individual task
   */
  private validateAndNormalizeTask(task: any, index: number): AgentTask {
    if (!task || typeof task !== 'object') {
      throw new Error(`Task ${index} is not a valid object`);
    }

    // Required fields
    const id = task.id || task.taskId || `task-${Date.now()}-${index}`;
    const title = task.title || task.name || task.summary || `Task ${index + 1}`;
    const description = task.description || task.details || task.content || title;

    // ✅ CRITICAL FIX: Handle agent assignment properly
    // Extract agent assignment from task data or use intelligent assignment
    let assignedAgentId = task.assignedAgentId || task.agent || task.assignedAgent;

    // If no agent assigned, use intelligent assignment based on task characteristics
    if (!assignedAgentId || assignedAgentId === 'unassigned') {
      assignedAgentId = this.intelligentAgentAssignment(task, title, description);
    }

    // Dependencies
    const dependencies = this.normalizeDependencies(task.dependencies || task.dependsOn || task.requires || []);

    // Priority
    const priority = this.normalizePriority(task.priority || task.importance || 'medium');

    // Optional fields
    const module = task.module || task.component || task.area;
    const milestone = task.milestone || task.epic || task.release;
    const phase = task.phase || task.stage || task.status;
    const estimatedHours = this.parseNumber(task.estimatedHours || task.effort || task.hours);
    const complexity = this.normalizeComplexity(task.complexity || task.difficulty);
    const tags = this.normalizeTags(task.tags || task.labels || []);
    const acceptanceCriteria = this.normalizeStringArray(task.acceptanceCriteria || task.criteria || task.acceptance || []);
    const subtasks = this.normalizeStringArray(task.subtasks || task.steps || task.checklist || []);
    const dueDate = task.dueDate || task.deadline || task.targetDate;
    const storyPoints = this.parseNumber(task.storyPoints || task.points || task.estimate);

    return {
      id,
      title,
      description,
      assignedAgentId,
      dependencies,
      priority,
      module,
      milestone,
      phase,
      estimatedHours,
      complexity,
      tags,
      acceptanceCriteria,
      subtasks,
      dueDate,
      storyPoints
    };
  }

  /**
   * ✅ Normalize agent ID to match our agent system
   */
  private normalizeAgentId(agentName: string): string {
    const agentMap: Record<string, string> = {
      'intern': 'intern',
      'junior': 'junior',
      'mid': 'midlevel',
      'midlevel': 'midlevel',
      'senior': 'senior',
      'architect': 'architect',
      'designer': 'designer',
      'tester': 'tester',
      'researcher': 'researcher',
      'micromanager': 'micromanager',
      // Common variations
      'dev': 'midlevel',
      'developer': 'midlevel',
      'qa': 'tester',
      'test': 'tester',
      'design': 'designer',
      'ui': 'designer',
      'ux': 'designer',
      'backend': 'senior',
      'frontend': 'midlevel',
      'fullstack': 'senior'
    };

    const normalized = agentName.toLowerCase().trim();
    return agentMap[normalized] || 'midlevel'; // Default to midlevel
  }

  /**
   * ARCHITECTURE RESTORATION: REMOVED unauthorized agent assignment logic
   * Agent assignment is ONLY allowed through Micromanager delegation
   */
  private inferAgentFromTask(task: any): string {
    // ARCHITECTURE VIOLATION REMOVED: No agent assignment logic allowed here
    // All agent assignment must go through Micromanager
    return 'unassigned'; // Will be properly assigned by Micromanager
  }

  /**
   * ✅ Normalize dependencies array
   */
  private normalizeDependencies(deps: any): string[] {
    if (!deps) return [];
    if (typeof deps === 'string') return [deps];
    if (Array.isArray(deps)) return deps.filter(d => typeof d === 'string');
    return [];
  }

  /**
   * ✅ Normalize priority value
   */
  private normalizePriority(priority: any): 'low' | 'medium' | 'high' {
    if (typeof priority !== 'string') return 'medium';

    const p = priority.toLowerCase();
    if (p.includes('high') || p.includes('urgent') || p.includes('critical')) return 'high';
    if (p.includes('low') || p.includes('minor')) return 'low';
    return 'medium';
  }

  /**
   * ✅ Normalize complexity value
   */
  private normalizeComplexity(complexity: any): 'simple' | 'moderate' | 'complex' | undefined {
    if (typeof complexity !== 'string') return undefined;

    const c = complexity.toLowerCase();
    if (c.includes('simple') || c.includes('easy') || c.includes('basic')) return 'simple';
    if (c.includes('complex') || c.includes('hard') || c.includes('difficult') || c.includes('advanced')) return 'complex';
    if (c.includes('moderate') || c.includes('medium') || c.includes('normal')) return 'moderate';
    return undefined;
  }

  /**
   * ✅ Normalize tags array
   */
  private normalizeTags(tags: any): string[] {
    if (!tags) return [];
    if (typeof tags === 'string') return tags.split(',').map(t => t.trim()).filter(Boolean);
    if (Array.isArray(tags)) return tags.filter(t => typeof t === 'string').map(t => t.trim()).filter(Boolean);
    return [];
  }

  /**
   * ✅ Normalize string array
   */
  private normalizeStringArray(arr: any): string[] {
    if (!arr) return [];
    if (typeof arr === 'string') return [arr];
    if (Array.isArray(arr)) return arr.filter(item => typeof item === 'string');
    return [];
  }

  /**
   * ✅ Parse number safely
   */
  private parseNumber(value: any): number | undefined {
    if (typeof value === 'number') return value;
    if (typeof value === 'string') {
      const parsed = parseFloat(value);
      return isNaN(parsed) ? undefined : parsed;
    }
    return undefined;
  }

  /**
   * ✅ Extract unique values from tasks for a specific field
   */
  private extractUniqueValues(tasks: AgentTask[], field: keyof AgentTask): string[] {
    const values = tasks
      .map(task => task[field])
      .filter((value): value is string => typeof value === 'string' && value.length > 0);

    return Array.from(new Set(values)).sort();
  }

  /**
   * ✅ Get cached tasks without reloading
   */
  public getCachedTasks(): TaskmasterData | null {
    return this.cachedTasks;
  }

  /**
   * ✅ CRITICAL FIX: Intelligent agent assignment based on task characteristics
   */
  private intelligentAgentAssignment(task: any, title: string, description: string): string {
    const titleLower = title.toLowerCase();
    const descriptionLower = description.toLowerCase();
    const combined = `${titleLower} ${descriptionLower}`;

    // Architecture and high-level design tasks
    if (combined.includes('architecture') || combined.includes('design system') ||
        combined.includes('framework') || combined.includes('infrastructure') ||
        combined.includes('scalability') || combined.includes('performance optimization')) {
      return 'senior';
    }

    // Complex implementation tasks
    if (combined.includes('complex') || combined.includes('integration') ||
        combined.includes('api design') || combined.includes('database') ||
        combined.includes('security') || combined.includes('optimization') ||
        task.complexity === 'high' || task.complexity === 'very_high') {
      return 'senior';
    }

    // Mid-level tasks
    if (combined.includes('component') || combined.includes('feature') ||
        combined.includes('service') || combined.includes('module') ||
        combined.includes('refactor') || combined.includes('enhancement') ||
        task.complexity === 'medium') {
      return 'midlevel';
    }

    // Simple implementation tasks
    if (combined.includes('simple') || combined.includes('basic') ||
        combined.includes('utility') || combined.includes('helper') ||
        combined.includes('template') || combined.includes('boilerplate') ||
        task.complexity === 'low') {
      return 'junior';
    }

    // Very simple tasks
    if (combined.includes('documentation') || combined.includes('readme') ||
        combined.includes('comments') || combined.includes('formatting') ||
        combined.includes('cleanup') || combined.includes('organize')) {
      return 'intern';
    }

    // Default assignment based on estimated effort
    const estimatedHours = task.estimatedHours || task.effort || task.hours || 0;
    if (estimatedHours > 8) return 'senior';
    if (estimatedHours > 4) return 'midlevel';
    if (estimatedHours > 1) return 'junior';

    // Default fallback
    return 'junior';
  }

  /**
   * ✅ Clear cache to force reload on next request
   */
  public clearCache(): void {
    this.cachedTasks = null;
    this.lastLoadTime = 0;
  }

  /**
   * ✅ Check if tasks file exists in current project
   */
  public async hasTasksFile(projectPath?: string): Promise<boolean> {
    try {
      const targetProjectPath = projectPath || activeProjectService.getActiveProject()?.path;
      if (!targetProjectPath) return false;

      // ✅ FIXED: Check the correct location where Claude Taskmaster creates tasks.json
      const tasksFilePath = `${targetProjectPath}/.taskmaster/tasks/tasks.json`;

      if (typeof window !== 'undefined' && window.electronAPI) {
        const result = await window.electronAPI.readFile(tasksFilePath);
        return result.success;
      }

      return false;
    } catch (error) {
      return false;
    }
  }
}

// Export singleton instance
export const taskmasterAdapter = TaskmasterAdapter.getInstance();
