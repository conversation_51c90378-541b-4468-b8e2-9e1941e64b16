/**
 * Orchestration Dialog Component
 * Handles Taskmaster task orchestration
 */

import { Button } from "@/components/ui/button"
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog"
import { TaskmasterOrchestrationUI } from "../../orchestrators/taskmaster-orchestration-ui"
import { OrchestrationDialogProps } from "../types"

export const OrchestrationDialog = ({
  open,
  onOpenChange,
  onTasksSubmitted
}: OrchestrationDialogProps) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Taskmaster Task Orchestration</DialogTitle>
          <p className="text-sm text-muted-foreground">
            Convert Taskmaster tasks into Kanban boards with automatic agent assignments and real-time execution.
          </p>
        </DialogHeader>
        <div className="py-4">
          <TaskmasterOrchestrationUI
            onTasksSubmittedToMicromanager={onTasksSubmitted}
            className="max-h-[60vh] overflow-y-auto"
          />
        </div>
        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={() => onOpenChange(false)}
          >
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
