/**
 * Unsaved Changes Dialog Component
 * Handles confirmation when switching projects with unsaved changes
 */

import { Save } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog"
import { UnsavedChangesDialogProps } from "../types"

export const UnsavedChangesDialog = ({
  open,
  onOpenChange,
  onConfirm
}: UnsavedChangesDialogProps) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Unsaved Changes</DialogTitle>
        </DialogHeader>
        <div className="py-4">
          <p className="text-sm text-muted-foreground">
            You have unsaved changes in the editor. Would you like to save them before switching projects?
          </p>
        </div>
        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={() => onConfirm(false)}
          >
            Don't Save
          </Button>
          <Button
            type="button"
            variant="outline"
            onClick={() => onOpenChange(false)}
          >
            Cancel
          </Button>
          <Button
            type="button"
            onClick={() => onConfirm(true)}
          >
            <Save className="h-4 w-4 mr-2" />
            Save & Switch
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
