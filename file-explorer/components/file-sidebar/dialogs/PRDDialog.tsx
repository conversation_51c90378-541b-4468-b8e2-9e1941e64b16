/**
 * PRD Dialog Component
 * Handles PRD upload and validation during project creation
 */

import { Button } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog"
import { PRDUploadUI } from "../../intake/prd-upload-ui"
import { PRDDialogProps } from "../types"

export const PRDDialog = ({
  open,
  onOpenChange,
  currentProjectPath,
  prdValidated,
  onPRDUploaded,
  onPRDParsed,
  onValidationChange,
  onCancel
}: PRDDialogProps) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Upload Project Requirements Document (PRD)</DialogTitle>
          <p className="text-sm text-muted-foreground">
            A valid PRD is required before creating the project. This ensures proper task orchestration and project structure.
          </p>
        </DialogHeader>
        <div className="py-4">
          <PRDUploadUI
            onPRDUploaded={onPRDUploaded}
            onPRDParsed={onPRDParsed}
            onValidationChange={onValidationChange}
            projectPath={currentProjectPath || undefined}
            className="max-h-[60vh] overflow-y-auto"
          />
        </div>
        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
          >
            Cancel Project Creation
          </Button>
          <div className="text-sm text-muted-foreground">
            {prdValidated ?
              "✅ PRD validated. Click 'Parse PRD with Taskmaster' to continue." :
              "❌ Please upload and validate a PRD first."
            }
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
