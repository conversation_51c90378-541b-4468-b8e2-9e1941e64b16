/**
 * Explorer Settings Dialog Component
 * Handles file explorer display and behavior settings
 */

import { Button } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { ExplorerSettingsDialogProps } from "../types"

export const ExplorerSettingsDialog = ({
  open,
  onOpenChange,
  settings,
  onSettingsChange,
  onSave
}: ExplorerSettingsDialogProps) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Explorer Settings</DialogTitle>
          <p className="text-sm text-muted-foreground">
            Configure how files and folders are displayed in the Explorer
          </p>
        </DialogHeader>
        <div className="py-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Display Options</CardTitle>
              <CardDescription>Control what is shown in the file tree</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <Label htmlFor="show-hidden">Show Hidden Files</Label>
                <Switch
                  id="show-hidden"
                  checked={settings.showHiddenFiles}
                  onCheckedChange={(checked) =>
                    onSettingsChange({ showHiddenFiles: checked })
                  }
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="show-extensions">Show File Extensions</Label>
                <Switch
                  id="show-extensions"
                  checked={settings.showFileExtensions}
                  onCheckedChange={(checked) =>
                    onSettingsChange({ showFileExtensions: checked })
                  }
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="show-icons">Show File Icons</Label>
                <Switch
                  id="show-icons"
                  checked={settings.showFileIcons}
                  onCheckedChange={(checked) =>
                    onSettingsChange({ showFileIcons: checked })
                  }
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="auto-expand">Auto Expand Folders</Label>
                <Switch
                  id="auto-expand"
                  checked={settings.autoExpandFolders}
                  onCheckedChange={(checked) =>
                    onSettingsChange({ autoExpandFolders: checked })
                  }
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="compact-view">Compact View</Label>
                <Switch
                  id="compact-view"
                  checked={settings.compactView}
                  onCheckedChange={(checked) =>
                    onSettingsChange({ compactView: checked })
                  }
                />
              </div>
            </CardContent>
          </Card>
        </div>
        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={() => onOpenChange(false)}
          >
            Close
          </Button>
          <Button
            type="button"
            onClick={onSave}
          >
            Save Settings
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
