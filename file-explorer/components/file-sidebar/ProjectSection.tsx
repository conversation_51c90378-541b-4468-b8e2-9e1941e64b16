/**
 * Project Section Component
 * Handles project tree logic and rendering
 */

import { Folder, Plus, FolderPlus, Clock } from "lucide-react"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { SidebarItem } from "./SidebarItem"
import { ProjectSectionProps } from "./types"
import { useFileSidebarStore } from "./useFileSidebarStore"

export const ProjectSection = ({
  projects,
  selectedFile,
  onToggle,
  onSelect,
  onCreateProject,
  onOpenProject
}: ProjectSectionProps) => {
  const { recentProjects, explorerSettings } = useFileSidebarStore()

  const handleSwitchProject = async (projectName: string, projectPath: string) => {
    // This would be handled by the parent component's event system
    console.log(`Switch to project: ${projectName} at ${projectPath}`)
  }

  return (
    <div className="p-2">
      {/* Projects Header */}
      <div className="flex items-center justify-between py-2 px-2 mb-2">
        <span className="text-xs font-medium uppercase tracking-wider text-muted-foreground">Projects</span>
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="icon"
            className="h-5 w-5 text-muted-foreground hover:text-foreground"
            onClick={onOpenProject}
            title="Open existing project"
          >
            <FolderPlus className="h-3 w-3" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            className="h-5 w-5 text-muted-foreground hover:text-foreground"
            onClick={onCreateProject}
            title="Create new project"
          >
            <Plus className="h-3 w-3" />
          </Button>
        </div>
      </div>

      {/* Always visible Create Project button for better UX */}
      <div className="px-2 mb-3">
        <Button
          size="sm"
          className="w-full justify-start h-8 text-xs"
          onClick={onCreateProject}
        >
          <Plus className="h-3.5 w-3.5 mr-2" />
          Create New Project
        </Button>
      </div>

      {/* Project List */}
      {projects.length > 0 ? (
        projects.map((project) => (
          <div key={project.id} className="mb-2">
            <SidebarItem
              item={project}
              onToggle={onToggle}
              onSelect={onSelect}
              selectedFile={selectedFile}
              showFileIcons={explorerSettings.showFileIcons}
            />
          </div>
        ))
      ) : (
        /* Empty State */
        <div className="flex flex-col items-center justify-center py-12 px-4 text-center">
          <Folder className="h-16 w-16 text-muted-foreground/20 mb-6" />
          <h3 className="text-sm font-medium text-foreground mb-2">Welcome to CodeFusion</h3>
          <p className="text-xs text-muted-foreground mb-6 max-w-[200px] leading-relaxed">
            Get started by opening an existing project or creating a new one
          </p>
          <div className="flex flex-col gap-3 w-full max-w-[180px]">
            <Button
              size="sm"
              variant="outline"
              className="w-full justify-start h-9"
              onClick={onOpenProject}
            >
              <FolderPlus className="h-4 w-4 mr-2" />
              Open Project
            </Button>
            <Button
              size="sm"
              className="w-full justify-start h-9"
              onClick={onCreateProject}
            >
              <Plus className="h-4 w-4 mr-2" />
              Create Project
            </Button>
          </div>
        </div>
      )}

      {/* Recent Projects Section */}
      {recentProjects.length > 0 && (
        <div className="mt-4">
          <div className="flex items-center justify-between py-1 px-2 mb-1">
            <span className="text-xs font-medium uppercase tracking-wider text-muted-foreground">Recent</span>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-6 w-6 text-muted-foreground hover:text-foreground"
                  title="Recent projects"
                >
                  <Clock className="sidebar-icon" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-64">
                {recentProjects.slice(0, 5).map((project, index) => (
                  <DropdownMenuItem
                    key={`${project.path}-${index}`}
                    onClick={() => handleSwitchProject(project.name, project.path)}
                    className="flex flex-col items-start p-3"
                  >
                    <div className="font-medium text-sm">{project.name}</div>
                    <div className="text-xs text-muted-foreground truncate w-full">
                      {project.path}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {new Date(project.lastOpened).toLocaleDateString()}
                    </div>
                  </DropdownMenuItem>
                ))}
                {recentProjects.length > 5 && (
                  <>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem className="text-xs text-muted-foreground">
                      +{recentProjects.length - 5} more projects
                    </DropdownMenuItem>
                  </>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* Show first 3 recent projects directly in sidebar */}
          {recentProjects.slice(0, 3).map((project, index) => (
            <div
              key={`${project.path}-${index}`}
              className="flex items-center py-1 px-2 text-sm rounded-md cursor-pointer hover:bg-accent/20 group"
              onClick={() => handleSwitchProject(project.name, project.path)}
              title={project.path}
            >
              <Clock className="sidebar-icon mr-1.5 text-muted-foreground flex-shrink-0" />
              <span className="truncate text-muted-foreground group-hover:text-foreground">
                {project.name}
              </span>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
