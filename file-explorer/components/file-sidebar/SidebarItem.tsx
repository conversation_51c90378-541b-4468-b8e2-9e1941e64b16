/**
 * Sidebar Item Component
 * Renders individual file or folder items in the sidebar
 */

import { ChevronDown, ChevronRight, Folder, FolderOpen, MoreHorizontal } from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { CodeFileIcon } from "./CodeFileIcon"
import { SidebarItemProps } from "./types"

export const SidebarItem = ({
  item,
  level = 0,
  onToggle,
  onSelect,
  selectedFile,
  showFileIcons = true
}: SidebarItemProps) => {
  const isFolder = item.type === "folder" || Array.isArray(item.files)
  const indent = level * 16
  const isSelected = selectedFile && selectedFile.id === item.id

  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation()
    if (isFolder) {
      onToggle(item.id)
    } else {
      onSelect(item)
    }
  }

  const handleMenuClick = (e: React.MouseEvent, action: string) => {
    e.stopPropagation()
    console.log(`${action} ${item.name}`)
  }

  return (
    <>
      <div
        className={cn(
          "flex items-center py-1 px-2 text-sm rounded-md cursor-pointer group",
          "transition-colors duration-100",
          isSelected ? "bg-accent text-accent-foreground" : "hover:bg-accent/20",
        )}
        style={{ paddingLeft: `${indent + 8}px` }}
        onClick={handleClick}
      >
        {/* Expand/Collapse Icon */}
        {isFolder ? (
          item.expanded ? (
            <ChevronDown className="sidebar-icon mr-1.5 text-muted-foreground flex-shrink-0" />
          ) : (
            <ChevronRight className="sidebar-icon mr-1.5 text-muted-foreground flex-shrink-0" />
          )
        ) : (
          <div className="w-5 mr-1.5 flex-shrink-0" />
        )}

        {/* File/Folder Icon */}
        {showFileIcons && (
          <>
            {isFolder ? (
              item.expanded ? (
                <FolderOpen className="file-icon mr-1.5 text-blue-400 flex-shrink-0" />
              ) : (
                <Folder className="file-icon mr-1.5 text-blue-400 flex-shrink-0" />
              )
            ) : (
              <CodeFileIcon extension={item.type} className="mr-1.5 flex-shrink-0" />
            )}
          </>
        )}

        {/* File/Folder Name */}
        <span className="truncate">{item.name}</span>

        {/* File Actions Menu */}
        {!isFolder && (
          <div className="ml-auto opacity-0 group-hover:opacity-100">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="h-5 w-5 text-muted-foreground">
                  <MoreHorizontal className="h-3 w-3" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem
                  onClick={(e) => handleMenuClick(e, "Rename")}
                >
                  Rename
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={(e) => handleMenuClick(e, "Delete")}
                >
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        )}
      </div>

      {/* Render Children */}
      {isFolder &&
        item.expanded &&
        item.files &&
        item.files.map((file) => (
          <SidebarItem
            key={file.id}
            item={file}
            level={level + 1}
            onToggle={onToggle}
            onSelect={onSelect}
            selectedFile={selectedFile}
            showFileIcons={showFileIcons}
          />
        ))}
    </>
  )
}
