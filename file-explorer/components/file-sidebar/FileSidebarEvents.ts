/**
 * File Sidebar Events
 * Handles drag/drop, expand/collapse, hotkeys, and other interactions
 */

import { useCallback } from 'react'
import { useToast } from "@/components/ui/use-toast"
import { useFileSidebarStore } from './useFileSidebarStore'
import { FileSystemItem } from './types'
import { 
  ensureUniqueIds, 
  checkForUnsavedChanges, 
  saveUnsavedChanges, 
  loadRecentProjects,
  updateRecentProjects,
  loadProjectFromPath,
  validateActiveProjectForPRD
} from './sidebar-utils'

export const useFileSidebarEvents = () => {
  const { toast } = useToast()
  const {
    projects,
    setProjects,
    setSelectedFile,
    setRecentProjects,
    setShowUnsavedChangesDialog,
    setPendingProjectSwitch,
    setShowPRDDialog,
    setCurrentProjectPath,
    setProjectFolderPath,
    setPrdValidated,
    setShowOrchestrationDialog,
    updateFolderExpansion,
    addProject
  } = useFileSidebarStore()

  // Load recent projects
  const handleLoadRecentProjects = useCallback(async () => {
    try {
      const recentProjectsList = await loadRecentProjects()
      setRecentProjects(recentProjectsList)
    } catch (error) {
      console.warn('Failed to load recent projects:', error)
    }
  }, [setRecentProjects])

  // Handle file selection
  const handleFileSelect = useCallback(async (file: FileSystemItem) => {
    console.log("File selected:", file);
    setSelectedFile(file);

    // Don't try to load content for folders
    if (file.type === 'folder') {
      console.log("Selected item is a folder, not loading content");
      return file;
    }

    // If the file already has content, use it
    if (file.content !== undefined) {
      console.log("File already has content, using cached version");
      return file;
    }

    // If the file has a path (from file system), load its content
    if (file.path && window.electronAPI) {
      console.log("Loading file content from disk:", file.path);
      try {
        const result = await window.electronAPI.readFile(file.path);
        console.log("File read result:", result.success ? "success" : "failed");

        if (result.success) {
          const fileWithContent = {
            ...file,
            content: result.content
          };
          console.log("File content loaded, length:", result.content.length);
          return fileWithContent;
        } else {
          console.error("Failed to read file:", result.error);
          return file;
        }
      } catch (error) {
        console.error('Error reading file:', error);
        return file;
      }
    } else {
      console.log("File has no path or electronAPI not available");
      return file;
    }
  }, [setSelectedFile])

  // Handle folder toggle with lazy loading
  const handleToggleFolder = useCallback(async (id: number | string) => {
    const updateFolderState = async (items: FileSystemItem[]): Promise<FileSystemItem[]> => {
      const updatedItems = [];

      for (const item of items) {
        if (item.id === id) {
          const newExpanded = !item.expanded;

          // If expanding a folder that has a path (from file system) and no files loaded yet
          if (newExpanded && item.path && item.type === 'folder' && (!item.files || item.files.length === 0)) {
            try {
              if (window.electronAPI) {
                const result = await window.electronAPI.readDirectory(item.path);
                if (result.success && result.items) {
                  const files = ensureUniqueIds(result.items.map((fsItem: any) => ({
                    ...fsItem,
                    files: fsItem.type === 'folder' ? [] : undefined
                  })), item.path || '', Date.now());
                  updatedItems.push({ ...item, expanded: newExpanded, files });
                } else {
                  updatedItems.push({ ...item, expanded: newExpanded });
                }
              } else {
                updatedItems.push({ ...item, expanded: newExpanded });
              }
            } catch (error) {
              console.error('Error loading folder contents:', error);
              updatedItems.push({ ...item, expanded: newExpanded });
            }
          } else {
            updatedItems.push({ ...item, expanded: newExpanded });
          }
        } else if (item.files && Array.isArray(item.files)) {
          const updatedFiles = await updateFolderState(item.files);
          updatedItems.push({
            ...item,
            files: updatedFiles,
          });
        } else {
          updatedItems.push(item);
        }
      }

      return updatedItems;
    }

    const updatedProjects = await updateFolderState(projects);
    setProjects(updatedProjects);
  }, [projects, setProjects])

  // Handle project switching with unsaved changes check
  const handleSwitchProject = useCallback(async (projectName: string, projectPath: string) => {
    try {
      // Check for unsaved changes
      const hasUnsavedChanges = await checkForUnsavedChanges()

      if (hasUnsavedChanges) {
        // Show confirmation dialog
        setPendingProjectSwitch({ name: projectName, path: projectPath })
        setShowUnsavedChangesDialog(true)
        return
      }

      // No unsaved changes, proceed with switch
      await performProjectSwitch(projectName, projectPath)
    } catch (error) {
      console.error('Error switching projects:', error)
      toast({
        title: "Project Switch Error",
        description: "Failed to switch projects. Please try again.",
        variant: "destructive",
      })
    }
  }, [setPendingProjectSwitch, setShowUnsavedChangesDialog, toast])

  // Perform actual project switch
  const performProjectSwitch = useCallback(async (projectName: string, projectPath: string) => {
    try {
      // Load the selected project
      const newProject = await loadProjectFromPath(projectPath, projectName)
      
      if (newProject) {
        addProject(newProject)
      }

      // Update recent projects list
      await updateRecentProjects(projectName, projectPath)
      await handleLoadRecentProjects()

      toast({
        title: "Project Switched",
        description: `Switched to ${projectName}`,
      })
    } catch (error) {
      console.error('Error performing project switch:', error)
      toast({
        title: "Project Switch Error",
        description: "Failed to load the selected project.",
        variant: "destructive",
      })
    }
  }, [addProject, handleLoadRecentProjects, toast])

  // Handle unsaved changes confirmation
  const handleUnsavedChangesConfirm = useCallback(async (saveChanges: boolean) => {
    setShowUnsavedChangesDialog(false)

    const pendingSwitch = useFileSidebarStore.getState().pendingProjectSwitch
    if (!pendingSwitch) return

    try {
      if (saveChanges) {
        const saveSuccess = await saveUnsavedChanges()
        if (!saveSuccess) {
          // User can choose to continue anyway or cancel
          return
        }
      }

      await performProjectSwitch(pendingSwitch.name, pendingSwitch.path)
    } finally {
      setPendingProjectSwitch(null)
    }
  }, [setShowUnsavedChangesDialog, setPendingProjectSwitch, performProjectSwitch])

  // Handle opening existing project
  const handleOpenProject = useCallback(async () => {
    try {
      console.log("handleOpenProject called")
      // Check if we're in an Electron environment
      if (typeof window !== 'undefined') {
        // Check if electronAPI is available
        if (window.electronAPI) {
          console.log("Calling electronAPI.selectFolder")
          try {
            const result = await window.electronAPI.selectFolder();
            console.log("selectFolder result:", result)
            if (result && result.success && result.path && result.name) {
              const newProject = await loadProjectFromPath(result.path, result.name);
              if (newProject) {
                addProject(newProject)
              }
            }
          } catch (err) {
            console.error("Error calling electronAPI.selectFolder:", err);
            toast({
              title: "Open Project Failed",
              description: "Failed to open project. Please check the console for details.",
              variant: "destructive"
            });
          }
        } else {
          // Fallback for web environment - show a message
          console.log("🌐 File Explorer: Running in browser environment - electronAPI not available (this is normal for web mode)");
          toast({
            title: "Feature Not Available",
            description: "Open Project functionality is only available in the desktop app. Please use the desktop version for full project management features.",
            variant: "destructive"
          });
        }
      } else {
        // This should never happen in a browser environment
        console.log("🌐 File Explorer: Window object not available (unusual browser state)");
        toast({
          title: "Browser Error",
          description: "Cannot access browser window object.",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('Error opening project:', error);
      toast({
        title: "Open Project Failed",
        description: "Failed to open project. Please try again.",
        variant: "destructive"
      });
    }
  }, [addProject, toast])

  // Handle starting orchestration
  const handleStartOrchestration = useCallback(() => {
    setShowOrchestrationDialog(true)
  }, [setShowOrchestrationDialog])

  return {
    handleFileSelect,
    handleToggleFolder,
    handleSwitchProject,
    handleUnsavedChangesConfirm,
    handleOpenProject,
    handleStartOrchestration,
    handleLoadRecentProjects
  }
}
