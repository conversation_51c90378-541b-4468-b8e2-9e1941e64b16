/**
 * File Sidebar Module Exports
 * Barrel file for all file sidebar components and utilities
 */

// Main component
export { default as FileSidebar } from './FileSidebar'

// Core components
export { SidebarLayout } from './SidebarLayout'
export { SidebarItem } from './SidebarItem'
export { ProjectSection } from './ProjectSection'
export { CodeFileIcon } from './CodeFileIcon'

// State management
export { useFileSidebarStore, FileSidebarProvider, loadExplorerSettings, saveExplorerSettings } from './useFileSidebarStore'

// Event handling
export { useFileSidebarEvents } from './FileSidebarEvents'

// Utilities
export * from './sidebar-utils'

// Types
export * from './types'

// Dialog components
export { UnsavedChangesDialog } from './dialogs/UnsavedChangesDialog'
export { PRDDialog } from './dialogs/PRDDialog'
export { OrchestrationDialog } from './dialogs/OrchestrationDialog'
export { ExplorerSettingsDialog } from './dialogs/ExplorerSettingsDialog'
