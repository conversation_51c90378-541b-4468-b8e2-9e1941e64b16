/**
 * Shared types for modular agent components
 */

export interface Task {
  id: string;
  title: string;
  description: string;
  type: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'pending' | 'delegated' | 'processing' | 'verifying' | 'finalizing' | 'completed' | 'failed';
  context: any;
  metadata: Record<string, any>;
  createdAt?: string;
  updatedAt?: string;
}

export interface TaskResult {
  success: boolean;
  output?: any;
  error?: string;
  metadata?: Record<string, any>;
}

export interface QueueStatus {
  pending: number;
  processing: number;
  completed: number;
  failed: number;
}
