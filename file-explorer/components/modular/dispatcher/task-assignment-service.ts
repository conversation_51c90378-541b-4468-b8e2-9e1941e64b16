/**
 * Task Assignment Service
 * Handles task assignment and routing logic
 */

import { Task } from '../types';
import { TaskAssignment } from '../../agents/types';

export class TaskAssignmentService {
  private tasks = new Map<string, Task>();
  private assignments = new Map<string, string>(); // taskId -> agentId

  async submitTask(task: Task): Promise<void> {
    console.log(`📝 TaskAssignmentService: Submitting task ${task.id}`);
    this.tasks.set(task.id, task);
  }

  async assignTaskToAgent(taskId: string, agentId: string): Promise<void> {
    console.log(`🎯 TaskAssignmentService: Assigning task ${taskId} to agent ${agentId}`);
    this.assignments.set(taskId, agentId);
  }

  getTaskById(taskId: string): TaskAssignment | null {
    console.log(`🔍 TaskAssignmentService: Getting task ${taskId}`);
    // Task retrieval logic would go here
    return null;
  }

  cancelTask(taskId: string): boolean {
    console.log(`❌ TaskAssignmentService: Cancelling task ${taskId}`);
    const cancelled = this.tasks.delete(taskId) || this.assignments.delete(taskId);
    return cancelled;
  }

  getAssignedAgent(taskId: string): string | null {
    return this.assignments.get(taskId) || null;
  }

  getAllTasks(): Task[] {
    return Array.from(this.tasks.values());
  }
}
