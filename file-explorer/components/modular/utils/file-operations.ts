/**
 * File Operations
 * Handles file system operations for agents
 */

export class FileOperations {
  async agentCreateFile(agentId: string, path: string, content: string = ''): Promise<boolean> {
    console.log(`📁 FileOperations: Agent ${agentId} creating file ${path}`);
    
    try {
      // Enforce project context requirement
      const { activeProjectService } = await import('../../../services/active-project-service');
      if (!activeProjectService.getActiveProjectPath()) {
        const errorMessage = "Agent execution blocked: no active project selected. Please open or create a project first.";
        console.error(`Agent ${agentId}: ${errorMessage}`);
        return false;
      }

      // File creation logic would go here
      console.log(`✅ FileOperations: File ${path} created successfully`);
      return true;
    } catch (error) {
      console.error(`❌ FileOperations: Failed to create file ${path}:`, error);
      return false;
    }
  }

  async agentWriteFile(agentId: string, path: string, content: string): Promise<boolean> {
    console.log(`✏️ FileOperations: Agent ${agentId} writing to file ${path}`);
    
    try {
      // File writing logic would go here
      console.log(`✅ FileOperations: File ${path} written successfully`);
      return true;
    } catch (error) {
      console.error(`❌ FileOperations: Failed to write file ${path}:`, error);
      return false;
    }
  }

  async agentDeleteFile(agentId: string, path: string): Promise<boolean> {
    console.log(`🗑️ FileOperations: Agent ${agentId} deleting file ${path}`);
    
    try {
      // File deletion logic would go here
      console.log(`✅ FileOperations: File ${path} deleted successfully`);
      return true;
    } catch (error) {
      console.error(`❌ FileOperations: Failed to delete file ${path}:`, error);
      return false;
    }
  }

  async agentCreateDirectory(agentId: string, path: string): Promise<boolean> {
    console.log(`📂 FileOperations: Agent ${agentId} creating directory ${path}`);
    
    try {
      // Enforce project context requirement
      const { activeProjectService } = await import('../../../services/active-project-service');
      if (!activeProjectService.getActiveProjectPath()) {
        const errorMessage = "Agent execution blocked: no active project selected. Please open or create a project first.";
        console.error(`Agent ${agentId}: ${errorMessage}`);
        return false;
      }

      // Directory creation logic would go here
      console.log(`✅ FileOperations: Directory ${path} created successfully`);
      return true;
    } catch (error) {
      console.error(`❌ FileOperations: Failed to create directory ${path}:`, error);
      return false;
    }
  }

  async agentExecuteCommand(agentId: string, command: string, cwd?: string): Promise<{ success: boolean; output?: string; error?: string }> {
    console.log(`⚡ FileOperations: Agent ${agentId} executing command: ${command}`);
    
    try {
      // Command execution logic would go here
      console.log(`✅ FileOperations: Command executed successfully`);
      return { success: true, output: `Simulated execution of: ${command}` };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error(`❌ FileOperations: Command execution failed:`, error);
      return { success: false, error: errorMessage };
    }
  }

  extractFileRequests(content: string, agentId: string): any[] {
    console.log(`🔍 FileOperations: Extracting file requests for agent ${agentId}`);
    
    const fileRequests: any[] = [];
    
    try {
      // Look for file creation patterns in the response
      const fileBlockRegex = /```(\w+)?\s*\/\/ FILE: ([^\n]+)\n([\s\S]*?)```/g;
      let match;
      while ((match = fileBlockRegex.exec(content)) !== null) {
        const [, language, filePath, fileContent] = match;
        fileRequests.push({
          path: filePath.trim(),
          content: fileContent.trim(),
          language: language || this.getLanguageFromPath(filePath),
          openInEditor: filePath.includes('.tsx') || filePath.includes('.ts') || filePath.includes('.js')
        });
      }

      console.log(`🔍 FileOperations: Extracted ${fileRequests.length} file requests`);
      return fileRequests;
    } catch (error) {
      console.error(`❌ FileOperations: Error extracting file requests:`, error);
      return [];
    }
  }

  private getLanguageFromPath(filePath: string): string {
    const extension = filePath.split('.').pop();
    const languageMap: Record<string, string> = {
      'ts': 'typescript',
      'tsx': 'typescript',
      'js': 'javascript',
      'jsx': 'javascript',
      'py': 'python',
      'css': 'css',
      'scss': 'scss',
      'html': 'html',
      'json': 'json',
      'md': 'markdown',
      'yml': 'yaml',
      'yaml': 'yaml'
    };
    return languageMap[extension || ''] || 'text';
  }
}
