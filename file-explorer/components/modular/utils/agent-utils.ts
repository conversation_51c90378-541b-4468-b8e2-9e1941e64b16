/**
 * Agent Utilities
 * Common utility functions for agent operations
 */

import { AgentContext, SystemMetrics, AgentStatus } from '../../agents/types';
import { Task } from '../types';

export class AgentUtils {
  async enhanceContext(context: AgentContext, agentId: string): Promise<AgentContext> {
    console.log(`🔧 AgentUtils: Enhancing context for agent ${agentId}`);
    // Context enhancement logic would go here
    return context;
  }

  inferTaskType(context: AgentContext): string {
    const task = context.task.toLowerCase();
    
    if (task.includes('create') || task.includes('generate') || task.includes('write')) {
      if (task.includes('file') || task.includes('code')) return 'code-generation';
      if (task.includes('test')) return 'testing';
      return 'code-generation';
    }
    
    if (task.includes('analyze') || task.includes('review') || task.includes('check')) {
      return 'analysis';
    }
    
    if (task.includes('test') || task.includes('verify')) {
      return 'testing';
    }
    
    if (task.includes('file') || task.includes('directory') || task.includes('folder')) {
      return 'file-operation';
    }
    
    return 'general';
  }

  calculateTaskPriority(task: Task): number {
    const priorityScores = { low: 25, medium: 50, high: 75, urgent: 100 };
    return priorityScores[task.priority] || 50;
  }

  extractLanguage(context: AgentContext): string {
    if (context.files) {
      const extensions = context.files.map(file => file.split('.').pop()).filter(Boolean);
      if (extensions.length > 0) {
        return extensions[0] || 'javascript';
      }
    }
    return 'javascript';
  }

  getFileExtension(filePath: string): string {
    const extension = filePath.split('.').pop();
    return extension || 'unknown';
  }

  getLanguageFromPath(filePath: string): string {
    const extension = this.getFileExtension(filePath);
    const languageMap: Record<string, string> = {
      'ts': 'typescript',
      'tsx': 'typescript',
      'js': 'javascript',
      'jsx': 'javascript',
      'py': 'python',
      'css': 'css',
      'scss': 'scss',
      'html': 'html',
      'json': 'json',
      'md': 'markdown',
      'yml': 'yaml',
      'yaml': 'yaml'
    };
    return languageMap[extension] || 'text';
  }

  extractTaskType(task: string): string {
    const taskLower = task.toLowerCase();
    if (taskLower.includes('implement') || taskLower.includes('create')) return 'implementation';
    if (taskLower.includes('design') || taskLower.includes('ui')) return 'design';
    if (taskLower.includes('test')) return 'testing';
    if (taskLower.includes('research') || taskLower.includes('analyze')) return 'research';
    if (taskLower.includes('fix') || taskLower.includes('debug')) return 'debugging';
    return 'general';
  }

  getAgentTypeFromId(agentId: string): string {
    const agentTypeMap: Record<string, string> = {
      'micromanager': 'micromanager',
      'intern': 'intern',
      'junior': 'junior',
      'midlevel': 'midlevel',
      'senior': 'senior',
      'architect': 'architect',
      'designer': 'designer',
      'tester': 'tester',
      'researcher': 'researcher'
    };
    return agentTypeMap[agentId] || agentId;
  }

  async generateSystemReport(metrics: SystemMetrics, agentStatuses: AgentStatus[]): Promise<string> {
    return `[SYSTEM STATUS REPORT]

OVERVIEW:
- System Health: ${metrics.systemHealthScore.toFixed(1)}%
- Active Agents: ${metrics.activeAgents}/${agentStatuses.length}
- Queue Length: ${metrics.queueLength}
- Total Tasks: ${metrics.totalTasks}
- Success Rate: ${((metrics.successfulTasks / Math.max(metrics.totalTasks, 1)) * 100).toFixed(1)}%

AGENT STATUS:
${agentStatuses.map(agent => {
  const health = agent.healthScore;
  const status = health > 70 ? '🟢' : health > 40 ? '🟡' : '🔴';
  return `${status} ${agent.name}: ${health.toFixed(1)}% (${agent.tasksCompleted} tasks)`;
}).join('\n')}

PERFORMANCE METRICS:
- Average Response Time: ${(metrics.averageResponseTime / 1000).toFixed(2)}s
- Total Tokens Used: ${metrics.totalTokensUsed.toLocaleString()}
- Failed Tasks: ${metrics.failedTasks}

[END REPORT]`;
  }

  // Placeholder methods for compatibility
  async generateProjectMetadata(projectPath: string): Promise<Record<string, any>> {
    return { projectPath, generated: Date.now() };
  }

  async generateProjectTree(projectPath: string, maxDepth: number = 2): Promise<string[]> {
    return [`Project: ${projectPath.split('/').pop()}`];
  }

  async detectProjectType(projectPath: string): Promise<string> {
    return 'Unknown';
  }

  async findMainEntries(projectPath: string): Promise<string[]> {
    return ['Unknown'];
  }

  async optimizeAgentModel(agentId: string, context: AgentContext, costSettings: any): Promise<{ provider?: string; model?: string } | null> {
    return null;
  }

  calculateModelCost(provider: string, model: string, inputTokens: number, outputTokens: number): number {
    return 0;
  }

  getMinContextSizeForAgent(agentId: string): number {
    return 2000;
  }
}
