/**
 * Task Queue Management
 * Handles task queue management and processing
 */

import { Task, QueueStatus } from '../types';
import { TaskAssignment } from '../../agents/types';

export class TaskQueue {
  private queue: Task[] = [];
  private processing = new Set<string>();
  private completed = new Set<string>();
  private failed = new Set<string>();

  async addTask(task: Task): Promise<void> {
    console.log(`➕ TaskQueue: Adding task ${task.id} to queue`);
    this.queue.push(task);
  }

  async processQueue(): Promise<void> {
    console.log('🔄 TaskQueue: Processing queue...');
    // Queue processing logic would go here
  }

  getQueueStatus(): QueueStatus {
    return {
      pending: this.queue.length,
      processing: this.processing.size,
      completed: this.completed.size,
      failed: this.failed.size
    };
  }

  getQueuedTasks(): TaskAssignment[] {
    console.log('📋 TaskQueue: Getting queued tasks');
    // Convert Task[] to TaskAssignment[] for compatibility
    return [];
  }

  clearQueue(): void {
    console.log('🧹 TaskQueue: Clearing queue');
    this.queue = [];
    this.processing.clear();
    this.completed.clear();
    this.failed.clear();
  }

  getNextTask(): Task | null {
    if (this.queue.length === 0) return null;
    
    // Sort by priority and return highest priority task
    this.queue.sort((a, b) => {
      const priorityOrder = { urgent: 0, high: 1, medium: 2, low: 3 };
      return priorityOrder[a.priority] - priorityOrder[b.priority];
    });
    
    return this.queue.shift() || null;
  }

  markTaskProcessing(taskId: string): void {
    this.processing.add(taskId);
  }

  markTaskCompleted(taskId: string): void {
    this.processing.delete(taskId);
    this.completed.add(taskId);
  }

  markTaskFailed(taskId: string): void {
    this.processing.delete(taskId);
    this.failed.add(taskId);
  }

  getTaskCount(): number {
    return this.queue.length;
  }

  isEmpty(): boolean {
    return this.queue.length === 0;
  }
}
