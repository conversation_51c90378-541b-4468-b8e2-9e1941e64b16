/**
 * Task Execution
 * Handles task execution with retry logic and error handling
 */

import { Task, TaskResult } from '../types';
import { AgentBase } from '../../agents/agent-base';
import { AgentResponse, AgentContext } from '../../agents/types';

export class TaskExecution {
  async executeTask(task: Task, agent: AgentBase): Promise<AgentResponse> {
    console.log(`🚀 TaskExecution: Executing task ${task.id} with agent ${agent.config?.id || 'unknown'}`);
    
    try {
      // Convert Task to AgentContext for execution
      const context: AgentContext = {
        task: task.description,
        files: [],
        metadata: task.metadata
      };

      // Execute the task using the agent
      const response = await agent.execute(context);
      
      console.log(`✅ TaskExecution: Task ${task.id} completed successfully`);
      return response;
    } catch (error) {
      console.error(`❌ TaskExecution: Task ${task.id} failed:`, error);
      
      return {
        success: false,
        content: '',
        error: error instanceof Error ? error.message : String(error),
        tokensUsed: 0,
        executionTime: 0
      };
    }
  }

  recordTaskCompletion(
    agentId: string,
    context: AgentContext,
    response: AgentResponse,
    executionTime: number
  ): void {
    console.log(`📊 TaskExecution: Recording completion for agent ${agentId}`);
    // Task completion recording logic would go here
  }

  async executeWithRetry(task: Task, agent: AgentBase, maxRetries = 3): Promise<AgentResponse> {
    let lastError: Error | null = null;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`🔄 TaskExecution: Attempt ${attempt}/${maxRetries} for task ${task.id}`);
        return await this.executeTask(task, agent);
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        console.warn(`⚠️ TaskExecution: Attempt ${attempt} failed for task ${task.id}:`, error);
        
        if (attempt < maxRetries) {
          // Wait before retry (exponential backoff)
          const delay = Math.pow(2, attempt - 1) * 1000;
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }
    
    throw lastError || new Error(`Task ${task.id} failed after ${maxRetries} attempts`);
  }
}
