// components/workflow/user-confirmation-dialog.tsx
// ✅ PHASE 3: User Confirmation Dialog for Sequential Workflow Control

import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '../ui/dialog';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Alert, AlertDescription } from '../ui/alert';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { CheckCircle, XCircle, AlertTriangle, FileText, Clock, User } from 'lucide-react';

export interface TaskCompletionReport {
  taskId: string;
  agentId: string;
  title: string;
  description: string;
  filesCreated: string[];
  filesModified: string[];
  filesDeleted: string[];
  executionTime: number;
  tokensUsed: number;
  success: boolean;
  validationResults: {
    isValid: boolean;
    reason?: string;
    details?: string[];
  };
  qualityScore: number;
  timestamp: number;
}

export interface UserDecision {
  action: 'proceed' | 'retry' | 'modify' | 'cancel';
  feedback?: string;
  modifications?: string[];
}

export interface UserConfirmationDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onDecision: (decision: UserDecision) => void;
  report: TaskCompletionReport;
  isLoading?: boolean;
}

export const UserConfirmationDialog: React.FC<UserConfirmationDialogProps> = ({
  isOpen,
  onClose,
  onDecision,
  report,
  isLoading = false
}) => {
  const [feedback, setFeedback] = useState('');
  const [selectedAction, setSelectedAction] = useState<UserDecision['action'] | null>(null);

  const handleDecision = (action: UserDecision['action']) => {
    const decision: UserDecision = {
      action,
      feedback: feedback.trim() || undefined
    };
    
    onDecision(decision);
    setFeedback('');
    setSelectedAction(null);
  };

  const formatExecutionTime = (ms: number): string => {
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
    return `${(ms / 60000).toFixed(1)}m`;
  };

  const getStatusIcon = (success: boolean, isValid: boolean) => {
    if (success && isValid) return <CheckCircle className="h-5 w-5 text-green-500" />;
    if (!success) return <XCircle className="h-5 w-5 text-red-500" />;
    return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
  };

  const getStatusColor = (success: boolean, isValid: boolean) => {
    if (success && isValid) return 'bg-green-50 border-green-200';
    if (!success) return 'bg-red-50 border-red-200';
    return 'bg-yellow-50 border-yellow-200';
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {getStatusIcon(report.success, report.validationResults.isValid)}
            Task Completion Review
          </DialogTitle>
          <DialogDescription>
            Review the task execution results and decide how to proceed
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Task Summary */}
          <Card className={getStatusColor(report.success, report.validationResults.isValid)}>
            <CardHeader>
              <CardTitle className="text-lg">{report.title}</CardTitle>
              <CardDescription>{report.description}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4" />
                  <span>Agent: <Badge variant="outline">{report.agentId}</Badge></span>
                </div>
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4" />
                  <span>Time: {formatExecutionTime(report.executionTime)}</span>
                </div>
                <div className="flex items-center gap-2">
                  <FileText className="h-4 w-4" />
                  <span>Files: {report.filesCreated.length + report.filesModified.length}</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4" />
                  <span>Quality: {report.qualityScore}/100</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Validation Results */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Validation Results</CardTitle>
            </CardHeader>
            <CardContent>
              <Alert className={report.validationResults.isValid ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
                {report.validationResults.isValid ? (
                  <CheckCircle className="h-4 w-4 text-green-600" />
                ) : (
                  <XCircle className="h-4 w-4 text-red-600" />
                )}
                <AlertDescription>
                  <strong>
                    {report.validationResults.isValid ? 'Validation Passed' : 'Validation Failed'}
                  </strong>
                  {report.validationResults.reason && (
                    <div className="mt-1">{report.validationResults.reason}</div>
                  )}
                </AlertDescription>
              </Alert>
              
              {report.validationResults.details && report.validationResults.details.length > 0 && (
                <div className="mt-3">
                  <div className="text-sm font-medium mb-2">Details:</div>
                  <ul className="text-sm space-y-1">
                    {report.validationResults.details.map((detail, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <span className="text-muted-foreground">•</span>
                        <span>{detail}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </CardContent>
          </Card>

          {/* File Operations */}
          {(report.filesCreated.length > 0 || report.filesModified.length > 0 || report.filesDeleted.length > 0) && (
            <Card>
              <CardHeader>
                <CardTitle className="text-base">File Operations</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {report.filesCreated.length > 0 && (
                  <div>
                    <div className="text-sm font-medium text-green-700 mb-1">
                      Created ({report.filesCreated.length})
                    </div>
                    <div className="flex flex-wrap gap-1">
                      {report.filesCreated.map((file, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {file}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
                
                {report.filesModified.length > 0 && (
                  <div>
                    <div className="text-sm font-medium text-blue-700 mb-1">
                      Modified ({report.filesModified.length})
                    </div>
                    <div className="flex flex-wrap gap-1">
                      {report.filesModified.map((file, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {file}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
                
                {report.filesDeleted.length > 0 && (
                  <div>
                    <div className="text-sm font-medium text-red-700 mb-1">
                      Deleted ({report.filesDeleted.length})
                    </div>
                    <div className="flex flex-wrap gap-1">
                      {report.filesDeleted.map((file, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {file}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Feedback Section */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Feedback (Optional)</CardTitle>
              <CardDescription>
                Provide feedback about the task execution or suggestions for improvement
              </CardDescription>
            </CardHeader>
            <CardContent>
              <textarea
                className="w-full p-3 border rounded-md resize-none"
                rows={3}
                placeholder="Enter your feedback here..."
                value={feedback}
                onChange={(e) => setFeedback(e.target.value)}
                disabled={isLoading}
              />
            </CardContent>
          </Card>
        </div>

        <DialogFooter className="flex flex-col sm:flex-row gap-2">
          <div className="flex gap-2 flex-wrap">
            <Button
              onClick={() => handleDecision('proceed')}
              disabled={isLoading}
              className="bg-green-600 hover:bg-green-700"
            >
              ✅ Proceed to Next Task
            </Button>
            
            <Button
              onClick={() => handleDecision('retry')}
              disabled={isLoading}
              variant="outline"
            >
              🔄 Retry This Task
            </Button>
            
            <Button
              onClick={() => handleDecision('modify')}
              disabled={isLoading}
              variant="outline"
            >
              ✏️ Request Modifications
            </Button>
            
            <Button
              onClick={() => handleDecision('cancel')}
              disabled={isLoading}
              variant="destructive"
            >
              ❌ Cancel Workflow
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default UserConfirmationDialog;
