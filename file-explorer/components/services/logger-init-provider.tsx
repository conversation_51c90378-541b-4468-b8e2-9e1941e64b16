/**
 * SYNAPSE LOGGER INITIALIZATION PROVIDER
 * React provider component to initialize the logging system at application startup
 */

'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { useLoggerInit, LoggerInitState } from '../../hooks/use-logger-init';
import { logInfo } from '../../services/logger';

interface LoggerInitContextType extends LoggerInitState {
  reinitialize: () => void;
}

const LoggerInitContext = createContext<LoggerInitContextType | undefined>(undefined);

interface LoggerInitProviderProps {
  children: React.ReactNode;
}

/**
 * Provider component that initializes the Synapse logging system
 * Should be placed high in the component tree
 */
export function LoggerInitProvider({ children }: LoggerInitProviderProps) {
  const loggerState = useLoggerInit();
  const [reinitKey, setReinitKey] = useState(0);

  const reinitialize = () => {
    setReinitKey(prev => prev + 1);
  };

  // Log initialization status changes
  useEffect(() => {
    if (loggerState.isInitialized) {
      console.log('✅ Synapse Logger initialized successfully');
      
      // Log the successful initialization
      logInfo('System', 'LoggerProviderInitialized', {
        timestamp: new Date().toISOString(),
        reinitKey
      });
    } else if (loggerState.error) {
      console.error('❌ Synapse Logger initialization failed:', loggerState.error);
    } else if (loggerState.isInitializing) {
      console.log('🔄 Synapse Logger initializing...');
    }
  }, [loggerState.isInitialized, loggerState.error, loggerState.isInitializing, reinitKey]);

  const contextValue: LoggerInitContextType = {
    ...loggerState,
    reinitialize
  };

  return (
    <LoggerInitContext.Provider value={contextValue}>
      {children}
    </LoggerInitContext.Provider>
  );
}

/**
 * Hook to access logger initialization state
 */
export function useLoggerInitContext(): LoggerInitContextType {
  const context = useContext(LoggerInitContext);
  if (context === undefined) {
    throw new Error('useLoggerInitContext must be used within a LoggerInitProvider');
  }
  return context;
}

/**
 * Hook to check if logger is ready (convenience hook)
 */
export function useLoggerReady(): boolean {
  const { isInitialized } = useLoggerInitContext();
  return isInitialized;
}

/**
 * Component to display logger initialization status (for debugging)
 */
export function LoggerStatusIndicator() {
  const { isInitialized, isInitializing, error, reinitialize } = useLoggerInitContext();

  if (isInitialized) {
    return (
      <div className="fixed bottom-4 right-4 bg-green-100 border border-green-300 text-green-800 px-3 py-1 rounded-md text-xs">
        📝 Logger Ready
      </div>
    );
  }

  if (isInitializing) {
    return (
      <div className="fixed bottom-4 right-4 bg-blue-100 border border-blue-300 text-blue-800 px-3 py-1 rounded-md text-xs">
        🔄 Logger Initializing...
      </div>
    );
  }

  if (error) {
    return (
      <div className="fixed bottom-4 right-4 bg-red-100 border border-red-300 text-red-800 px-3 py-1 rounded-md text-xs">
        ❌ Logger Error: {error}
        <button 
          onClick={reinitialize}
          className="ml-2 underline hover:no-underline"
        >
          Retry
        </button>
      </div>
    );
  }

  return null;
}
