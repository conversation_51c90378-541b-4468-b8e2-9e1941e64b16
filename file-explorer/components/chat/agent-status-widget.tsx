// components/chat/agent-status-widget.tsx

"use client"

import { useState } from "react"
import { cn } from "@/lib/utils"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { 
  Brain, 
  Loader2, 
  CheckCircle, 
  AlertCircle, 
  Clock,
  ChevronUp,
  ChevronDown,
  Activity
} from "lucide-react"
import { useAgentPresence } from "@/hooks/useAgentPresence"
import type { LiveAgentState } from "@/types/agent-events"

interface AgentStatusWidgetProps {
  className?: string
  compact?: boolean
}

export default function AgentStatusWidget({ className, compact = false }: AgentStatusWidgetProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const {
    agents,
    activeTaskCount,
    recentActivity,
    getActiveAgents,
    getCurrentTasks,
    getWorkingSummary
  } = useAgentPresence()

  const activeAgents = getActiveAgents()
  const currentTasks = getCurrentTasks()

  const getStatusIcon = (status: LiveAgentState['status']) => {
    switch (status) {
      case 'thinking':
        return <Brain className="h-3 w-3 text-blue-500 animate-pulse" />
      case 'working':
        return <Loader2 className="h-3 w-3 text-green-500 animate-spin" />
      case 'done':
        return <CheckCircle className="h-3 w-3 text-green-600" />
      case 'error':
        return <AlertCircle className="h-3 w-3 text-red-500" />
      case 'idle':
      default:
        return <Clock className="h-3 w-3 text-gray-400" />
    }
  }

  const getStatusColor = (status: LiveAgentState['status']) => {
    switch (status) {
      case 'thinking':
        return "bg-blue-500/10 text-blue-600 border-blue-500/20"
      case 'working':
        return "bg-green-500/10 text-green-600 border-green-500/20"
      case 'done':
        return "bg-green-500/10 text-green-600 border-green-500/20"
      case 'error':
        return "bg-red-500/10 text-red-600 border-red-500/20"
      case 'idle':
      default:
        return "bg-gray-500/10 text-gray-600 border-gray-500/20"
    }
  }

  const getAgentIcon = (agentType: string) => {
    const icons: Record<string, string> = {
      micromanager: "🧠",
      intern: "🛠️",
      junior: "📦",
      midlevel: "⚙️",
      senior: "🧱",
      architect: "🏗️",
      designer: "🎨",
      tester: "🧪",
      researcher: "📘"
    }
    return icons[agentType] || "🤖"
  }

  const formatTimeElapsed = (startedAt: number) => {
    const elapsed = Date.now() - startedAt
    const seconds = Math.floor(elapsed / 1000)
    const minutes = Math.floor(seconds / 60)
    
    if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`
    }
    return `${seconds}s`
  }

  if (compact) {
    return (
      <div className={cn("flex items-center gap-2 text-xs text-muted-foreground", className)}>
        <Activity className="h-3 w-3" />
        <span>{getWorkingSummary()}</span>
        {activeTaskCount > 0 && (
          <Badge variant="outline" className="text-xs h-4">
            {activeTaskCount} active
          </Badge>
        )}
      </div>
    )
  }

  return (
    <div className={cn("border-t border-editor-border bg-background", className)}>
      {/* Main status bar */}
      <div className="flex items-center justify-between p-2 text-xs">
        <div className="flex items-center gap-2">
          <Activity className="h-3 w-3 text-muted-foreground" />
          <span className="text-muted-foreground">Agent Status:</span>
          <span className="font-medium">{getWorkingSummary()}</span>
          {activeTaskCount > 0 && (
            <Badge variant="outline" className="text-xs h-4">
              {activeTaskCount} active
            </Badge>
          )}
        </div>
        
        {activeAgents.length > 0 && (
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0"
            onClick={() => setIsExpanded(!isExpanded)}
          >
            {isExpanded ? (
              <ChevronDown className="h-3 w-3" />
            ) : (
              <ChevronUp className="h-3 w-3" />
            )}
          </Button>
        )}
      </div>

      {/* Expanded view */}
      {isExpanded && activeAgents.length > 0 && (
        <div className="border-t border-editor-border p-2 space-y-2">
          {currentTasks.map((task) => {
            const agent = agents.find(a => a.agentId === task.agentId)
            if (!agent) return null

            return (
              <div key={task.taskId} className="space-y-1">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <span className="text-xs">{getAgentIcon(agent.agentType)}</span>
                    <Badge
                      variant="outline"
                      className={cn("text-xs h-4 flex items-center gap-1", getStatusColor(agent.status))}
                    >
                      {getStatusIcon(agent.status)}
                      {agent.agentName}
                    </Badge>
                    <span className="text-xs text-muted-foreground">
                      {formatTimeElapsed(task.startedAt)}
                    </span>
                  </div>
                  <span className="text-xs font-medium">{task.progress}%</span>
                </div>
                
                <div className="space-y-1">
                  <Progress value={task.progress} className="h-1" />
                  <p className="text-xs text-muted-foreground break-words">
                    {task.taskDescription}
                  </p>
                </div>
              </div>
            )
          })}
        </div>
      )}

      {/* Recent activity (when expanded and no active tasks) */}
      {isExpanded && activeAgents.length === 0 && recentActivity.length > 0 && (
        <div className="border-t border-editor-border p-2 space-y-1">
          <p className="text-xs font-medium text-muted-foreground mb-1">Recent Activity</p>
          {recentActivity.slice(0, 3).map((activity) => (
            <div key={activity.id} className="flex items-center gap-2 text-xs">
              <span>{getAgentIcon(activity.agentType)}</span>
              <span className="text-muted-foreground">
                {new Date(activity.timestamp).toLocaleTimeString([], { 
                  hour: '2-digit', 
                  minute: '2-digit' 
                })}
              </span>
              <span className="break-words">{activity.message}</span>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
