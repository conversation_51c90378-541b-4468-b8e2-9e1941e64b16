"use client"

import React, { useState, useCallback, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/components/ui/use-toast';
import { claudeTaskmasterService } from '../../services/claude-taskmaster-service';
import { PRDParseResult } from '../intake/prd-intake-service';
import { 
  Loader2, 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  FileText, 
  Cog, 
  Zap,
  Clock,
  Target
} from 'lucide-react';

interface TaskmasterTaskGenerationUIProps {
  projectPath: string;
  prdContent: string;
  anthropicApiKey: string;
  perplexityApiKey: string;
  onTasksGenerated: (result: PRDParseResult) => void;
  onCancel?: () => void;
  className?: string;
}

interface GenerationStep {
  id: string;
  title: string;
  description: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  error?: string;
  startTime?: number;
  endTime?: number;
  subSteps?: GenerationSubStep[];
  currentSubStep?: number;
}

interface GenerationSubStep {
  id: string;
  title: string;
  description: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  error?: string;
  startTime?: number;
  endTime?: number;
}

interface GenerationState {
  isActive: boolean;
  currentStepIndex: number;
  steps: GenerationStep[];
  startTime?: number;
  tasksFilePath?: string;
  tasksGenerated?: number;
}

export const TaskmasterTaskGenerationUI: React.FC<TaskmasterTaskGenerationUIProps> = ({
  projectPath,
  prdContent,
  anthropicApiKey,
  perplexityApiKey,
  onTasksGenerated,
  onCancel,
  className = ''
}) => {
  const { toast } = useToast();
  const [generationState, setGenerationState] = useState<GenerationState>({
    isActive: false,
    currentStepIndex: 0,
    steps: []
  });
  const [hasStarted, setHasStarted] = useState(false);

  // Initialize generation steps
  const initializeSteps = useCallback((): GenerationStep[] => {
    return [
      {
        id: 'validate-prd',
        title: 'Validate PRD Content',
        description: 'Checking PRD content and project structure',
        status: 'pending'
      },
      {
        id: 'save-prd',
        title: 'Save PRD File',
        description: 'Saving PRD to .taskmaster/docs/prd.txt',
        status: 'pending'
      },
      {
        id: 'configure-taskmaster',
        title: 'Configure Claude Taskmaster',
        description: 'Setting up API keys and configuration',
        status: 'pending'
      },
      {
        id: 'parse-prd',
        title: 'Parse PRD with Claude Taskmaster',
        description: 'Generating tasks from PRD content',
        status: 'pending',
        subSteps: [
          {
            id: 'init-taskmaster',
            title: 'Initialize Taskmaster CLI',
            description: 'Starting Claude Taskmaster process and validating environment',
            status: 'pending'
          },
          {
            id: 'analyze-prd',
            title: 'Analyze PRD Content',
            description: 'Claude AI analyzing project requirements and scope',
            status: 'pending'
          },
          {
            id: 'generate-structure',
            title: 'Generate Task Structure',
            description: 'Creating hierarchical task breakdown with dependencies',
            status: 'pending'
          },
          {
            id: 'wait-for-file',
            title: 'Wait for Task File Creation',
            description: 'Monitoring filesystem for tasks.json creation',
            status: 'pending'
          },
          {
            id: 'validate-output',
            title: 'Validate Task Output',
            description: 'Verifying task format, structure, and completeness',
            status: 'pending'
          },
          {
            id: 'save-tasks',
            title: 'Finalize Task Generation',
            description: 'Confirming tasks.json is ready for orchestration',
            status: 'pending'
          }
        ]
      },
      {
        id: 'validate-tasks',
        title: 'Validate Generated Tasks',
        description: 'Checking tasks.json file and content',
        status: 'pending'
      },
      {
        id: 'complete',
        title: 'Task Generation Complete',
        description: 'Tasks ready for orchestration',
        status: 'pending'
      }
    ];
  }, []);

  // Update step status
  const updateStep = useCallback((stepId: string, status: GenerationStep['status'], error?: string) => {
    setGenerationState(prev => ({
      ...prev,
      steps: prev.steps.map(step => {
        if (step.id === stepId) {
          const updatedStep = {
            ...step,
            status,
            error,
            endTime: status === 'completed' || status === 'failed' ? Date.now() : step.endTime
          };
          if (status === 'running' && !step.startTime) {
            updatedStep.startTime = Date.now();
          }
          return updatedStep;
        }
        return step;
      })
    }));
  }, []);

  // Update sub-step status
  const updateSubStep = useCallback((stepId: string, subStepId: string, status: GenerationSubStep['status'], error?: string) => {
    setGenerationState(prev => ({
      ...prev,
      steps: prev.steps.map(step => {
        if (step.id === stepId && step.subSteps) {
          const updatedSubSteps = step.subSteps.map(subStep => {
            if (subStep.id === subStepId) {
              const updatedSubStep = {
                ...subStep,
                status,
                error,
                endTime: status === 'completed' || status === 'failed' ? Date.now() : subStep.endTime
              };
              if (status === 'running' && !subStep.startTime) {
                updatedSubStep.startTime = Date.now();
              }
              return updatedSubStep;
            }
            return subStep;
          });

          return {
            ...step,
            subSteps: updatedSubSteps,
            currentSubStep: status === 'running' ?
              updatedSubSteps.findIndex(s => s.id === subStepId) : step.currentSubStep
          };
        }
        return step;
      })
    }));
  }, []);

  // Start task generation process
  const startGeneration = useCallback(async () => {
    if (!projectPath || !prdContent) {
      toast({
        title: "Missing Information",
        description: "Project path and PRD content are required.",
        variant: "destructive",
      });
      return;
    }

    setHasStarted(true);
    const steps = initializeSteps();
    setGenerationState({
      isActive: true,
      currentStepIndex: 0,
      steps,
      startTime: Date.now()
    });

    try {
      // Step 1: Validate PRD Content
      updateStep('validate-prd', 'running');
      await new Promise(resolve => setTimeout(resolve, 500)); // Brief validation
      
      if (!prdContent.trim()) {
        throw new Error('PRD content is empty');
      }
      
      updateStep('validate-prd', 'completed');

      // Step 2: Save PRD File
      updateStep('save-prd', 'running');
      const docsDir = `${projectPath}/.taskmaster/docs`;
      const prdFilePath = `${docsDir}/prd.txt`;
      
      // Create directory and save file
      const saveResult = await window.electronAPI.createFile(prdFilePath, prdContent);
      if (!saveResult.success && saveResult.error === 'File already exists') {
        // Overwrite existing file
        const overwriteResult = await window.electronAPI.saveFile(prdFilePath, prdContent);
        if (!overwriteResult.success) {
          throw new Error(`Failed to save PRD file: ${overwriteResult.error}`);
        }
      } else if (!saveResult.success) {
        throw new Error(`Failed to save PRD file: ${saveResult.error}`);
      }
      
      updateStep('save-prd', 'completed');

      // Step 3: Configure Claude Taskmaster
      updateStep('configure-taskmaster', 'running');
      const configResult = await claudeTaskmasterService.configureTaskmaster(
        projectPath,
        anthropicApiKey,
        perplexityApiKey
      );
      
      if (!configResult.success) {
        throw new Error(`Configuration failed: ${configResult.error}`);
      }
      
      updateStep('configure-taskmaster', 'completed');

      // Step 4: Parse PRD with Claude Taskmaster (with enhanced real-time sub-steps)
      updateStep('parse-prd', 'running');

      // Sub-step 1: Initialize Taskmaster CLI
      updateSubStep('parse-prd', 'init-taskmaster', 'running');
      console.log('🚀 [TaskGen] Starting Claude Taskmaster CLI initialization...');

      // Brief pause to show initialization step
      await new Promise(resolve => setTimeout(resolve, 800));
      updateSubStep('parse-prd', 'init-taskmaster', 'completed');

      // Sub-step 2: Analyze PRD Content
      updateSubStep('parse-prd', 'analyze-prd', 'running');
      console.log('🧠 [TaskGen] Claude AI analyzing PRD content...');

      // Brief pause to show analysis step
      await new Promise(resolve => setTimeout(resolve, 1200));
      updateSubStep('parse-prd', 'analyze-prd', 'completed');

      // Sub-step 3: Generate Task Structure
      updateSubStep('parse-prd', 'generate-structure', 'running');
      console.log('⚙️ [TaskGen] Generating task structure...');

      // Actually call the Claude Taskmaster service
      const parseResult = await claudeTaskmasterService.parsePRD(projectPath, prdContent, 'prd.txt');

      if (!parseResult.success) {
        updateSubStep('parse-prd', 'generate-structure', 'failed', parseResult.error);
        throw new Error(`PRD parsing failed: ${parseResult.error}`);
      }

      updateSubStep('parse-prd', 'generate-structure', 'completed');

      // Sub-step 4: Wait for Task File Creation
      updateSubStep('parse-prd', 'wait-for-file', 'running');
      console.log('📁 [TaskGen] Waiting for tasks.json file creation...');

      // Brief pause to show file waiting step (the actual waiting happens in the service)
      await new Promise(resolve => setTimeout(resolve, 600));
      updateSubStep('parse-prd', 'wait-for-file', 'completed');

      // Sub-step 5: Validate Task Output
      updateSubStep('parse-prd', 'validate-output', 'running');
      console.log('✅ [TaskGen] Validating generated tasks...');

      // Brief validation pause
      await new Promise(resolve => setTimeout(resolve, 400));
      updateSubStep('parse-prd', 'validate-output', 'completed');

      // Sub-step 6: Finalize Task Generation
      updateSubStep('parse-prd', 'save-tasks', 'running');
      console.log('🎯 [TaskGen] Finalizing task generation...');

      // Brief finalization pause
      await new Promise(resolve => setTimeout(resolve, 300));
      updateSubStep('parse-prd', 'save-tasks', 'completed');

      updateStep('parse-prd', 'completed');

      // Step 5: Validate Generated Tasks
      updateStep('validate-tasks', 'running');
      if (!parseResult.tasksFilePath) {
        throw new Error('No tasks file path returned from parsing');
      }

      // Verify the tasks file exists and is valid
      const fileResult = await window.electronAPI.readFile(parseResult.tasksFilePath);
      if (!fileResult.success) {
        throw new Error(`Tasks file not found: ${fileResult.error}`);
      }

      try {
        JSON.parse(fileResult.content);
      } catch (error) {
        throw new Error('Generated tasks file contains invalid JSON');
      }
      
      updateStep('validate-tasks', 'completed');

      // Step 6: Complete
      updateStep('complete', 'completed');
      
      setGenerationState(prev => ({ ...prev, isActive: false }));

      // Notify parent component
      onTasksGenerated({
        success: true,
        tasksGenerated: parseResult.tasksGenerated,
        filePath: parseResult.tasksFilePath
      });

      toast({
        title: "Tasks Generated Successfully",
        description: `Generated ${parseResult.tasksGenerated} tasks from PRD.`,
      });

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error('Task generation failed:', error);
      
      // Mark current step as failed
      const currentStep = generationState.steps[generationState.currentStepIndex];
      if (currentStep) {
        updateStep(currentStep.id, 'failed', errorMessage);
      }
      
      setGenerationState(prev => ({ ...prev, isActive: false }));
      
      toast({
        title: "Task Generation Failed",
        description: errorMessage,
        variant: "destructive",
      });

      onTasksGenerated({
        success: false,
        error: errorMessage
      });
    }
  }, [projectPath, prdContent, anthropicApiKey, perplexityApiKey, initializeSteps, updateStep, generationState.currentStepIndex, generationState.steps, onTasksGenerated, toast]);

  // Calculate progress percentage (including sub-steps)
  const getProgressPercentage = useCallback(() => {
    let totalTasks = 0;
    let completedTasks = 0;

    generationState.steps.forEach(step => {
      if (step.subSteps && step.subSteps.length > 0) {
        // For steps with sub-steps, count each sub-step
        totalTasks += step.subSteps.length;
        completedTasks += step.subSteps.filter(subStep => subStep.status === 'completed').length;
      } else {
        // For regular steps, count the step itself
        totalTasks += 1;
        if (step.status === 'completed') completedTasks += 1;
      }
    });

    return totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;
  }, [generationState.steps]);

  // Get elapsed time
  const getElapsedTime = useCallback(() => {
    if (!generationState.startTime) return 0;
    return Math.floor((Date.now() - generationState.startTime) / 1000);
  }, [generationState.startTime]);

  // Get estimated completion time
  const getEstimatedCompletion = useCallback(() => {
    const progress = getProgressPercentage();
    const elapsed = getElapsedTime();
    if (progress <= 0 || elapsed <= 0) return null;

    const estimatedTotal = (elapsed / progress) * 100;
    const remaining = Math.max(0, estimatedTotal - elapsed);
    return Math.round(remaining);
  }, [getProgressPercentage, getElapsedTime]);

  // Get current activity description
  const getCurrentActivity = useCallback(() => {
    const runningStep = generationState.steps.find(s => s.status === 'running');
    if (runningStep?.subSteps) {
      const runningSubStep = runningStep.subSteps.find(ss => ss.status === 'running');
      return runningSubStep ? runningSubStep.title : runningStep.title;
    }
    return runningStep?.title || 'Processing...';
  }, [generationState.steps]);

  // Render step status icon
  const renderStepIcon = (step: GenerationStep) => {
    switch (step.status) {
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'failed':
        return <XCircle className="h-5 w-5 text-red-500" />;
      case 'running':
        return <Loader2 className="h-5 w-5 text-blue-500 animate-spin" />;
      default:
        return <div className="h-5 w-5 rounded-full border-2 border-gray-300" />;
    }
  };

  // Render sub-step status icon
  const renderSubStepIcon = (subStep: GenerationSubStep) => {
    switch (subStep.status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'running':
        return <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />;
      default:
        return <div className="h-4 w-4 rounded-full border-2 border-gray-300" />;
    }
  };

  // Get step duration
  const getStepDuration = (step: GenerationStep) => {
    if (!step.startTime) return null;
    const endTime = step.endTime || Date.now();
    return Math.floor((endTime - step.startTime) / 1000);
  };

  // Get sub-step duration
  const getSubStepDuration = (subStep: GenerationSubStep) => {
    if (!subStep.startTime) return null;
    const endTime = subStep.endTime || Date.now();
    return Math.floor((endTime - subStep.startTime) / 1000);
  };

  return (
    <div className={`space-y-6 ${className}`}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Claude Taskmaster Task Generation
          </CardTitle>
          <CardDescription>
            Generate structured tasks from your PRD using Claude Taskmaster AI
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {!hasStarted ? (
            <div className="space-y-4">
              <Alert>
                <FileText className="h-4 w-4" />
                <AlertDescription>
                  <strong>Ready to generate tasks!</strong> This process will:
                  <ul className="list-disc list-inside mt-2 space-y-1">
                    <li>Save your PRD to the project structure</li>
                    <li>Configure Claude Taskmaster with your API keys</li>
                    <li>Parse the PRD and generate structured tasks</li>
                    <li>Validate the generated tasks.json file</li>
                  </ul>
                </AlertDescription>
              </Alert>
              
              <div className="flex gap-2">
                <Button onClick={startGeneration} className="flex items-center gap-2">
                  <Zap className="h-4 w-4" />
                  Start Task Generation
                </Button>
                {onCancel && (
                  <Button variant="outline" onClick={onCancel}>
                    Cancel
                  </Button>
                )}
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              {/* Enhanced Progress Overview */}
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">
                    Overall Progress: {getProgressPercentage()}%
                  </span>
                  <div className="flex items-center gap-3 text-sm text-muted-foreground">
                    <span className="flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      {getElapsedTime()}s elapsed
                    </span>
                    <span className="flex items-center gap-1">
                      <Target className="h-3 w-3" />
                      {generationState.steps.filter(s => s.status === 'completed').length}/{generationState.steps.length} steps
                    </span>
                    {generationState.isActive && getEstimatedCompletion() && (
                      <span className="flex items-center gap-1 text-blue-600">
                        <Clock className="h-3 w-3" />
                        ~{getEstimatedCompletion()}s remaining
                      </span>
                    )}
                  </div>
                </div>
                <Progress value={getProgressPercentage()} className="w-full h-3" />

                {/* Current Activity Indicator */}
                {generationState.isActive && (
                  <div className="flex items-center gap-2 text-sm text-blue-600 bg-blue-50 p-2 rounded-md">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <span>
                      {(() => {
                        const runningStep = generationState.steps.find(s => s.status === 'running');
                        if (runningStep?.subSteps) {
                          const runningSubStep = runningStep.subSteps.find(ss => ss.status === 'running');
                          if (runningSubStep) {
                            // Return more descriptive live activity based on the step
                            switch (runningSubStep.id) {
                              case 'initialize':
                                return 'Initializing Claude Taskmaster CLI environment and validating API keys...';
                              case 'analyze':
                                return 'Claude AI is analyzing your PRD content and extracting project requirements...';
                              case 'generate':
                                return 'Creating hierarchical task breakdown with dependencies and priority mapping...';
                              case 'wait':
                                return 'Monitoring filesystem for tasks.json file creation and validation...';
                              case 'validate':
                                return 'Verifying task structure, dependencies, and JSON format completeness...';
                              case 'finalize':
                                return 'Confirming tasks.json is ready for project orchestration and board creation...';
                              default:
                                return runningSubStep.description;
                            }
                          }
                          return runningStep.description;
                        }
                        return runningStep?.description || 'Processing...';
                      })()}
                    </span>
                  </div>
                )}
              </div>

              {/* Step Details */}
              <div className="space-y-3">
                {generationState.steps.map((step, index) => (
                  <div key={step.id} className="space-y-2">
                    <div className="flex items-start gap-3 p-3 rounded-lg border">
                      {renderStepIcon(step)}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <h4 className={`font-medium ${
                            step.status === 'completed' ? 'text-green-600' :
                            step.status === 'failed' ? 'text-red-600' :
                            step.status === 'running' ? 'text-blue-600' :
                            'text-gray-600'
                          }`}>
                            {step.title}
                          </h4>
                          {getStepDuration(step) && (
                            <span className="text-xs text-muted-foreground">
                              {getStepDuration(step)}s
                            </span>
                          )}
                        </div>
                        <p className="text-sm text-muted-foreground">
                          {step.description}
                        </p>
                        {step.error && (
                          <p className="text-sm text-red-500 mt-1">
                            Error: {step.error}
                          </p>
                        )}
                      </div>
                    </div>

                    {/* Enhanced Sub-steps for detailed progress */}
                    {step.subSteps && (step.status === 'running' || step.status === 'completed') && (
                      <div className="ml-6 space-y-2 mt-3">
                        <div className="text-xs font-medium text-gray-600 mb-2 flex items-center gap-1">
                          <div className="h-px bg-gray-300 flex-1"></div>
                          <span className="px-2">Detailed Progress</span>
                          <div className="h-px bg-gray-300 flex-1"></div>
                        </div>
                        {step.subSteps.map((subStep, subIndex) => (
                          <div key={subStep.id} className={`flex items-start gap-3 p-3 rounded-md border-l-4 ${
                            subStep.status === 'completed' ? 'border-green-400 bg-green-50/50' :
                            subStep.status === 'failed' ? 'border-red-400 bg-red-50/50' :
                            subStep.status === 'running' ? 'border-blue-400 bg-blue-50/50' :
                            'border-gray-300 bg-gray-50/30'
                          }`}>
                            {renderSubStepIcon(subStep)}
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center justify-between">
                                <h5 className={`text-sm font-medium ${
                                  subStep.status === 'completed' ? 'text-green-700' :
                                  subStep.status === 'failed' ? 'text-red-700' :
                                  subStep.status === 'running' ? 'text-blue-700' :
                                  'text-gray-600'
                                }`}>
                                  {subStep.title}
                                  {subStep.status === 'running' && (
                                    <span className="ml-2 inline-flex items-center gap-1 text-xs text-blue-600 font-medium">
                                      <div className="w-1.5 h-1.5 bg-blue-600 rounded-full animate-pulse"></div>
                                      • Active
                                    </span>
                                  )}
                                </h5>
                                <div className="flex items-center gap-2">
                                  {getSubStepDuration(subStep) && (
                                    <span className="text-xs text-muted-foreground bg-white px-2 py-1 rounded">
                                      {getSubStepDuration(subStep)}s
                                    </span>
                                  )}
                                </div>
                              </div>
                              <p className="text-xs text-muted-foreground mt-1">
                                {subStep.description}
                              </p>

                              {/* Real-time activity indicator for running steps */}
                              {subStep.status === 'running' && (
                                <div className="mt-2 p-2 bg-blue-50 border border-blue-200 rounded text-xs">
                                  <div className="flex items-center gap-2 text-blue-700">
                                    <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                                    <span className="font-medium">Currently:</span>
                                    <span>{(() => {
                                      // Return more descriptive live activity based on the step
                                      switch (subStep.id) {
                                        case 'initialize':
                                          return 'Initializing Claude Taskmaster CLI environment and validating API keys...';
                                        case 'analyze':
                                          return 'Claude AI is analyzing your PRD content and extracting project requirements...';
                                        case 'generate':
                                          return 'Creating hierarchical task breakdown with dependencies and priority mapping...';
                                        case 'wait':
                                          return 'Monitoring filesystem for tasks.json file creation and validation...';
                                        case 'validate':
                                          return 'Verifying task structure, dependencies, and JSON format completeness...';
                                        case 'finalize':
                                          return 'Confirming tasks.json is ready for project orchestration and board creation...';
                                        default:
                                          return subStep.description;
                                      }
                                    })()}</span>
                                  </div>
                                </div>
                              )}

                              {subStep.error && (
                                <div className="mt-2 p-2 bg-red-100 border border-red-200 rounded text-xs text-red-700">
                                  <strong>Error:</strong> {subStep.error}
                                </div>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </div>

              {/* Completion Summary */}
              {!generationState.isActive && generationState.steps.every(step => step.status === 'completed') && (
                <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
                  <div className="flex items-center gap-2 text-green-800 font-medium mb-2">
                    <CheckCircle className="h-5 w-5" />
                    Task Generation Complete!
                  </div>
                  <div className="text-sm text-green-700 space-y-1">
                    <p>✅ Successfully generated tasks from your PRD</p>
                    <p>✅ Tasks.json file created and validated</p>
                    <p>✅ Ready for project orchestration</p>
                  </div>
                  <div className="mt-3 text-xs text-green-600">
                    Total time: {getElapsedTime()}s
                  </div>
                </div>
              )}

              {/* Action Buttons */}
              {!generationState.isActive && (
                <div className="flex gap-2 pt-4 border-t">
                  {generationState.steps.some(step => step.status === 'failed') ? (
                    <Button onClick={startGeneration} variant="outline" className="flex items-center gap-2">
                      <Loader2 className="h-4 w-4" />
                      Retry Generation
                    </Button>
                  ) : null}
                  {onCancel && (
                    <Button
                      onClick={onCancel}
                      className={generationState.steps.every(step => step.status === 'completed') ?
                        "bg-green-600 hover:bg-green-700" : ""}
                    >
                      {generationState.steps.every(step => step.status === 'completed') ?
                        'Continue to Orchestration' : 'Cancel'}
                    </Button>
                  )}
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
