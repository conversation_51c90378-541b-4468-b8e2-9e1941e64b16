"use client"

import React, { useState, useCallback, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/components/ui/use-toast';
import { PRDUploadUI } from '../intake/prd-upload-ui';
import { prdIntakeService, PRDValidationResult, PRDParseResult } from '../intake/prd-intake-service';
import { claudeTaskmasterService } from '../../services/claude-taskmaster-service';
import { getGlobalSettingsManager } from '../settings/global-settings';
import { TaskmasterTaskGenerationUI } from './taskmaster-task-generation-ui';
import { TaskmasterOrchestrationUI } from '../orchestrators/taskmaster-orchestration-ui';
import { LLMRequestService } from '../agents/llm-request-service';
// ARCHITECTURE RESTORATION: Removed unauthorized Kanban orchestration import
// import { OrchestrationResult } from '../orchestrators/kanban-task-orchestrator';
import { FolderOpen, Plus, FileText, Settings, CheckCircle, Key, Check, X, Loader2, Target } from 'lucide-react';

interface CreateProjectWizardProps {
  onProjectCreated?: () => void;
  onCancel?: () => void;
}

// ✅ Step-by-step state machine approach
type WizardStep = 'name' | 'apikeys' | 'prd' | 'taskmaster' | 'orchestration' | 'complete';

interface ProjectCreationState {
  step: WizardStep;
  projectName: string;
  projectPath: string | null;
  folderPath: string | null;
  taskmasterInitialized: boolean;
  apiKeysConfigured: boolean;
  prdValidated: boolean;
  prdUploaded: boolean;
  prdParsed: boolean;
  tasksGenerated: boolean;
  electronAPIAvailable: boolean | null;
}

export const CreateProjectWizard: React.FC<CreateProjectWizardProps> = ({
  onProjectCreated,
  onCancel
}) => {
  const { toast } = useToast();

  // ✅ Simplified state management with explicit tracking
  const [state, setState] = useState<ProjectCreationState>({
    step: 'name',
    projectName: '',
    projectPath: null,
    folderPath: null,
    taskmasterInitialized: false,
    apiKeysConfigured: false,
    prdValidated: false,
    prdUploaded: false,
    prdParsed: false,
    tasksGenerated: false,
    electronAPIAvailable: null
  });

  // ✅ Separate API key state for form management
  const [apiKeys, setApiKeys] = useState({
    anthropic: '',
    perplexity: ''
  });

  // ✅ PRD content state for Taskmaster step
  const [prdContent, setPrdContent] = useState('');

  // ✅ API Key validation states
  const [apiKeyValidation, setApiKeyValidation] = useState({
    anthropic: { isValidating: false, isValid: null as boolean | null },
    perplexity: { isValidating: false, isValid: null as boolean | null }
  });

  // ✅ Loading and error state tracking
  const [isProcessing, setIsProcessing] = useState(false);
  const [lastError, setLastError] = useState<string | null>(null);

  // ✅ Enhanced Electron API detection with better logging
  useEffect(() => {
    const detectElectronAPI = async () => {
      console.log('🔍 Starting Electron API detection...');

      const checkAPI = () => {
        const available = typeof window !== 'undefined' &&
                         !!window.electronAPI &&
                         typeof window.electronAPI.selectFolder === 'function';

        console.log('🔍 API Check:', {
          windowExists: typeof window !== 'undefined',
          electronAPIExists: !!window.electronAPI,
          selectFolderExists: window.electronAPI && typeof window.electronAPI.selectFolder === 'function',
          available
        });

        return available;
      };

      // Immediate check
      if (checkAPI()) {
        console.log('✅ Electron API available immediately');
        setState(prev => ({ ...prev, electronAPIAvailable: true }));
        return;
      }

      // Polling with timeout
      console.log('⏳ Polling for Electron API...');
      let attempts = 0;
      const maxAttempts = 25; // 5 seconds

      const poll = async () => {
        attempts++;
        console.log(`🔍 Poll attempt ${attempts}/${maxAttempts}`);

        if (checkAPI()) {
          console.log('✅ Electron API detected via polling');
          setState(prev => ({ ...prev, electronAPIAvailable: true }));
          return;
        }

        if (attempts < maxAttempts) {
          setTimeout(poll, 200);
        } else {
          console.warn('⚠️ Electron API not available after polling');
          setState(prev => ({ ...prev, electronAPIAvailable: false }));
        }
      };

      setTimeout(poll, 200);
    };

    detectElectronAPI();
  }, []);
  // ✅ Get global settings manager instance
  const settingsManager = getGlobalSettingsManager();

  // ✅ Load existing API keys on mount
  useEffect(() => {
    const loadApiKeys = () => {
      try {
        const anthropicKey = settingsManager.getApiKey('anthropic') || '';
        const perplexityKey = settingsManager.getApiKey('perplexity') || '';

        setApiKeys({
          anthropic: anthropicKey,
          perplexity: perplexityKey
        });

        console.log('🔑 Loaded existing API keys:', {
          anthropic: anthropicKey ? '***' : 'none',
          perplexity: perplexityKey ? '***' : 'none'
        });
      } catch (error) {
        console.warn('⚠️ Failed to load API keys:', error);
      }
    };

    loadApiKeys();
  }, []);

  // ✅ Auto-validate API keys when entering the apikeys step
  useEffect(() => {
    const autoValidateApiKeys = async () => {
      if (state.step !== 'apikeys') return;

      console.log('🔄 Auto-validating API keys on step entry...');

      // Check if we're in web mode (Electron not available)
      const isElectronAvailable = typeof window !== 'undefined' &&
                                 window.electronAPI?.taskmaster !== undefined;
      const isWebMode = !isElectronAvailable;

      if (isWebMode) {
        console.log('🌐 Web mode detected - skipping auto-validation');
        return;
      }

      // Auto-validate Anthropic key if it exists and hasn't been validated yet
      if (apiKeys.anthropic.trim() && apiKeyValidation.anthropic.isValid === null) {
        console.log('🔑 Auto-validating Anthropic API key...');
        await validateApiKey('anthropic');
      }

      // Auto-validate Perplexity key if it exists and hasn't been validated yet
      if (apiKeys.perplexity.trim() && apiKeyValidation.perplexity.isValid === null) {
        console.log('🔑 Auto-validating Perplexity API key...');
        await validateApiKey('perplexity');
      }
    };

    autoValidateApiKeys();
  }, [state.step, apiKeys.anthropic, apiKeys.perplexity, apiKeyValidation.anthropic.isValid, apiKeyValidation.perplexity.isValid]);

  // ✅ API Key validation function
  const validateApiKey = async (provider: 'anthropic' | 'perplexity') => {
    const apiKey = apiKeys[provider];
    if (!apiKey.trim()) return;

    setApiKeyValidation(prev => ({
      ...prev,
      [provider]: { isValidating: true, isValid: null }
    }));

    try {
      const llmService = LLMRequestService.getInstance();
      const isValid = await llmService.validateApiKey(provider, apiKey);

      setApiKeyValidation(prev => ({
        ...prev,
        [provider]: { isValidating: false, isValid }
      }));

      console.log(`🔑 ${provider} API key validation:`, isValid ? '✅ Valid' : '❌ Invalid');
    } catch (error) {
      console.error(`❌ ${provider} API key validation failed:`, error);
      setApiKeyValidation(prev => ({
        ...prev,
        [provider]: { isValidating: false, isValid: false }
      }));
    }
  };

  // ✅ Handle API key changes with validation reset
  const handleApiKeyChange = (provider: 'anthropic' | 'perplexity', value: string) => {
    setApiKeys(prev => ({ ...prev, [provider]: value }));

    // Reset validation state when key changes
    setApiKeyValidation(prev => ({
      ...prev,
      [provider]: { isValidating: false, isValid: null }
    }));

    // Save to settings manager
    if (value.trim()) {
      settingsManager.setApiKey(provider, value);
    } else {
      settingsManager.removeApiKey(provider);
    }
  };

  // ✅ Get validation icon for API key
  const getValidationIcon = (provider: 'anthropic' | 'perplexity') => {
    const validation = apiKeyValidation[provider];
    if (validation.isValidating) {
      return <div className="animate-spin h-4 w-4 border-2 border-gray-300 border-t-blue-600 rounded-full" />;
    }
    if (validation.isValid === true) {
      return <Check className="h-4 w-4 text-green-500" />;
    }
    if (validation.isValid === false) {
      return <X className="h-4 w-4 text-red-500" />;
    }
    return null;
  };

  // ✅ Reset wizard to initial state
  const resetWizard = useCallback(() => {
    console.log('🔄 Resetting wizard state');
    setState({
      step: 'name',
      projectName: '',
      projectPath: null,
      folderPath: null,
      taskmasterInitialized: false,
      apiKeysConfigured: false,
      prdValidated: false,
      prdUploaded: false,
      prdParsed: false,
      tasksGenerated: false,
      electronAPIAvailable: state.electronAPIAvailable // Preserve detection result
    });
    setApiKeys({ anthropic: '', perplexity: '' });
    setIsProcessing(false);
    setLastError(null);
  }, [state.electronAPIAvailable]);

  // ✅ Handle cancel with proper cleanup
  const handleCancel = useCallback(() => {
    console.log('❌ User cancelled wizard');
    resetWizard();
    onCancel?.();
  }, [resetWizard, onCancel]);

  // ✅ Step 1: Validate inputs before proceeding
  const validateInputs = (): boolean => {
    console.log('🔍 Validating inputs:', {
      projectName: state.projectName,
      electronAPIAvailable: state.electronAPIAvailable
    });

    if (!state.projectName.trim()) {
      console.log('❌ Validation failed: Project name is empty');
      setLastError('Project name is required');
      return false;
    }

    if (state.electronAPIAvailable !== true) {
      console.log('❌ Validation failed: Electron API not available');
      setLastError('Desktop app required for project creation');
      return false;
    }

    console.log('✅ Validation passed');
    return true;
  };

  // ✅ Step 2: Select folder with error handling
  const selectProjectFolder = async (): Promise<string | null> => {
    try {
      console.log('📁 Opening folder selection dialog...');
      const selectedFolder = await window.electronAPI.selectFolder();

      if (!selectedFolder || !selectedFolder.success || !selectedFolder.path) {
        console.log('📁 User cancelled folder selection');
        return null;
      }

      console.log('✅ Folder selected:', selectedFolder.path);
      return selectedFolder.path;
    } catch (error) {
      console.error('❌ Folder selection failed:', error);
      throw new Error(`Folder selection failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  // ✅ Step 3: Check if directory exists
  const checkDirectoryExists = async (projectPath: string): Promise<boolean> => {
    try {
      // Try to read the directory to see if it exists
      const result = await window.electronAPI.readDirectory(projectPath);
      return result.success; // If successful, directory exists
    } catch (error) {
      return false; // If error, directory doesn't exist
    }
  };

  // ✅ Helper: Generate alternative project name suggestions
  const generateAlternativeNames = async (basePath: string, originalName: string): Promise<string[]> => {
    const suggestions: string[] = [];
    const timestamp = new Date().toISOString().slice(0, 10).replace(/-/g, ''); // YYYYMMDD

    // Try numbered variations
    for (let i = 2; i <= 5; i++) {
      const suggestion = `${originalName}-${i}`;
      const exists = await checkDirectoryExists(`${basePath}/${suggestion}`);
      if (!exists) {
        suggestions.push(suggestion);
      }
    }

    // Try timestamp variation
    const timestampSuggestion = `${originalName}-${timestamp}`;
    const timestampExists = await checkDirectoryExists(`${basePath}/${timestampSuggestion}`);
    if (!timestampExists) {
      suggestions.push(timestampSuggestion);
    }

    return suggestions.slice(0, 3); // Return max 3 suggestions
  };

  // ✅ Step 3: Create project directory with existence handling
  const createProjectDirectory = async (basePath: string, projectName: string): Promise<string> => {
    try {
      const projectPath = `${basePath}/${projectName}`;
      console.log('📁 Creating project directory:', projectPath);

      // Check if directory already exists
      const directoryExists = await checkDirectoryExists(projectPath);

      if (directoryExists) {
        console.log('📁 Directory already exists, checking if it\'s a valid project...');

        // Check if it's already a project (has .project file)
        try {
          const projectFileResult = await window.electronAPI.readFile(`${projectPath}/.project`);
          if (projectFileResult.success) {
            // Directory exists and is already a project - generate suggestions
            const suggestions = await generateAlternativeNames(basePath, projectName);
            const suggestionText = suggestions.length > 0
              ? ` Try: ${suggestions.join(', ')}`
              : '';
            throw new Error(`A project named "${projectName}" already exists in this location. Please choose a different name.${suggestionText}`);
          }
        } catch (readError) {
          // .project file doesn't exist, so we can use this directory
          console.log('📁 Directory exists but is not a project, creating .project file...');
        }

        // Directory exists but is not a project, create .project file
        const directoryResult = await window.electronAPI.createFile(`${projectPath}/.project`, '');

        if (!directoryResult || !directoryResult.success) {
          // If .project file already exists, that's actually fine - just use saveFile to overwrite
          if (directoryResult?.error === 'File already exists') {
            console.log('📝 .project file already exists, overwriting...');
            const overwriteResult = await window.electronAPI.saveFile(`${projectPath}/.project`, '');
            if (!overwriteResult || !overwriteResult.success) {
              throw new Error(`Failed to overwrite existing .project file: ${overwriteResult?.error || 'Unknown error'}`);
            }
          } else {
            throw new Error(`Failed to initialize project in existing directory: ${directoryResult?.error || 'Unknown error'}`);
          }
        }
      } else {
        // Directory doesn't exist, create it with .project file
        const directoryResult = await window.electronAPI.createFile(`${projectPath}/.project`, '');

        if (!directoryResult || !directoryResult.success) {
          if (directoryResult?.error === 'File already exists') {
            const suggestions = await generateAlternativeNames(basePath, projectName);
            const suggestionText = suggestions.length > 0
              ? ` Try: ${suggestions.join(', ')}`
              : '';
            throw new Error(`A project named "${projectName}" already exists in this location. Please choose a different name.${suggestionText}`);
          }
          throw new Error(`Failed to create project directory: ${directoryResult?.error || 'Unknown error'}`);
        }
      }

      console.log('✅ Project directory created:', projectPath);
      return projectPath;
    } catch (error) {
      console.error('❌ Directory creation failed:', error);
      throw new Error(`${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  // ✅ Step 4: Register project with services
  const registerProject = async (projectPath: string, projectName: string): Promise<void> => {
    try {
      console.log('🔄 Registering project with services...');

      // Import services dynamically to avoid circular dependencies
      console.log('📦 Importing active project service...');
      const { activeProjectService } = await import('../../services/active-project-service');
      console.log('✅ Active project service imported');

      // Set as active project
      console.log('🔄 Setting as active project...');
      activeProjectService.setActiveProject(projectPath, projectName);
      console.log('✅ Project set as active');

      // Register with settings manager
      console.log('🔄 Registering with settings manager...');
      await Promise.race([
        settingsManager.createProject(projectName, projectPath),
        new Promise((_, reject) => setTimeout(() => reject(new Error('Settings manager timeout')), 5000))
      ]);
      console.log('✅ Project registered with settings manager');

      // Verify registration
      console.log('🔄 Verifying registration...');
      const activeProject = activeProjectService.getActiveProject();
      if (!activeProject || activeProject.path !== projectPath) {
        throw new Error('Failed to verify project registration');
      }

      console.log('✅ Project registration verified');
    } catch (error) {
      console.error('❌ Project registration failed:', error);
      throw new Error(`Project registration failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  // ✅ Step 5: Initialize Taskmaster (optional, non-blocking with graceful degradation)
  const initializeTaskmaster = async (projectPath: string, projectName: string): Promise<boolean> => {
    try {
      console.log('🔄 Initializing Taskmaster...');

      // Check if we're in Electron environment
      console.log('🔍 Checking Electron environment...');
      const isElectronAvailable = typeof window !== 'undefined' &&
                                 window.electronAPI?.taskmaster !== undefined;

      if (!isElectronAvailable) {
        console.log('⚠️ Taskmaster not available in web mode - skipping initialization');
        return false;
      }
      console.log('✅ Electron environment detected');

      // ✅ ENHANCED: Check installation with longer timeout and graceful fallback
      console.log('📦 Checking Taskmaster installation...');
      let installCheck;
      try {
        installCheck = await Promise.race([
          claudeTaskmasterService.checkInstallation(),
          new Promise((_, reject) => setTimeout(() => reject(new Error('Installation check timeout')), 20000)) // Increased to 20s
        ]);
        console.log('📦 Taskmaster installation check:', installCheck);
      } catch (timeoutError) {
        console.warn('⚠️ Installation check timed out - assuming not installed and continuing');
        installCheck = { installed: false, error: 'Check timed out' };
      }

      // ✅ ENHANCED: Install with longer timeout and better error handling
      if (!installCheck.installed) {
        console.log('📦 Installing Taskmaster...');
        toast({
          title: "Installing Claude Taskmaster",
          description: "This may take a few minutes for first-time installation...",
        });

        try {
          const installResult = await Promise.race([
            claudeTaskmasterService.installTaskmaster(),
            new Promise((_, reject) => setTimeout(() => reject(new Error('Installation timeout')), 90000)) // Increased to 90s
          ]);

          if (!installResult.success) {
            console.warn('⚠️ Taskmaster installation failed:', installResult.error);
            toast({
              title: "Taskmaster Installation Skipped",
              description: "Installation failed but project creation will continue. You can install manually later.",
              variant: "default",
            });
            return false;
          }
          console.log('✅ Taskmaster installed successfully');
        } catch (installTimeoutError) {
          console.warn('⚠️ Taskmaster installation timed out - continuing without it');
          toast({
            title: "Taskmaster Installation Timeout",
            description: "Installation is taking longer than expected. Project creation will continue. You can install manually later.",
            variant: "default",
          });
          return false;
        }
      }

      // ✅ ENHANCED: Initialize project with longer timeout and graceful fallback
      console.log('🔄 Initializing Taskmaster project...');
      try {
        const initResult = await Promise.race([
          claudeTaskmasterService.initializeProject(projectPath, projectName),
          new Promise((_, reject) => setTimeout(() => reject(new Error('Initialization timeout')), 30000)) // Increased to 30s
        ]);

        if (!initResult.success) {
          console.warn('⚠️ Taskmaster initialization failed:', initResult.error);
          toast({
            title: "Taskmaster Initialization Skipped",
            description: "Initialization failed but project creation will continue. You can initialize manually later.",
            variant: "default",
          });
          return false;
        }

        console.log('✅ Taskmaster initialized successfully');
        toast({
          title: "Taskmaster Ready",
          description: "Claude Taskmaster has been successfully initialized for this project.",
        });
        return true;
      } catch (initTimeoutError) {
        console.warn('⚠️ Taskmaster initialization timed out - continuing without it');
        toast({
          title: "Taskmaster Initialization Timeout",
          description: "Initialization is taking longer than expected. Project creation will continue. You can initialize manually later.",
          variant: "default",
        });
        return false;
      }

    } catch (error) {
      console.warn('⚠️ Taskmaster initialization error (non-blocking):', error);
      toast({
        title: "Taskmaster Setup Skipped",
        description: "Taskmaster setup encountered an issue but project creation will continue. You can set up Taskmaster manually later.",
        variant: "default",
      });
      return false;
    }
  };

  // ✅ Step 6: Advance to next step
  const advanceToApiKeys = (): void => {
    console.log('🎯 Advancing to API keys step...');
    console.log('🎯 Current state before advancement:', state);
    setState(prev => {
      const newState = {
        ...prev,
        step: 'apikeys' as WizardStep
      };
      console.log('🎯 New state after advancement:', newState);
      return newState;
    });
    console.log('✅ Successfully advanced to API keys step');
  };

  // ✅ Main project creation function - broken down into clear steps
  const createProject = async (): Promise<void> => {
    if (isProcessing) {
      console.log('⚠️ Project creation already in progress');
      return;
    }

    console.log('🚀 Starting project creation process...');
    setIsProcessing(true);
    setLastError(null);

    // ✅ ENHANCED: Add a longer timeout to prevent infinite loading but allow for Taskmaster installation
    const timeoutId = setTimeout(() => {
      console.warn('⏰ Project creation timed out after 2 minutes');
      setLastError('Project creation timed out. The project may have been created but Taskmaster setup is incomplete.');
      setIsProcessing(false);
      toast({
        title: "Project Creation Timeout",
        description: "The process took longer than expected. Check if your project was created and try setting up Taskmaster manually if needed.",
        variant: "destructive",
      });
    }, 120000); // 2 minute timeout to allow for Taskmaster installation

    try {
      // Step 1: Validate inputs
      console.log('📋 Step 1: Validating inputs...');
      if (!validateInputs()) {
        console.log('❌ Validation failed, stopping process');
        clearTimeout(timeoutId);
        setIsProcessing(false);
        return;
      }

      // Step 2: Select folder
      console.log('📁 Step 2: Selecting project folder...');
      const selectedPath = await selectProjectFolder();
      if (!selectedPath) {
        console.log('📁 User cancelled folder selection, stopping process');
        clearTimeout(timeoutId);
        setIsProcessing(false);
        return; // User cancelled
      }

      // Update state with folder path
      setState(prev => ({ ...prev, folderPath: selectedPath }));

      // Step 3: Create directory
      console.log('📁 Step 3: Creating project directory...');
      const projectPath = await createProjectDirectory(selectedPath, state.projectName);

      // Update state with project path
      setState(prev => ({ ...prev, projectPath }));

      // Step 4: Register project
      console.log('🔄 Step 4: Registering project...');
      await registerProject(projectPath, state.projectName);

      // Step 5: Initialize Taskmaster (non-blocking)
      console.log('🔧 Step 5: Initializing Taskmaster (optional)...');

      // ✅ ENHANCED: Show progress during Taskmaster initialization
      toast({
        title: "Setting up Claude Taskmaster",
        description: "Checking installation and initializing project...",
      });

      const taskmasterResult = await initializeTaskmaster(projectPath, state.projectName);

      // Update state with Taskmaster result
      setState(prev => ({ ...prev, taskmasterInitialized: taskmasterResult }));

      // Step 6: Advance to next step
      console.log('🎯 Step 6: Advancing to next step...');
      advanceToApiKeys();

      console.log('✅ Project creation completed successfully');

      toast({
        title: "Project Created",
        description: `${state.projectName} created successfully. Configure API keys to continue.`,
      });

      // Clear timeout and set processing to false after successful completion
      clearTimeout(timeoutId);
      setIsProcessing(false);

    } catch (error) {
      console.error('❌ Project creation failed:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      setLastError(errorMessage);

      toast({
        title: "Project Creation Failed",
        description: errorMessage,
        variant: "destructive",
      });

      // Clear timeout and set processing to false on error
      clearTimeout(timeoutId);
      setIsProcessing(false);
    }
  };

  // ✅ Handle API keys configuration
  const handleApiKeysNext = async (): Promise<void> => {
    try {
      console.log('🔑 Configuring API keys...');

      const isElectronAvailable = typeof window !== 'undefined' &&
                                 window.electronAPI?.taskmaster !== undefined;

      if (isElectronAvailable) {
        // In Electron mode, API keys are required and must be validated
        if (!apiKeys.anthropic.trim() || !apiKeys.perplexity.trim()) {
          setLastError('Both Anthropic and Perplexity API keys are required');
          toast({
            title: "Missing API Keys",
            description: "Both Anthropic and Perplexity API keys are required for Claude Taskmaster integration.",
            variant: "destructive",
          });
          return;
        }

        // Check if API keys are validated
        const anthropicValid = apiKeyValidation.anthropic.isValid === true;
        const perplexityValid = apiKeyValidation.perplexity.isValid === true;

        if (!anthropicValid || !perplexityValid) {
          const invalidKeys = [];
          if (!anthropicValid) invalidKeys.push('Anthropic');
          if (!perplexityValid) invalidKeys.push('Perplexity');

          setLastError(`Please validate your ${invalidKeys.join(' and ')} API key${invalidKeys.length > 1 ? 's' : ''} before proceeding`);
          toast({
            title: "API Key Validation Required",
            description: `Please validate your ${invalidKeys.join(' and ')} API key${invalidKeys.length > 1 ? 's' : ''} using the Validate button.`,
            variant: "destructive",
          });
          return;
        }
      }

      // Save API keys to settings
      console.log('🔑 [Wizard] About to save API keys:', {
        anthropic: apiKeys.anthropic ? '***' + apiKeys.anthropic.slice(-4) : 'empty',
        perplexity: apiKeys.perplexity ? '***' + apiKeys.perplexity.slice(-4) : 'empty'
      });

      if (apiKeys.anthropic.trim()) {
        settingsManager.setApiKey('anthropic', apiKeys.anthropic);
        console.log('🔑 [Wizard] Saved Anthropic API key:', '***' + apiKeys.anthropic.slice(-4));
      }
      if (apiKeys.perplexity.trim()) {
        settingsManager.setApiKey('perplexity', apiKeys.perplexity);
        console.log('🔑 [Wizard] Saved Perplexity API key:', '***' + apiKeys.perplexity.slice(-4));
      }

      // ✅ CRITICAL: Add delay to ensure settings are persisted
      console.log('🔑 [Wizard] Waiting for settings persistence...');
      await new Promise(resolve => setTimeout(resolve, 500));

      // Verify API keys were saved correctly
      const savedAnthropicKey = settingsManager.getApiKey('anthropic');
      const savedPerplexityKey = settingsManager.getApiKey('perplexity');
      console.log('🔍 [Wizard] Verification - API keys after save:', {
        anthropic: savedAnthropicKey ? '***' + savedAnthropicKey.slice(-4) : 'missing',
        perplexity: savedPerplexityKey ? '***' + savedPerplexityKey.slice(-4) : 'missing'
      });

      // ✅ CRITICAL: Verify keys match what we saved
      if (apiKeys.anthropic.trim() && savedAnthropicKey !== apiKeys.anthropic) {
        console.error('❌ [Wizard] Anthropic API key mismatch after saving!');
        setLastError('Failed to save Anthropic API key. Please try again.');
        return;
      }
      if (apiKeys.perplexity.trim() && savedPerplexityKey !== apiKeys.perplexity) {
        console.error('❌ [Wizard] Perplexity API key mismatch after saving!');
        setLastError('Failed to save Perplexity API key. Please try again.');
        return;
      }

      // Debug: Check all settings
      const allSettings = settingsManager.getSettings();
      console.log('🔍 [Wizard] All API keys in settings after save:', allSettings.apiKeys);

      // Update state and advance
      setState(prev => ({
        ...prev,
        apiKeysConfigured: true,
        step: 'prd'
      }));

      console.log('✅ API keys configured, advanced to PRD step');

      toast({
        title: "API Keys Configured",
        description: "Ready to upload PRD document.",
      });

    } catch (error) {
      console.error('❌ API keys configuration failed:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setLastError(errorMessage);
    }
  };

  // ✅ Handle PRD upload completion (no parsing yet)
  const handlePRDUploaded = useCallback((filePath: string, validation: PRDValidationResult, content?: string) => {
    console.log('✅ PRD uploaded:', filePath, validation);
    setState(prev => ({
      ...prev,
      prdValidated: validation.isValid,
      prdUploaded: true
    }));
    if (content) {
      setPrdContent(content);
    }
  }, []);

  // ✅ Handle PRD validation change
  const handlePRDValidationChange = useCallback((isValid: boolean) => {
    setState(prev => ({ ...prev, prdValidated: isValid }));
  }, []);

  // ✅ Handle advancing from PRD to Taskmaster step
  const advanceToTaskmaster = useCallback(() => {
    if (state.prdValidated && state.prdUploaded) {
      console.log('🎯 Advancing to Taskmaster step...');
      setState(prev => ({ ...prev, step: 'taskmaster' }));

      toast({
        title: "PRD Ready",
        description: "Ready to generate tasks with Claude Taskmaster.",
      });
    }
  }, [state.prdValidated, state.prdUploaded, toast]);

  // ✅ Handle task generation completion
  const handleTasksGenerated = useCallback((result: PRDParseResult) => {
    console.log('✅ Tasks generated:', result);
    if (result.success) {
      setState(prev => ({
        ...prev,
        prdParsed: true,
        tasksGenerated: true,
        step: 'orchestration'
      }));

      toast({
        title: "Tasks Generated Successfully",
        description: `Generated ${result.tasksGenerated} tasks. Ready for orchestration.`,
      });
    } else {
      setLastError(result.error || "Failed to generate tasks");
      toast({
        title: "Task Generation Failed",
        description: result.error || "Failed to generate tasks with Claude Taskmaster.",
        variant: "destructive",
      });
    }
  }, [toast]);



  // ✅ PHASE 4: Handle task submission to Micromanager with automatic sequential workflow
  const handleTasksSubmittedToMicromanager = useCallback((result: { success: boolean; tasksSubmitted: number; error?: string }) => {
    console.log('✅ Tasks submitted to Micromanager:', result);

    if (result.success) {
      setState(prev => ({ ...prev, step: 'complete' }));

      toast({
        title: "Sequential Workflow Ready",
        description: `Successfully set up ${result.tasksSubmitted} tasks for controlled sequential execution. The system is now ready for automated development.`,
      });
    } else {
      setLastError(result.error || "Task submission failed");
      toast({
        title: "Task Submission Failed",
        description: result.error || "Failed to submit tasks to Micromanager",
        variant: "destructive",
      });
    }
  }, [toast]);



  // ✅ Render current step
  const renderCurrentStep = () => {
    switch (state.step) {
      case 'name':
        return (
          <Card className="w-full max-w-md mx-auto">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Plus className="h-5 w-5" />
                Create New Project
              </CardTitle>
              <CardDescription>
                Enter a name for your new project. You'll then select a folder location.
                {state.electronAPIAvailable === false && (
                  <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-red-700 text-sm">
                    ⚠️ Project creation requires the desktop app.
                  </div>
                )}
                {state.electronAPIAvailable === null && (
                  <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-yellow-700 text-sm">
                    🔄 Checking desktop app availability...
                  </div>
                )}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <label htmlFor="project-name" className="text-sm font-medium">
                  Project Name
                </label>
                <Input
                  id="project-name"
                  value={state.projectName}
                  onChange={(e) => setState(prev => ({ ...prev, projectName: e.target.value }))}
                  placeholder="Enter project name"
                  disabled={state.electronAPIAvailable !== true || isProcessing}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && state.electronAPIAvailable === true && !isProcessing) {
                      createProject();
                    }
                  }}
                />
              </div>

              {lastError && (
                <div className="p-2 bg-red-50 border border-red-200 rounded text-red-700 text-sm">
                  {lastError}
                </div>
              )}

              <div className="flex gap-2 pt-4">
                <Button variant="outline" onClick={handleCancel} className="flex-1" disabled={isProcessing}>
                  Cancel
                </Button>
                <Button
                  onClick={createProject}
                  disabled={!state.projectName.trim() || state.electronAPIAvailable !== true || isProcessing}
                  className="flex-1"
                >
                  <FolderOpen className="h-4 w-4 mr-2" />
                  {isProcessing ? 'Creating...' :
                   state.electronAPIAvailable === null ? 'Checking...' :
                   state.electronAPIAvailable === false ? 'Desktop App Required' :
                   'Select Folder & Create'}
                </Button>
              </div>
            </CardContent>
          </Card>
        );

      case 'apikeys':
        const isElectronAvailable = typeof window !== 'undefined' &&
                                   window.electronAPI?.taskmaster !== undefined;
        const isWebMode = !isElectronAvailable;

        return (
          <Card className="w-full max-w-2xl mx-auto">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Key className="h-5 w-5" />
                Configure API Keys
              </CardTitle>
              <CardDescription>
                {isWebMode ? (
                  <>
                    Configure API keys for enhanced features. In web mode, these are optional.
                    <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-yellow-700 text-sm">
                      ⚠️ Running in web mode. Use the desktop app for full Claude Taskmaster integration.
                    </div>
                  </>
                ) : (
                  "Claude Taskmaster requires API keys for Anthropic and Perplexity to parse your PRD and generate tasks."
                )}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <label htmlFor="anthropic-key" className="text-sm font-medium">
                      Anthropic API Key {isWebMode ? '(Optional)' : '*'}
                    </label>
                    <div className="flex items-center gap-2">
                      {getValidationIcon('anthropic')}
                      {apiKeys.anthropic && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => validateApiKey('anthropic')}
                          disabled={apiKeyValidation.anthropic.isValidating}
                          className="h-7 px-2 text-xs"
                        >
                          Validate
                        </Button>
                      )}
                    </div>
                  </div>
                  <Input
                    id="anthropic-key"
                    type="password"
                    value={apiKeys.anthropic}
                    onChange={(e) => handleApiKeyChange('anthropic', e.target.value)}
                    placeholder="sk-ant-api03-..."
                  />
                  <p className="text-xs text-muted-foreground">
                    Used for main task generation and PRD parsing
                  </p>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <label htmlFor="perplexity-key" className="text-sm font-medium">
                      Perplexity API Key {isWebMode ? '(Optional)' : '*'}
                    </label>
                    <div className="flex items-center gap-2">
                      {getValidationIcon('perplexity')}
                      {apiKeys.perplexity && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => validateApiKey('perplexity')}
                          disabled={apiKeyValidation.perplexity.isValidating}
                          className="h-7 px-2 text-xs"
                        >
                          Validate
                        </Button>
                      )}
                    </div>
                  </div>
                  <Input
                    id="perplexity-key"
                    type="password"
                    value={apiKeys.perplexity}
                    onChange={(e) => handleApiKeyChange('perplexity', e.target.value)}
                    placeholder="pplx-..."
                  />
                  <p className="text-xs text-muted-foreground">
                    Used for research and enhanced task analysis
                  </p>
                </div>
              </div>

              {/* ✅ Validation Status Messages */}
              {!isWebMode && (
                <div className="space-y-2">
                  {apiKeys.anthropic && apiKeyValidation.anthropic.isValid === true && (
                    <div className="flex items-center gap-2 p-2 bg-green-50 border border-green-200 rounded text-green-700 text-sm">
                      <Check className="h-4 w-4" />
                      Anthropic API key validated successfully
                    </div>
                  )}
                  {apiKeys.perplexity && apiKeyValidation.perplexity.isValid === true && (
                    <div className="flex items-center gap-2 p-2 bg-green-50 border border-green-200 rounded text-green-700 text-sm">
                      <Check className="h-4 w-4" />
                      Perplexity API key validated successfully
                    </div>
                  )}
                  {apiKeys.anthropic && apiKeyValidation.anthropic.isValid === false && (
                    <div className="flex items-center gap-2 p-2 bg-red-50 border border-red-200 rounded text-red-700 text-sm">
                      <X className="h-4 w-4" />
                      Anthropic API key validation failed
                    </div>
                  )}
                  {apiKeys.perplexity && apiKeyValidation.perplexity.isValid === false && (
                    <div className="flex items-center gap-2 p-2 bg-red-50 border border-red-200 rounded text-red-700 text-sm">
                      <X className="h-4 w-4" />
                      Perplexity API key validation failed
                    </div>
                  )}
                </div>
              )}

              {lastError && (
                <div className="p-2 bg-red-50 border border-red-200 rounded text-red-700 text-sm">
                  {lastError}
                </div>
              )}

              <div className="flex gap-2 pt-4">
                <Button variant="outline" onClick={handleCancel} className="flex-1">
                  Cancel
                </Button>
                <Button
                  onClick={handleApiKeysNext}
                  disabled={
                    !isWebMode && (
                      !apiKeys.anthropic.trim() ||
                      !apiKeys.perplexity.trim() ||
                      apiKeyValidation.anthropic.isValid !== true ||
                      apiKeyValidation.perplexity.isValid !== true
                    )
                  }
                  className="flex-1"
                >
                  Continue to PRD Upload
                  {!isWebMode && (
                    apiKeyValidation.anthropic.isValid === true &&
                    apiKeyValidation.perplexity.isValid === true
                  ) && <CheckCircle className="ml-2 h-4 w-4" />}
                </Button>
              </div>
            </CardContent>
          </Card>
        );

      case 'prd':
        return (
          <Card className="w-full max-w-4xl mx-auto">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Upload Project Requirements Document (PRD)
              </CardTitle>
              <CardDescription>
                Upload and validate your PRD to generate structured tasks.
                {state.taskmasterInitialized && (
                  <div className="mt-2 text-green-600 text-sm flex items-center gap-1">
                    <CheckCircle className="h-4 w-4" />
                    Claude Taskmaster initialized successfully
                  </div>
                )}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <PRDUploadUI
                onPRDUploaded={handlePRDUploaded}
                onPRDParsed={() => {}} // No parsing in this step
                onValidationChange={handlePRDValidationChange}
                projectPath={state.projectPath || undefined}
                className="max-h-[60vh] overflow-y-auto"
                anthropicApiKey={apiKeys.anthropic}
                perplexityApiKey={apiKeys.perplexity}
                disableParsing={true} // Disable parsing button
              />
              <div className="flex gap-2 pt-4">
                <Button variant="outline" onClick={handleCancel}>
                  Cancel Project Creation
                </Button>
                <Button
                  onClick={advanceToTaskmaster}
                  disabled={!state.prdValidated || !state.prdUploaded}
                  className="flex items-center gap-2"
                >
                  Continue to Task Generation
                  {state.prdValidated && state.prdUploaded && <CheckCircle className="h-4 w-4" />}
                </Button>
              </div>
            </CardContent>
          </Card>
        );

      case 'taskmaster':
        return (
          <Card className="w-full max-w-4xl mx-auto">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5" />
                Generate Tasks with Claude Taskmaster
              </CardTitle>
              <CardDescription>
                Convert your PRD into structured tasks using Claude Taskmaster AI.
                This process will create a tasks.json file with detailed task breakdowns.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <TaskmasterTaskGenerationUI
                projectPath={state.projectPath || ''}
                prdContent={prdContent}
                anthropicApiKey={apiKeys.anthropic}
                perplexityApiKey={apiKeys.perplexity}
                onTasksGenerated={handleTasksGenerated}
                onCancel={() => setState(prev => ({ ...prev, step: 'prd' }))}
              />
            </CardContent>
          </Card>
        );

      case 'orchestration':
        return (
          <Card className="w-full max-w-4xl mx-auto">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Task Orchestration
              </CardTitle>
              <CardDescription>
                Configure how your project tasks will be organized and executed.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <TaskmasterOrchestrationUI
                onTasksSubmittedToMicromanager={handleTasksSubmittedToMicromanager}
                onCancel={() => setState(prev => ({ ...prev, step: 'prd' }))}
              />
            </CardContent>
          </Card>
        );

      case 'complete':
        return (
          <div className="w-full max-w-4xl mx-auto space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-green-600">
                  <CheckCircle className="h-6 w-6" />
                  Sequential Workflow Ready!
                </CardTitle>
                <CardDescription>
                  Your project is configured with controlled sequential execution for reliable code generation.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <Alert className="border-green-200 bg-green-50">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <AlertDescription>
                    <strong>🎯 Sequential Workflow Activated!</strong> Your development environment is ready with:
                    <ul className="list-disc list-inside mt-2 space-y-1">
                      <li>✅ Project folder structure created</li>
                      <li>✅ Claude Taskmaster integration configured</li>
                    <li>✅ API keys validated and stored</li>
                    <li>✅ PRD processed and tasks generated</li>
                    <li>✅ Sequential workflow initialized with Micromanager</li>
                    <li>🎮 <strong>Controlled execution with user confirmation checkpoints</strong></li>
                    <li>🔍 <strong>Real-time code generation monitoring</strong></li>
                    <li>✨ <strong>Automatic quality validation and approval</strong></li>
                  </ul>
                </AlertDescription>
              </Alert>

              <Alert className="border-blue-200 bg-blue-50">
                <Settings className="h-4 w-4 text-blue-600" />
                <AlertDescription>
                  <strong>🚀 What's Different:</strong> This project uses sequential workflow (not parallel execution):
                  <ul className="list-disc list-inside mt-2 space-y-1">
                    <li><strong>One agent at a time</strong> - No resource conflicts or coordination issues</li>
                    <li><strong>User control</strong> - You approve each task before proceeding</li>
                    <li><strong>Real validation</strong> - Files are verified before marking tasks complete</li>
                    <li><strong>Live coding view</strong> - Watch agents work in real-time</li>
                    <li><strong>Quality gates</strong> - Automatic approval for high-quality output</li>
                  </ul>
                </AlertDescription>
              </Alert>

              <div className="p-4 bg-gray-50 rounded-lg">
                <div className="text-sm space-y-2">
                  <p><strong>Project:</strong> {state.projectName}</p>
                  <p><strong>Location:</strong> {state.projectPath}</p>
                  <p><strong>Taskmaster:</strong> {state.taskmasterInitialized ? 'Initialized ✅' : 'Not available ❌'}</p>
                  <p><strong>Workflow:</strong> Sequential Execution ✅</p>
                </div>
              </div>
              <Button onClick={async () => {
                console.log('🎯 Project creation wizard completing...');

                // ✅ CRITICAL FIX: Ensure active project is set before completion
                try {
                  if (state.projectPath && state.projectName) {
                    const { activeProjectService } = await import('../../services/active-project-service');

                    // Verify active project is set
                    const currentActive = activeProjectService.getActiveProject();
                    if (!currentActive || currentActive.path !== state.projectPath) {
                      console.log('🔄 Setting active project before completion...');
                      activeProjectService.setActiveProject(state.projectPath, state.projectName);
                      console.log('✅ Active project set:', state.projectName);
                    } else {
                      console.log('✅ Active project already set:', currentActive.name);
                    }
                  }
                } catch (error) {
                  console.error('❌ Failed to verify/set active project:', error);
                }

                resetWizard();
                onProjectCreated?.();
              }} className="w-full">
                Continue to Project
              </Button>
            </CardContent>
          </Card>

          {/* ✅ CRITICAL FIX: Include TaskmasterOrchestrationUI in completion step for Sequential Workflow Control */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Sequential Workflow Control
              </CardTitle>
              <CardDescription>
                Start executing your project tasks with controlled sequential workflow
              </CardDescription>
            </CardHeader>
            <CardContent>
              <TaskmasterOrchestrationUI
                onTasksSubmittedToMicromanager={() => {
                  // Tasks already submitted, this is just for the Sequential Workflow Control UI
                  console.log('Sequential workflow control accessed from completion step');
                }}
                onCancel={() => {
                  // No cancel needed in completion step
                }}
                forceShowSequentialWorkflow={true}
              />
            </CardContent>
          </Card>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="h-full flex flex-col">
      {/* Progress indicator */}
      <div className="border-b p-4">
        <div className="flex items-center justify-between max-w-4xl mx-auto">
          <h1 className="text-lg font-semibold">Create New Project</h1>
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <span className={state.step === 'name' ? 'text-foreground font-medium' : ''}>
              1. Project Name
            </span>
            <span>→</span>
            <span className={state.step === 'apikeys' ? 'text-foreground font-medium' : ''}>
              2. API Keys
            </span>
            <span>→</span>
            <span className={state.step === 'prd' ? 'text-foreground font-medium' : ''}>
              3. Upload PRD
            </span>
            <span>→</span>
            <span className={state.step === 'taskmaster' ? 'text-foreground font-medium' : ''}>
              4. Generate Tasks
            </span>
            <span>→</span>
            <span className={state.step === 'orchestration' ? 'text-foreground font-medium' : ''}>
              5. Orchestration
            </span>
            <span>→</span>
            <span className={state.step === 'complete' ? 'text-foreground font-medium' : ''}>
              6. Complete
            </span>
          </div>
        </div>
      </div>

      {/* Current step content */}
      <div className="flex-1 overflow-auto p-6">
        {renderCurrentStep()}
      </div>
    </div>
  );
};

export default CreateProjectWizard;
