// useDialog.ts - Hook for dialog management

"use client"

import { useState, useEffect } from 'react';
import { UseDialogReturn } from './types';
import { useDialogContext } from './DialogProvider';
import { dialogManager } from './DialogManager';

export const useDialog = (): UseDialogReturn => {
  const context = useDialogContext();
  const [openDialogCount, setOpenDialogCount] = useState(0);

  useEffect(() => {
    const updateCount = () => {
      setOpenDialogCount(dialogManager.getOpenDialogs().length);
    };

    dialogManager.addListener(updateCount);
    updateCount(); // Initial count
    
    return () => dialogManager.removeListener(updateCount);
  }, []);

  return {
    openDialog: context.openDialog,
    closeDialog: context.closeDialog,
    closeAllDialogs: context.closeAllDialogs,
    isDialogOpen: context.isDialogOpen,
    openDialogCount
  };
};
