// Dialog.tsx - Main dialog component with accessibility and proper positioning

"use client"

import React, { useEffect, useRef, useCallback } from 'react';
import { DialogProps, DIALOG_SIZES, DIALOG_POSITIONS } from './types';
import DialogPortal from './DialogPortal';

export const Dialog: React.FC<DialogProps> = ({
  id,
  isOpen,
  onClose,
  children,
  options
}) => {
  const dialogRef = useRef<HTMLDivElement>(null);
  const backdropRef = useRef<HTMLDivElement>(null);

  // Focus management
  const setupFocusTrap = useCallback(() => {
    if (!dialogRef.current) return;

    const focusableElements = dialogRef.current.querySelectorAll(
      'button:not([disabled]), [href], input:not([disabled]), select:not([disabled]), textarea:not([disabled]), [tabindex]:not([tabindex="-1"]):not([disabled])'
    );

    const firstElement = focusableElements[0] as HTMLElement;
    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

    const handleTabKey = (e: KeyboardEvent) => {
      if (e.key !== 'Tab') return;

      if (focusableElements.length === 0) {
        e.preventDefault();
        return;
      }

      if (focusableElements.length === 1) {
        e.preventDefault();
        firstElement?.focus();
        return;
      }

      if (e.shiftKey) {
        if (document.activeElement === firstElement) {
          e.preventDefault();
          lastElement?.focus();
        }
      } else {
        if (document.activeElement === lastElement) {
          e.preventDefault();
          firstElement?.focus();
        }
      }
    };

    document.addEventListener('keydown', handleTabKey);

    // Focus first element
    if (firstElement) {
      firstElement.focus();
    } else {
      // If no focusable elements, focus the dialog itself
      dialogRef.current.focus();
    }

    return () => {
      document.removeEventListener('keydown', handleTabKey);
    };
  }, []);

  // Setup focus trap when dialog opens
  useEffect(() => {
    if (isOpen) {
      const cleanup = setupFocusTrap();
      return cleanup;
    }
  }, [isOpen, setupFocusTrap]);

  // Handle escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && options.closable !== false) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      return () => document.removeEventListener('keydown', handleEscape);
    }
  }, [isOpen, onClose, options.closable]);

  if (!isOpen) return null;

  // Get size configuration
  const sizeConfig = DIALOG_SIZES[options.size || 'md'];
  const positionConfig = DIALOG_POSITIONS[options.position || 'center'];

  return (
    <DialogPortal>
      <div
        ref={backdropRef}
        className="dialog-backdrop"
        style={{
          position: 'fixed',
          top: 0,
          left: 0,
          width: '100vw',
          height: '100vh',
          backgroundColor: options.backdrop ? 'rgba(0, 0, 0, 0.5)' : 'transparent',
          display: 'flex',
          ...positionConfig,
          padding: '20px',
          zIndex: options.zIndex || 1000,
          pointerEvents: 'all'
        }}
        onClick={(e) => {
          if (e.target === e.currentTarget && options.closable !== false) {
            onClose();
          }
        }}
      >
        <div
          ref={dialogRef}
          className={`dialog-content dialog-size-${options.size || 'md'} bg-background border border-border ${options.className || ''}`}
          style={{
            borderRadius: '8px',
            boxShadow: '0 10px 25px rgba(0, 0, 0, 0.2)',
            ...sizeConfig,
            maxHeight: '90vh',
            overflow: 'hidden',
            pointerEvents: 'all'
          }}
          onClick={(e) => e.stopPropagation()}
          tabIndex={-1}
          role="dialog"
          aria-modal="true"
          aria-labelledby={`dialog-${id}-title`}
          aria-describedby={`dialog-${id}-description`}
        >
          {children}
        </div>
      </div>
    </DialogPortal>
  );
};

export default Dialog;
