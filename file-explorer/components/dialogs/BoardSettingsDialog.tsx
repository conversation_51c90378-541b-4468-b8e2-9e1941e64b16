// BoardSettingsDialog.tsx - Board settings dialog using new dialog system

"use client"

import React, { useState, useEffect } from 'react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { X } from "lucide-react";

interface BoardData {
  id: string;
  name: string;
  description?: string;
}

interface BoardSettingsDialogProps {
  boardData: BoardData;
  onSave: (boardId: string, name: string, description: string) => void;
  onCancel: () => void;
}

export const BoardSettingsDialog: React.FC<BoardSettingsDialogProps> = ({
  boardData,
  onSave,
  onCancel
}) => {

  const [name, setName] = useState(boardData?.name || "");
  const [description, setDescription] = useState(boardData?.description || "");

  useEffect(() => {
    if (boardData) {
      setName(boardData.name);
      setDescription(boardData.description || "");
    } else {
      setName("");
      setDescription("");
    }
  }, [boardData]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (boardData && boardData.id && name.trim()) {
      onSave(boardData.id, name.trim(), description.trim());
    }
  };

  if (!boardData || !boardData.id) {
    return (
      <div className="p-6 w-full">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-foreground">Board Settings</h2>
          <Button
            variant="ghost"
            size="icon"
            onClick={onCancel}
            className="h-8 w-8 rounded-sm opacity-70 hover:opacity-100"
          >
            <X className="h-4 w-4" />
            <span className="sr-only">Close</span>
          </Button>
        </div>
        <div className="py-4 text-center text-muted-foreground">
          Board not found or an error occurred.
        </div>
        <div className="flex justify-end pt-4">
          <Button variant="outline" onClick={onCancel}>
            Close
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 w-full">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h2 id="dialog-board-settings-title" className="text-xl font-semibold text-foreground">
          Board Settings: {boardData.name}
        </h2>
        <Button
          variant="ghost"
          size="icon"
          onClick={onCancel}
          className="h-8 w-8 rounded-sm opacity-70 hover:opacity-100"
        >
          <X className="h-4 w-4" />
          <span className="sr-only">Close</span>
        </Button>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="space-y-2">
          <label htmlFor="board-name" className="text-sm font-medium text-foreground">
            Board Name *
          </label>
          <Input
            id="board-name"
            value={name}
            onChange={(e) => setName(e.target.value)}
            placeholder="Enter board name..."
            required
            autoFocus
          />
        </div>

        <div className="space-y-2">
          <label htmlFor="board-description" className="text-sm font-medium text-foreground">
            Description
          </label>
          <Textarea
            id="board-description"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            placeholder="Optional board description..."
            rows={3}
          />
        </div>

        {/* Footer */}
        <div className="flex justify-end space-x-2 pt-4">
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit" disabled={!name.trim()}>
            Save Changes
          </Button>
        </div>
      </form>
    </div>
  );
};
