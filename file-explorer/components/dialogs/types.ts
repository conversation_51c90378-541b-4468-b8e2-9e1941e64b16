// types.ts - Dialog system type definitions

export interface DialogOptions {
  /** Show backdrop overlay */
  backdrop?: boolean;
  /** Allow closing via escape key or backdrop click */
  closable?: boolean;
  /** Dialog size preset */
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  /** Dialog position on screen */
  position?: 'center' | 'top' | 'bottom';
  /** Custom z-index (auto-managed if not provided) */
  zIndex?: number;
  /** Custom CSS class for dialog container */
  className?: string;
  /** Animation duration in milliseconds */
  animationDuration?: number;
  /** Prevent body scroll when dialog is open */
  preventBodyScroll?: boolean;
}

export interface DialogState {
  id: string;
  component: React.ReactNode;
  options: DialogOptions;
  isOpen: boolean;
  zIndex: number;
  openedAt: number;
}

export interface DialogContextType {
  /** Open a new dialog */
  openDialog: (id: string, component: React.ReactNode, options?: DialogOptions) => void;
  /** Close a specific dialog */
  closeDialog: (id: string) => void;
  /** Close all open dialogs */
  closeAllDialogs: () => void;
  /** Check if a dialog is currently open */
  isDialogOpen: (id: string) => boolean;
  /** Get all open dialog IDs */
  getOpenDialogs: () => string[];
  /** Get the topmost dialog ID */
  getTopDialog: () => string | null;
}

export interface DialogProps {
  /** Unique dialog identifier */
  id: string;
  /** Whether the dialog is currently open */
  isOpen: boolean;
  /** Callback when dialog should close */
  onClose: () => void;
  /** Dialog content */
  children: React.ReactNode;
  /** Dialog configuration options */
  options: DialogOptions;
}

export interface UseDialogReturn {
  /** Open a new dialog */
  openDialog: (id: string, component: React.ReactNode, options?: DialogOptions) => void;
  /** Close a specific dialog */
  closeDialog: (id: string) => void;
  /** Close all dialogs */
  closeAllDialogs: () => void;
  /** Check if dialog is open */
  isDialogOpen: (id: string) => boolean;
  /** Get open dialog count */
  openDialogCount: number;
}

// Predefined dialog configurations
export const DIALOG_SIZES = {
  sm: { width: '400px', maxWidth: '90vw' },
  md: { width: '600px', maxWidth: '90vw' },
  lg: { width: '800px', maxWidth: '90vw' },
  xl: { width: '1000px', maxWidth: '95vw' },
  full: { width: '95vw', height: '95vh' }
} as const;

export const DIALOG_POSITIONS = {
  center: { alignItems: 'center', justifyContent: 'center' },
  top: { alignItems: 'flex-start', justifyContent: 'center', paddingTop: '5vh' },
  bottom: { alignItems: 'flex-end', justifyContent: 'center', paddingBottom: '5vh' }
} as const;

// Default dialog options
export const DEFAULT_DIALOG_OPTIONS: Required<DialogOptions> = {
  backdrop: true,
  closable: true,
  size: 'md',
  position: 'center',
  zIndex: 1000,
  className: '',
  animationDuration: 200,
  preventBodyScroll: true
};
