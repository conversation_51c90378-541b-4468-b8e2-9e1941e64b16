// DialogProvider.tsx - Context provider for dialog system

"use client"

import React, { createContext, useContext, useState, useEffect } from 'react';
import { DialogManager, dialogManager } from './DialogManager';
import { DialogContextType, DialogState } from './types';
import { Dialog } from './Dialog';

const DialogContext = createContext<DialogContextType | null>(null);

export const DialogProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [dialogs, setDialogs] = useState<DialogState[]>([]);

  useEffect(() => {
    const updateDialogs = () => {
      setDialogs(dialogManager.getOpenDialogs());
    };

    dialogManager.addListener(updateDialogs);
    updateDialogs(); // Initial state

    return () => dialogManager.removeListener(updateDialogs);
  }, []);

  const contextValue: DialogContextType = {
    openDialog: dialogManager.openDialog.bind(dialogManager),
    closeDialog: dialogManager.closeDialog.bind(dialogManager),
    closeAllDialogs: dialogManager.closeAllDialogs.bind(dialogManager),
    isDialogOpen: dialogManager.isDialogOpen.bind(dialogManager),
    getOpenDialogs: dialogManager.getOpenDialogIds.bind(dialogManager),
    getTopDialog: () => dialogManager.getTopDialog()?.id || null
  };

  return (
    <DialogContext.Provider value={contextValue}>
      {children}
      {dialogs.map((dialog) => (
        <Dialog
          key={dialog.id}
          id={dialog.id}
          isOpen={dialog.isOpen}
          onClose={() => dialogManager.closeDialog(dialog.id)}
          options={dialog.options}
        >
          {dialog.component}
        </Dialog>
      ))}
    </DialogContext.Provider>
  );
};

export const useDialogContext = () => {
  const context = useContext(DialogContext);
  if (!context) {
    throw new Error('useDialogContext must be used within a DialogProvider');
  }
  return context;
};

export default DialogProvider;
