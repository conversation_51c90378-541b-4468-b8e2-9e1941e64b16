// TestDialog.tsx - Simple test dialog to verify the dialog system works

"use client"

import React from 'react';
import { Button } from "@/components/ui/button";
import { useDialog } from './useDialog';

export const TestDialogContent: React.FC<{ onClose: () => void }> = ({ onClose }) => {
  return (
    <div className="p-6 w-full">
      <h2 className="text-xl font-semibold mb-4">Test Dialog</h2>
      <p className="text-muted-foreground mb-6">
        This is a test dialog to verify that the new dialog system is working correctly.
        The dialog should be centered on the screen with a backdrop.
      </p>
      <div className="flex justify-end space-x-2">
        <Button variant="outline" onClick={onClose}>
          Close
        </Button>
        <Button onClick={onClose}>
          OK
        </Button>
      </div>
    </div>
  );
};

export const TestDialogTrigger: React.FC = () => {
  const { openDialog, closeDialog } = useDialog();

  const openTestDialog = () => {
    openDialog('test-dialog', 
      <TestDialogContent onClose={() => closeDialog('test-dialog')} />,
      { 
        size: 'md', 
        position: 'center',
        closable: true
      }
    );
  };

  return (
    <Button onClick={openTestDialog} variant="outline">
      Test Dialog System
    </Button>
  );
};
