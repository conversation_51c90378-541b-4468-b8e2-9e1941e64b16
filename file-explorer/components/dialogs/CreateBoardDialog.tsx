// CreateBoardDialog.tsx - Create board dialog using new dialog system

"use client"

import React, { useState } from 'react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { X } from "lucide-react";

interface CreateBoardDialogProps {
  onCreateBoard: (name: string, description?: string) => void;
  onCancel: () => void;
}

export const CreateBoardDialog: React.FC<CreateBoardDialogProps> = ({
  onCreateBoard,
  onCancel
}) => {
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (name.trim()) {
      onCreateBoard(name.trim(), description.trim() || undefined);
    }
  };

  return (
    <div className="p-6 w-full">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h2 id="dialog-create-board-title" className="text-xl font-semibold text-foreground">
          Create New Board
        </h2>
        <Button
          variant="ghost"
          size="icon"
          onClick={onCancel}
          className="h-8 w-8 rounded-sm opacity-70 hover:opacity-100"
        >
          <X className="h-4 w-4" />
          <span className="sr-only">Close</span>
        </Button>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="space-y-2">
          <label htmlFor="board-name" className="text-sm font-medium text-foreground">
            Board Name *
          </label>
          <Input
            id="board-name"
            value={name}
            onChange={(e) => setName(e.target.value)}
            placeholder="Enter board name..."
            required
            autoFocus
          />
        </div>

        <div className="space-y-2">
          <label htmlFor="board-description" className="text-sm font-medium text-foreground">
            Description
          </label>
          <Textarea
            id="board-description"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            placeholder="Optional board description..."
            rows={3}
          />
        </div>

        {/* Footer */}
        <div className="flex justify-end space-x-2 pt-4">
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit" disabled={!name.trim()}>
            Create Board
          </Button>
        </div>
      </form>
    </div>
  );
};
