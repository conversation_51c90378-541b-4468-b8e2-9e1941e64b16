// DialogManager.ts - Centralized dialog state management

import { DialogState, DialogOptions, DEFAULT_DIALOG_OPTIONS } from './types';

export class DialogManager {
  private dialogs: Map<string, DialogState> = new Map();
  private zIndexCounter: number = 1000;
  private listeners: Set<() => void> = new Set();
  private bodyScrollLocked: boolean = false;
  private originalBodyOverflow: string = '';

  /**
   * Open a new dialog
   */
  openDialog(id: string, component: React.ReactNode, options: DialogOptions = {}): void {
    const mergedOptions = { ...DEFAULT_DIALOG_OPTIONS, ...options };
    
    // Close existing dialog with same ID
    if (this.dialogs.has(id)) {
      this.closeDialog(id);
    }

    // Create dialog state
    const dialogState: DialogState = {
      id,
      component,
      options: mergedOptions,
      isOpen: true,
      zIndex: mergedOptions.zIndex || this.getNextZIndex(),
      openedAt: Date.now()
    };

    this.dialogs.set(id, dialogState);

    // Handle body scroll locking
    if (mergedOptions.preventBodyScroll && !this.bodyScrollLocked) {
      this.lockBodyScroll();
    }

    this.notifyListeners();
    console.log(`[DialogManager] Opened dialog: ${id}`);
  }

  /**
   * Close a specific dialog
   */
  closeDialog(id: string): void {
    const dialog = this.dialogs.get(id);
    if (!dialog) {
      console.warn(`[DialogManager] Attempted to close non-existent dialog: ${id}`);
      return;
    }

    this.dialogs.delete(id);

    // Unlock body scroll if no dialogs remain
    if (this.dialogs.size === 0 && this.bodyScrollLocked) {
      this.unlockBodyScroll();
    }

    this.notifyListeners();
    console.log(`[DialogManager] Closed dialog: ${id}`);
  }

  /**
   * Close all open dialogs
   */
  closeAllDialogs(): void {
    const dialogIds = Array.from(this.dialogs.keys());
    this.dialogs.clear();

    if (this.bodyScrollLocked) {
      this.unlockBodyScroll();
    }

    this.notifyListeners();
    console.log(`[DialogManager] Closed all dialogs (${dialogIds.length})`);
  }

  /**
   * Check if a dialog is open
   */
  isDialogOpen(id: string): boolean {
    return this.dialogs.has(id);
  }

  /**
   * Get all open dialog states
   */
  getOpenDialogs(): DialogState[] {
    return Array.from(this.dialogs.values()).sort((a, b) => a.openedAt - b.openedAt);
  }

  /**
   * Get all open dialog IDs
   */
  getOpenDialogIds(): string[] {
    return Array.from(this.dialogs.keys());
  }

  /**
   * Get the topmost (most recent) dialog
   */
  getTopDialog(): DialogState | null {
    const dialogs = this.getOpenDialogs();
    return dialogs.length > 0 ? dialogs[dialogs.length - 1] : null;
  }

  /**
   * Get next available z-index
   */
  private getNextZIndex(): number {
    return ++this.zIndexCounter;
  }

  /**
   * Handle escape key press
   */
  handleEscapeKey(): void {
    const topDialog = this.getTopDialog();
    if (topDialog && topDialog.options.closable) {
      this.closeDialog(topDialog.id);
    }
  }

  /**
   * Handle backdrop click
   */
  handleBackdropClick(dialogId: string): void {
    const dialog = this.dialogs.get(dialogId);
    if (dialog && dialog.options.closable) {
      this.closeDialog(dialogId);
    }
  }

  /**
   * Lock body scroll
   */
  private lockBodyScroll(): void {
    if (typeof document !== 'undefined') {
      this.originalBodyOverflow = document.body.style.overflow;
      document.body.style.overflow = 'hidden';
      this.bodyScrollLocked = true;
    }
  }

  /**
   * Unlock body scroll
   */
  private unlockBodyScroll(): void {
    if (typeof document !== 'undefined') {
      document.body.style.overflow = this.originalBodyOverflow;
      this.bodyScrollLocked = false;
    }
  }

  /**
   * Add state change listener
   */
  addListener(listener: () => void): void {
    this.listeners.add(listener);
  }

  /**
   * Remove state change listener
   */
  removeListener(listener: () => void): void {
    this.listeners.delete(listener);
  }

  /**
   * Notify all listeners of state changes
   */
  private notifyListeners(): void {
    this.listeners.forEach(listener => {
      try {
        listener();
      } catch (error) {
        console.error('[DialogManager] Listener error:', error);
      }
    });
  }

  /**
   * Cleanup all dialogs and reset state
   */
  cleanup(): void {
    this.closeAllDialogs();
    this.listeners.clear();
    this.zIndexCounter = 1000;
    console.log('[DialogManager] Cleanup completed');
  }

  /**
   * Get debug information
   */
  getDebugInfo(): {
    openDialogCount: number;
    openDialogIds: string[];
    zIndexCounter: number;
    bodyScrollLocked: boolean;
    listenerCount: number;
  } {
    return {
      openDialogCount: this.dialogs.size,
      openDialogIds: this.getOpenDialogIds(),
      zIndexCounter: this.zIndexCounter,
      bodyScrollLocked: this.bodyScrollLocked,
      listenerCount: this.listeners.size
    };
  }
}

// Singleton instance
export const dialogManager = new DialogManager();

// Global escape key handler
if (typeof document !== 'undefined') {
  document.addEventListener('keydown', (event) => {
    if (event.key === 'Escape') {
      dialogManager.handleEscapeKey();
    }
  });
}
