// DialogPortal.tsx - Portal component for rendering dialogs outside normal DOM flow

"use client"

import React, { useEffect, useState } from 'react';
import { createPortal } from 'react-dom';

interface DialogPortalProps {
  children: React.ReactNode;
}

export const DialogPortal: React.FC<DialogPortalProps> = ({ children }) => {
  const [portalContainer, setPortalContainer] = useState<HTMLElement | null>(null);

  useEffect(() => {
    // Create or get the dialog root container
    let container = document.getElementById('dialog-root');
    
    if (!container) {
      container = document.createElement('div');
      container.id = 'dialog-root';
      
      // Set container styles for proper positioning
      container.style.position = 'fixed';
      container.style.top = '0';
      container.style.left = '0';
      container.style.width = '100vw';
      container.style.height = '100vh';
      container.style.pointerEvents = 'none'; // Allow clicks to pass through when no dialogs
      container.style.zIndex = '1000';
      
      // Append to body
      document.body.appendChild(container);
      console.log('[DialogPortal] Created dialog-root container');
    }

    setPortalContainer(container);

    // Cleanup function
    return () => {
      // Only remove container if it's empty and we're the last component using it
      if (container && container.children.length === 0) {
        // Small delay to prevent race conditions
        setTimeout(() => {
          if (container && container.children.length === 0 && container.parentNode) {
            document.body.removeChild(container);
            console.log('[DialogPortal] Removed empty dialog-root container');
          }
        }, 100);
      }
    };
  }, []);

  // Update container pointer events based on whether we have children
  useEffect(() => {
    if (portalContainer) {
      portalContainer.style.pointerEvents = children ? 'all' : 'none';
    }
  }, [portalContainer, children]);

  // Only render portal if we have a container
  if (!portalContainer) {
    return null;
  }

  return createPortal(children, portalContainer);
};

export default DialogPortal;
