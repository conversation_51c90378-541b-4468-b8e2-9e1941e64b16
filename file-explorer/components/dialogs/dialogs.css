/* dialogs.css - Dialog system styles */

/* Dialog root container */
#dialog-root {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  pointer-events: none;
  z-index: 1000;
}

/* Dialog backdrop */
.dialog-backdrop {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  z-index: 1000;
  pointer-events: all;
}

/* Dialog content container */
.dialog-content {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  max-width: 90vw;
  max-height: 90vh;
  overflow: auto;
  pointer-events: all;
  outline: none;
}

/* Dark theme support */
.dark .dialog-content {
  background-color: hsl(var(--background));
  border: 1px solid hsl(var(--border));
  color: hsl(var(--foreground));
}

/* Dialog size variants */
.dialog-size-sm { 
  width: 400px; 
}

.dialog-size-md { 
  width: 600px; 
}

.dialog-size-lg { 
  width: 800px; 
}

.dialog-size-xl { 
  width: 1000px; 
}

.dialog-size-full { 
  width: 95vw; 
  height: 95vh; 
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .dialog-size-sm,
  .dialog-size-md,
  .dialog-size-lg,
  .dialog-size-xl {
    width: 95vw;
    max-height: 90vh;
  }
  
  .dialog-backdrop {
    padding: 10px;
  }
}

/* Animation classes */
.dialog-enter {
  opacity: 0;
  transform: scale(0.95);
}

.dialog-enter-active {
  opacity: 1;
  transform: scale(1);
  transition: opacity 200ms ease-out, transform 200ms ease-out;
}

.dialog-exit {
  opacity: 1;
  transform: scale(1);
}

.dialog-exit-active {
  opacity: 0;
  transform: scale(0.95);
  transition: opacity 200ms ease-in, transform 200ms ease-in;
}

/* Focus styles */
.dialog-content:focus {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 2px;
}

/* Scrollbar styling for dialog content */
.dialog-content::-webkit-scrollbar {
  width: 8px;
}

.dialog-content::-webkit-scrollbar-track {
  background: hsl(var(--muted));
  border-radius: 4px;
}

.dialog-content::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground));
  border-radius: 4px;
}

.dialog-content::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--foreground));
}
