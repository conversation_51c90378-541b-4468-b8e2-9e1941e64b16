// AgentManagementDialog.tsx - Agent management dialog using new dialog system

"use client"

import React, { useState, useEffect } from 'react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { X, Settings, Cpu, Shield, Edit2 } from "lucide-react";

interface Agent {
  id: string;
  name: string;
  type: string;
  status: string;
  capabilities: string[];
  customPrompt?: string;
  resourceUsage: {
    cpu: number;
    memory: number;
    tokens: number;
  };
}

interface AgentManagementDialogProps {
  agents: Agent[];
  onUpdateAgents: (agents: Agent[]) => void;
  onCancel: () => void;
}

// Default agents that should not be removable
const DEFAULT_AGENT_IDS = [
  'micromanager',
  'intern',
  'junior',
  'midlevel',
  'senior',
  'architect',
  'designer',
  'tester',
  'researcher'
];

export const AgentManagementDialog: React.FC<AgentManagementDialogProps> = ({
  agents,
  onUpdateAgents,
  onCancel
}) => {
  const [localAgents, setLocalAgents] = useState<Agent[]>(agents);
  const [newAgentName, setNewAgentName] = useState("");
  const [newAgentType, setNewAgentType] = useState("worker");
  const [newAgentCapabilities, setNewAgentCapabilities] = useState("");
  const [newAgentCustomPrompt, setNewAgentCustomPrompt] = useState("");
  const [editingAgent, setEditingAgent] = useState<Agent | null>(null);

  useEffect(() => {
    setLocalAgents(agents);
  }, [agents]);

  const handleAddAgent = () => {
    if (!newAgentName.trim() || !newAgentType) return;

    const newAgent: Agent = {
      id: `agent-${Date.now()}`,
      name: newAgentName.trim(),
      type: newAgentType,
      status: "idle",
      capabilities: newAgentCapabilities.split(",").map(cap => cap.trim()).filter(Boolean),
      customPrompt: newAgentCustomPrompt.trim() || undefined,
      resourceUsage: {
        cpu: 0,
        memory: 0,
        tokens: 0,
      },
    };

    const updatedAgents = [...localAgents, newAgent];
    setLocalAgents(updatedAgents);
    onUpdateAgents(updatedAgents);

    // Reset form
    resetForm();
  };

  const handleEditAgent = (agent: Agent) => {
    setEditingAgent(agent);
    setNewAgentName(agent.name);
    setNewAgentType(agent.type);
    setNewAgentCapabilities(agent.capabilities.join(", "));
    setNewAgentCustomPrompt(agent.customPrompt || "");
  };

  const handleUpdateAgent = () => {
    if (!editingAgent || !newAgentName.trim() || !newAgentType) return;

    const updatedAgent: Agent = {
      ...editingAgent,
      name: newAgentName.trim(),
      type: newAgentType,
      capabilities: newAgentCapabilities.split(",").map(cap => cap.trim()).filter(Boolean),
      customPrompt: newAgentCustomPrompt.trim() || undefined,
    };

    const updatedAgents = localAgents.map(agent =>
      agent.id === editingAgent.id ? updatedAgent : agent
    );

    setLocalAgents(updatedAgents);
    onUpdateAgents(updatedAgents);

    // Reset form and editing state
    resetForm();
    setEditingAgent(null);
  };

  const resetForm = () => {
    setNewAgentName("");
    setNewAgentType("worker");
    setNewAgentCapabilities("");
    setNewAgentCustomPrompt("");
  };

  const cancelEdit = () => {
    setEditingAgent(null);
    resetForm();
  };

  const handleRemoveAgent = (agentId: string) => {
    // Prevent removal of default agents
    if (DEFAULT_AGENT_IDS.includes(agentId)) {
      return;
    }

    const updatedAgents = localAgents.filter(agent => agent.id !== agentId);
    setLocalAgents(updatedAgents);
    onUpdateAgents(updatedAgents);
  };

  const isDefaultAgent = (agentId: string) => {
    return DEFAULT_AGENT_IDS.includes(agentId);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (editingAgent) {
      handleUpdateAgent();
    } else {
      handleAddAgent();
    }
  };

  return (
    <div className="w-full h-full flex flex-col bg-background text-foreground">
      {/* Header */}
      <div className="flex items-center justify-between p-6 border-b border-border">
        <div className="flex items-center gap-2">
          <Settings className="h-5 w-5 text-primary" />
          <h2 id="dialog-agent-management-title" className="text-xl font-semibold text-foreground">
            Manage Agents
          </h2>
        </div>
        <Button
          variant="ghost"
          size="icon"
          onClick={onCancel}
          className="h-8 w-8 rounded-sm opacity-70 hover:opacity-100"
        >
          <X className="h-4 w-4" />
          <span className="sr-only">Close</span>
        </Button>
      </div>

      {/* Current Agents Section */}
      <div className="flex-1 overflow-y-auto p-6">
        <div className="space-y-4 mb-6">
          <div className="flex items-center justify-between">
            <h3 className="text-sm font-medium text-foreground">Current Agents</h3>
            <div className="flex items-center gap-1 text-xs text-muted-foreground">
              <Shield className="h-3 w-3 text-blue-500" />
              <span>Default agents are protected</span>
            </div>
          </div>
          {localAgents.length === 0 ? (
            <div className="text-sm text-muted-foreground p-4 border border-dashed border-border rounded-md text-center">
              No agents configured. Add an agent below to get started.
            </div>
          ) : (
            <div className="space-y-2 max-h-48 overflow-y-auto">
              {localAgents.map((agent) => {
                const isDefault = isDefaultAgent(agent.id);
                return (
                  <div key={agent.id} className="flex items-center justify-between p-3 border border-border rounded-md bg-muted/30 hover:bg-muted/50 transition-colors">
                    <div className="flex items-center gap-3">
                      {isDefault ? (
                        <Shield className="h-4 w-4 text-blue-500" title="Default Agent - Cannot be removed" />
                      ) : (
                        <Cpu className="h-4 w-4 text-primary" />
                      )}
                      <div>
                        <div className="flex items-center gap-2">
                          <span className="font-medium text-foreground">{agent.name}</span>
                          {isDefault && (
                            <span className="text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-0.5 rounded-full">
                              Default
                            </span>
                          )}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          Type: {agent.type} • Status: {agent.status}
                        </div>
                        {agent.capabilities.length > 0 && (
                          <div className="text-xs text-muted-foreground mt-1">
                            Capabilities: {agent.capabilities.join(", ")}
                          </div>
                        )}
                        {agent.customPrompt && (
                          <div className="text-xs text-muted-foreground mt-1">
                            Custom Prompt: {agent.customPrompt.length > 50 ? `${agent.customPrompt.substring(0, 50)}...` : agent.customPrompt}
                          </div>
                        )}
                      </div>
                    </div>
                    {isDefault ? (
                      <div className="text-xs text-muted-foreground px-3 py-2 bg-muted/50 rounded">
                        Protected
                      </div>
                    ) : (
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEditAgent(agent)}
                        >
                          <Edit2 className="h-3 w-3 mr-1" />
                          Edit
                        </Button>
                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={() => handleRemoveAgent(agent.id)}
                        >
                          Remove
                        </Button>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          )}
        </div>

        {/* Add/Edit Agent Form */}
        <div className="border-t border-border pt-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-medium text-foreground">
              {editingAgent ? `Edit Agent: ${editingAgent.name}` : 'Add New Agent'}
            </h3>
            {editingAgent && (
              <Button
                variant="ghost"
                size="sm"
                onClick={cancelEdit}
                className="text-muted-foreground hover:text-foreground"
              >
                Cancel Edit
              </Button>
            )}
          </div>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label htmlFor="agent-name" className="text-sm font-medium text-foreground">
                  Agent Name *
                </label>
                <Input
                  id="agent-name"
                  value={newAgentName}
                  onChange={(e) => setNewAgentName(e.target.value)}
                  placeholder="Enter agent name..."
                  required
                  className="bg-background border-input"
                />
              </div>
              <div className="space-y-2">
                <label htmlFor="agent-type" className="text-sm font-medium text-foreground">
                  Agent Type *
                </label>
                <select
                  id="agent-type"
                  value={newAgentType}
                  onChange={(e) => setNewAgentType(e.target.value)}
                  className="w-full h-10 px-3 py-2 text-sm border border-input rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                  required
                >
                  <option value="worker">Worker</option>
                  <option value="researcher">Researcher</option>
                  <option value="architect">Architect</option>
                  <option value="designer">Designer</option>
                  <option value="tester">Tester</option>
                  <option value="orchestrator">Orchestrator</option>
                </select>
              </div>
            </div>

            <div className="space-y-2">
              <label htmlFor="agent-capabilities" className="text-sm font-medium text-foreground">
                Capabilities
              </label>
              <Input
                id="agent-capabilities"
                value={newAgentCapabilities}
                onChange={(e) => setNewAgentCapabilities(e.target.value)}
                placeholder="Comma-separated capabilities (e.g., coding, testing, documentation)"
                className="bg-background border-input"
              />
              <p className="text-xs text-muted-foreground">
                Optional: Enter capabilities separated by commas
              </p>
            </div>

            <div className="space-y-2">
              <label htmlFor="agent-custom-prompt" className="text-sm font-medium text-foreground">
                Custom Prompt
              </label>
              <Textarea
                id="agent-custom-prompt"
                value={newAgentCustomPrompt}
                onChange={(e) => setNewAgentCustomPrompt(e.target.value)}
                placeholder="Enter custom prompt for this agent..."
                rows={3}
                className="resize-none bg-background border-input"
              />
              <p className="text-xs text-muted-foreground">
                Optional: Custom instructions or behavior for this agent
              </p>
            </div>

            <div className="flex justify-end gap-2">
              {editingAgent && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={cancelEdit}
                >
                  Cancel
                </Button>
              )}
              <Button type="submit" disabled={!newAgentName.trim()}>
                {editingAgent ? (
                  <>
                    <Edit2 className="h-4 w-4 mr-2" />
                    Update Agent
                  </>
                ) : (
                  <>
                    <Cpu className="h-4 w-4 mr-2" />
                    Add Agent
                  </>
                )}
              </Button>
            </div>
          </form>
        </div>
      </div>

      {/* Footer */}
      <div className="flex justify-end p-6 border-t border-border bg-muted/20">
        <Button variant="outline" onClick={onCancel}>
          Close
        </Button>
      </div>
    </div>
  );
};
