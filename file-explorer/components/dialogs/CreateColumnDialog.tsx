// CreateColumnDialog.tsx - Create column dialog using new dialog system

"use client"

import React, { useState } from 'react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { X } from "lucide-react";

interface CreateColumnDialogProps {
  onCreateColumn: (title: string) => void;
  onCancel: () => void;
}

export const CreateColumnDialog: React.FC<CreateColumnDialogProps> = ({
  onCreateColumn,
  onCancel
}) => {
  const [title, setTitle] = useState("");

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (title.trim()) {
      onCreateColumn(title.trim());
    }
  };

  return (
    <div className="p-6 w-full">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h2 id="dialog-create-column-title" className="text-xl font-semibold text-foreground">
          Create New Column
        </h2>
        <Button
          variant="ghost"
          size="icon"
          onClick={onCancel}
          className="h-8 w-8 rounded-sm opacity-70 hover:opacity-100"
        >
          <X className="h-4 w-4" />
          <span className="sr-only">Close</span>
        </Button>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="space-y-2">
          <label htmlFor="column-title" className="text-sm font-medium text-foreground">
            Column Title *
          </label>
          <Input
            id="column-title"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            placeholder="Enter column title..."
            required
            autoFocus
          />
        </div>

        {/* Footer */}
        <div className="flex justify-end space-x-2 pt-4">
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit" disabled={!title.trim()}>
            Create Column
          </Button>
        </div>
      </form>
    </div>
  );
};
