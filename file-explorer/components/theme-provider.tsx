"use client"
import { ThemeProvider as NextThemesProvider } from "next-themes"
import type { ThemeProviderProps } from "next-themes"

/**
 * ✅ Custom Theme Provider
 * 🔧 SURGICAL FIX: Uses custom storage key to avoid conflicts
 */
export function ThemeProvider({ children, ...props }: ThemeProviderProps) {
  return (
    <NextThemesProvider
      storageKey="synapse-theme"
      {...props}
    >
      {children}
    </NextThemesProvider>
  )
}
