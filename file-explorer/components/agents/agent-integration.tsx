// components/agents/agent-integration.tsx
"use client"

import React, { useState, useEffect, useCallback } from 'react';
import { AgentManager } from './agent-manager';
import { AgentBase, AgentContext, AgentResponse, AgentMessage, AgentStatus, TaskAssignment } from './agent-base';
import { useSharedAgentState } from './shared-agent-state';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { useDialog } from '@/components/dialogs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Play,
  Pause,
  Square,
  Refresh<PERSON>w,
  <PERSON><PERSON><PERSON>riangle,
  CheckCircle,
  Clock,
  Zap,
  Brain,
  Code,
  Search,
  Layers,
  Palette,
  Settings
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { getGlobalSettingsManager } from '../settings/global-settings';
import { IsolatedAgentCard } from '../settings/isolated-agent-card';
import { getAgentProviders, getProviderModels } from './llm-provider-registry';

interface AgentIntegrationProps {
  className?: string;
}

export function AgentIntegration({ className }: AgentIntegrationProps) {
  const sharedState = useSharedAgentState();
  const [agentManager] = useState(() => new AgentManager());
  // Task input functionality removed - available in main Orchestrator tab
  const { openDialog, closeDialog } = useDialog();
  const [settingsManager] = useState(() => getGlobalSettingsManager());

  // Use shared state instead of local state
  const agentStatuses = sharedState.agents;
  const activeTasks = sharedState.getActiveTasks();
  const messages = sharedState.messages;

  // Initialize agent manager and set up listeners
  useEffect(() => {
    // Set up message listener
    const handleMessage = (message: AgentMessage) => {
      // Map message types to valid shared state types
      const mapMessageType = (type: string): 'info' | 'error' | 'success' | 'warning' => {
        switch (type) {
          case 'error': return 'error';
          case 'success': return 'success';
          case 'warning': return 'warning';
          case 'completion': return 'success';
          case 'update': return 'info';
          case 'question': return 'info';
          default: return 'info';
        }
      };

      // Add message to shared state instead of local state
      sharedState.addMessage({
        agentId: message.agentId,
        message: message.message,
        timestamp: message.timestamp,
        type: mapMessageType(message.type || 'info')
      });
    };

    agentManager.onMessage(handleMessage);

    return () => {
      agentManager.offMessage(handleMessage);
    };
  }, [agentManager, sharedState]);

  // handleTaskSubmit function removed - task submission available in main Orchestrator tab

  const getAgentIcon = (agentType: string) => {
    switch (agentType) {
      case 'orchestrator': return <Brain className="h-4 w-4" />;
      case 'implementation': return <Code className="h-4 w-4" />;
      case 'specialized': return <Zap className="h-4 w-4" />;
      default: return <Brain className="h-4 w-4" />; // Default to Brain icon instead of Settings
    }
  };

  const getAgentColor = (agentId: string) => {
    const colors: Record<string, string> = {
      micromanager: 'bg-purple-500',
      intern: 'bg-green-500',
      junior: 'bg-blue-500',
      midlevel: 'bg-yellow-500',
      senior: 'bg-red-500',
      researcher: 'bg-indigo-500',
      architect: 'bg-gray-500',
      designer: 'bg-pink-500'
    };
    return colors[agentId] || 'bg-gray-400';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'idle': return 'text-green-500';
      case 'busy': return 'text-blue-500';
      case 'error': return 'text-red-500';
      case 'offline': return 'text-gray-500';
      default: return 'text-gray-500';
    }
  };

  const getHealthColor = (score: number) => {
    if (score >= 80) return 'text-green-500';
    if (score >= 60) return 'text-yellow-500';
    return 'text-red-500';
  };

  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  // ✅ SYNC VERIFICATION: Function to verify agent settings sync
  const verifyAgentSettingsSync = useCallback(() => {
    const settings = settingsManager.getSettings();
    const syncIssues: string[] = [];

    // Check if all agents in shared state have corresponding settings
    agentStatuses.forEach(agent => {
      const agentSetting = settings.agents.find(a => a.id === agent.id);
      if (!agentSetting) {
        syncIssues.push(`Agent ${agent.id} (${agent.name}) missing from Settings Manager`);
      } else {
        // Verify key properties match
        if (agentSetting.name !== agent.name) {
          syncIssues.push(`Agent ${agent.id} name mismatch: UI="${agent.name}", Settings="${agentSetting.name}"`);
        }
      }
    });

    // Check if all settings have corresponding agents in shared state
    settings.agents.forEach(agentSetting => {
      const agent = agentStatuses.find(a => a.id === agentSetting.id);
      if (!agent) {
        syncIssues.push(`Settings agent ${agentSetting.id} (${agentSetting.name}) missing from UI state`);
      }
    });

    if (syncIssues.length > 0) {
      console.warn('⚠️ Agent Settings Sync Issues Found:', syncIssues);
      return { synced: false, issues: syncIssues };
    } else {
      console.log('✅ Agent Settings Sync Verification: All agents properly synchronized');
      return { synced: true, issues: [] };
    }
  }, [agentStatuses, settingsManager]);

  // ✅ SYNC VERIFICATION: Run sync verification on component mount and when agents change
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      verifyAgentSettingsSync();
    }, 1000); // Delay to allow initialization

    return () => clearTimeout(timeoutId);
  }, [verifyAgentSettingsSync]);

  const openAgentSettings = (agentId: string) => {
    const settings = settingsManager.getSettings();
    const agentSettings = settings.agents.find(a => a.id === agentId);

    if (!agentSettings) {
      console.warn(`Agent settings not found for agent: ${agentId}`);
      return;
    }

    const providerData = {
      providers: getAgentProviders(),
      getModelsForProvider: getProviderModels
    };

    // ✅ SYNC VERIFICATION: Enhanced updateAgent with validation and sync verification
    const updateAgent = (agentId: string, updates: any) => {
      try {
        console.log(`🔄 Agent Integration: Updating agent ${agentId} with:`, updates);

        // Update settings
        settingsManager.updateAgentSettings(agentId, updates);

        // ✅ SYNC VERIFICATION: Verify the update was applied
        const updatedSettings = settingsManager.getSettings();
        const updatedAgent = updatedSettings.agents.find(a => a.id === agentId);

        if (updatedAgent) {
          // Verify each updated field
          Object.keys(updates).forEach(key => {
            if (updatedAgent[key as keyof typeof updatedAgent] !== updates[key]) {
              console.warn(`⚠️ Agent Integration: Sync verification failed for ${key}. Expected: ${updates[key]}, Got: ${updatedAgent[key as keyof typeof updatedAgent]}`);
            } else {
              console.log(`✅ Agent Integration: Sync verified for ${key}: ${updates[key]}`);
            }
          });
        } else {
          console.error(`❌ Agent Integration: Agent ${agentId} not found after update`);
        }

      } catch (error) {
        console.error(`❌ Agent Integration: Failed to update agent ${agentId}:`, error);
      }
    };

    openDialog(`agent-settings-${agentId}`,
      <IsolatedAgentCard
        agent={agentSettings}
        providers={providerData.providers}
        getModelsForProvider={providerData.getModelsForProvider}
        updateAgent={updateAgent}
      />,
      {
        size: 'lg',
        position: 'center',
        closable: true,
        title: `${agentSettings.name} Settings`
      }
    );
  };

  return (
    <div className={cn("h-full bg-background", className)}>
      <div className="flex flex-col h-full">
        {/* Duplicate AI Agent Orchestrator removed - available in main Orchestrator tab */}

        <div className="flex-1 overflow-hidden">
          <div className="h-full flex flex-col">
            <div className="flex-1 p-4 overflow-auto">
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {agentStatuses.map((agent) => (
                  <Card key={agent.id} className="relative">
                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className={cn("w-2 h-2 rounded-full", getAgentColor(agent.id))} />
                          <CardTitle className="text-sm">{agent.name}</CardTitle>
                        </div>
                        <div className="flex items-center gap-1">
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-6 w-6 opacity-60 hover:opacity-100"
                            onClick={() => openAgentSettings(agent.id)}
                            title={`Configure ${agent.name}`}
                          >
                            <Settings className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                      <CardDescription className="text-xs">
                        {agent.type} • {agent.status}
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-2">
                      <div className="flex justify-between text-xs">
                        <span>Health Score</span>
                        <span className={getHealthColor(agent.healthScore || 0)}>
                          {isNaN(agent.healthScore) ? '0' : (agent.healthScore || 0).toFixed(0)}%
                        </span>
                      </div>
                      <Progress value={isNaN(agent.healthScore) ? 0 : (agent.healthScore || 0)} className="h-1" />

                      <div className="grid grid-cols-2 gap-2 text-xs">
                        <div>
                          <span className="text-muted-foreground">Tasks:</span>
                          <span className="ml-1 font-medium">{isNaN(agent.tasksCompleted) ? '0' : (agent.tasksCompleted || 0).toString()}</span>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Errors:</span>
                          <span className="ml-1 font-medium">{isNaN(agent.errorCount) ? '0' : (agent.errorCount || 0).toString()}</span>
                        </div>
                      </div>

                      <div className="text-xs">
                        <span className="text-muted-foreground">Tokens:</span>
                        <span className="ml-1 font-medium">{isNaN(agent.tokensUsed) ? '0' : (agent.tokensUsed || 0).toLocaleString()}</span>
                      </div>

                      {agent.currentTask && (
                        <div className="text-xs">
                          <span className="text-muted-foreground">Current:</span>
                          <div className="break-words mt-1 p-1 bg-muted rounded text-xs">
                            {agent.currentTask}
                          </div>
                        </div>
                      )}

                      <Badge
                        variant={agent.status === 'idle' ? 'default' : agent.status === 'busy' ? 'secondary' : 'destructive'}
                        className="text-xs"
                      >
                        <span className={getStatusColor(agent.status)}>●</span>
                        <span className="ml-1">{agent.status}</span>
                      </Badge>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>






          </div>
        </div>
      </div>
    </div>
  );
}