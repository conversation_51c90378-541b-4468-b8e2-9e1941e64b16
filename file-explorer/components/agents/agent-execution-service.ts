// components/agents/agent-execution-service.ts
import { MonacoIntegrationManager } from '../background/monaco-integration';
import { FileOperationsManager } from '../background/file-operations';

import { boardIPCBridge } from '../kanban/lib/board-ipc-bridge';
import { AgentContext } from './agent-base';
import { withTimeout, TimeoutError, isTimeoutError } from '../../lib/utils/timeout';
import { executionLogger } from './agent-execution-trace';
import { taskOutputLoggingService } from '../services/task-output-logging-service';
import { terminalEventBus, getAgentDisplayName } from '../terminal/terminal-event-bus';
// ✅ Task 99: Import agent terminal bridge
import { agentExecuteCommand, agentTerminalBridge } from '../services/agent-terminal-bridge';
// ✅ MANDATORY UPGRADE: Import execution outcome logging
import {
  logTaskExecutionSucceeded,
  logTaskExecutionFailed,
  logTaskOutputWritten,
  logFileWriteAttempt
} from '../../services/logger';
// ✅ PHASE 2: Import completion verification and streaming services
import { completionVerificationService } from '../../services/completion-verification-service';
import { sequentialExecutionController } from '../../services/task-state-service';

export interface ExecutionResult {
  success: boolean;
  output: string;
  files?: Array<{ path: string; content: string; action: 'created' | 'modified' | 'deleted' }>;

  kanbanUpdates?: Array<{ cardId: string; action: string; result: any }>;
  error?: string;
  metadata?: Record<string, any>;
}

export interface FileCreationRequest {
  path: string;
  content: string;
  language?: string;
  openInEditor?: boolean;
}



export interface KanbanUpdateRequest {
  cardId?: string;
  action: 'create' | 'update' | 'move' | 'complete';
  data: any;
}

// ✅ PHASE 2: Real-time streaming interfaces
export interface StreamingUpdate {
  type: 'file_progress' | 'code_generation' | 'validation' | 'completion';
  agentId: string;
  taskId: string;
  data: {
    filePath?: string;
    content?: string;
    progress?: number;
    message?: string;
    timestamp: number;
  };
}

export interface MonacoIntegration {
  displayActiveWork: (agentId: string, filePath: string, content: string) => void;
  streamCodeGeneration: (filePath: string, content: string, progress: number) => void;
  highlightWorkArea: (filePath: string, lineRange: [number, number]) => void;
  showProgress: (agentId: string, percentage: number, description: string) => void;
}

export type StreamingListener = (update: StreamingUpdate) => void;

export class AgentExecutionService {
  private static instance: AgentExecutionService;
  private monacoManager: MonacoIntegrationManager;
  private fileManager: FileOperationsManager;
  // ✅ PHASE 2: Real-time streaming capabilities
  private streamingListeners = new Set<StreamingListener>();
  private monacoIntegration: MonacoIntegration | null = null;

  constructor() {
    this.monacoManager = MonacoIntegrationManager.getInstance();
    this.fileManager = FileOperationsManager.getInstance();
  }

  public static getInstance(): AgentExecutionService {
    if (!AgentExecutionService.instance) {
      AgentExecutionService.instance = new AgentExecutionService();
    }
    return AgentExecutionService.instance;
  }

  // ✅ PHASE 2: Real-time streaming methods
  public addStreamingListener(listener: StreamingListener): () => void {
    this.streamingListeners.add(listener);
    return () => this.streamingListeners.delete(listener);
  }

  public setMonacoIntegration(integration: MonacoIntegration): void {
    this.monacoIntegration = integration;
    console.log('AgentExecutionService: Monaco integration set for real-time code display');
  }

  private emitStreamingUpdate(update: StreamingUpdate): void {
    this.streamingListeners.forEach(listener => {
      try {
        listener(update);
      } catch (error) {
        console.error('AgentExecutionService: Error in streaming listener:', error);
      }
    });

    // Also update Monaco editor if available
    if (this.monacoIntegration && update.data.filePath && update.data.content) {
      try {
        this.monacoIntegration.streamCodeGeneration(
          update.data.filePath,
          update.data.content,
          update.data.progress || 0
        );
      } catch (error) {
        console.error('AgentExecutionService: Error updating Monaco editor:', error);
      }
    }
  }

  /**
   * Create files and optionally open them in Monaco editor
   */
  async createFiles(files: FileCreationRequest[], agentId: string, taskId?: string): Promise<ExecutionResult> {
    const results: Array<{ path: string; content: string; action: 'created' | 'modified' | 'deleted' }> = [];
    const errors: string[] = [];

    console.log(`AgentExecutionService: Creating ${files.length} files for agent ${agentId}`);

    // ✅ Task 67: Log file operation start
    executionLogger.logEvent({
      agentId,
      action: 'file_operation',
      details: `Starting file creation: ${files.length} files`,
      status: 'running',
      metadata: { fileCount: files.length, filePaths: files.map(f => f.path) }
    });

    // ✅ Task 84: Write file operation start to terminal and log
    if (taskId) {
      const operationMsg = `Starting file creation: ${files.length} files`;
      taskOutputLoggingService.addLogEntry(taskId, agentId, operationMsg, 'system', {
        fileCount: files.length,
        filePaths: files.map(f => f.path)
      });


    }

    for (const file of files) {
      const startTime = Date.now();
      try {
        // ✅ PHASE 2: Emit streaming update for file creation start
        this.emitStreamingUpdate({
          type: 'file_progress',
          agentId,
          taskId: taskId || `file-op-${Date.now()}`,
          data: {
            filePath: file.path,
            message: `Starting creation of ${file.path}`,
            progress: 0,
            timestamp: Date.now()
          }
        });

        // ✅ MANDATORY UPGRADE: Log file write attempt
        logFileWriteAttempt(agentId, {
          taskId: taskId || `file-op-${Date.now()}`,
          filePath: file.path,
          operation: 'create',
          success: false // Will be updated based on result
        });

        // Create the file
        const result = await this.fileManager.createFile(file.path, file.content, {
          encoding: 'utf8',
          createDirectories: true
        }, agentId);

        if (result.success) {
          results.push({
            path: file.path,
            content: file.content,
            action: 'created'
          });

          // ✅ PHASE 2: Emit streaming update for successful file creation
          this.emitStreamingUpdate({
            type: 'code_generation',
            agentId,
            taskId: taskId || `file-op-${Date.now()}`,
            data: {
              filePath: file.path,
              content: file.content,
              message: `Successfully created ${file.path}`,
              progress: 100,
              timestamp: Date.now()
            }
          });

          // ✅ MANDATORY UPGRADE: Log successful file write
          logFileWriteAttempt(agentId, {
            taskId: taskId || `file-op-${Date.now()}`,
            filePath: file.path,
            operation: 'create',
            success: true
          });

          // ✅ Task 84: Write file creation success to terminal and log
          if (taskId) {
            const successMsg = `Created file: ${file.path} (${file.content.length} chars)`;
            taskOutputLoggingService.addLogEntry(taskId, agentId, successMsg, 'output', {
              operation: 'create',
              fileSize: file.content.length,
              filePath: file.path
            });
          }

          // Open in Monaco editor if requested
          if (file.openInEditor) {
            try {
              // Note: In a real implementation, we'd need to trigger the editor to open this file
              // For now, we'll log the intent
              console.log(`AgentExecutionService: File ${file.path} marked for editor opening`);
            } catch (editorError) {
              console.warn(`Failed to open ${file.path} in editor:`, editorError);
            }
          }

          console.log(`AgentExecutionService: Created file ${file.path} (${file.content.length} chars)`);

          // ✅ Task 67: Log successful file creation
          executionLogger.logEvent({
            agentId,
            action: 'file_operation',
            targetFile: file.path,
            details: `Successfully created file: ${file.path}`,
            status: 'completed',
            duration: Date.now() - startTime,
            metadata: { operation: 'create', fileSize: file.content.length }
          });
        } else {
          const errorMsg = `Failed to create ${file.path}: ${result.error}`;
          errors.push(errorMsg);

          // ✅ MANDATORY UPGRADE: Log failed file write
          logFileWriteAttempt(agentId, {
            taskId: taskId || `file-op-${Date.now()}`,
            filePath: file.path,
            operation: 'create',
            success: false,
            error: result.error
          });

          // ✅ Task 84: Write file creation error to terminal and log
          if (taskId) {
            taskOutputLoggingService.writeAgentErrorToTerminal(taskId, agentId, errorMsg);
          }

          // ✅ Task 67: Log file creation failure
          executionLogger.logEvent({
            agentId,
            action: 'error',
            targetFile: file.path,
            details: `Failed to create file: ${file.path} - ${result.error}`,
            status: 'failed',
            duration: Date.now() - startTime,
            metadata: { operation: 'create', error: result.error }
          });
        }
      } catch (error) {
        const errorMsg = `Error creating ${file.path}: ${error instanceof Error ? error.message : String(error)}`;
        errors.push(errorMsg);
        console.error(`AgentExecutionService: ${errorMsg}`);

        // ✅ Task 67: Log file creation error
        executionLogger.logEvent({
          agentId,
          action: 'error',
          targetFile: file.path,
          details: errorMsg,
          status: 'failed',
          duration: Date.now() - startTime,
          metadata: { operation: 'create', error: error instanceof Error ? error.message : String(error) }
        });
      }
    }

    return {
      success: errors.length === 0,
      output: errors.length === 0
        ? `Successfully created ${results.length} files`
        : `Created ${results.length} files with ${errors.length} errors`,
      files: results,
      error: errors.length > 0 ? errors.join('; ') : undefined,
      metadata: {
        filesCreated: results.length,
        errors: errors.length,
        agentId
      }
    };
  }



  /**
   * Update Kanban board
   */
  async updateKanban(updates: KanbanUpdateRequest[], agentId: string): Promise<ExecutionResult> {
    const results: Array<{ cardId: string; action: string; result: any }> = [];
    const errors: string[] = [];

    console.log(`AgentExecutionService: Processing ${updates.length} Kanban updates for agent ${agentId}`);

    for (const update of updates) {
      try {
        let result: any;

        // ✅ Validate required parameters before processing
        console.log(`AgentExecutionService: Processing Kanban ${update.action} with cardId: ${update.cardId || 'undefined'}`);

        switch (update.action) {
          case 'create':
            result = await boardIPCBridge.createCard('main', 'column-1', {
              title: update.data.title || 'Agent Task',
              description: update.data.description || '',
              priority: update.data.priority || 'medium',
              ...update.data
            });
            break;

          case 'update':
            if (!update.cardId) {
              throw new Error(`Card ID is required for update action but was: ${update.cardId}`);
            }
            console.log(`AgentExecutionService: Updating card ${update.cardId} with data:`, update.data);

            // ✅ Fix: Get current card state and merge with update data
            const boardState = await boardIPCBridge.getBoardState('main');
            if (!boardState) {
              throw new Error('Board state not found for card update');
            }

            // Find the current card across all columns
            let currentCard: any = null;
            for (const column of boardState.columns) {
              const card = column.cards.find((c: any) => c.id === update.cardId);
              if (card) {
                currentCard = card;
                break;
              }
            }

            if (!currentCard) {
              throw new Error(`Card ${update.cardId} not found for update`);
            }

            // Merge current card with update data, preserving essential fields
            const updatedCard = {
              ...currentCard,
              ...update.data,
              id: update.cardId, // Ensure ID is preserved
              columnId: currentCard.columnId, // Ensure columnId is preserved
              updatedAt: new Date().toISOString()
            };

            result = await boardIPCBridge.updateCard('main', updatedCard);
            break;

          case 'move':
            if (!update.cardId) {
              throw new Error(`Card ID is required for move action but was: ${update.cardId}`);
            }
            if (!update.data.columnId) {
              throw new Error(`Column ID is required for move action but was: ${update.data.columnId}`);
            }
            console.log(`AgentExecutionService: Moving card ${update.cardId} to column ${update.data.columnId}`);
            result = await boardIPCBridge.moveCard('main', update.cardId, 'current', update.data.columnId, 'swimlane-1');
            break;

          case 'complete':
            if (!update.cardId) {
              throw new Error(`Card ID is required for complete action but was: ${update.cardId}`);
            }
            console.log(`AgentExecutionService: Completing card ${update.cardId} for agent ${agentId}`);
            result = await boardIPCBridge.updateCardProgress('main', update.cardId, 100, agentId);
            break;

          default:
            throw new Error(`Unknown Kanban action: ${update.action}`);
        }

        if (result) {
          results.push({
            cardId: update.cardId || result.id || 'unknown',
            action: update.action,
            result
          });
          console.log(`AgentExecutionService: Kanban ${update.action} successful for card ${update.cardId || result.id}`);
        } else {
          console.warn(`AgentExecutionService: Kanban ${update.action} returned null result for card ${update.cardId}`);
        }
      } catch (error) {
        const errorMsg = `Kanban ${update.action} failed: ${error instanceof Error ? error.message : String(error)}`;
        errors.push(errorMsg);
        console.error(`AgentExecutionService: ${errorMsg}`);
        console.error(`AgentExecutionService: Update details:`, { action: update.action, cardId: update.cardId, data: update.data });
      }
    }

    return {
      success: errors.length === 0,
      output: `Processed ${results.length} Kanban updates`,
      kanbanUpdates: results,
      error: errors.length > 0 ? errors.join('; ') : undefined,
      metadata: {
        updatesProcessed: results.length,
        errors: errors.length,
        agentId
      }
    };
  }

  /**
   * Comprehensive execution method that handles multiple types of work
   */
  async executeWork(
    context: AgentContext,
    agentId: string,
    work: {
      files?: FileCreationRequest[];
      kanban?: KanbanUpdateRequest[];
    },
    timeoutMs?: number,
    taskId?: string // ✅ Task 84: Add taskId parameter for logging
  ): Promise<ExecutionResult> {
    // ✅ Task 84: Initialize task log if taskId provided
    if (taskId) {
      taskOutputLoggingService.initializeTaskLog(taskId, agentId);
    }

    // ✅ Enhanced logging for Taskmaster orchestration debugging
    console.log(`🔧 AgentExecutionService: Starting work execution for agent ${agentId}`);
    console.log(`📋 AgentExecutionService: Context task: ${context.task}`);
    console.log(`📁 AgentExecutionService: Files to create: ${work.files?.length || 0}`);
    console.log(`🎯 AgentExecutionService: Kanban updates: ${work.kanban?.length || 0}`);

    // ✅ Apply timeout if specified
    if (timeoutMs) {
      return withTimeout(
        this.executeWorkInternal(context, agentId, work, taskId),
        timeoutMs,
        `Agent ${agentId} work execution`
      );
    }

    return this.executeWorkInternal(context, agentId, work, taskId);
  }

  /**
   * Internal work execution method (without timeout wrapper)
   */
  private async executeWorkInternal(
    context: AgentContext,
    agentId: string,
    work: {
      files?: FileCreationRequest[];
      kanban?: KanbanUpdateRequest[];
    },
    taskId?: string // ✅ Task 84: Add taskId parameter
  ): Promise<ExecutionResult> {
    const allResults: ExecutionResult[] = [];
    const allFiles: Array<{ path: string; content: string; action: 'created' | 'modified' | 'deleted' }> = [];
    const allOutputs: string[] = [];
    const allErrors: string[] = [];

    console.log(`AgentExecutionService: Executing comprehensive work for agent ${agentId}`);

    // Execute file operations
    if (work.files && work.files.length > 0) {
      const fileResult = await this.createFiles(work.files, agentId, taskId); // ✅ Task 84: Pass taskId
      allResults.push(fileResult);
      if (fileResult.files) allFiles.push(...fileResult.files);
      allOutputs.push(fileResult.output);
      if (fileResult.error) allErrors.push(fileResult.error);
    }



    // Execute Kanban updates
    if (work.kanban && work.kanban.length > 0) {
      const kanbanResult = await this.updateKanban(work.kanban, agentId);
      allResults.push(kanbanResult);
      allOutputs.push(kanbanResult.output);
      if (kanbanResult.error) allErrors.push(kanbanResult.error);
    }

    // ✅ PHASE 2: Enhanced completion verification
    this.emitStreamingUpdate({
      type: 'validation',
      agentId,
      taskId: taskId || `execution-${Date.now()}`,
      data: {
        message: 'Validating execution output...',
        progress: 90,
        timestamp: Date.now()
      }
    });

    // ✅ MANDATORY UPGRADE: Validate execution output before declaring success
    const hasValidOutput = this.validateExecutionOutput(allFiles, allOutputs, work);

    // ✅ PHASE 2: Additional completion verification using completion service
    let completionVerified = true;
    if (taskId && allFiles.length > 0) {
      try {
        const validationResult = await completionVerificationService.validateFileOutput(
          taskId,
          allFiles.map(f => f.path)
        );
        completionVerified = validationResult.isValid;
        console.log(`AgentExecutionService: Completion verification result: ${completionVerified}`);
      } catch (error) {
        console.warn('AgentExecutionService: Completion verification failed:', error);
        completionVerified = false;
      }
    }

    const overallSuccess = allErrors.length === 0 && hasValidOutput && completionVerified;
    const combinedOutput = allOutputs.join('\n\n');

    // ✅ PHASE 2: Emit completion update
    this.emitStreamingUpdate({
      type: 'completion',
      agentId,
      taskId: taskId || `execution-${Date.now()}`,
      data: {
        message: overallSuccess ? 'Execution completed successfully' : 'Execution completed with issues',
        progress: 100,
        timestamp: Date.now()
      }
    });

    console.log(`AgentExecutionService: Work execution complete. Success: ${overallSuccess}, Files: ${allFiles.length}, Errors: ${allErrors.length}, ValidOutput: ${hasValidOutput}, CompletionVerified: ${completionVerified}`);

    // ✅ MANDATORY UPGRADE: Log execution outcome with detailed validation
    const executionTime = Date.now() - (context.metadata?.startTime || Date.now());

    if (overallSuccess && hasValidOutput) {
      // Log successful execution with file outputs
      logTaskExecutionSucceeded(agentId, {
        taskId: taskId || `execution-${Date.now()}`,
        generatedFiles: allFiles.map(f => f.path),
        functionsCreated: this.extractFunctionsFromFiles(allFiles),
        diffStats: {
          additions: allFiles.length,
          deletions: 0,
          modifications: allFiles.filter(f => f.action === 'modified').length
        },
        outputPaths: allFiles.map(f => f.path),
        executionTime,
        tokensUsed: 0 // Will be updated by calling agent
      });

      // Log file outputs if any were created
      if (allFiles.length > 0) {
        logTaskOutputWritten(agentId, {
          taskId: taskId || `execution-${Date.now()}`,
          filePaths: allFiles.map(f => f.path),
          fileTypes: allFiles.map(f => this.getFileType(f.path)),
          totalFiles: allFiles.length,
          totalSize: allFiles.reduce((sum, f) => sum + f.content.length, 0)
        });
      }
    } else {
      // Log execution failure with detailed reason
      const failureReason = allErrors.length > 0
        ? `Execution errors: ${allErrors.join('; ')}`
        : !hasValidOutput
          ? 'No valid output generated - execution produced no meaningful results'
          : 'Unknown execution failure';

      logTaskExecutionFailed(agentId, {
        taskId: taskId || `execution-${Date.now()}`,
        error: failureReason,
        reason: allErrors.length > 0 ? 'execution_errors' : 'no_valid_output',
        agentState: 'error',
        stack: undefined,
        executionTime
      });
    }

    // ✅ Task 84: Complete task log if taskId provided
    if (taskId) {
      taskOutputLoggingService.completeTaskLog(taskId, overallSuccess ? 'completed' : 'failed');
    }

    return {
      success: overallSuccess,
      output: combinedOutput,
      files: allFiles.length > 0 ? allFiles : undefined,
      kanbanUpdates: allResults.find(r => r.kanbanUpdates)?.kanbanUpdates,
      error: allErrors.length > 0 ? allErrors.join('; ') : undefined,
      metadata: {
        totalOperations: allResults.length,
        filesCreated: allFiles.length,
        errors: allErrors.length,
        hasValidOutput,
        agentId,
        context: {
          task: context.task,
          timestamp: Date.now()
        }
      }
    };
  }

  /**
   * ✅ Task 99: Enhanced terminal execution with session support
   */
  async executeTerminalCommand(
    agentId: string,
    command: string,
    context?: AgentContext,
    options?: {
      timeout?: number;
      workingDirectory?: string;
      environment?: Record<string, string>;
    },
    taskId?: string
  ): Promise<ExecutionResult> {
    if (!command || typeof command !== 'string') {
      const errorMsg = 'Invalid command: command must be a non-empty string';
      console.error(`AgentExecutionService: ${errorMsg}`);

      if (taskId) {
        taskOutputLoggingService.addLogEntry(taskId, agentId, errorMsg, 'error');
      }

      return {
        success: false,
        output: '',
        error: errorMsg,
        metadata: { agentId, command: command || 'undefined' }
      };
    }

    const startTime = Date.now();
    console.log(`AgentExecutionService: Agent ${agentId} executing terminal command: ${command}`);

    // ✅ Task 99: Log terminal command start
    executionLogger.logEvent({
      agentId,
      action: 'terminal_command',
      details: `Executing terminal command: ${command}`,
      status: 'running',
      metadata: { command, sessionId: context?.terminalSessionId }
    });

    // ✅ Task 99: Write terminal command start to terminal and log
    if (taskId) {
      const commandMsg = `Executing terminal command: ${command}`;
      taskOutputLoggingService.addLogEntry(taskId, agentId, commandMsg, 'system', {
        command,
        operation: 'terminal_command',
        sessionId: context?.terminalSessionId
      });
    }

    // ✅ Task 99: Emit command start to terminal UI
    const agentDisplayName = getAgentDisplayName(agentId);
    terminalEventBus.emitSystemMessage(
      `[${agentDisplayName}] Executing: ${command}`,
      'info'
    );

    try {
      // ✅ Task 99: Use enhanced agent terminal bridge
      const result = await agentExecuteCommand(agentId, command, context?.terminalSessionId, options);

      if (result.success) {
        const successMsg = `Terminal command completed successfully: ${command}`;
        console.log(`AgentExecutionService: ${successMsg}`);

        // ✅ Task 99: Log successful terminal command execution
        executionLogger.logEvent({
          agentId,
          action: 'terminal_command',
          details: successMsg,
          status: 'completed',
          duration: result.executionTime,
          metadata: {
            command,
            output: result.output,
            sessionId: result.sessionId,
            exitCode: result.exitCode
          }
        });

        // ✅ Task 99: Write terminal command success to terminal and log
        if (taskId) {
          taskOutputLoggingService.addLogEntry(taskId, agentId, successMsg, 'output', {
            command,
            output: result.output,
            operation: 'terminal_command',
            sessionId: result.sessionId,
            executionTime: result.executionTime
          });
        }

        // ✅ Task 99: Emit successful output to terminal UI
        if (result.output) {
          terminalEventBus.emitAgentOutput(agentId, result.output, {
            agentName: agentDisplayName,
            command,
            success: true,
            sessionId: result.sessionId
          });
        }

        return {
          success: true,
          output: result.output || '',
          metadata: {
            agentId,
            command,
            executionTime: result.executionTime,
            sessionId: result.sessionId,
            exitCode: result.exitCode
          }
        };
      } else {
        const errorMsg = `Terminal command failed: ${result.error}`;
        console.error(`AgentExecutionService: ${errorMsg}`);

        // ✅ Task 99: Log terminal command failure
        executionLogger.logEvent({
          agentId,
          action: 'error',
          details: errorMsg,
          status: 'failed',
          duration: result.executionTime,
          metadata: {
            command,
            error: result.error,
            sessionId: result.sessionId
          }
        });

        // ✅ Task 99: Write terminal command error to terminal and log
        if (taskId) {
          taskOutputLoggingService.writeAgentErrorToTerminal(taskId, agentId, errorMsg);
        }

        // ✅ Task 99: Emit error to terminal UI
        terminalEventBus.emitSystemMessage(
          `[${agentDisplayName}] Error: ${result.error}`,
          'error'
        );

        return {
          success: false,
          output: result.output || '',
          error: result.error,
          metadata: {
            agentId,
            command,
            sessionId: result.sessionId,
            executionTime: result.executionTime
          }
        };
      }
    } catch (error) {
      const errorMsg = `Terminal command execution failed: ${error instanceof Error ? error.message : String(error)}`;
      console.error(`AgentExecutionService: ${errorMsg}`);

      // ✅ Task 99: Log terminal command error
      executionLogger.logEvent({
        agentId,
        action: 'error',
        details: errorMsg,
        status: 'failed',
        duration: Date.now() - startTime,
        metadata: { command, error: errorMsg }
      });

      // ✅ Task 99: Write terminal command error to terminal and log
      if (taskId) {
        taskOutputLoggingService.writeAgentErrorToTerminal(taskId, agentId, errorMsg);
      }

      // ✅ Task 99: Emit error to terminal UI
      terminalEventBus.emitSystemMessage(
        `[${agentDisplayName}] Error: ${errorMsg}`,
        'error'
      );

      return {
        success: false,
        output: '',
        error: errorMsg,
        metadata: { agentId, command }
      };
    }
  }

  /**
   * ✅ Task 91: Execute shell command through secure interface (legacy method)
   */
  async executeShellCommand(agentId: string, command: string, taskId?: string): Promise<ExecutionResult> {
    if (!command || typeof command !== 'string') {
      const errorMsg = 'Invalid command: command must be a non-empty string';
      console.error(`AgentExecutionService: ${errorMsg}`);

      if (taskId) {
        taskOutputLoggingService.addLogEntry(taskId, agentId, errorMsg, 'error');
      }

      return {
        success: false,
        output: '',
        error: errorMsg,
        metadata: { agentId, command: command || 'undefined' }
      };
    }

    const startTime = Date.now();
    console.log(`AgentExecutionService: Agent ${agentId} executing shell command: ${command}`);

    // ✅ Task 91: Log shell command start
    executionLogger.logEvent({
      agentId,
      action: 'shell_command',
      details: `Executing shell command: ${command}`,
      status: 'running',
      metadata: { command }
    });

    // ✅ Task 91: Write shell command start to terminal and log
    if (taskId) {
      const commandMsg = `Executing shell command: ${command}`;
      taskOutputLoggingService.addLogEntry(taskId, agentId, commandMsg, 'system', {
        command,
        operation: 'shell_command'
      });
    }

    // ✅ Task 92: Emit command start to terminal UI
    const agentDisplayName = getAgentDisplayName(agentId);
    terminalEventBus.emitSystemMessage(
      `[${agentDisplayName}] Executing: ${command}`,
      'info'
    );

    try {
      // Check if we have access to the terminal API
      if (typeof window !== 'undefined' && window.electronAPI?.terminal) {
        const result = await window.electronAPI.terminal.agentCommand({ command, agentId });

        if (result.success) {
          const successMsg = `Shell command completed successfully: ${command}`;
          console.log(`AgentExecutionService: ${successMsg}`);

          // ✅ Task 91: Log successful shell command execution
          executionLogger.logEvent({
            agentId,
            action: 'shell_command',
            details: successMsg,
            status: 'completed',
            duration: Date.now() - startTime,
            metadata: { command, output: result.output }
          });

          // ✅ Task 91: Write shell command success to terminal and log
          if (taskId) {
            taskOutputLoggingService.addLogEntry(taskId, agentId, successMsg, 'output', {
              command,
              output: result.output,
              operation: 'shell_command'
            });
          }

          // ✅ Task 92: Emit successful output to terminal UI
          if (result.output) {
            terminalEventBus.emitAgentOutput(agentId, result.output, {
              agentName: agentDisplayName,
              command,
              success: true
            });
          }

          return {
            success: true,
            output: result.output || '',
            metadata: {
              agentId,
              command,
              executionTime: Date.now() - startTime
            }
          };
        } else {
          const errorMsg = `Shell command failed: ${result.error}`;
          console.error(`AgentExecutionService: ${errorMsg}`);

          // ✅ Task 91: Log shell command failure
          executionLogger.logEvent({
            agentId,
            action: 'error',
            details: errorMsg,
            status: 'failed',
            duration: Date.now() - startTime,
            metadata: { command, error: result.error }
          });

          // ✅ Task 91: Write shell command error to terminal and log
          if (taskId) {
            taskOutputLoggingService.writeAgentErrorToTerminal(taskId, agentId, errorMsg);
          }

          // ✅ Task 92: Emit error to terminal UI
          terminalEventBus.emitSystemMessage(
            `[${agentDisplayName}] Error: ${result.error}`,
            'error'
          );

          return {
            success: false,
            output: '',
            error: result.error,
            metadata: { agentId, command }
          };
        }
      } else {
        const errorMsg = 'Terminal API not available - cannot execute shell commands';
        console.warn(`AgentExecutionService: ${errorMsg}`);

        // ✅ Task 91: Log API unavailable
        executionLogger.logEvent({
          agentId,
          action: 'error',
          details: errorMsg,
          status: 'failed',
          duration: Date.now() - startTime,
          metadata: { command, error: 'api_unavailable' }
        });

        if (taskId) {
          taskOutputLoggingService.writeAgentErrorToTerminal(taskId, agentId, errorMsg);
        }

        // ✅ Task 92: Emit API unavailable error to terminal UI
        terminalEventBus.emitSystemMessage(
          `[${agentDisplayName}] ${errorMsg}`,
          'error'
        );

        return {
          success: false,
          output: '',
          error: errorMsg,
          metadata: { agentId, command }
        };
      }
    } catch (error) {
      const errorMsg = `Shell command execution error: ${error instanceof Error ? error.message : String(error)}`;
      console.error(`AgentExecutionService: ${errorMsg}`);

      // ✅ Task 91: Log shell command error
      executionLogger.logEvent({
        agentId,
        action: 'error',
        details: errorMsg,
        status: 'failed',
        duration: Date.now() - startTime,
        metadata: { command, error: error instanceof Error ? error.message : String(error) }
      });

      if (taskId) {
        taskOutputLoggingService.writeAgentErrorToTerminal(taskId, agentId, errorMsg);
      }

      // ✅ Task 92: Emit execution error to terminal UI
      terminalEventBus.emitSystemMessage(
        `[${agentDisplayName}] ${errorMsg}`,
        'error'
      );

      return {
        success: false,
        output: '',
        error: errorMsg,
        metadata: { agentId, command }
      };
    }
  }

  /**
   * ✅ MANDATORY UPGRADE: Validate execution output to prevent false "Done" states
   */
  private validateExecutionOutput(
    files: Array<{ path: string; content: string; action: 'created' | 'modified' | 'deleted' }>,
    outputs: string[],
    work: { files?: FileCreationRequest[]; kanban?: KanbanUpdateRequest[] }
  ): boolean {
    // If work was requested but no files were created, validation fails
    if (work.files && work.files.length > 0 && files.length === 0) {
      console.warn('❌ AgentExecutionService: Validation failed - files were requested but none were created');
      return false;
    }

    // Check if created files have meaningful content
    if (files.length > 0) {
      const hasValidContent = files.every(file => {
        // File must have content
        if (!file.content || file.content.trim().length === 0) {
          console.warn(`❌ AgentExecutionService: Validation failed - file ${file.path} has no content`);
          return false;
        }

        // File content must not be just placeholder/error text
        const content = file.content.toLowerCase();
        const invalidPatterns = [
          'todo', 'placeholder', 'not implemented', 'coming soon',
          'error', 'failed', 'undefined', 'null', '// empty'
        ];

        if (invalidPatterns.some(pattern => content.includes(pattern) && content.length < 100)) {
          console.warn(`❌ AgentExecutionService: Validation failed - file ${file.path} contains placeholder content`);
          return false;
        }

        return true;
      });

      if (!hasValidContent) {
        return false;
      }
    }

    // If no files were requested and no meaningful output was produced, validation fails
    if ((!work.files || work.files.length === 0) && files.length === 0 && outputs.every(o => o.trim().length < 10)) {
      console.warn('❌ AgentExecutionService: Validation failed - no meaningful output produced');
      return false;
    }

    console.log('✅ AgentExecutionService: Execution output validation passed');
    return true;
  }

  /**
   * ✅ MANDATORY UPGRADE: Extract function names from generated files
   */
  private extractFunctionsFromFiles(files: Array<{ path: string; content: string; action: string }>): string[] {
    const functions: string[] = [];

    files.forEach(file => {
      const content = file.content;

      // Extract function declarations (basic patterns)
      const functionPatterns = [
        /function\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/g,
        /const\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\s*=\s*\(/g,
        /export\s+function\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/g,
        /class\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/g,
        /interface\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/g
      ];

      functionPatterns.forEach(pattern => {
        let match;
        while ((match = pattern.exec(content)) !== null) {
          functions.push(match[1]);
        }
      });
    });

    return [...new Set(functions)]; // Remove duplicates
  }

  /**
   * ✅ MANDATORY UPGRADE: Get file type from path
   */
  private getFileType(filePath: string): string {
    const extension = filePath.split('.').pop()?.toLowerCase();

    const typeMap: Record<string, string> = {
      'ts': 'typescript',
      'tsx': 'typescript-react',
      'js': 'javascript',
      'jsx': 'javascript-react',
      'py': 'python',
      'java': 'java',
      'cpp': 'cpp',
      'c': 'c',
      'cs': 'csharp',
      'go': 'go',
      'rs': 'rust',
      'php': 'php',
      'rb': 'ruby',
      'swift': 'swift',
      'kt': 'kotlin',
      'scala': 'scala',
      'html': 'html',
      'css': 'css',
      'scss': 'scss',
      'json': 'json',
      'xml': 'xml',
      'yaml': 'yaml',
      'yml': 'yaml',
      'md': 'markdown',
      'txt': 'text',
      'sql': 'sql'
    };

    return typeMap[extension || ''] || 'unknown';
  }

  /**
   * Get execution statistics
   */
  getExecutionStats(): {
    files: any;
    monaco: any;
  } {
    return {
      files: this.fileManager.getStats(),
      monaco: this.monacoManager.getStats()
    };
  }
}
