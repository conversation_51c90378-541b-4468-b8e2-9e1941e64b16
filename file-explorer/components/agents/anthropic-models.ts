// components/agents/anthropic-models.ts
// Complete and accurate Anthropic model list based on latest known model IDs
// This ensures agents configured with Anthropic have access to all high-performance models

export interface AnthropicModel {
  id: string;
  label: string;
  description?: string;
  contextLength?: number;
  maxOutputTokens?: number; // Maximum output tokens for this model
  costPer1kTokens?: {
    input: number;
    output: number;
  };
  capabilities?: string[];
  releaseDate?: string;
}

// ✅ CURATED MODEL LIST: Only models approved for Agent System use
export const ANTHROPIC_MODELS_CURATED: AnthropicModel[] = [
  // Claude 4 Series (Latest Generation)
  {
    id: 'claude-opus-4-20250514',
    label: 'Claude Opus 4.0',
    description: 'Most powerful Claude model with exceptional reasoning and analysis capabilities',
    contextLength: 200000,
    costPer1kTokens: {
      input: 0.015,
      output: 0.075
    },
    capabilities: ['advanced-reasoning', 'complex-analysis', 'creative-writing', 'code'],
    releaseDate: '2025-05-14'
  },
  {
    id: 'claude-opus-4-thinking-20250514',
    label: 'Claude Opus 4.0 Thinking',
    description: 'Claude Opus 4 with enhanced reasoning and step-by-step thinking',
    contextLength: 200000,
    costPer1kTokens: {
      input: 0.020,
      output: 0.100
    },
    capabilities: ['advanced-reasoning', 'enhanced-reasoning', 'complex-analysis', 'code'],
    releaseDate: '2025-05-14'
  },
  {
    id: 'claude-sonnet-4-20250514',
    label: 'Claude Sonnet 4.0',
    description: 'Balanced performance and speed for most use cases',
    contextLength: 200000,
    costPer1kTokens: {
      input: 0.003,
      output: 0.015
    },
    capabilities: ['general-purpose', 'code', 'complex-analysis', 'creative-writing'],
    releaseDate: '2025-05-14'
  },
  {
    id: 'claude-sonnet-4-thinking-20250514',
    label: 'Claude Sonnet 4.0 Thinking',
    description: 'Claude Sonnet 4 with enhanced reasoning capabilities',
    contextLength: 200000,
    costPer1kTokens: {
      input: 0.005,
      output: 0.025
    },
    capabilities: ['general-purpose', 'enhanced-reasoning', 'code', 'complex-analysis'],
    releaseDate: '2025-05-14'
  },
  {
    id: 'claude-sonnet-4-thinking-max-20250514',
    label: 'Claude Sonnet 4.0 Thinking Max',
    description: 'Claude Sonnet 4 with maximum reasoning and analysis capabilities',
    contextLength: 200000,
    costPer1kTokens: {
      input: 0.008,
      output: 0.040
    },
    capabilities: ['general-purpose', 'advanced-reasoning', 'enhanced-reasoning', 'code'],
    releaseDate: '2025-05-14'
  },

  // Claude 3.5 Series (Current Production)
  {
    id: 'claude-3-5-haiku-20241022',
    label: 'Claude Haiku 3.5',
    description: 'Fastest Claude model optimized for speed and efficiency',
    contextLength: 200000,
    costPer1kTokens: {
      input: 0.00025,
      output: 0.00125
    },
    capabilities: ['fast', 'chat', 'conversational'],
    releaseDate: '2024-10-22'
  },
  {
    id: 'claude-3-5-sonnet-20241022',
    label: 'Claude Sonnet 3.5',
    description: 'Most intelligent Claude model with enhanced reasoning and coding capabilities',
    contextLength: 200000,
    maxOutputTokens: 8192,
    costPer1kTokens: {
      input: 0.003,
      output: 0.015
    },
    capabilities: ['advanced-reasoning', 'code', 'creative-writing', 'complex-analysis'],
    releaseDate: '2024-10-22'
  }
];

// Helper functions for working with Anthropic models
export function getAnthropicModelById(id: string): AnthropicModel | undefined {
  return ANTHROPIC_MODELS.find(model => model.id === id);
}

export function getAnthropicModelOptions(): Array<{ label: string; value: string }> {
  return ANTHROPIC_MODELS.map(model => ({
    label: model.label,
    value: model.id
  }));
}

export function getLatestAnthropicModels(): AnthropicModel[] {
  // Return models from the latest generation (Claude 4 and 3.7)
  return ANTHROPIC_MODELS.filter(model =>
    model.id.includes('claude-opus-4') ||
    model.id.includes('claude-sonnet-4') ||
    model.id.includes('claude-3-7') ||
    model.id.includes('claude-3-5-sonnet-20241022') ||
    model.id.includes('claude-3-5-haiku-20241022')
  );
}

export function getAnthropicModelsByCapability(capability: string): AnthropicModel[] {
  return ANTHROPIC_MODELS.filter(model =>
    model.capabilities?.includes(capability)
  );
}

export function isValidAnthropicModel(modelId: string): boolean {
  return ANTHROPIC_MODELS.some(model => model.id === modelId);
}

export function getAnthropicModelDescription(modelId: string): string {
  const model = getAnthropicModelById(modelId);
  return model?.description || `Anthropic model: ${modelId}`;
}

export function getAnthropicModelCost(modelId: string): { input: number; output: number } | null {
  const model = getAnthropicModelById(modelId);
  return model?.costPer1kTokens || null;
}

export function getAnthropicModelMaxOutputTokens(modelId: string): number | null {
  const model = getAnthropicModelById(modelId);
  return model?.maxOutputTokens || null;
}

// ✅ CURATED: Export the curated models array for backward compatibility
export const ANTHROPIC_MODELS = [
  {
    id: 'claude-3-5-haiku-20241022',
    label: 'Claude Haiku 3.5',
    description: 'Latest Claude Haiku model with enhanced speed and efficiency',
    contextLength: 200000,
    maxOutputTokens: 8192,
    costPer1kTokens: {
      input: 0.001,
      output: 0.005
    },
    capabilities: ['fast', 'high-context', 'chat', 'general-purpose'],
    releaseDate: '2024-10-22'
  },
  {
    id: 'claude-opus-4-20250514',
    label: 'Claude Opus 4.0',
    description: 'Most powerful Claude 4 model for complex reasoning tasks',
    contextLength: 200000,
    maxOutputTokens: 8192,
    costPer1kTokens: {
      input: 0.015,
      output: 0.075
    },
    capabilities: ['advanced-reasoning', 'complex-analysis', 'creative-writing'],
    releaseDate: '2025-05-14'
  },
  {
    id: 'claude-opus-4-thinking-20250514',
    label: 'Claude Opus 4.0 Thinking',
    description: 'Claude Opus 4 with enhanced reasoning and thinking capabilities',
    contextLength: 200000,
    maxOutputTokens: 8192,
    costPer1kTokens: {
      input: 0.020,
      output: 0.100
    },
    capabilities: ['advanced-reasoning', 'reasoning', 'complex-analysis'],
    releaseDate: '2025-05-14'
  },
  {
    id: 'claude-3-5-sonnet-20241022',
    label: 'Claude Sonnet 3.5',
    description: 'Enhanced Claude 3.5 Sonnet with improved capabilities',
    contextLength: 200000,
    maxOutputTokens: 8192,
    costPer1kTokens: {
      input: 0.003,
      output: 0.015
    },
    capabilities: ['general-purpose', 'code', 'complex-analysis', 'creative-writing'],
    releaseDate: '2024-10-22'
  },
  {
    id: 'claude-sonnet-4-20250514',
    label: 'Claude Sonnet 4.0',
    description: 'Next generation Claude Sonnet with enhanced capabilities',
    contextLength: 200000,
    maxOutputTokens: 8192,
    costPer1kTokens: {
      input: 0.005,
      output: 0.025
    },
    capabilities: ['general-purpose', 'code', 'complex-analysis', 'enhanced-reasoning'],
    releaseDate: '2025-05-14'
  },
  {
    id: 'claude-sonnet-4-thinking-20250514',
    label: 'Claude Sonnet 4.0 Thinking',
    description: 'Claude Sonnet 4 with enhanced reasoning and thinking capabilities',
    contextLength: 200000,
    maxOutputTokens: 8192,
    costPer1kTokens: {
      input: 0.008,
      output: 0.040
    },
    capabilities: ['general-purpose', 'reasoning', 'reasoning', 'enhanced-reasoning'],
    releaseDate: '2025-05-14'
  },
  {
    id: 'claude-sonnet-4-thinking-max-20250514',
    label: 'Claude Sonnet 4.0 Thinking Max',
    description: 'Maximum capability Claude Sonnet 4 with advanced thinking features',
    contextLength: 200000,
    maxOutputTokens: 8192,
    costPer1kTokens: {
      input: 0.012,
      output: 0.060
    },
    capabilities: ['general-purpose', 'reasoning', 'reasoning', 'multimodal', 'enhanced-reasoning'],
    releaseDate: '2025-05-14'
  }
];

// Export model IDs for backward compatibility
export const ANTHROPIC_MODEL_IDS = ANTHROPIC_MODELS.map(model => model.id);

// Export simplified model map for provider registry compatibility
export const ANTHROPIC_MODEL_MAP: Record<string, string> = ANTHROPIC_MODELS.reduce((acc, model) => {
  // Create simplified keys for common usage
  const key = model.label.toLowerCase()
    .replace(/claude\s+/g, '')
    .replace(/\s+\(.+\)/g, '')
    .replace(/\s+/g, '-');
  acc[key] = model.id;
  return acc;
}, {} as Record<string, string>);

// Add direct ID mappings for exact matches
ANTHROPIC_MODELS.forEach(model => {
  ANTHROPIC_MODEL_MAP[model.id] = model.id;
});

// ✅ FIX: Add common model name mappings to working models
ANTHROPIC_MODEL_MAP['claude-3-sonnet'] = 'claude-3-5-sonnet-20241022'; // Map deprecated name to working model
ANTHROPIC_MODEL_MAP['claude-3-opus'] = 'claude-opus-4-20250514';
ANTHROPIC_MODEL_MAP['claude-3-haiku'] = 'claude-3-5-haiku-20241022';
ANTHROPIC_MODEL_MAP['claude-3.5-sonnet'] = 'claude-3-5-sonnet-20241022';
ANTHROPIC_MODEL_MAP['claude-3.5-haiku'] = 'claude-3-5-haiku-20241022';

export default ANTHROPIC_MODELS;
