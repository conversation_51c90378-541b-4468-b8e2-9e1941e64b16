// components/agents/curated-models-config.ts
// ✅ CURATED MODEL CONFIGURATION: Central control for approved models in Agent System

export interface CuratedModelConfig {
  enabled: boolean; // Whether to use curated list or show all models
  models: {
    anthropic: string[];
    openai: string[];
    google: string[];
    deepseek: string[];
    fireworks: string[];
    openrouter: string[];
  };
}

/**
 * ✅ CURATED MODEL LIST: Only models approved for Agent System use
 * Based on user's screenshot requirements
 */
export const CURATED_MODELS_CONFIG: CuratedModelConfig = {
  enabled: true, // ✅ Enable curated mode by default
  models: {
    anthropic: [
      'claude-3-5-haiku-20241022',      // <PERSON> 3.5
      'claude-opus-4-20250514',        // <PERSON> 4.0
      'claude-opus-4-thinking-20250514', // <PERSON> Opus 4.0 Thinking
      'claude-3-5-sonnet-20241022',    // <PERSON> 3.5
      'claude-sonnet-4-20250514',      // <PERSON> 4.0
      'claude-sonnet-4-thinking-20250514', // <PERSON> 4.0 Thinking
      'claude-sonnet-4-thinking-max-20250514' // <PERSON> 4.0 Thinking Max
    ],
    deepseek: [
      'deepseek-r1',                   // DeepSeek R1
      'deepseek-v3'                    // DeepSeek V3
    ],
    fireworks: [
      'deepseek-r1',                   // DeepSeek R1
      'deepseek-v3-03-24',            // DeepSeek V3 (03-24)
      'llama-4-maverick',             // Llama 4 Maverick
      'qwen-3-235b-a22b'              // Qwen 3 (235b-a22b)
    ],
    google: [
      'gemini-2.0-flash',             // Gemini 2.0 Flash
      'gemini-2.0-flash-lite',        // Gemini 2.0 Flash Lite
      'gemini-2.5-flash-preview-05-20', // Gemini 2.5 Flash Preview 05-20
      'gemini-2.5-pro-preview-06-05'  // Gemini 2.5 Pro Preview 06-05
    ],
    openai: [
      'gpt-4.1',                      // gpt 4.1
      'gpt-4.1-mini',                 // gpt 4.1 mini
      'gpt-4.1-nano',                 // gpt 4.1 nano
      'gpt-4o',                       // gpt 4o
      'o3',                           // o3
      'o3-pro',                       // o3 pro
      'o4-mini',                      // o4-mini
      'o4-mini-high',                 // o4-mini-high
      'o4-mini-low'                   // o4-mini-low
    ],
    openrouter: [
      // OpenRouter models will be filtered from the above providers
    ]
  }
};

/**
 * Check if a model is in the curated list for a provider
 */
export function isModelCurated(provider: string, modelId: string): boolean {
  if (!CURATED_MODELS_CONFIG.enabled) {
    return true; // If curated mode is disabled, all models are allowed
  }
  
  const providerModels = CURATED_MODELS_CONFIG.models[provider as keyof typeof CURATED_MODELS_CONFIG.models];
  return providerModels ? providerModels.includes(modelId) : false;
}

/**
 * Get curated models for a specific provider
 */
export function getCuratedModels(provider: string): string[] {
  if (!CURATED_MODELS_CONFIG.enabled) {
    return []; // Return empty array to indicate no filtering
  }
  
  return CURATED_MODELS_CONFIG.models[provider as keyof typeof CURATED_MODELS_CONFIG.models] || [];
}

/**
 * Filter a list of models to only include curated ones
 */
export function filterToCuratedModels(provider: string, models: string[]): string[] {
  if (!CURATED_MODELS_CONFIG.enabled) {
    return models; // Return all models if curated mode is disabled
  }
  
  const curatedList = getCuratedModels(provider);
  if (curatedList.length === 0) {
    return models; // No filtering if no curated list exists
  }
  
  return models.filter(model => curatedList.includes(model));
}

/**
 * Toggle curated mode on/off
 */
export function setCuratedMode(enabled: boolean): void {
  CURATED_MODELS_CONFIG.enabled = enabled;
}

/**
 * Check if curated mode is enabled
 */
export function isCuratedModeEnabled(): boolean {
  return CURATED_MODELS_CONFIG.enabled;
}
