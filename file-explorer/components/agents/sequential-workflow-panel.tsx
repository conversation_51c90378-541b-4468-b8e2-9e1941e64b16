// file-explorer/components/agents/sequential-workflow-panel.tsx
// ✅ TASK 3.1: SequentialWorkflowPanel Component - UI controls for sequential workflow execution

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Play, CheckCircle, RefreshCw, Clock, User, List, AlertTriangle } from 'lucide-react';
import { AgentUIBridge, SequentialWorkflowStatus, TaskApproval } from './agent-ui-bridge';

// ✅ TASK 3.4: Import TaskCompletionDialog for task completion workflow
import { TaskCompletionDialog, TaskState, DeliverableReport } from './task-completion-dialog';

// ✅ Component Props Interface
export interface SequentialWorkflowPanelProps {
  className?: string;
  onTaskStart?: (taskId: string) => void;
  onTaskComplete?: (taskId: string, approval: TaskApproval) => void;
}

// ✅ SequentialWorkflowPanel Component
export const SequentialWorkflowPanel: React.FC<SequentialWorkflowPanelProps> = ({
  className,
  onTaskStart,
  onTaskComplete
}) => {
  const [workflowStatus, setWorkflowStatus] = useState<SequentialWorkflowStatus>({
    isActive: false,
    currentAgent: null,
    currentTask: null,
    queueLength: 0,
    completedTasks: 0,
    totalTasks: 0
  });
  const [isStarting, setIsStarting] = useState(false);
  const [isCompleting, setIsCompleting] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<number>(Date.now());

  // ✅ TASK 3.4: State for task completion dialog
  const [showCompletionDialog, setShowCompletionDialog] = useState(false);
  const [currentTaskForReview, setCurrentTaskForReview] = useState<TaskState | null>(null);
  const [completionReport, setCompletionReport] = useState<DeliverableReport | null>(null);

  // ✅ Real-time workflow status updates
  useEffect(() => {
    console.log('SequentialWorkflowPanel: Initializing...');
    const agentUIBridge = AgentUIBridge.getInstance();
    
    // Subscribe to workflow status updates
    const unsubscribe = agentUIBridge.subscribeToWorkflowStatus((status: SequentialWorkflowStatus) => {
      console.log('SequentialWorkflowPanel: Received workflow status update:', status);
      setWorkflowStatus(status);
      setLastUpdate(Date.now());
    });

    // Get initial status
    const initialStatus = agentUIBridge.getSequentialWorkflowStatus();
    setWorkflowStatus(initialStatus);
    console.log('SequentialWorkflowPanel: Initial status:', initialStatus);

    return () => {
      console.log('SequentialWorkflowPanel: Cleaning up subscription');
      unsubscribe();
    };
  }, []);

  // ✅ Handle Start Next Task
  const handleStartNextTask = async () => {
    setIsStarting(true);
    try {
      console.log('SequentialWorkflowPanel: Starting next task...');
      const agentUIBridge = AgentUIBridge.getInstance();
      const result = await agentUIBridge.startNextSequentialTask();
      
      console.log('SequentialWorkflowPanel: Start task result:', result);
      
      if (result.success && onTaskStart && workflowStatus.currentTask) {
        onTaskStart(workflowStatus.currentTask);
      }
      
      if (!result.success) {
        console.warn('SequentialWorkflowPanel: Failed to start task:', result.message);
      }
    } catch (error) {
      console.error('SequentialWorkflowPanel: Error starting task:', error);
    } finally {
      setIsStarting(false);
    }
  };

  // ✅ Handle Complete Current Task - Show completion dialog
  const handleCompleteCurrentTask = async () => {
    if (!workflowStatus.currentTask || !workflowStatus.currentAgent) {
      console.warn('SequentialWorkflowPanel: No current task to complete');
      return;
    }

    // ✅ INTEGRATION FIX: Get real task data from AgentUIBridge instead of mock data
    const agentUIBridge = AgentUIBridge.getInstance();
    const realTask = agentUIBridge.getCurrentTaskState();
    const realReport = agentUIBridge.getTaskCompletionReport(workflowStatus.currentTask);

    if (!realTask || !realReport) {
      console.warn('SequentialWorkflowPanel: No real task data available for completion');
      return;
    }

    setCurrentTaskForReview(realTask);
    setCompletionReport(realReport);
    setShowCompletionDialog(true);
  };

  // ✅ Handle task approval from dialog
  const handleTaskApproval = async (approval: TaskApproval) => {
    setIsCompleting(true);
    try {
      console.log('SequentialWorkflowPanel: Processing task approval:', approval);
      const agentUIBridge = AgentUIBridge.getInstance();
      const result = await agentUIBridge.completeCurrentTask(approval);

      console.log('SequentialWorkflowPanel: Complete task result:', result);

      if (result.success && onTaskComplete && workflowStatus.currentTask) {
        onTaskComplete(workflowStatus.currentTask, approval);
      }

      if (!result.success) {
        console.warn('SequentialWorkflowPanel: Failed to complete task:', result.message);
      }
    } catch (error) {
      console.error('SequentialWorkflowPanel: Error completing task:', error);
    } finally {
      setIsCompleting(false);
      setShowCompletionDialog(false);
      setCurrentTaskForReview(null);
      setCompletionReport(null);
    }
  };

  // ✅ Calculate progress percentage
  const progressPercentage = workflowStatus.totalTasks > 0 
    ? Math.round((workflowStatus.completedTasks / workflowStatus.totalTasks) * 100)
    : 0;

  // ✅ Get status color and icon
  const getStatusDisplay = () => {
    if (workflowStatus.isActive) {
      return {
        color: 'text-green-500',
        bgColor: 'bg-green-50 dark:bg-green-950',
        icon: <Play className="h-4 w-4" />,
        text: 'Active'
      };
    } else if (workflowStatus.queueLength > 0) {
      return {
        color: 'text-yellow-500',
        bgColor: 'bg-yellow-50 dark:bg-yellow-950',
        icon: <Clock className="h-4 w-4" />,
        text: 'Queued'
      };
    } else {
      return {
        color: 'text-gray-500',
        bgColor: 'bg-gray-50 dark:bg-gray-950',
        icon: <RefreshCw className="h-4 w-4" />,
        text: 'Idle'
      };
    }
  };

  const statusDisplay = getStatusDisplay();

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Sequential Execution Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Play className="h-5 w-5" />
            Sequential Workflow Control
          </CardTitle>
          <CardDescription>
            Controlled sequential execution with user confirmation checkpoints
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Status Overview */}
          <div className={`p-4 rounded-lg ${statusDisplay.bgColor}`}>
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center gap-2">
                <div className={statusDisplay.color}>
                  {statusDisplay.icon}
                </div>
                <span className="font-medium">Workflow Status</span>
                <Badge variant={workflowStatus.isActive ? "default" : "secondary"}>
                  {statusDisplay.text}
                </Badge>
              </div>
              <div className="text-sm text-muted-foreground">
                Last updated: {new Date(lastUpdate).toLocaleTimeString()}
              </div>
            </div>
            
            {/* Status Details Grid */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div className="flex items-center gap-2">
                <User className="h-4 w-4 text-muted-foreground" />
                <div>
                  <div className="text-muted-foreground">Current Agent</div>
                  <div className="font-medium">
                    {workflowStatus.currentAgent || "None"}
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <List className="h-4 w-4 text-muted-foreground" />
                <div>
                  <div className="text-muted-foreground">Queue Length</div>
                  <div className="font-medium">{workflowStatus.queueLength}</div>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-muted-foreground" />
                <div>
                  <div className="text-muted-foreground">Completed</div>
                  <div className="font-medium">{workflowStatus.completedTasks}</div>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <RefreshCw className="h-4 w-4 text-muted-foreground" />
                <div>
                  <div className="text-muted-foreground">Total Tasks</div>
                  <div className="font-medium">{workflowStatus.totalTasks}</div>
                </div>
              </div>
            </div>
          </div>

          {/* Progress Bar */}
          {workflowStatus.totalTasks > 0 && (
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Overall Progress</span>
                <span>{progressPercentage}%</span>
              </div>
              <Progress value={progressPercentage} className="h-3" />
            </div>
          )}

          {/* Control Buttons */}
          <div className="flex gap-3">
            <Button
              onClick={handleStartNextTask}
              disabled={isStarting || workflowStatus.isActive || workflowStatus.queueLength === 0}
              className="min-w-[160px]"
            >
              {isStarting ? (
                <>
                  <RefreshCw className="h-4 w-4 animate-spin mr-2" />
                  Starting...
                </>
              ) : (
                <>
                  <Play className="h-4 w-4 mr-2" />
                  Start Next Task
                </>
              )}
            </Button>
            
            <Button
              variant="outline"
              onClick={handleCompleteCurrentTask}
              disabled={isCompleting || !workflowStatus.isActive}
              className="min-w-[160px]"
            >
              {isCompleting ? (
                <>
                  <RefreshCw className="h-4 w-4 animate-spin mr-2" />
                  Completing...
                </>
              ) : (
                <>
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Complete Current Task
                </>
              )}
            </Button>
          </div>

          {/* Current Task Display */}
          {workflowStatus.currentTask && (
            <div className="p-4 border rounded-lg bg-blue-50 dark:bg-blue-950">
              <div className="flex items-center gap-2 mb-2">
                <Play className="h-4 w-4 text-blue-600" />
                <span className="font-medium text-blue-600">Current Task</span>
              </div>
              <div className="text-sm">
                <div className="font-medium">{workflowStatus.currentTask}</div>
                <div className="text-muted-foreground mt-1">
                  Assigned to: {workflowStatus.currentAgent}
                </div>
              </div>
            </div>
          )}

          {/* Help Text */}
          {!workflowStatus.isActive && workflowStatus.queueLength === 0 && (
            <div className="p-4 border rounded-lg bg-muted/50">
              <div className="flex items-center gap-2 mb-2">
                <AlertTriangle className="h-4 w-4 text-muted-foreground" />
                <span className="font-medium text-muted-foreground">No Tasks in Queue</span>
              </div>
              <p className="text-sm text-muted-foreground">
                Submit tasks through the Agent Orchestrator to begin sequential workflow execution.
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Task Queue Visualization */}
      <TaskQueueVisualization workflowStatus={workflowStatus} />

      {/* ✅ TASK 3.4: Task Completion Dialog */}
      {showCompletionDialog && currentTaskForReview && completionReport && (
        <TaskCompletionDialog
          isOpen={showCompletionDialog}
          task={currentTaskForReview}
          completionReport={completionReport}
          onApprove={() => handleTaskApproval({ approved: true })}
          onReject={(feedback) => handleTaskApproval({ approved: false, feedback })}
          onModify={(modifications) => handleTaskApproval({ approved: false, modifications, retryRequested: true })}
          onClose={() => setShowCompletionDialog(false)}
        />
      )}
    </div>
  );
};

// ✅ Task Queue Visualization Component
const TaskQueueVisualization: React.FC<{ workflowStatus: SequentialWorkflowStatus }> = ({ workflowStatus }) => {
  if (workflowStatus.queueLength === 0 && !workflowStatus.isActive) {
    return null;
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <List className="h-5 w-5" />
          Task Queue
        </CardTitle>
        <CardDescription>
          Sequential task execution queue
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {/* Current Task */}
          {workflowStatus.isActive && workflowStatus.currentTask && (
            <div className="flex items-center gap-3 p-3 border rounded-lg bg-green-50 dark:bg-green-950">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 rounded-full bg-green-500 flex items-center justify-center">
                  <Play className="h-4 w-4 text-white" />
                </div>
              </div>
              <div className="flex-1">
                <div className="font-medium text-green-700 dark:text-green-300">
                  Currently Executing
                </div>
                <div className="text-sm text-muted-foreground">
                  {workflowStatus.currentAgent} • {workflowStatus.currentTask}
                </div>
              </div>
              <Badge variant="default">Active</Badge>
            </div>
          )}

          {/* Queued Tasks */}
          {Array.from({ length: Math.min(workflowStatus.queueLength, 5) }, (_, index) => (
            <div key={index} className="flex items-center gap-3 p-3 border rounded-lg">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                  <span className="text-sm font-medium">{index + 1}</span>
                </div>
              </div>
              <div className="flex-1">
                <div className="font-medium">Queued Task {index + 1}</div>
                <div className="text-sm text-muted-foreground">
                  Waiting for execution
                </div>
              </div>
              <Badge variant="secondary">Pending</Badge>
            </div>
          ))}

          {/* Show more indicator */}
          {workflowStatus.queueLength > 5 && (
            <div className="text-center text-sm text-muted-foreground py-2">
              ... and {workflowStatus.queueLength - 5} more tasks
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default SequentialWorkflowPanel;
