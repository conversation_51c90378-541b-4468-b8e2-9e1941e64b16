// components/agents/agent-work-tracker.ts

export interface AgentWorkload {
  agentId: string;
  agentRole: string;
  numberOfActiveTasks: number;
  maxConcurrentTasks: number;
  averageTaskDuration: number;
  lastTaskStartTime: number;
  lastTaskEndTime: number;
  totalTasksCompleted: number;
  totalTasksFailed: number;
  currentLoadPercentage: number;
  healthScore: number;
  isAvailable: boolean;
}

export interface LoadBalancingMetrics {
  totalAgents: number;
  availableAgents: number;
  overloadedAgents: number;
  averageLoad: number;
  maxLoad: number;
  minLoad: number;
  totalActiveTasks: number;
  queuedTasks: number;
}

/**
 * ✅ Task 81: Agent Work Tracker
 * Tracks which agents are actively executing tasks and prevents overload
 */
export class AgentWorkTracker {
  private agentWorkMap = new Map<string, AgentWorkload>();
  private taskAssignments = new Map<string, string>(); // taskId -> agentId
  private loadBalancingHistory: LoadBalancingMetrics[] = [];
  private maxHistorySize = 100;

  // Configuration
  private readonly config = {
    maxConcurrentTasksDefault: 5, // ✅ PERFORMANCE FIX: Increased from 3 to 5
    overloadThreshold: 0.8, // 80% load considered overloaded
    healthScoreThreshold: 10, // ✅ PERFORMANCE FIX: Reduced from 30 to 10 for better availability
    taskTimeoutMs: 120000, // ✅ PERFORMANCE FIX: Reduced from 5min to 2min
    loadBalancingStrategy: 'least_loaded' as 'least_loaded' | 'round_robin' | 'capability_based'
  };

  constructor() {
    this.startPeriodicCleanup();
  }

  /**
   * Register an agent with the work tracker
   */
  public registerAgent(
    agentId: string,
    agentRole: string,
    maxConcurrentTasks: number = this.config.maxConcurrentTasksDefault
  ): void {
    const workload: AgentWorkload = {
      agentId,
      agentRole,
      numberOfActiveTasks: 0,
      maxConcurrentTasks,
      averageTaskDuration: 0,
      lastTaskStartTime: 0,
      lastTaskEndTime: 0,
      totalTasksCompleted: 0,
      totalTasksFailed: 0,
      currentLoadPercentage: 0,
      healthScore: 100,
      isAvailable: true
    };

    this.agentWorkMap.set(agentId, workload);
    console.log(`AgentWorkTracker: Registered agent ${agentId} (${agentRole}) with max ${maxConcurrentTasks} concurrent tasks`);
  }

  /**
   * Assign a task to an agent and track the workload
   */
  public assignTaskToAgent(taskId: string, agentId: string): boolean {
    const workload = this.agentWorkMap.get(agentId);
    if (!workload) {
      console.error(`AgentWorkTracker: Agent ${agentId} not registered`);
      return false;
    }

    if (workload.numberOfActiveTasks >= workload.maxConcurrentTasks) {
      console.warn(`AgentWorkTracker: Agent ${agentId} is at capacity (${workload.numberOfActiveTasks}/${workload.maxConcurrentTasks})`);
      return false;
    }

    if (!workload.isAvailable || workload.healthScore < this.config.healthScoreThreshold) {
      console.warn(`AgentWorkTracker: Agent ${agentId} is not available (health: ${workload.healthScore})`);
      return false;
    }

    // Assign the task
    workload.numberOfActiveTasks++;
    workload.lastTaskStartTime = Date.now();
    workload.currentLoadPercentage = (workload.numberOfActiveTasks / workload.maxConcurrentTasks) * 100;

    this.taskAssignments.set(taskId, agentId);

    console.log(`AgentWorkTracker: Assigned task ${taskId} to agent ${agentId} (load: ${workload.currentLoadPercentage.toFixed(1)}%)`);
    return true;
  }

  /**
   * Mark a task as completed and update agent workload
   */
  public completeTask(taskId: string, success: boolean = true): void {
    const agentId = this.taskAssignments.get(taskId);
    if (!agentId) {
      console.warn(`AgentWorkTracker: Task ${taskId} not found in assignments`);
      return;
    }

    const workload = this.agentWorkMap.get(agentId);
    if (!workload) {
      console.warn(`AgentWorkTracker: Agent ${agentId} not found for task completion`);
      return;
    }

    // Update workload
    workload.numberOfActiveTasks = Math.max(0, workload.numberOfActiveTasks - 1);
    workload.lastTaskEndTime = Date.now();
    workload.currentLoadPercentage = (workload.numberOfActiveTasks / workload.maxConcurrentTasks) * 100;

    // Update task completion stats
    if (success) {
      workload.totalTasksCompleted++;
    } else {
      workload.totalTasksFailed++;
    }

    // Update average task duration
    if (workload.lastTaskStartTime > 0) {
      const taskDuration = workload.lastTaskEndTime - workload.lastTaskStartTime;
      if (workload.averageTaskDuration === 0) {
        workload.averageTaskDuration = taskDuration;
      } else {
        workload.averageTaskDuration = (workload.averageTaskDuration * 0.8) + (taskDuration * 0.2);
      }
    }

    // ✅ Update health score with more robust calculation
    const totalTasks = workload.totalTasksCompleted + workload.totalTasksFailed;
    if (totalTasks > 0) {
      const successRate = workload.totalTasksCompleted / totalTasks;

      // ✅ More gradual health score calculation to prevent immediate drops to 0
      if (success) {
        // Successful task: increase health score gradually
        workload.healthScore = Math.min(100, workload.healthScore + 5);
      } else {
        // Failed task: decrease health score more gradually
        workload.healthScore = Math.max(20, workload.healthScore - 10); // Minimum 20% health
      }

      // ✅ Ensure health score doesn't go below minimum threshold for availability
      if (workload.healthScore < 30 && totalTasks < 5) {
        // Give new agents benefit of the doubt for first few tasks
        workload.healthScore = 50;
      }
    }

    // Remove task assignment
    this.taskAssignments.delete(taskId);

    console.log(`AgentWorkTracker: Completed task ${taskId} for agent ${agentId} (success: ${success}, load: ${workload.currentLoadPercentage.toFixed(1)}%)`);
  }

  /**
   * Find the agent with the least load for a specific role
   */
  public assignAgentWithLeastLoad(role: string): string | null {
    const availableAgents = Array.from(this.agentWorkMap.values())
      .filter(workload =>
        workload.agentRole === role &&
        workload.isAvailable &&
        workload.numberOfActiveTasks < workload.maxConcurrentTasks &&
        workload.healthScore >= this.config.healthScoreThreshold
      );

    if (availableAgents.length === 0) {
      console.warn(`AgentWorkTracker: No available agents found for role ${role}`);
      return null;
    }

    // Sort by load percentage (ascending)
    availableAgents.sort((a, b) => a.currentLoadPercentage - b.currentLoadPercentage);

    const selectedAgent = availableAgents[0];
    console.log(`AgentWorkTracker: Selected agent ${selectedAgent.agentId} for role ${role} (load: ${selectedAgent.currentLoadPercentage.toFixed(1)}%)`);

    return selectedAgent.agentId;
  }

  /**
   * Get workload information for a specific agent
   */
  public getAgentWorkload(agentId: string): AgentWorkload | null {
    return this.agentWorkMap.get(agentId) || null;
  }

  /**
   * Get workload information for all agents
   */
  public getAllAgentWorkloads(): AgentWorkload[] {
    return Array.from(this.agentWorkMap.values());
  }

  /**
   * Get agents by role
   */
  public getAgentsByRole(role: string): AgentWorkload[] {
    return Array.from(this.agentWorkMap.values())
      .filter(workload => workload.agentRole === role);
  }

  /**
   * Get overloaded agents (above threshold)
   */
  public getOverloadedAgents(): AgentWorkload[] {
    return Array.from(this.agentWorkMap.values())
      .filter(workload => workload.currentLoadPercentage > (this.config.overloadThreshold * 100));
  }

  /**
   * Get load balancing metrics
   */
  public getLoadBalancingMetrics(): LoadBalancingMetrics {
    const workloads = Array.from(this.agentWorkMap.values());
    const availableAgents = workloads.filter(w => w.isAvailable && w.healthScore >= this.config.healthScoreThreshold);
    const overloadedAgents = workloads.filter(w => w.currentLoadPercentage > (this.config.overloadThreshold * 100));

    const loads = workloads.map(w => w.currentLoadPercentage);
    const totalActiveTasks = workloads.reduce((sum, w) => sum + w.numberOfActiveTasks, 0);

    const metrics: LoadBalancingMetrics = {
      totalAgents: workloads.length,
      availableAgents: availableAgents.length,
      overloadedAgents: overloadedAgents.length,
      averageLoad: loads.length > 0 ? loads.reduce((sum, load) => sum + load, 0) / loads.length : 0,
      maxLoad: loads.length > 0 ? Math.max(...loads) : 0,
      minLoad: loads.length > 0 ? Math.min(...loads) : 0,
      totalActiveTasks,
      queuedTasks: this.taskAssignments.size
    };

    // Store in history
    this.loadBalancingHistory.push(metrics);
    if (this.loadBalancingHistory.length > this.maxHistorySize) {
      this.loadBalancingHistory.shift();
    }

    return metrics;
  }

  /**
   * Set agent availability
   */
  public setAgentAvailability(agentId: string, isAvailable: boolean): void {
    const workload = this.agentWorkMap.get(agentId);
    if (workload) {
      workload.isAvailable = isAvailable;
      console.log(`AgentWorkTracker: Set agent ${agentId} availability to ${isAvailable}`);
    }
  }

  /**
   * Update agent health score
   */
  public updateAgentHealthScore(agentId: string, healthScore: number): void {
    const workload = this.agentWorkMap.get(agentId);
    if (workload) {
      workload.healthScore = Math.max(20, Math.min(100, healthScore)); // ✅ Minimum 20% health
      console.log(`AgentWorkTracker: Updated agent ${agentId} health score to ${workload.healthScore}`);
    }
  }

  /**
   * ✅ Reset agent health score to default (useful for recovery)
   */
  public resetAgentHealth(agentId: string): void {
    const workload = this.agentWorkMap.get(agentId);
    if (workload) {
      workload.healthScore = 80; // Reset to good health
      workload.totalTasksCompleted = 0;
      workload.totalTasksFailed = 0;
      workload.isAvailable = true;
      console.log(`AgentWorkTracker: Reset agent ${agentId} health to 80%`);
    }
  }

  /**
   * ✅ Reset all agent health scores
   */
  public resetAllAgentHealth(): void {
    for (const [agentId, workload] of this.agentWorkMap.entries()) {
      workload.healthScore = 80;
      workload.totalTasksCompleted = 0;
      workload.totalTasksFailed = 0;
      workload.isAvailable = true;
      console.log(`AgentWorkTracker: Reset agent ${agentId} health to 80%`);
    }
    console.log('AgentWorkTracker: Reset all agent health scores');
  }

  /**
   * Get load balancing history
   */
  public getLoadBalancingHistory(): LoadBalancingMetrics[] {
    return [...this.loadBalancingHistory];
  }

  /**
   * Clear all workload data
   */
  public clear(): void {
    this.agentWorkMap.clear();
    this.taskAssignments.clear();
    this.loadBalancingHistory = [];
    console.log('AgentWorkTracker: Cleared all workload data');
  }

  /**
   * Periodic cleanup of stale task assignments
   */
  private startPeriodicCleanup(): void {
    setInterval(() => {
      const now = Date.now();
      const staleTaskIds: string[] = [];

      // Find tasks that have been running too long
      for (const [taskId, agentId] of this.taskAssignments.entries()) {
        const workload = this.agentWorkMap.get(agentId);
        if (workload && workload.lastTaskStartTime > 0) {
          const taskDuration = now - workload.lastTaskStartTime;
          if (taskDuration > this.config.taskTimeoutMs) {
            staleTaskIds.push(taskId);
          }
        }
      }

      // Clean up stale tasks
      for (const taskId of staleTaskIds) {
        console.warn(`AgentWorkTracker: Cleaning up stale task ${taskId}`);
        this.completeTask(taskId, false);
      }
    }, 60000); // Run every minute
  }
}

// Global instance
export const agentWorkTracker = new AgentWorkTracker();
