// components/agents/task-status-service.ts
import { AgentResponse } from './agent-base';
import { TaskCoordinationResult } from './agent-task-coordinator';
import { KanbanTaskBridge } from './kanban-task-bridge';
// ✅ MANDATORY UPGRADE: Import execution outcome logging
import {
  logTaskExecutionSucceeded,
  logTaskExecutionFailed,
  logSilentExecutionFailure,
  logCardMarkedDoneWithoutOutput
} from '../../services/logger';

export interface TaskStatusUpdate {
  taskId: string;
  agentId: string;
  status: 'pending' | 'delegated' | 'running' | 'verifying' | 'finalizing' | 'completed' | 'failed' | 'retrying' | 'waiting';
  progress?: number;
  message?: string;
  timestamp: number;
  metadata?: Record<string, any>;
}

export interface TaskStatusCallbacks {
  onStatusUpdate?: (update: TaskStatusUpdate) => void;
  onProgressUpdate?: (taskId: string, progress: number) => void;
  onKanbanUpdate?: (taskId: string, cardId: string, status: string) => void;
}

// ✅ MANDATORY UPGRADE: Validation interface for execution results
export interface ExecutionValidationResult {
  isValid: boolean;
  hasOutput: boolean;
  filesCreated: number;
  outputSize: number;
  validationErrors: string[];
  reason?: string;
}

export interface TaskMetrics {
  totalTasks: number;
  completedTasks: number;
  failedTasks: number;
  runningTasks: number;
  waitingTasks: number;
  retryingTasks: number;
  averageExecutionTime: number;
  successRate: number;
  totalExecutionTime: number;
}

export class TaskStatusService {
  private static instance: TaskStatusService;
  private statusHistory: Map<string, TaskStatusUpdate[]> = new Map();
  private currentStatuses: Map<string, TaskStatusUpdate> = new Map();
  private callbacks: TaskStatusCallbacks = {};
  private taskMetrics: TaskMetrics = {
    totalTasks: 0,
    completedTasks: 0,
    failedTasks: 0,
    runningTasks: 0,
    waitingTasks: 0,
    retryingTasks: 0,
    averageExecutionTime: 0,
    successRate: 0,
    totalExecutionTime: 0
  };
  private taskStartTimes: Map<string, number> = new Map();

  public static getInstance(): TaskStatusService {
    if (!TaskStatusService.instance) {
      TaskStatusService.instance = new TaskStatusService();
    }
    return TaskStatusService.instance;
  }

  /**
   * Set callbacks for status updates
   */
  public setCallbacks(callbacks: TaskStatusCallbacks): void {
    this.callbacks = { ...this.callbacks, ...callbacks };
  }

  /**
   * Update task status with real-time sync
   */
  public async updateTaskStatus(
    taskId: string,
    agentId: string,
    status: TaskStatusUpdate['status'],
    options: {
      progress?: number;
      message?: string;
      metadata?: Record<string, any>;
      kanbanCardId?: string;
    } = {}
  ): Promise<void> {
    const timestamp = Date.now();

    const statusUpdate: TaskStatusUpdate = {
      taskId,
      agentId,
      status,
      progress: options.progress,
      message: options.message,
      timestamp,
      metadata: options.metadata
    };

    // Store current status
    this.currentStatuses.set(taskId, statusUpdate);

    // Add to history
    if (!this.statusHistory.has(taskId)) {
      this.statusHistory.set(taskId, []);
    }
    this.statusHistory.get(taskId)!.push(statusUpdate);

    // Track task timing
    if (status === 'running' && !this.taskStartTimes.has(taskId)) {
      this.taskStartTimes.set(taskId, timestamp);
    } else if ((status === 'completed' || status === 'failed') && this.taskStartTimes.has(taskId)) {
      const startTime = this.taskStartTimes.get(taskId)!;
      const executionTime = timestamp - startTime;
      this.taskStartTimes.delete(taskId);

      // Update metrics
      this.updateMetrics(status, executionTime);
    }

    // Update Kanban card if provided
    if (options.kanbanCardId) {
      await this.updateKanbanCard(taskId, options.kanbanCardId, status, agentId, options.progress);
    }

    // Notify callbacks
    if (this.callbacks.onStatusUpdate) {
      this.callbacks.onStatusUpdate(statusUpdate);
    }

    if (options.progress !== undefined && this.callbacks.onProgressUpdate) {
      this.callbacks.onProgressUpdate(taskId, options.progress);
    }

    console.log(`TaskStatusService: Updated task ${taskId} status to ${status}${options.progress !== undefined ? ` (${options.progress}%)` : ''}`);
  }

  /**
   * Update Kanban card based on task status
   */
  private async updateKanbanCard(
    taskId: string,
    cardId: string,
    status: TaskStatusUpdate['status'],
    agentId: string,
    progress?: number
  ): Promise<void> {
    try {
      // Update card progress
      if (progress !== undefined) {
        await KanbanTaskBridge.updateCardProgress(cardId, progress, agentId);
      }

      // Move card based on status - Enhanced for automated agent workflow
      let targetStatus: 'pending' | 'delegated' | 'running' | 'verifying' | 'finalizing' | 'completed' | 'failed';
      switch (status) {
        case 'waiting':
        case 'pending':
          targetStatus = 'pending';
          break;
        case 'delegated':
        case 'assigned':
          targetStatus = 'delegated';
          break;
        case 'running':
        case 'retrying':
          targetStatus = 'running';
          break;
        case 'verifying':
        case 'testing':
          targetStatus = 'verifying';
          break;
        case 'finalizing':
        case 'post_processing':
          targetStatus = 'finalizing';
          break;
        case 'completed':
          targetStatus = 'completed';
          break;
        case 'failed':
          targetStatus = 'failed';
          break;
        default:
          targetStatus = 'pending';
      }

      // ✅ Check if card is already in the target column to prevent duplicate moves
      const currentCardState = await this.getCurrentCardState(cardId);

      // Map status to column IDs - Optimized for automated agent execution
      const columnMap = {
        'pending': 'column-1',    // Pending
        'delegated': 'column-2',  // Delegated
        'running': 'column-3',    // Running
        'verifying': 'column-4',  // Verifying
        'finalizing': 'column-5', // Finalizing
        'completed': 'column-6',  // Complete
        'failed': 'column-7'      // Error / Retry
      };

      const targetColumnId = columnMap[targetStatus];

      // Only move if card is not already in the target column
      if (currentCardState && currentCardState.columnId === targetColumnId) {
        console.log(`⚠️ [TaskStatusService] Card ${cardId} is already in target column ${targetColumnId} for status ${status}, skipping move`);

        // Still notify callback even if we skip the move
        if (this.callbacks.onKanbanUpdate) {
          this.callbacks.onKanbanUpdate(taskId, cardId, status);
        }
        return;
      }

      console.log(`🔄 [TaskStatusService] Moving card ${cardId} to ${targetStatus} status (column ${targetColumnId}) for task ${taskId}`);
      await KanbanTaskBridge.moveCardBasedOnTaskStatus(cardId, targetStatus, agentId);

      // Notify Kanban update callback
      if (this.callbacks.onKanbanUpdate) {
        this.callbacks.onKanbanUpdate(taskId, cardId, status);
      }

      console.log(`TaskStatusService: Updated Kanban card ${cardId} for task ${taskId} to ${targetStatus}`);
    } catch (error) {
      console.error(`TaskStatusService: Failed to update Kanban card ${cardId}:`, error);
    }
  }

  /**
   * ✅ Get current card state to prevent duplicate moves
   */
  private async getCurrentCardState(cardId: string): Promise<{ columnId: string; title: string } | null> {
    try {
      // Import board IPC bridge dynamically to avoid circular dependencies
      const { boardIPCBridge } = await import('../kanban/lib/board-ipc-bridge');

      // Get board state and find the card
      const boardState = await boardIPCBridge.getBoardState('main');
      if (!boardState) {
        console.warn(`TaskStatusService: Failed to get board state for card ${cardId}`);
        return null;
      }

      // Find the card across all columns
      for (const column of boardState.columns) {
        const card = column.cards.find(c => c.id === cardId);
        if (card) {
          return {
            columnId: column.id,
            title: card.title
          };
        }
      }

      console.warn(`TaskStatusService: Card ${cardId} not found in any column`);
      return null;
    } catch (error) {
      console.warn(`TaskStatusService: Failed to get current state for card ${cardId}:`, error);
      return null;
    }
  }

  /**
   * Update task metrics
   */
  private updateMetrics(status: 'completed' | 'failed', executionTime: number): void {
    this.taskMetrics.totalTasks++;
    this.taskMetrics.totalExecutionTime += executionTime;

    if (status === 'completed') {
      this.taskMetrics.completedTasks++;
    } else if (status === 'failed') {
      this.taskMetrics.failedTasks++;
    }

    // Calculate averages
    this.taskMetrics.averageExecutionTime = this.taskMetrics.totalExecutionTime / this.taskMetrics.totalTasks;
    this.taskMetrics.successRate = (this.taskMetrics.completedTasks / this.taskMetrics.totalTasks) * 100;

    // Update current counts
    this.updateCurrentCounts();
  }

  /**
   * Update current task counts
   */
  private updateCurrentCounts(): void {
    const currentStatuses = Array.from(this.currentStatuses.values());

    this.taskMetrics.runningTasks = currentStatuses.filter(s => s.status === 'running').length;
    this.taskMetrics.waitingTasks = currentStatuses.filter(s => s.status === 'waiting' || s.status === 'pending').length;
    this.taskMetrics.retryingTasks = currentStatuses.filter(s => s.status === 'retrying').length;
  }

  /**
   * Get current task status
   */
  public getTaskStatus(taskId: string): TaskStatusUpdate | null {
    return this.currentStatuses.get(taskId) || null;
  }

  /**
   * Get task status history
   */
  public getTaskHistory(taskId: string): TaskStatusUpdate[] {
    return this.statusHistory.get(taskId) || [];
  }

  /**
   * Get all current task statuses
   */
  public getAllCurrentStatuses(): TaskStatusUpdate[] {
    return Array.from(this.currentStatuses.values());
  }

  /**
   * Get task metrics
   */
  public getMetrics(): TaskMetrics {
    this.updateCurrentCounts();
    return { ...this.taskMetrics };
  }

  /**
   * Get tasks by status
   */
  public getTasksByStatus(status: TaskStatusUpdate['status']): TaskStatusUpdate[] {
    return Array.from(this.currentStatuses.values()).filter(s => s.status === status);
  }

  /**
   * Get tasks by agent
   */
  public getTasksByAgent(agentId: string): TaskStatusUpdate[] {
    return Array.from(this.currentStatuses.values()).filter(s => s.agentId === agentId);
  }

  /**
   * Report task progress
   */
  public async reportProgress(
    taskId: string,
    agentId: string,
    progress: number,
    message?: string,
    kanbanCardId?: string
  ): Promise<void> {
    await this.updateTaskStatus(taskId, agentId, 'running', {
      progress,
      message,
      kanbanCardId
    });
  }

  /**
   * ✅ MANDATORY UPGRADE: Report task completion with validation
   */
  public async reportCompletion(
    taskId: string,
    agentId: string,
    result: AgentResponse,
    kanbanCardId?: string,
    executionResult?: any // Additional execution metadata
  ): Promise<void> {
    // ✅ MANDATORY UPGRADE: Validate execution result before marking as completed
    const validation = this.validateExecutionResult(result, executionResult);

    if (!validation.isValid) {
      console.warn(`❌ TaskStatusService: Task ${taskId} completion rejected - validation failed:`, validation.validationErrors);

      // Log the validation failure
      logTaskExecutionFailed(agentId, {
        taskId,
        error: `Completion validation failed: ${validation.validationErrors.join(', ')}`,
        reason: 'validation_failed',
        agentState: 'error',
        executionTime: result.executionTime || 0
      });

      // Mark as failed instead of completed
      await this.updateTaskStatus(taskId, agentId, 'failed', {
        progress: 0,
        message: `Validation failed: ${validation.reason}`,
        metadata: {
          result,
          validation,
          tokensUsed: result.tokensUsed,
          executionTime: result.executionTime
        },
        kanbanCardId
      });
      return;
    }

    // ✅ MANDATORY UPGRADE: Log successful completion with validation details
    logTaskExecutionSucceeded(agentId, {
      taskId,
      generatedFiles: executionResult?.files?.map((f: any) => f.path) || [],
      functionsCreated: executionResult?.metadata?.functionsCreated || [],
      diffStats: executionResult?.metadata?.diffStats || { additions: 0, deletions: 0, modifications: 0 },
      outputPaths: executionResult?.files?.map((f: any) => f.path) || [],
      executionTime: result.executionTime || 0,
      tokensUsed: result.tokensUsed || 0
    });

    await this.updateTaskStatus(taskId, agentId, 'completed', {
      progress: 100,
      message: result.content,
      metadata: {
        result,
        validation,
        tokensUsed: result.tokensUsed,
        executionTime: result.executionTime
      },
      kanbanCardId
    });
  }

  /**
   * Report task error
   */
  public async reportError(
    taskId: string,
    agentId: string,
    error: string,
    willRetry: boolean = false,
    kanbanCardId?: string
  ): Promise<void> {
    const status = willRetry ? 'retrying' : 'failed';

    await this.updateTaskStatus(taskId, agentId, status, {
      message: error,
      metadata: {
        error,
        willRetry
      },
      kanbanCardId
    });
  }

  /**
   * Report task waiting for dependencies
   */
  public async reportWaiting(
    taskId: string,
    agentId: string,
    dependencies: string[],
    kanbanCardId?: string
  ): Promise<void> {
    await this.updateTaskStatus(taskId, agentId, 'waiting', {
      message: `Waiting for dependencies: ${dependencies.join(', ')}`,
      metadata: {
        dependencies
      },
      kanbanCardId
    });
  }

  // ✅ Task 84: Specific card status methods for real agent execution tracking

  /**
   * Mark card as in progress when agent execution begins
   */
  public async markCardAsInProgress(cardId: string, agentId?: string, taskId?: string): Promise<void> {
    console.log(`🔄 TaskStatusService: Marking card ${cardId} as IN PROGRESS`);

    try {
      // Move card to "In Development" column
      await KanbanTaskBridge.moveCardBasedOnTaskStatus(cardId, 'running', agentId || 'system');

      // Update progress to indicate start
      await KanbanTaskBridge.updateCardProgress(cardId, 10, agentId || 'system');

      // If taskId provided, update task status too
      if (taskId && agentId) {
        await this.updateTaskStatus(taskId, agentId, 'running', {
          progress: 10,
          message: 'Agent execution started',
          kanbanCardId: cardId
        });
      }

      console.log(`✅ TaskStatusService: Card ${cardId} marked as IN PROGRESS`);
    } catch (error) {
      console.error(`❌ TaskStatusService: Failed to mark card ${cardId} as in progress:`, error);
    }
  }

  /**
   * ✅ MANDATORY UPGRADE: Mark card as done with execution validation
   */
  public async markCardAsDone(
    cardId: string,
    agentId?: string,
    taskId?: string,
    executionResult?: any
  ): Promise<void> {
    console.log(`✅ TaskStatusService: Attempting to mark card ${cardId} as DONE`);

    try {
      // ✅ MANDATORY UPGRADE: Validate execution result before marking as done
      if (executionResult) {
        const validation = this.validateExecutionResult(executionResult, executionResult);

        if (!validation.isValid) {
          console.warn(`❌ TaskStatusService: Card ${cardId} completion rejected - validation failed:`, validation.validationErrors);

          // Log the silent execution failure
          logSilentExecutionFailure({
            cardId,
            taskId: taskId || 'unknown',
            agentId: agentId || 'unknown',
            reason: `Card completion validation failed: ${validation.reason}`,
            columnTransition: { from: 'in-progress', to: 'done' }
          });

          // Log card marked done without valid output
          logCardMarkedDoneWithoutOutput({
            cardId,
            taskId: taskId || 'unknown',
            agentId: agentId || 'unknown',
            columnId: 'done',
            timestamp: Date.now()
          });

          // Don't mark as done - leave in current state
          console.error(`❌ TaskStatusService: Card ${cardId} NOT marked as done due to validation failure`);
          return;
        }
      } else {
        // No execution result provided - this is a potential silent failure
        console.warn(`⚠️ TaskStatusService: Card ${cardId} marked as done without execution result validation`);

        logSilentExecutionFailure({
          cardId,
          taskId: taskId || 'unknown',
          agentId: agentId || 'unknown',
          reason: 'Card marked as done without providing execution result for validation'
        });
      }

      // Move card to "Done" column
      await KanbanTaskBridge.moveCardBasedOnTaskStatus(cardId, 'completed', agentId || 'system');

      // Update progress to 100%
      await KanbanTaskBridge.updateCardProgress(cardId, 100, agentId || 'system');

      // If taskId provided, update task status too
      if (taskId && agentId) {
        await this.updateTaskStatus(taskId, agentId, 'completed', {
          progress: 100,
          message: 'Agent execution completed successfully',
          kanbanCardId: cardId
        });
      }

      console.log(`✅ TaskStatusService: Card ${cardId} marked as DONE`);
    } catch (error) {
      console.error(`❌ TaskStatusService: Failed to mark card ${cardId} as done:`, error);
    }
  }

  /**
   * Mark card as failed when agent execution fails
   */
  public async markCardAsFailed(cardId: string, errorMessage: string, agentId?: string, taskId?: string): Promise<void> {
    console.log(`❌ TaskStatusService: Marking card ${cardId} as FAILED: ${errorMessage}`);

    try {
      // Move card to "In Review" column for error analysis
      await KanbanTaskBridge.moveCardBasedOnTaskStatus(cardId, 'failed', agentId || 'system');

      // Keep progress as is (don't reset to 0)

      // If taskId provided, update task status too
      if (taskId && agentId) {
        await this.updateTaskStatus(taskId, agentId, 'failed', {
          message: `Agent execution failed: ${errorMessage}`,
          metadata: {
            error: errorMessage,
            failureTime: Date.now()
          },
          kanbanCardId: cardId
        });
      }

      console.log(`❌ TaskStatusService: Card ${cardId} marked as FAILED`);
    } catch (error) {
      console.error(`❌ TaskStatusService: Failed to mark card ${cardId} as failed:`, error);
    }
  }

  /**
   * ✅ MANDATORY UPGRADE: Validate execution result before marking task as completed
   */
  private validateExecutionResult(result: AgentResponse, executionResult?: any): ExecutionValidationResult {
    const validationErrors: string[] = [];
    let hasOutput = false;
    let filesCreated = 0;
    let outputSize = 0;

    // Check if the agent response indicates success
    if (!result.success) {
      validationErrors.push('Agent response indicates failure');
    }

    // Check if there's meaningful content in the response
    if (!result.content || result.content.trim().length < 10) {
      validationErrors.push('Agent response has no meaningful content');
    } else {
      hasOutput = true;
      outputSize += result.content.length;
    }

    // Check for error indicators in the response
    if (result.error) {
      validationErrors.push(`Agent response contains error: ${result.error}`);
    }

    // Validate execution result if provided
    if (executionResult) {
      // Check for created files
      if (executionResult.files && Array.isArray(executionResult.files)) {
        filesCreated = executionResult.files.length;

        // Validate file content
        executionResult.files.forEach((file: any, index: number) => {
          if (!file.content || file.content.trim().length === 0) {
            validationErrors.push(`File ${index + 1} (${file.path || 'unknown'}) has no content`);
          } else {
            hasOutput = true;
            outputSize += file.content.length;

            // Check for placeholder content
            const content = file.content.toLowerCase();
            const placeholderPatterns = ['todo', 'placeholder', 'not implemented', 'coming soon'];
            if (placeholderPatterns.some(pattern => content.includes(pattern) && content.length < 100)) {
              validationErrors.push(`File ${file.path || 'unknown'} contains placeholder content`);
            }
          }
        });
      }

      // Check execution metadata
      if (executionResult.metadata) {
        if (executionResult.metadata.errors && executionResult.metadata.errors > 0) {
          validationErrors.push(`Execution had ${executionResult.metadata.errors} errors`);
        }
      }

      // Check if execution was marked as failed
      if (executionResult.success === false) {
        validationErrors.push('Execution result indicates failure');
      }
    }

    // Determine overall validation result
    const isValid = validationErrors.length === 0 && (hasOutput || filesCreated > 0);
    const reason = validationErrors.length > 0
      ? validationErrors[0]
      : !hasOutput && filesCreated === 0
        ? 'No meaningful output or files generated'
        : undefined;

    return {
      isValid,
      hasOutput,
      filesCreated,
      outputSize,
      validationErrors,
      reason
    };
  }

  /**
   * Get status summary for dashboard
   */
  public getStatusSummary(): {
    metrics: TaskMetrics;
    recentUpdates: TaskStatusUpdate[];
    activeAgents: string[];
    criticalTasks: TaskStatusUpdate[];
  } {
    const metrics = this.getMetrics();
    const allStatuses = this.getAllCurrentStatuses();

    // Get recent updates (last 10)
    const allUpdates = Array.from(this.statusHistory.values()).flat();
    const recentUpdates = allUpdates
      .sort((a, b) => b.timestamp - a.timestamp)
      .slice(0, 10);

    // Get active agents
    const activeAgents = [...new Set(allStatuses.map(s => s.agentId))];

    // Get critical tasks (failed or retrying)
    const criticalTasks = allStatuses.filter(s => s.status === 'failed' || s.status === 'retrying');

    return {
      metrics,
      recentUpdates,
      activeAgents,
      criticalTasks
    };
  }

  /**
   * Clear status data
   */
  public clearStatus(): void {
    this.statusHistory.clear();
    this.currentStatuses.clear();
    this.taskStartTimes.clear();
    this.taskMetrics = {
      totalTasks: 0,
      completedTasks: 0,
      failedTasks: 0,
      runningTasks: 0,
      waitingTasks: 0,
      retryingTasks: 0,
      averageExecutionTime: 0,
      successRate: 0,
      totalExecutionTime: 0
    };
  }

  /**
   * Export status data for analysis
   */
  public exportStatusData(): {
    currentStatuses: Record<string, TaskStatusUpdate>;
    statusHistory: Record<string, TaskStatusUpdate[]>;
    metrics: TaskMetrics;
  } {
    return {
      currentStatuses: Object.fromEntries(this.currentStatuses),
      statusHistory: Object.fromEntries(this.statusHistory),
      metrics: this.getMetrics()
    };
  }
}

// Export singleton instance
export const taskStatusService = TaskStatusService.getInstance();
