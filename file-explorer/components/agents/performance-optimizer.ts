// file-explorer/components/agents/performance-optimizer.ts
// ✅ TASK 7.2: Performance Optimization - Optimize real-time updates and minimize re-renders

import React from 'react';

// ✅ Performance Configuration Interface
export interface PerformanceConfig {
  updateFrequency: number; // milliseconds between updates
  batchSize: number; // number of updates to batch together
  debounceDelay: number; // debounce delay for rapid updates
  maxConcurrentUpdates: number; // maximum concurrent update operations
  enableVirtualization: boolean; // enable virtualization for large lists
  enableMemoization: boolean; // enable React.memo and useMemo optimizations
  enableLazyLoading: boolean; // enable lazy loading for components
  cacheTimeout: number; // cache timeout in milliseconds
}

// ✅ Default performance configuration
const defaultConfig: PerformanceConfig = {
  updateFrequency: 1000, // 1 second
  batchSize: 10,
  debounceDelay: 300,
  maxConcurrentUpdates: 5,
  enableVirtualization: true,
  enableMemoization: true,
  enableLazyLoading: true,
  cacheTimeout: 30000 // 30 seconds
};

// ✅ Update Batch Interface
interface UpdateBatch {
  id: string;
  updates: any[];
  timestamp: number;
  priority: 'low' | 'medium' | 'high';
}

// ✅ Performance Metrics Interface
interface PerformanceMetrics {
  updateCount: number;
  averageUpdateTime: number;
  batchedUpdates: number;
  skippedUpdates: number;
  memoryUsage: number;
  renderCount: number;
  lastOptimization: number;
}

// ✅ PerformanceOptimizer Class
export class PerformanceOptimizer {
  private static instance: PerformanceOptimizer;
  private config: PerformanceConfig;
  private updateQueue: UpdateBatch[] = [];
  private isProcessing = false;
  private metrics: PerformanceMetrics;
  private cache = new Map<string, { data: any; timestamp: number }>();
  private debounceTimers = new Map<string, NodeJS.Timeout>();
  private activeUpdates = 0;

  private constructor() {
    this.config = { ...defaultConfig };
    this.metrics = {
      updateCount: 0,
      averageUpdateTime: 0,
      batchedUpdates: 0,
      skippedUpdates: 0,
      memoryUsage: 0,
      renderCount: 0,
      lastOptimization: Date.now()
    };

    // Start processing queue
    this.startProcessing();
    
    // Monitor performance
    this.startPerformanceMonitoring();
  }

  public static getInstance(): PerformanceOptimizer {
    if (!PerformanceOptimizer.instance) {
      PerformanceOptimizer.instance = new PerformanceOptimizer();
    }
    return PerformanceOptimizer.instance;
  }

  // ✅ Update configuration
  public updateConfig(newConfig: Partial<PerformanceConfig>): void {
    this.config = { ...this.config, ...newConfig };
    console.log('PerformanceOptimizer: Configuration updated:', this.config);
  }

  // ✅ Get current configuration
  public getConfig(): PerformanceConfig {
    return { ...this.config };
  }

  // ✅ Queue update with batching and debouncing
  public queueUpdate(
    id: string, 
    updateData: any, 
    priority: 'low' | 'medium' | 'high' = 'medium',
    immediate = false
  ): void {
    // Skip if too many concurrent updates
    if (this.activeUpdates >= this.config.maxConcurrentUpdates && !immediate) {
      this.metrics.skippedUpdates++;
      console.warn('PerformanceOptimizer: Update skipped due to high load');
      return;
    }

    // Clear existing debounce timer for this ID
    if (this.debounceTimers.has(id)) {
      clearTimeout(this.debounceTimers.get(id)!);
    }

    // Create update batch
    const batch: UpdateBatch = {
      id,
      updates: [updateData],
      timestamp: Date.now(),
      priority
    };

    if (immediate || priority === 'high') {
      // Process immediately for high priority updates
      this.processUpdateBatch(batch);
    } else {
      // Debounce for other updates
      const timer = setTimeout(() => {
        this.addToBatch(batch);
        this.debounceTimers.delete(id);
      }, this.config.debounceDelay);

      this.debounceTimers.set(id, timer);
    }
  }

  // ✅ Add to batch queue
  private addToBatch(batch: UpdateBatch): void {
    // Check if we can merge with existing batch
    const existingBatchIndex = this.updateQueue.findIndex(
      b => b.id === batch.id && b.priority === batch.priority
    );

    if (existingBatchIndex !== -1) {
      // Merge with existing batch
      this.updateQueue[existingBatchIndex].updates.push(...batch.updates);
      this.updateQueue[existingBatchIndex].timestamp = batch.timestamp;
    } else {
      // Add new batch
      this.updateQueue.push(batch);
    }

    // Sort by priority and timestamp
    this.updateQueue.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
      return priorityDiff !== 0 ? priorityDiff : a.timestamp - b.timestamp;
    });
  }

  // ✅ Start processing queue
  private startProcessing(): void {
    setInterval(() => {
      if (!this.isProcessing && this.updateQueue.length > 0) {
        this.processBatches();
      }
    }, this.config.updateFrequency);
  }

  // ✅ Process batches
  private async processBatches(): Promise<void> {
    if (this.isProcessing) return;

    this.isProcessing = true;
    const startTime = Date.now();

    try {
      const batchesToProcess = this.updateQueue.splice(0, this.config.batchSize);
      
      for (const batch of batchesToProcess) {
        await this.processUpdateBatch(batch);
      }

      this.metrics.batchedUpdates += batchesToProcess.length;
      
    } catch (error) {
      console.error('PerformanceOptimizer: Error processing batches:', error);
    } finally {
      this.isProcessing = false;
      
      // Update metrics
      const processingTime = Date.now() - startTime;
      this.metrics.averageUpdateTime = 
        (this.metrics.averageUpdateTime + processingTime) / 2;
    }
  }

  // ✅ Process individual update batch
  private async processUpdateBatch(batch: UpdateBatch): Promise<void> {
    this.activeUpdates++;
    
    try {
      // Simulate update processing
      await new Promise(resolve => setTimeout(resolve, 10));
      
      this.metrics.updateCount++;
      console.log(`PerformanceOptimizer: Processed batch ${batch.id} with ${batch.updates.length} updates`);
      
    } catch (error) {
      console.error(`PerformanceOptimizer: Error processing batch ${batch.id}:`, error);
    } finally {
      this.activeUpdates--;
    }
  }

  // ✅ Cache management
  public setCache(key: string, data: any): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });

    // Clean up expired cache entries
    this.cleanupCache();
  }

  public getCache(key: string): any | null {
    const cached = this.cache.get(key);
    
    if (!cached) return null;
    
    // Check if cache is expired
    if (Date.now() - cached.timestamp > this.config.cacheTimeout) {
      this.cache.delete(key);
      return null;
    }
    
    return cached.data;
  }

  public clearCache(): void {
    this.cache.clear();
    console.log('PerformanceOptimizer: Cache cleared');
  }

  private cleanupCache(): void {
    const now = Date.now();
    for (const [key, cached] of this.cache.entries()) {
      if (now - cached.timestamp > this.config.cacheTimeout) {
        this.cache.delete(key);
      }
    }
  }

  // ✅ Performance monitoring
  private startPerformanceMonitoring(): void {
    setInterval(() => {
      this.updatePerformanceMetrics();
      this.optimizeIfNeeded();
    }, 10000); // Every 10 seconds
  }

  private updatePerformanceMetrics(): void {
    // Update memory usage (approximate)
    this.metrics.memoryUsage = this.cache.size + this.updateQueue.length;
    
    // Log metrics periodically
    if (this.metrics.updateCount % 100 === 0 && this.metrics.updateCount > 0) {
      console.log('PerformanceOptimizer: Metrics:', this.metrics);
    }
  }

  private optimizeIfNeeded(): void {
    const now = Date.now();
    const timeSinceLastOptimization = now - this.metrics.lastOptimization;
    
    // Optimize every 5 minutes
    if (timeSinceLastOptimization > 300000) {
      this.performOptimization();
      this.metrics.lastOptimization = now;
    }
  }

  private performOptimization(): void {
    console.log('PerformanceOptimizer: Performing optimization...');
    
    // Clean up cache
    this.cleanupCache();
    
    // Adjust update frequency based on load
    if (this.metrics.skippedUpdates > 10) {
      this.config.updateFrequency = Math.min(this.config.updateFrequency * 1.2, 5000);
      console.log(`PerformanceOptimizer: Increased update frequency to ${this.config.updateFrequency}ms`);
    } else if (this.metrics.skippedUpdates === 0 && this.config.updateFrequency > 500) {
      this.config.updateFrequency = Math.max(this.config.updateFrequency * 0.9, 500);
      console.log(`PerformanceOptimizer: Decreased update frequency to ${this.config.updateFrequency}ms`);
    }
    
    // Reset skip counter
    this.metrics.skippedUpdates = 0;
  }

  // ✅ Get performance metrics
  public getMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }

  // ✅ Get queue status
  public getQueueStatus(): {
    queueLength: number;
    activeUpdates: number;
    isProcessing: boolean;
    cacheSize: number;
  } {
    return {
      queueLength: this.updateQueue.length,
      activeUpdates: this.activeUpdates,
      isProcessing: this.isProcessing,
      cacheSize: this.cache.size
    };
  }

  // ✅ Force process queue
  public forceProcessQueue(): void {
    if (!this.isProcessing) {
      this.processBatches();
    }
  }

  // ✅ Reset metrics
  public resetMetrics(): void {
    this.metrics = {
      updateCount: 0,
      averageUpdateTime: 0,
      batchedUpdates: 0,
      skippedUpdates: 0,
      memoryUsage: 0,
      renderCount: 0,
      lastOptimization: Date.now()
    };
    console.log('PerformanceOptimizer: Metrics reset');
  }
}

// ✅ React Hook for Performance Optimization
export const usePerformanceOptimizer = () => {
  const optimizer = PerformanceOptimizer.getInstance();

  // ✅ CRITICAL FIX: Memoize the returned object to prevent infinite re-renders
  return React.useMemo(() => ({
    queueUpdate: optimizer.queueUpdate.bind(optimizer),
    setCache: optimizer.setCache.bind(optimizer),
    getCache: optimizer.getCache.bind(optimizer),
    clearCache: optimizer.clearCache.bind(optimizer),
    getMetrics: optimizer.getMetrics.bind(optimizer),
    getQueueStatus: optimizer.getQueueStatus.bind(optimizer),
    updateConfig: optimizer.updateConfig.bind(optimizer),
    getConfig: optimizer.getConfig.bind(optimizer),
    forceProcessQueue: optimizer.forceProcessQueue.bind(optimizer),
    resetMetrics: optimizer.resetMetrics.bind(optimizer)
  }), [optimizer]);
};

// ✅ Export singleton instance
export const performanceOptimizer = PerformanceOptimizer.getInstance();
