// file-explorer/components/agents/agent-performance-optimizer.ts
// ✅ PERFORMANCE FIX: Agent System Performance Optimizer

import { SettingsManager } from '../settings/settings-manager';
import { getGlobalSettingsManager } from '../settings/global-settings';
import { agentWorkTracker } from './agent-work-tracker';
import { updateGlobalConcurrencyLimit } from '../../lib/utils/concurrency-manager';

export interface PerformanceMetrics {
  averageResponseTime: number;
  concurrentTasks: number;
  queuedTasks: number;
  failureRate: number;
  throughput: number; // tasks per minute
  systemLoad: number; // percentage
}

export interface PerformanceOptimization {
  recommendedConcurrency: number;
  recommendedTimeout: number;
  healthThresholdAdjustment: number;
  reasoning: string;
}

/**
 * ✅ PERFORMANCE FIX: Agent Performance Optimizer
 * Dynamically adjusts system settings for optimal performance
 */
export class AgentPerformanceOptimizer {
  private static instance: AgentPerformanceOptimizer;
  private static isInitialized = false;
  private settingsManager: SettingsManager;
  private performanceHistory: PerformanceMetrics[] = [];
  private optimizationInterval: NodeJS.Timeout | null = null;

  private constructor() {
    this.settingsManager = getGlobalSettingsManager(); // ✅ CRITICAL FIX: Use singleton instead of new instance
    // ✅ PERFORMANCE FIX: Only start monitoring once globally
    if (!AgentPerformanceOptimizer.isInitialized) {
      this.startPerformanceMonitoring();
      AgentPerformanceOptimizer.isInitialized = true;
    }
  }

  public static getInstance(): AgentPerformanceOptimizer {
    if (!AgentPerformanceOptimizer.instance) {
      AgentPerformanceOptimizer.instance = new AgentPerformanceOptimizer();
    }
    return AgentPerformanceOptimizer.instance;
  }

  /**
   * ✅ Start conservative performance monitoring and optimization
   */
  private startPerformanceMonitoring(): void {
    // ✅ PERFORMANCE FIX: Disable in development to prevent excessive optimization
    if (process.env.NODE_ENV === 'development') {
      console.log('🔧 AgentPerformanceOptimizer: Disabled in development mode');
      return;
    }

    // ✅ PERFORMANCE FIX: Monitor performance every 5 minutes instead of 30 seconds
    this.optimizationInterval = setInterval(() => {
      this.analyzeAndOptimize();
    }, 300000); // 5 minutes = 300,000ms

    console.log('🚀 AgentPerformanceOptimizer: Started conservative performance monitoring (5min intervals)');
  }

  /**
   * ✅ Analyze current performance and apply conservative optimizations
   */
  private async analyzeAndOptimize(): Promise<void> {
    try {
      const metrics = this.collectPerformanceMetrics();
      this.performanceHistory.push(metrics);

      // Keep only last 12 measurements (1 hour of history at 5min intervals)
      if (this.performanceHistory.length > 12) {
        this.performanceHistory.shift();
      }

      // ✅ PERFORMANCE FIX: Only optimize if we have sufficient data and significant issues
      if (this.performanceHistory.length < 3) {
        console.log('🔍 AgentPerformanceOptimizer: Insufficient data for optimization, collecting metrics...');
        return;
      }

      const optimization = this.calculateOptimizations(metrics);

      // ✅ PERFORMANCE FIX: Only apply optimizations if they represent significant changes
      if (this.shouldApplyOptimization(optimization)) {
        await this.applyOptimizations(optimization);
      } else {
        console.log('🔍 AgentPerformanceOptimizer: No significant optimizations needed');
      }

    } catch (error) {
      console.error('❌ AgentPerformanceOptimizer: Error during optimization:', error);
    }
  }

  /**
   * ✅ Collect current performance metrics
   */
  private collectPerformanceMetrics(): PerformanceMetrics {
    const workloads = agentWorkTracker.getAllAgentWorkloads();
    const loadMetrics = agentWorkTracker.getLoadBalancingMetrics();

    const totalTasks = workloads.reduce((sum, w) => sum + w.totalTasksCompleted + w.totalTasksFailed, 0);
    const totalFailed = workloads.reduce((sum, w) => sum + w.totalTasksFailed, 0);
    const avgDuration = workloads.reduce((sum, w) => sum + w.averageTaskDuration, 0) / workloads.length || 0;

    return {
      averageResponseTime: avgDuration,
      concurrentTasks: loadMetrics.totalActiveTasks,
      queuedTasks: loadMetrics.queuedTasks,
      failureRate: totalTasks > 0 ? (totalFailed / totalTasks) * 100 : 0,
      throughput: this.calculateThroughput(),
      systemLoad: loadMetrics.averageLoad
    };
  }

  /**
   * ✅ Calculate system throughput (tasks per minute)
   */
  private calculateThroughput(): number {
    if (this.performanceHistory.length < 2) return 0;

    const recent = this.performanceHistory.slice(-5); // Last 5 measurements
    const totalTasks = recent.reduce((sum, m) => sum + m.concurrentTasks, 0);
    const timeSpan = recent.length * 0.5; // 30-second intervals = 0.5 minutes

    return timeSpan > 0 ? totalTasks / timeSpan : 0;
  }

  /**
   * ✅ Calculate conservative performance optimizations
   */
  private calculateOptimizations(metrics: PerformanceMetrics): PerformanceOptimization {
    const settings = this.settingsManager.getSettings();
    let recommendedConcurrency = settings.system.maxConcurrentTasks;
    let recommendedTimeout = settings.system.defaultTimeout;
    let healthThresholdAdjustment = 0;
    let reasoning = '';

    // ✅ PERFORMANCE FIX: More conservative optimization thresholds
    // Only optimize concurrency for significant load issues
    if (metrics.systemLoad < 30 && metrics.queuedTasks > 10) {
      // Very low load but very high queue - increase concurrency
      recommendedConcurrency = Math.min(8, recommendedConcurrency + 1);
      reasoning += 'Increased concurrency due to very low load and high queue. ';
    } else if (metrics.systemLoad > 95 || metrics.failureRate > 30) {
      // Very high load or failure rate - decrease concurrency
      recommendedConcurrency = Math.max(3, recommendedConcurrency - 1);
      reasoning += 'Decreased concurrency due to very high load or failure rate. ';
    }

    // ✅ PERFORMANCE FIX: Much more conservative timeout adjustments
    // Only adjust timeouts for significant response time issues
    if (metrics.averageResponseTime > recommendedTimeout * 0.9) {
      // Response times very close to timeout - increase timeout
      recommendedTimeout = Math.min(25000, recommendedTimeout + 3000);
      reasoning += 'Increased timeout due to very slow response times. ';
    } else if (metrics.averageResponseTime > 0 && metrics.averageResponseTime < recommendedTimeout * 0.1) {
      // Very fast response times - can reduce timeout slightly
      recommendedTimeout = Math.max(12000, recommendedTimeout - 1000);
      reasoning += 'Decreased timeout due to very fast response times. ';
    }

    // ✅ PERFORMANCE FIX: More conservative health threshold adjustments
    if (metrics.failureRate > 25) {
      healthThresholdAdjustment = -3; // Lower threshold to keep agents available
      reasoning += 'Lowered health threshold due to high failure rate. ';
    } else if (metrics.failureRate < 2) {
      healthThresholdAdjustment = 2; // Raise threshold for better quality
      reasoning += 'Raised health threshold due to very low failure rate. ';
    }

    return {
      recommendedConcurrency,
      recommendedTimeout,
      healthThresholdAdjustment,
      reasoning: reasoning || 'No optimizations needed.'
    };
  }

  /**
   * ✅ Check if optimization should be applied (prevent excessive changes)
   */
  private shouldApplyOptimization(optimization: PerformanceOptimization): boolean {
    const settings = this.settingsManager.getSettings();

    // Check if changes are significant enough to warrant adjustment
    const concurrencyChange = Math.abs(optimization.recommendedConcurrency - settings.system.maxConcurrentTasks);
    const timeoutChange = Math.abs(optimization.recommendedTimeout - settings.system.defaultTimeout);

    // ✅ PERFORMANCE FIX: Only apply if changes are significant
    const significantConcurrencyChange = concurrencyChange >= 2; // At least 2 task difference
    const significantTimeoutChange = timeoutChange >= 3000; // At least 3 second difference

    return significantConcurrencyChange || significantTimeoutChange;
  }

  /**
   * ✅ Apply calculated optimizations
   */
  private async applyOptimizations(optimization: PerformanceOptimization): Promise<void> {
    const settings = this.settingsManager.getSettings();
    let changesApplied = false;

    // Apply concurrency changes
    if (optimization.recommendedConcurrency !== settings.system.maxConcurrentTasks) {
      this.settingsManager.updateSystemSettings({
        maxConcurrentTasks: optimization.recommendedConcurrency
      });
      updateGlobalConcurrencyLimit(optimization.recommendedConcurrency);
      changesApplied = true;
    }

    // Apply timeout changes
    if (optimization.recommendedTimeout !== settings.system.defaultTimeout) {
      this.settingsManager.updateSystemSettings({
        defaultTimeout: optimization.recommendedTimeout
      });
      changesApplied = true;
    }

    if (changesApplied) {
      console.log(`🔧 AgentPerformanceOptimizer: Applied significant optimizations`, {
        concurrency: optimization.recommendedConcurrency,
        timeout: optimization.recommendedTimeout,
        reasoning: optimization.reasoning
      });
    }
  }

  /**
   * ✅ Get current performance metrics
   */
  public getCurrentMetrics(): PerformanceMetrics | null {
    return this.performanceHistory.length > 0 
      ? this.performanceHistory[this.performanceHistory.length - 1] 
      : null;
  }

  /**
   * ✅ Get performance history
   */
  public getPerformanceHistory(): PerformanceMetrics[] {
    return [...this.performanceHistory];
  }

  /**
   * ✅ Force immediate optimization (conservative)
   */
  public async forceOptimization(): Promise<void> {
    // ✅ PERFORMANCE FIX: Skip forced optimization in development
    if (process.env.NODE_ENV === 'development') {
      console.log('🔧 AgentPerformanceOptimizer: Skipping forced optimization in development mode');
      return;
    }

    console.log('🔧 AgentPerformanceOptimizer: Performing initial optimization check');
    await this.analyzeAndOptimize();
  }

  /**
   * ✅ Stop performance monitoring
   */
  public stopMonitoring(): void {
    if (this.optimizationInterval) {
      clearInterval(this.optimizationInterval);
      this.optimizationInterval = null;
      console.log('🛑 AgentPerformanceOptimizer: Stopped performance monitoring');
    }
  }
}

// Export singleton instance
export const agentPerformanceOptimizer = AgentPerformanceOptimizer.getInstance();
