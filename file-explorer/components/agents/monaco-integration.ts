// file-explorer/components/agents/monaco-integration.ts
// ✅ TASK 4.2: MonacoIntegration Service - Live code generation streaming to Monaco editor

import { AgentUIBridge, ExecutionUpdate } from './agent-ui-bridge';

// ✅ Monaco Integration Interface
export interface MonacoIntegrationConfig {
  enableLiveStreaming: boolean;
  highlightDuration: number;
  streamingDelay: number;
  maxStreamingLines: number;
}

// ✅ Live Coding Update Interface
export interface LiveCodingUpdate {
  filePath: string;
  content: string;
  progress: number;
  lineStart?: number;
  lineEnd?: number;
  operation: 'create' | 'modify' | 'highlight' | 'complete';
}

// ✅ Monaco Editor Reference Interface
export interface MonacoEditorRef {
  setValue: (value: string) => void;
  getValue: () => string;
  setPosition: (line: number, column: number) => void;
  revealLine: (line: number) => void;
  deltaDecorations: (oldDecorations: string[], newDecorations: any[]) => string[];
  getModel: () => any;
  focus: () => void;
}

// ✅ MonacoIntegration Service Class
export class MonacoIntegration {
  private static instance: MonacoIntegration;
  private editorRef: MonacoEditorRef | null = null;
  private config: MonacoIntegrationConfig;
  private currentDecorations: string[] = [];
  private streamingTimeout: NodeJS.Timeout | null = null;
  private isStreaming = false;
  private agentUIBridge: AgentUIBridge;

  private constructor() {
    this.config = {
      enableLiveStreaming: true,
      highlightDuration: 3000, // 3 seconds
      streamingDelay: 100, // 100ms between character updates
      maxStreamingLines: 50
    };
    
    this.agentUIBridge = AgentUIBridge.getInstance();
    this.initializeExecutionListener();
  }

  public static getInstance(): MonacoIntegration {
    if (!MonacoIntegration.instance) {
      MonacoIntegration.instance = new MonacoIntegration();
    }
    return MonacoIntegration.instance;
  }

  // ✅ Register Monaco Editor Reference
  public registerEditor(editorRef: MonacoEditorRef): void {
    this.editorRef = editorRef;
    console.log('MonacoIntegration: Editor registered');
  }

  // ✅ Unregister Monaco Editor Reference
  public unregisterEditor(): void {
    this.editorRef = null;
    this.clearHighlights();
    console.log('MonacoIntegration: Editor unregistered');
  }

  // ✅ Stream Code Generation to Monaco Editor
  public async streamCodeGeneration(filePath: string, content: string, progress: number): Promise<void> {
    if (!this.editorRef || !this.config.enableLiveStreaming) {
      console.log('MonacoIntegration: Editor not available or streaming disabled');
      return;
    }

    console.log(`MonacoIntegration: Streaming code generation for ${filePath} (${progress}% complete)`);

    try {
      // Clear previous streaming
      if (this.streamingTimeout) {
        clearTimeout(this.streamingTimeout);
      }

      this.isStreaming = true;

      // Get current editor content
      const currentContent = this.editorRef.getValue();
      
      // If this is a new file or complete replacement
      if (progress === 0 || currentContent.length === 0) {
        await this.streamContentCharacterByCharacter(content);
      } else {
        // Append or modify existing content
        await this.streamContentUpdate(currentContent, content, progress);
      }

      // Highlight the work area
      this.highlightWorkArea(filePath, content);

    } catch (error) {
      console.error('MonacoIntegration: Error streaming code generation:', error);
    } finally {
      this.isStreaming = false;
    }
  }

  // ✅ Highlight Work Area in Monaco Editor
  public highlightWorkArea(filePath: string, content: string): void {
    if (!this.editorRef) {
      console.log('MonacoIntegration: Editor not available for highlighting');
      return;
    }

    try {
      const model = this.editorRef.getModel();
      if (!model) return;

      // Clear previous decorations
      this.clearHighlights();

      // Calculate lines to highlight based on content
      const lines = content.split('\n');
      const totalLines = model.getLineCount();
      const startLine = Math.max(1, totalLines - lines.length);
      const endLine = totalLines;

      // Create highlight decoration
      const decorations = [{
        range: {
          startLineNumber: startLine,
          startColumn: 1,
          endLineNumber: endLine,
          endColumn: model.getLineMaxColumn(endLine)
        },
        options: {
          className: 'monaco-live-coding-highlight',
          isWholeLine: true,
          backgroundColor: 'rgba(0, 122, 255, 0.1)',
          marginClassName: 'monaco-live-coding-margin'
        }
      }];

      // Apply decorations
      this.currentDecorations = this.editorRef.deltaDecorations([], decorations);

      // Reveal the highlighted area
      this.editorRef.revealLine(startLine);

      console.log(`MonacoIntegration: Highlighted lines ${startLine}-${endLine} for ${filePath}`);

      // Auto-clear highlights after duration
      setTimeout(() => {
        this.clearHighlights();
      }, this.config.highlightDuration);

    } catch (error) {
      console.error('MonacoIntegration: Error highlighting work area:', error);
    }
  }

  // ✅ Clear Highlights
  public clearHighlights(): void {
    if (this.editorRef && this.currentDecorations.length > 0) {
      this.editorRef.deltaDecorations(this.currentDecorations, []);
      this.currentDecorations = [];
    }
  }

  // ✅ Update Configuration
  public updateConfig(newConfig: Partial<MonacoIntegrationConfig>): void {
    this.config = { ...this.config, ...newConfig };
    console.log('MonacoIntegration: Configuration updated:', this.config);
  }

  // ✅ Get Current Configuration
  public getConfig(): MonacoIntegrationConfig {
    return { ...this.config };
  }

  // ✅ Check if Currently Streaming
  public isCurrentlyStreaming(): boolean {
    return this.isStreaming;
  }

  // ✅ Private: Initialize Execution Listener
  private initializeExecutionListener(): void {
    this.agentUIBridge.subscribeToExecutionUpdates((update: ExecutionUpdate) => {
      if (update.type === 'code_generation' && update.data.filePath && update.data.content) {
        this.streamCodeGeneration(
          update.data.filePath,
          update.data.content,
          update.data.progress || 0
        );
      }
    });
  }

  // ✅ Private: Stream Content Character by Character
  private async streamContentCharacterByCharacter(content: string): Promise<void> {
    if (!this.editorRef) return;

    const lines = content.split('\n');
    let currentContent = '';

    for (let lineIndex = 0; lineIndex < lines.length; lineIndex++) {
      const line = lines[lineIndex];
      
      // Add line character by character
      for (let charIndex = 0; charIndex < line.length; charIndex++) {
        if (!this.isStreaming) break; // Stop if streaming was cancelled
        
        currentContent += line[charIndex];
        this.editorRef.setValue(currentContent);
        
        // Small delay for visual effect
        await new Promise(resolve => setTimeout(resolve, this.config.streamingDelay));
      }
      
      // Add newline (except for last line)
      if (lineIndex < lines.length - 1) {
        currentContent += '\n';
        this.editorRef.setValue(currentContent);
      }
      
      // Longer pause at end of line
      await new Promise(resolve => setTimeout(resolve, this.config.streamingDelay * 2));
    }
  }

  // ✅ Private: Stream Content Update
  private async streamContentUpdate(currentContent: string, newContent: string, progress: number): Promise<void> {
    if (!this.editorRef) return;

    // For updates, we'll append the new content
    const updatedContent = currentContent + '\n\n' + newContent;
    
    // Stream the new content
    await this.streamContentCharacterByCharacter(newContent);
    
    // Set the complete updated content
    this.editorRef.setValue(updatedContent);
  }

  // ✅ Public: Force Stop Streaming
  public stopStreaming(): void {
    this.isStreaming = false;
    if (this.streamingTimeout) {
      clearTimeout(this.streamingTimeout);
      this.streamingTimeout = null;
    }
    console.log('MonacoIntegration: Streaming stopped');
  }

  // ✅ Public: Get Streaming Status
  public getStreamingStatus(): {
    isStreaming: boolean;
    hasEditor: boolean;
    config: MonacoIntegrationConfig;
  } {
    return {
      isStreaming: this.isStreaming,
      hasEditor: this.editorRef !== null,
      config: this.config
    };
  }
}

// ✅ Export singleton instance
export const monacoIntegration = MonacoIntegration.getInstance();

// ✅ React Hook for Monaco Integration
export const useMonacoIntegration = () => {
  const integration = MonacoIntegration.getInstance();
  
  return {
    registerEditor: integration.registerEditor.bind(integration),
    unregisterEditor: integration.unregisterEditor.bind(integration),
    streamCodeGeneration: integration.streamCodeGeneration.bind(integration),
    highlightWorkArea: integration.highlightWorkArea.bind(integration),
    clearHighlights: integration.clearHighlights.bind(integration),
    updateConfig: integration.updateConfig.bind(integration),
    getConfig: integration.getConfig.bind(integration),
    isStreaming: integration.isCurrentlyStreaming.bind(integration),
    stopStreaming: integration.stopStreaming.bind(integration),
    getStatus: integration.getStreamingStatus.bind(integration)
  };
};

// ✅ CSS Styles for Monaco Live Coding (to be added to global styles)
export const monacoLiveCodingStyles = `
.monaco-live-coding-highlight {
  background-color: rgba(0, 122, 255, 0.1) !important;
  border-left: 3px solid #007ACC !important;
}

.monaco-live-coding-margin {
  background-color: rgba(0, 122, 255, 0.2) !important;
}

.monaco-editor .monaco-live-coding-highlight {
  animation: monaco-live-coding-pulse 2s ease-in-out infinite;
}

@keyframes monaco-live-coding-pulse {
  0% { background-color: rgba(0, 122, 255, 0.1); }
  50% { background-color: rgba(0, 122, 255, 0.2); }
  100% { background-color: rgba(0, 122, 255, 0.1); }
}
`;
