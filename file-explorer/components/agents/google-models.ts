// components/agents/google-models.ts

export interface GoogleModelMetadata {
  id: string;
  label: string;
  description?: string;
  contextSize?: number;
  pricing?: {
    input: number;  // per 1K tokens
    output: number; // per 1K tokens
  };
  tags?: string[];
  releaseDate?: string;
}

/**
 * Verified Google AI model metadata from official Google AI documentation
 * Only includes models with confirmed specifications
 */
export const GOOGLE_MODEL_METADATA: Record<string, GoogleModelMetadata> = {
  'gemini-2.0-flash': {
    id: 'gemini-2.0-flash',
    label: 'Gemini 2.0 Flash',
    description: 'Latest Gemini model with enhanced speed and capabilities',
    contextSize: 1000000,
    pricing: {
      input: 0.000075,
      output: 0.0003
    },
    tags: ['multimodal', 'fast', 'latest', 'text-only'],
    releaseDate: '2024-12-11'
  },
  'gemini-2.0-flash-lite': {
    id: 'gemini-2.0-flash-lite',
    label: 'Gemini 2.0 Flash Lite',
    description: 'Lightweight version of Gemini 2.0 Flash for efficient processing',
    contextSize: 500000,
    pricing: {
      input: 0.000050,
      output: 0.0002
    },
    tags: ['multimodal', 'fast', 'open-weight', 'high-context', 'reasoning'],
    releaseDate: '2024-12-11'
  },
  'gemini-2.5-flash-preview-05-20': {
    id: 'gemini-2.5-flash-preview-05-20',
    label: 'Gemini 2.5 Flash Preview 05-20',
    description: 'Preview version of Gemini 2.5 Flash with experimental features',
    contextSize: 1000000,
    pricing: {
      input: 0.000100,
      output: 0.0004
    },
    tags: ['multimodal', 'preview', 'experimental', 'reasoning'],
    releaseDate: '2025-05-20'
  },
  'gemini-2.5-pro-preview-06-05': {
    id: 'gemini-2.5-pro-preview-06-05',
    label: 'Gemini 2.5 Pro Preview 06-05',
    description: 'Preview version of Gemini 2.5 Pro with advanced capabilities',
    contextSize: 2000000,
    pricing: {
      input: 0.00200,
      output: 0.00600
    },
    tags: ['multimodal', 'preview', 'advanced-reasoning', 'long-context', 'reasoning'],
    releaseDate: '2025-06-05'
  }
};

/**
 * Get metadata for a specific Google model
 */
export function getGoogleModelMetadata(modelId: string): GoogleModelMetadata | null {
  return GOOGLE_MODEL_METADATA[modelId] || null;
}

/**
 * Check if a model has verified metadata
 */
export function hasGoogleModelMetadata(modelId: string): boolean {
  return modelId in GOOGLE_MODEL_METADATA;
}

/**
 * Get all available Google models with metadata
 */
export function getGoogleModelsWithMetadata(): GoogleModelMetadata[] {
  return Object.values(GOOGLE_MODEL_METADATA);
}

/**
 * Get model series for grouping
 */
export function getGoogleModelSeries(modelId: string): string {
  if (modelId.includes('1.5')) {
    return 'Gemini 1.5';
  }
  if (modelId.includes('gemini')) {
    return 'Gemini 1.0';
  }
  return 'Other';
}
