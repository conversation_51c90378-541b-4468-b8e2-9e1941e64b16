// components/agents/kanban-task-bridge.ts
import { AgentSubtask, AgentR<PERSON> } from './task-orchestrator';
import { Card } from '../kanban/board-context';
import { boardIPCBridge } from '../kanban/lib/board-ipc-bridge';

export interface KanbanTaskCard extends Omit<Card, 'id' | 'createdAt' | 'updatedAt'> {
  // Additional properties specific to task cards
  linkedTaskId: string;
  agentRole: AgentRole;
  taskPhase?: string;
  estimatedTokens?: number;
}

export class KanbanTaskBridge {
  private static readonly DEFAULT_BOARD_ID = 'main';
  private static readonly DEFAULT_SWIMLANE_ID = 'swimlane-1';

  /**
   * Create a Kanban card from an agent subtask
   */
  static createCardFromTask(task: AgentSubtask): KanbanTaskCard {
    const now = new Date().toISOString();

    return {
      title: task.title,
      description: task.description,
      priority: this.mapPriorityToKanban(task.priority),
      columnId: this.getColumnForAgent(task.agent),
      swimlaneId: this.DEFAULT_SWIMLANE_ID,
      projectId: task.linkedTo || `TASK-${Date.now()}`,
      tags: this.generateTagsForTask(task),
      labels: [{
        id: task.priority,
        name: task.priority.charAt(0).toUpperCase() + task.priority.slice(1),
        color: this.getPriorityColor(task.priority)
      }],
      progress: 0,
      assignee: this.getAgentDisplayName(task.agent),
      assignedAgentId: task.agent, // ✅ Task 83: Set assignedAgentId for direct agent routing
      agentAssignments: [{
        agentId: task.agent,
        agentType: 'AI',
        assignmentTime: now,
        role: 'primary',
        status: 'assigned'
      }],
      dependencies: task.dependencies || [],
      resourceMetrics: {
        tokenUsage: 0,
        cpuTime: 0,
        memoryUsage: 0,
        estimatedTokens: task.estimatedTokens
      },
      taskHistory: [{
        timestamp: now,
        action: 'created',
        agentId: 'task-orchestrator',
        details: `Task created from orchestration: ${task.title}`
      }],
      subtasks: [],
      comments: [],
      attachments: [],
      storyPoints: this.estimateStoryPoints(task),

      // Bridge-specific properties
      linkedTaskId: task.id,
      agentRole: task.agent,
      taskPhase: task.metadata?.phase,
      estimatedTokens: task.estimatedTokens
    };
  }

  /**
   * ✅ Task 84: Map agent roles to appropriate Kanban columns - ALL START IN PENDING
   */
  private static getColumnForAgent(agent: AgentRole): string {
    // ✅ Task 84: All cards start in "Pending" regardless of agent type
    // They will only move to "Delegated" when assigned, then "Running" when execution begins
    return 'column-1'; // Always start in Pending

    // All agents now start in Pending (column-1) for automated workflow consistency
  }

  /**
   * Map task priority to Kanban priority format
   */
  private static mapPriorityToKanban(priority: 'low' | 'medium' | 'high' | 'urgent'): string {
    const priorityMap = {
      'low': 'low',
      'medium': 'medium',
      'high': 'high',
      'urgent': 'high' // Map urgent to high for Kanban
    };
    return priorityMap[priority];
  }

  /**
   * Get color for priority level
   */
  private static getPriorityColor(priority: string): string {
    const colorMap: Record<string, string> = {
      'low': '#22c55e',      // Green
      'medium': '#facc15',   // Yellow
      'high': '#ef4444',     // Red
      'urgent': '#dc2626'    // Dark red
    };
    return colorMap[priority] || '#6b7280'; // Default gray
  }

  /**
   * Generate appropriate tags for the task
   */
  private static generateTagsForTask(task: AgentSubtask): string[] {
    const tags: string[] = [];

    // Add agent type tag
    tags.push(`agent:${task.agent}`);

    // Add phase tag if available
    if (task.metadata?.phase) {
      tags.push(`phase:${task.metadata.phase}`);
    }

    // Add capability tags
    if (task.requiredCapabilities) {
      task.requiredCapabilities.forEach(capability => {
        tags.push(`skill:${capability}`);
      });
    }

    // Add complexity tag based on estimated tokens
    if (task.estimatedTokens) {
      if (task.estimatedTokens < 500) tags.push('complexity:simple');
      else if (task.estimatedTokens < 1000) tags.push('complexity:medium');
      else tags.push('complexity:complex');
    }

    return tags;
  }

  /**
   * Get display name for agent
   */
  private static getAgentDisplayName(agent: AgentRole): string {
    const displayNames: Record<AgentRole, string> = {
      'micromanager': '🤖 Micromanager',
      'researcher': '📘 Researcher',
      'architect': '🏗️ Architect',
      'designer': '🎨 Designer',
      'intern': '1️⃣ Intern',
      'junior': '2️⃣ Junior',
      'midlevel': '3️⃣ MidLevel',
      'senior': '4️⃣ Senior',
      'tester': '🧪 Tester'
    };
    return displayNames[agent] || agent;
  }

  /**
   * Estimate story points based on task complexity
   */
  private static estimateStoryPoints(task: AgentSubtask): number {
    const tokens = task.estimatedTokens || 0;

    if (tokens < 300) return 1;      // Simple task
    if (tokens < 600) return 2;      // Small task
    if (tokens < 1000) return 3;     // Medium task
    if (tokens < 1500) return 5;     // Large task
    return 8;                        // Complex task
  }

  /**
   * Create multiple Kanban cards from a list of subtasks
   */
  static async createCardsFromSubtasks(
    subtasks: AgentSubtask[],
    boardId: string = KanbanTaskBridge.DEFAULT_BOARD_ID
  ): Promise<{ success: Card[], failed: { task: AgentSubtask, error: string }[] }> {
    const success: Card[] = [];
    const failed: { task: AgentSubtask, error: string }[] = [];

    console.log(`KanbanTaskBridge: Creating ${subtasks.length} cards from subtasks`);

    for (const subtask of subtasks) {
      try {
        const cardData = this.createCardFromTask(subtask);

        // Create card via IPC bridge
        const createdCard = await boardIPCBridge.createCard(
          boardId,
          cardData.columnId,
          cardData
        );

        if (createdCard) {
          success.push(createdCard);
          console.log(`KanbanTaskBridge: Created card ${createdCard.id} for task ${subtask.id} (${subtask.agent})`);
        } else {
          failed.push({
            task: subtask,
            error: 'IPC bridge returned null'
          });
        }
      } catch (error) {
        console.error(`KanbanTaskBridge: Failed to create card for task ${subtask.id}:`, error);
        failed.push({
          task: subtask,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    console.log(`KanbanTaskBridge: Created ${success.length} cards, ${failed.length} failed`);
    return { success, failed };
  }

  /**
   * Update card status based on task progress
   */
  static async updateCardProgress(
    cardId: string,
    progress: number,
    agentId: string,
    boardId: string = KanbanTaskBridge.DEFAULT_BOARD_ID
  ): Promise<boolean> {
    try {
      const result = await boardIPCBridge.updateCardProgress(boardId, cardId, progress, agentId);
      return !!result;
    } catch (error) {
      console.error(`KanbanTaskBridge: Failed to update card progress for ${cardId}:`, error);
      return false;
    }
  }

  /**
   * Move card to different column based on task status - Optimized for automated agent execution
   */
  static async moveCardBasedOnTaskStatus(
    cardId: string,
    taskStatus: 'pending' | 'delegated' | 'running' | 'verifying' | 'finalizing' | 'completed' | 'failed',
    agentId: string,
    boardId: string = KanbanTaskBridge.DEFAULT_BOARD_ID
  ): Promise<boolean> {
    const statusColumnMap = {
      'pending': 'column-1',    // Pending
      'delegated': 'column-2',  // Delegated
      'running': 'column-3',    // Running
      'verifying': 'column-4',  // Verifying
      'finalizing': 'column-5', // Finalizing
      'completed': 'column-6',  // Complete
      'failed': 'column-7'      // Error / Retry
    };

    const targetColumn = statusColumnMap[taskStatus];
    if (!targetColumn) {
      console.error(`KanbanTaskBridge: Invalid task status: ${taskStatus}`);
      return false;
    }

    try {
      // ✅ Check if card is already in the target column to prevent duplicate moves
      const currentCardState = await KanbanTaskBridge.getCurrentCardState(cardId, boardId);
      if (currentCardState && currentCardState.columnId === targetColumn) {
        console.log(`⚠️ [KanbanTaskBridge] Card ${cardId} is already in target column ${targetColumn} for status ${taskStatus}, skipping move`);
        return true; // Return true since the card is already in the correct position
      }

      console.log(`🔄 [KanbanTaskBridge] Moving card ${cardId} to ${targetColumn} (${taskStatus}) for agent ${agentId}`);

      // ✅ Use the enhanced moveCardToColumn method that handles source column detection
      const result = await boardIPCBridge.moveCardToColumn(
        boardId,
        cardId,
        targetColumn,
        agentId
      );

      if (result) {
        console.log(`KanbanTaskBridge: Successfully moved card ${cardId} to ${targetColumn} (${taskStatus})`);
        return true;
      } else {
        console.warn(`KanbanTaskBridge: Failed to move card ${cardId} to ${targetColumn} - no result returned`);
        return false;
      }
    } catch (error) {
      console.error(`KanbanTaskBridge: Failed to move card ${cardId} to ${targetColumn}:`, error);
      return false;
    }
  }

  /**
   * ✅ Get current card state to prevent duplicate moves
   */
  static async getCurrentCardState(cardId: string, boardId: string = KanbanTaskBridge.DEFAULT_BOARD_ID): Promise<{ columnId: string; title: string } | null> {
    try {
      // Get board state and find the card
      const boardState = await boardIPCBridge.getBoardState(boardId);
      if (!boardState) {
        console.warn(`KanbanTaskBridge: Failed to get board state for card ${cardId}`);
        return null;
      }

      // Find the card across all columns
      for (const column of boardState.columns) {
        const card = column.cards.find(c => c.id === cardId);
        if (card) {
          return {
            columnId: column.id,
            title: card.title
          };
        }
      }

      console.warn(`KanbanTaskBridge: Card ${cardId} not found in any column`);
      return null;
    } catch (error) {
      console.warn(`KanbanTaskBridge: Failed to get current state for card ${cardId}:`, error);
      return null;
    }
  }

  /**
   * Link task execution to card updates
   */
  static async linkTaskToCard(
    taskId: string,
    cardId: string,
    agentId: string
  ): Promise<boolean> {
    try {
      // This would typically update the task metadata to include the card ID
      // and the card metadata to include the task ID
      console.log(`KanbanTaskBridge: Linked task ${taskId} to card ${cardId} for agent ${agentId}`);
      return true;
    } catch (error) {
      console.error(`KanbanTaskBridge: Failed to link task ${taskId} to card ${cardId}:`, error);
      return false;
    }
  }

  /**
   * Get column mapping for reference - Optimized for automated agent execution
   */
  static getColumnMapping(): Record<string, string> {
    return {
      'column-1': 'Pending',
      'column-2': 'Delegated',
      'column-3': 'Running',
      'column-4': 'Verifying',
      'column-5': 'Finalizing',
      'column-6': 'Complete',
      'column-7': 'Error / Retry'
    };
  }

  /**
   * Get agent to column mapping for reference - All agents start in Pending for automated workflow
   */
  static getAgentColumnMapping(): Record<AgentRole, string> {
    return {
      'micromanager': 'Pending',
      'researcher': 'Pending',
      'architect': 'Pending',
      'designer': 'Pending',
      'intern': 'Pending',
      'junior': 'Pending',
      'midlevel': 'Pending',
      'senior': 'Pending',
      'tester': 'Pending'
    };
  }
}
