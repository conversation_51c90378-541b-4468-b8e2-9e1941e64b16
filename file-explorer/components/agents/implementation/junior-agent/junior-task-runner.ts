/**
 * Junior Task Runner Module
 * Core logic for executing assigned tasks
 */

import { AgentContext, AgentResponse } from '../../agent-base';
import { JuniorAgentConfig } from './agent-config';
import { <PERSON><PERSON>ommLayer } from './agent-comm-layer';
import { JuniorLearning } from './junior-learning';
import { JuniorUtils } from './junior-utils';

export interface TaskAnalysis {
  type: string;
  complexity: string;
  domain: string[];
  requirements: string[];
  constraints: string[];
  estimatedLines: number;
}

export interface PatternResearch {
  patterns: string[];
  recommendations: string[];
}

export class JuniorTaskRunner {
  constructor(
    private config: JuniorAgentConfig,
    private commLayer: JuniorCommLayer,
    private learning: JuniorLearning,
    private utils: JuniorUtils
  ) {}

  public async executeTask(context: AgentContext): Promise<AgentResponse> {
    const startTime = Date.now();
    let attemptCount = 0;
    let lastError: string | null = null;

    while (attemptCount < this.config.getMaxRetryAttempts()) {
      try {
        attemptCount++;

        const validation = this.utils.validateContext(context);
        if (!validation.valid) {
          return this.utils.createErrorResponse(validation.error!);
        }

        // Validate LLM configuration
        this.config.validateLLMConfig();

        // Check if task is within junior capabilities
        const complexityCheck = this.config.checkTaskComplexity(context.task, context.files);
        if (!complexityCheck.suitable) {
          return this.utils.createErrorResponse(
            `Task complexity may require escalation: ${complexityCheck.reason}. Consider MidLevel or Senior agent.`
          );
        }

        // Analyze the task in detail
        const taskAnalysis = this.analyzeTask(context);

        // Make LLM call for junior-level implementation
        const llmResponse = await this.makeLLMCall(context, taskAnalysis);

        // Log execution
        await this.commLayer.logExecution(context, llmResponse, startTime);

        // Research similar patterns if available
        const patternResearch = this.researchPatterns(context, taskAnalysis);

        // Perform real implementation work based on LLM guidance
        const implementationResult = await this.performImplementationWork(context, taskAnalysis, patternResearch);

        const executionTime = Date.now() - startTime;

        return this.utils.createSuccessResponse(
          llmResponse.content,
          llmResponse.tokensUsed.total,
          executionTime,
          this.generateSuggestions(taskAnalysis, attemptCount > 1),
          {
            taskType: taskAnalysis.type,
            complexity: taskAnalysis.complexity,
            attempts: attemptCount,
            filesCreated: implementationResult.files?.length || 0,
            realWork: true,
            patterns: patternResearch.patterns,
            llmProvider: llmResponse.provider,
            llmModel: llmResponse.model
          }
        );

      } catch (error) {
        lastError = error instanceof Error ? error.message : String(error);
        if (attemptCount >= this.config.getMaxRetryAttempts()) {
          break;
        }
        // Continue to next attempt
      }
    }

    const executionTime = Date.now() - startTime;
    return this.utils.createErrorResponse(
      `Junior execution failed after ${attemptCount} attempts. Last error: ${lastError}. Consider escalating to MidLevel agent.`,
      this.utils.estimateTokens(context)
    );
  }

  private analyzeTask(context: AgentContext): TaskAnalysis {
    const task = context.task.toLowerCase();

    // Determine task type
    let type = 'implementation';
    if (task.includes('api') || task.includes('endpoint')) type = 'api';
    else if (task.includes('form') || task.includes('validation')) type = 'form';
    else if (task.includes('component') && task.includes('react')) type = 'react_component';
    else if (task.includes('service') || task.includes('class')) type = 'service';
    else if (task.includes('utility') || task.includes('helper')) type = 'utility';
    else if (task.includes('hook') && task.includes('react')) type = 'react_hook';
    else if (task.includes('database') || task.includes('db')) type = 'database';
    else if (task.includes('auth') || task.includes('authentication')) type = 'authentication';

    // Determine complexity
    let complexity = 'moderate';
    if (task.includes('simple') || task.includes('basic')) {
      complexity = 'simple';
    } else if (task.includes('complex') || task.includes('advanced')) {
      complexity = 'complex';
    }

    // Identify domain areas
    const domains: string[] = [];
    if (task.includes('frontend') || task.includes('ui') || task.includes('react')) domains.push('frontend');
    if (task.includes('backend') || task.includes('server') || task.includes('api')) domains.push('backend');
    if (task.includes('database') || task.includes('db') || task.includes('sql')) domains.push('database');
    if (task.includes('test') || task.includes('spec')) domains.push('testing');

    // Extract requirements
    const requirements: string[] = [];
    if (task.includes('validation')) requirements.push('input_validation');
    if (task.includes('error handling')) requirements.push('error_handling');
    if (task.includes('logging')) requirements.push('logging');
    if (task.includes('async') || task.includes('promise')) requirements.push('async_operations');
    if (task.includes('state')) requirements.push('state_management');
    if (task.includes('responsive')) requirements.push('responsive_design');

    // Identify constraints
    const constraints: string[] = [];
    if (task.includes('performance')) constraints.push('performance_sensitive');
    if (task.includes('security')) constraints.push('security_critical');
    if (task.includes('accessible')) constraints.push('accessibility_required');
    if (task.includes('mobile')) constraints.push('mobile_optimized');

    // Estimate lines of code
    let estimatedLines = 50;
    if (complexity === 'simple') estimatedLines = 30;
    else if (complexity === 'complex') estimatedLines = 100;

    if (type === 'react_component') estimatedLines *= 1.5;
    if (type === 'service') estimatedLines *= 1.3;
    if (requirements.length > 3) estimatedLines *= 1.2;

    return { type, complexity, domain: domains, requirements, constraints, estimatedLines };
  }

  private async makeLLMCall(context: AgentContext, analysis: TaskAnalysis): Promise<any> {
    const { LLMRequestService } = await import('../../llm-request-service');
    const llmService = LLMRequestService.getInstance();

    const messages = [
      {
        role: 'system' as const,
        content: this.config.getSystemPrompt()
      },
      {
        role: 'user' as const,
        content: this.buildJuniorPrompt(context, analysis)
      }
    ];

    return await llmService.callLLM(this.config.getConfig(), messages);
  }

  private buildJuniorPrompt(context: AgentContext, analysis: TaskAnalysis): string {
    return `
JUNIOR IMPLEMENTATION TASK:
${context.task}

TASK ANALYSIS:
- Type: ${analysis.type}
- Complexity: ${analysis.complexity}
- Domain: ${analysis.domain.join(', ')}
- Estimated Lines: ${analysis.estimatedLines}

REQUIREMENTS:
${analysis.requirements.join('\n') || 'No specific requirements'}

CONSTRAINTS:
${analysis.constraints.join('\n') || 'No specific constraints'}

CONTEXT:
${context.codeContext || 'No specific code context provided'}

Please provide a junior-level implementation that:
1. Follows best practices for ${analysis.type} development
2. Includes proper error handling and validation
3. Uses appropriate patterns for the task complexity
4. Includes clear comments and documentation
5. Implements the requirements within the given constraints

Focus on clean, readable code that demonstrates good junior-level programming skills.
    `.trim();
  }

  private researchPatterns(context: AgentContext, analysis: TaskAnalysis): PatternResearch {
    const patterns: string[] = [];
    const recommendations: string[] = [];

    // Identify applicable patterns based on task type
    switch (analysis.type) {
      case 'react_component':
        patterns.push('functional_component', 'hooks_pattern');
        if (analysis.requirements.includes('state_management')) {
          patterns.push('useState', 'useEffect');
        }
        break;
      case 'api':
        patterns.push('express_router', 'async_await', 'error_middleware');
        break;
      case 'service':
        patterns.push('class_pattern', 'dependency_injection', 'singleton');
        break;
      case 'form':
        patterns.push('controlled_components', 'form_validation', 'submit_handling');
        break;
      case 'utility':
        patterns.push('pure_functions', 'exports_pattern');
        break;
    }

    // Add recommendations based on analysis
    if (analysis.requirements.includes('error_handling')) {
      recommendations.push('Implement try-catch blocks and proper error propagation');
    }
    if (analysis.requirements.includes('async_operations')) {
      recommendations.push('Use async/await pattern for better readability');
    }
    if (analysis.constraints.includes('performance_sensitive')) {
      recommendations.push('Consider memoization and optimization techniques');
    }

    return { patterns, recommendations };
  }

  private async performImplementationWork(context: AgentContext, taskAnalysis: TaskAnalysis, patternResearch: PatternResearch): Promise<any> {
    // This will be handled by the AgentExecutionService
    const { AgentExecutionService } = await import('../../agent-execution-service');
    const executionService = AgentExecutionService.getInstance();

    // Generate implementation files
    const implementationFiles = this.utils.generateImplementationFiles(context, taskAnalysis, patternResearch);

    // Generate test commands if needed
    const testCommands = this.utils.generateTestCommands(context, taskAnalysis);

    // Execute real file creation and testing
    const result = await executionService.executeWork(context, 'junior', {
      files: implementationFiles,
      commands: testCommands,
      kanban: [{
        cardId: context.metadata?.kanbanCardId,
        action: 'update',
        data: {
          progress: 75,
          tags: ['implementation', 'junior', taskAnalysis.type],
          agentAssignments: [{
            agentId: 'junior',
            status: 'implementing',
            assignmentTime: new Date().toISOString()
          }]
        }
      }]
    });

    console.log(`JuniorAgent: Real implementation work completed. Files: ${result.files?.length || 0}, Success: ${result.success}`);
    return result;
  }

  private generateSuggestions(analysis: TaskAnalysis, hadRetries: boolean): string[] {
    const suggestions: string[] = [];

    if (hadRetries) {
      suggestions.push('Task required multiple attempts - consider reviewing requirements for clarity');
    }

    if (analysis.complexity === 'complex') {
      suggestions.push('Complex task detected - consider breaking into smaller components');
    }

    if (analysis.requirements.includes('performance_sensitive')) {
      suggestions.push('Add performance monitoring and optimization');
    }

    if (analysis.requirements.includes('security_critical')) {
      suggestions.push('Implement additional security measures and input validation');
    }

    if (analysis.type === 'react_component') {
      suggestions.push('Add PropTypes or TypeScript interfaces for better type safety');
      suggestions.push('Consider accessibility features and ARIA attributes');
    }

    if (analysis.type === 'api') {
      suggestions.push('Add comprehensive request/response logging');
      suggestions.push('Implement rate limiting and security middleware');
    }

    suggestions.push('Add comprehensive unit tests');
    suggestions.push('Consider integration tests for external dependencies');
    suggestions.push('Add JSDoc comments for better documentation');

    return suggestions;
  }
}
