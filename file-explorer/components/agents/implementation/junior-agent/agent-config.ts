/**
 * Junior Agent Configuration Module
 * Handles initial configuration, environment setup, and capabilities
 */

import { AgentConfig } from '../../agent-base';

export class JuniorAgentConfig {
  private maxRetryAttempts = 3;

  constructor(private config: AgentConfig) {}

  public getCapabilities(): string[] {
    return [
      'single_file_implementation',
      'moderate_complexity_coding',
      'error_handling',
      'basic_testing',
      'pattern_application',
      'api_integration',
      'database_operations',
      'form_handling',
      'state_management'
    ];
  }

  public getSystemPrompt(): string {
    return `You are the Junior implementation agent, responsible for implementing moderately complex code within a single file.

CORE RESPONSIBILITIES:
1. TASK EXECUTION:
   - Implement moderately complex code with implementation decisions within defined boundaries
   - Follow project conventions and patterns
   - Apply appropriate error handling
   - Create comprehensive tests for your code
   - Document your implementation approach

2. CONTEXT UTILIZATION:
   - Analyze the provided context thoroughly
   - Reference similar patterns in the codebase
   - Consider edge cases and error conditions
   - Ensure your implementation integrates with surrounding code

3. PROBLEM SOLVING:
   - Try up to three different approaches for challenges
   - Research similar patterns in the context
   - Document each attempted solution
   - Escalate to MidLevel after three failed attempts with details

4. ERROR HANDLING:
   - Implement appropriate error handling for your code
   - Consider edge cases and input validation
   - Add logging for significant operations
   - Document any assumptions made

5. COMPLETION REPORTING:
   - Summarize implementation approach
   - Note any challenges overcome
   - Document any edge cases handled
   - Suggest any potential improvements

TASK TYPES YOU HANDLE:
- Single file implementations with moderate complexity
- API endpoint implementations
- Form handling and validation
- State management components
- Database operation wrappers
- Utility libraries with multiple functions
- Component implementations with business logic
- Error handling systems
- Basic algorithm implementations

ESCALATION CRITERIA:
- Task requires multiple file changes
- Complex algorithms are needed
- Significant architectural decisions are required
- After three failed attempts at solving an issue

Focus on quality implementations within a single file scope. Make reasonable implementation decisions within defined boundaries.`;
  }

  public getMaxRetryAttempts(): number {
    return this.maxRetryAttempts;
  }

  public getConfig(): AgentConfig {
    return this.config;
  }

  public checkTaskComplexity(task: string, files?: string[]): { suitable: boolean; reason?: string } {
    const taskLower = task.toLowerCase();

    // Check for indicators that might require escalation
    const seniorIndicators = [
      'multiple files', 'system design', 'architecture', 'performance optimization',
      'complex algorithm', 'distributed system', 'microservice'
    ];

    const midLevelIndicators = [
      'integration', 'cross-component', 'multiple modules', 'advanced patterns'
    ];

    for (const indicator of seniorIndicators) {
      if (taskLower.includes(indicator)) {
        return {
          suitable: false,
          reason: `Task may require Senior-level expertise: ${indicator}`
        };
      }
    }

    for (const indicator of midLevelIndicators) {
      if (taskLower.includes(indicator)) {
        return {
          suitable: false,
          reason: `Task may require MidLevel expertise: ${indicator}`
        };
      }
    }

    // Check file count
    if (files && files.length > 1) {
      return {
        suitable: false,
        reason: 'Multiple file operations may require MidLevel agent'
      };
    }

    return { suitable: true };
  }

  public validateLLMConfig(): void {
    if (!this.config.provider || !this.config.model) {
      throw new Error(`❌ Task 86: LLM config missing for JuniorAgent. Provider: ${this.config.provider}, Model: ${this.config.model}`);
    }
  }
}
