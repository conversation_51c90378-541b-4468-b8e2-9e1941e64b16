/**
 * Junior Agent Communication Layer
 * Handles IPC, logs, messages with other agents
 */

import { AgentContext } from '../../agent-base';

export class JuniorCommLayer {
  
  public async logExecution(context: AgentContext, llmResponse: any, startTime: number): Promise<void> {
    try {
      // Add completion logging to executionLogStore
      const { executionLogger } = await import('../../agent-execution-trace');
      executionLogger.record({
        agentId: 'junior',
        taskId: context.metadata?.taskId || context.metadata?.originalTaskId,
        cardId: context.metadata?.kanbanCardId,
        output: llmResponse.content,
        modelUsed: llmResponse.model,
        tokensUsed: llmResponse.tokensUsed.total,
        provider: llmResponse.provider,
        executionTime: Date.now() - startTime,
        success: true
      });
    } catch (error) {
      console.error('Failed to log execution:', error);
    }
  }

  public async broadcastMessage(message: string, type: 'info' | 'error' | 'success' = 'info'): Promise<void> {
    try {
      // Broadcast to other agents or systems
      console.log(`[JuniorAgent ${type.toUpperCase()}]: ${message}`);
      
      // Could integrate with IPC bridge here
      // const { agentIPCBridge } = await import('../../lib/agent-ipc-bridge');
      // await agentIPCBridge.addMessage({
      //   agentId: 'junior',
      //   message,
      //   timestamp: Date.now(),
      //   type
      // });
    } catch (error) {
      console.error('Failed to broadcast message:', error);
    }
  }

  public async notifyTaskStart(context: AgentContext): Promise<void> {
    await this.broadcastMessage(`Starting task: ${context.task.substring(0, 100)}...`, 'info');
  }

  public async notifyTaskComplete(context: AgentContext, result: any): Promise<void> {
    await this.broadcastMessage(
      `Task completed successfully. Files created: ${result.files?.length || 0}`, 
      'success'
    );
  }

  public async notifyTaskError(context: AgentContext, error: string): Promise<void> {
    await this.broadcastMessage(`Task failed: ${error}`, 'error');
  }

  public async updateKanbanCard(cardId: string, updates: any): Promise<void> {
    try {
      // Update Kanban card status
      if (cardId) {
        console.log(`Updating Kanban card ${cardId}:`, updates);
        // Integration with Kanban system would go here
      }
    } catch (error) {
      console.error('Failed to update Kanban card:', error);
    }
  }

  public async sendProgressUpdate(context: AgentContext, progress: number): Promise<void> {
    try {
      const cardId = context.metadata?.kanbanCardId;
      if (cardId) {
        await this.updateKanbanCard(cardId, { progress });
      }
      
      await this.broadcastMessage(`Progress: ${progress}%`, 'info');
    } catch (error) {
      console.error('Failed to send progress update:', error);
    }
  }

  public async requestHelp(context: AgentContext, issue: string): Promise<void> {
    await this.broadcastMessage(
      `Requesting help with: ${issue}. Task: ${context.task.substring(0, 50)}...`, 
      'info'
    );
  }

  public async escalateToMidLevel(context: AgentContext, reason: string): Promise<void> {
    await this.broadcastMessage(
      `Escalating to MidLevel agent. Reason: ${reason}. Task: ${context.task.substring(0, 50)}...`, 
      'info'
    );
  }
}
