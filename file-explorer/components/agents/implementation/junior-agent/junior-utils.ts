/**
 * Junior Agent Utilities
 * Shared helper functions, validation, parsing, and code generation
 */

import { AgentContext, AgentResponse } from '../../agent-base';
import { TaskAnalysis, PatternResearch } from './junior-task-runner';
import { FileCreationRequest, TerminalCommandRequest } from '../../agent-execution-service';
import { JuniorTestGenerators } from './junior-test-generators';

export class JuniorUtils {
  private testGenerators = new JuniorTestGenerators();

  public validateContext(context: AgentContext): { valid: boolean; error?: string } {
    if (!context.task || context.task.trim().length === 0) {
      return { valid: false, error: 'Task is required' };
    }

    if (context.task.length > 10000) {
      return { valid: false, error: 'Task description is too long' };
    }

    return { valid: true };
  }

  public estimateTokens(context: AgentContext): number {
    const taskLength = context.task.length;
    const contextLength = (context.codeContext?.length || 0) + 
                         (context.rules?.join(' ').length || 0);
    
    // Rough estimation: ~4 characters per token
    return Math.ceil((taskLength + contextLength) / 4);
  }

  public createErrorResponse(error: string, tokensUsed = 0): AgentResponse {
    return {
      success: false,
      content: '',
      error,
      tokensUsed,
      executionTime: 0,
      suggestions: ['Consider reviewing task requirements', 'Try breaking down the task into smaller parts'],
      metadata: {
        agentType: 'junior',
        errorType: 'execution_error'
      }
    };
  }

  public createSuccessResponse(
    content: string,
    tokensUsed: number,
    executionTime: number,
    suggestions: string[],
    metadata: any
  ): AgentResponse {
    return {
      success: true,
      content,
      tokensUsed,
      executionTime,
      suggestions,
      metadata: {
        agentType: 'junior',
        ...metadata
      }
    };
  }

  public generateImplementationFiles(context: AgentContext, taskAnalysis: TaskAnalysis, patternResearch: PatternResearch): FileCreationRequest[] {
    const files: FileCreationRequest[] = [];

    // Main implementation file
    const fileName = this.extractFileName(context.task);
    const filePath = `src/${fileName}.ts`;

    files.push({
      path: filePath,
      content: this.generateRealImplementation(context, taskAnalysis, patternResearch),
      language: 'typescript',
      openInEditor: true
    });

    // Test file
    files.push({
      path: `src/__tests__/${fileName}.test.ts`,
      content: this.generateRealTests(fileName, taskAnalysis),
      language: 'typescript'
    });

    return files;
  }

  public generateTestCommands(context: AgentContext, taskAnalysis: TaskAnalysis): TerminalCommandRequest[] {
    const commands: TerminalCommandRequest[] = [];

    // Run tests
    commands.push({
      command: 'npm test',
      category: 'test',
      timeout: 30000
    });

    return commands;
  }

  private extractFileName(task: string): string {
    const words = task.toLowerCase().split(' ');
    for (const word of words) {
      if (word.includes('service') || word.includes('util') || word.includes('helper') || word.includes('component')) {
        return word.replace(/[^a-zA-Z]/g, '');
      }
    }
    return 'implementation';
  }

  private generateRealImplementation(context: AgentContext, taskAnalysis: TaskAnalysis, patternResearch: PatternResearch): string {
    const fileName = this.extractFileName(context.task);
    const componentName = this.toPascalCase(fileName);

    // Generate real implementation based on task type
    switch (taskAnalysis.type) {
      case 'react_component':
        return this.generateReactComponent(context, taskAnalysis);
      case 'react_hook':
        return this.generateReactHook(context, taskAnalysis);
      case 'api':
        return this.generateApiEndpoint(context, taskAnalysis);
      case 'service':
        return this.generateService(context, taskAnalysis);
      case 'utility':
        return this.generateUtility(context, taskAnalysis);
      case 'form':
        return this.generateFormHandler(context, taskAnalysis);
      case 'database':
        return this.generateDatabaseOperation(context, taskAnalysis);
      default:
        return this.generateTaskSpecificImplementation(context, taskAnalysis, componentName);
    }
  }

  private generateTaskSpecificImplementation(context: AgentContext, taskAnalysis: TaskAnalysis, componentName: string): string {
    const task = context.task.toLowerCase();

    if (task.includes('function') || task.includes('method')) {
      return this.generateFunctionImplementation(context, taskAnalysis, componentName);
    } else if (task.includes('class')) {
      return this.generateClassImplementation(context, taskAnalysis, componentName);
    } else if (task.includes('interface') || task.includes('type')) {
      return this.generateTypeDefinition(context, taskAnalysis, componentName);
    } else {
      return this.generateModuleImplementation(context, taskAnalysis, componentName);
    }
  }

  private generateFunctionImplementation(context: AgentContext, taskAnalysis: TaskAnalysis, componentName: string): string {
    const functionName = this.extractFunctionName(context.task);
    return `/**
 * ${context.task}
 * Generated by Junior Agent
 */

export function ${functionName}(input: any): any {
  // Validate input
  if (!input) {
    throw new Error('Input is required');
  }

  try {
    // Process the input based on task requirements
    const result = processInput(input);
    return result;
  } catch (error) {
    console.error('Function execution failed:', error);
    throw error;
  }
}

function processInput(input: any): any {
  // Implementation logic based on task: ${context.task}
  return {
    processed: true,
    input,
    timestamp: Date.now()
  };
}

export default ${functionName};
`;
  }

  private generateClassImplementation(context: AgentContext, taskAnalysis: TaskAnalysis, componentName: string): string {
    return `/**
 * ${context.task}
 * Generated by Junior Agent
 */

export class ${componentName} {
  private initialized = false;
  private data: any = null;

  constructor(initialData?: any) {
    this.data = initialData || {};
  }

  async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      // Initialize based on task requirements
      this.initialized = true;
      console.log('${componentName} initialized successfully');
    } catch (error) {
      console.error('${componentName} initialization failed:', error);
      throw error;
    }
  }

  async process(input: any): Promise<any> {
    if (!this.initialized) {
      await this.initialize();
    }

    try {
      // Process based on task: ${context.task}
      return {
        success: true,
        data: input,
        processedBy: '${componentName}',
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('${componentName} processing failed:', error);
      throw error;
    }
  }

  getData(): any {
    return this.data;
  }

  setData(data: any): void {
    this.data = data;
  }
}

export default ${componentName};
`;
  }

  private generateTypeDefinition(context: AgentContext, taskAnalysis: TaskAnalysis, componentName: string): string {
    return `/**
 * ${context.task}
 * Generated by Junior Agent
 */

export interface ${componentName} {
  id: string;
  name: string;
  data: any;
  timestamp: number;
  status: 'active' | 'inactive' | 'pending';
}

export type ${componentName}Status = 'active' | 'inactive' | 'pending';

export interface ${componentName}Options {
  autoInitialize?: boolean;
  validateInput?: boolean;
  enableLogging?: boolean;
}

export interface ${componentName}Result {
  success: boolean;
  data: any;
  error?: string;
  timestamp: number;
}

export default ${componentName};
`;
  }

  private generateModuleImplementation(context: AgentContext, taskAnalysis: TaskAnalysis, componentName: string): string {
    return `/**
 * ${context.task}
 * Generated by Junior Agent
 */

// Module configuration
const config = {
  name: '${componentName}',
  version: '1.0.0',
  enabled: true
};

// Main module function
export function execute(input: any): any {
  if (!config.enabled) {
    throw new Error('Module is disabled');
  }

  try {
    // Execute based on task: ${context.task}
    return processModule(input);
  } catch (error) {
    console.error('Module execution failed:', error);
    throw error;
  }
}

function processModule(input: any): any {
  return {
    module: config.name,
    input,
    processed: true,
    timestamp: Date.now()
  };
}

// Utility functions
export function getConfig() {
  return { ...config };
}

export function setConfig(newConfig: Partial<typeof config>) {
  Object.assign(config, newConfig);
}

export default {
  execute,
  getConfig,
  setConfig
};
`;
  }

  private extractFunctionName(task: string): string {
    const words = task.toLowerCase().split(' ');
    for (const word of words) {
      if (word.includes('function') || word.includes('method')) {
        const nextWord = words[words.indexOf(word) + 1];
        if (nextWord && nextWord.length > 2) {
          return this.toCamelCase(nextWord);
        }
      }
    }
    return 'processTask';
  }

  private toCamelCase(str: string): string {
    return str.charAt(0).toLowerCase() + str.slice(1).replace(/[^a-zA-Z0-9]/g, '');
  }

  private toPascalCase(str: string): string {
    return str.charAt(0).toUpperCase() + str.slice(1).replace(/[^a-zA-Z0-9]/g, '');
  }

  private generateRealTests(fileName: string, taskAnalysis: TaskAnalysis): string {
    const componentName = this.toPascalCase(fileName);

    switch (taskAnalysis.type) {
      case 'react_component':
        return this.testGenerators.generateReactComponentTests(fileName, componentName, taskAnalysis);
      case 'react_hook':
        return this.testGenerators.generateReactHookTests(fileName, componentName, taskAnalysis);
      case 'api':
        return this.testGenerators.generateApiTests(fileName, componentName, taskAnalysis);
      case 'service':
        return this.testGenerators.generateServiceTests(fileName, componentName, taskAnalysis);
      case 'utility':
        return this.testGenerators.generateUtilityTests(fileName, componentName, taskAnalysis);
      default:
        return this.testGenerators.generateGenericFunctionalTests(fileName, componentName, taskAnalysis);
    }
  }

  // React Component Generation
  private generateReactComponent(context: AgentContext, analysis: TaskAnalysis): string {
    const componentName = this.extractComponentName(context.task) || 'GeneratedComponent';

    let component = `interface ${componentName}Props {\n`;
    component += `  className?: string;\n`;
    component += `  children?: React.ReactNode;\n`;

    if (analysis.requirements.includes('state_management')) {
      component += `  initialValue?: any;\n`;
      component += `  onChange?: (value: any) => void;\n`;
    }

    component += `}\n\n`;

    component += `export const ${componentName}: React.FC<${componentName}Props> = ({\n`;
    component += `  className,\n`;
    component += `  children,\n`;

    if (analysis.requirements.includes('state_management')) {
      component += `  initialValue,\n`;
      component += `  onChange,\n`;
    }

    component += `  ...props\n`;
    component += `}) => {\n`;

    // Add state if needed
    if (analysis.requirements.includes('state_management')) {
      component += `  const [value, setValue] = useState(initialValue);\n`;
      component += `  const [loading, setLoading] = useState(false);\n`;
      component += `  const [error, setError] = useState<string | null>(null);\n\n`;
    }

    // Add effects if needed
    if (analysis.requirements.includes('async_operations')) {
      component += `  useEffect(() => {\n`;
      component += `    const fetchData = async () => {\n`;
      component += `      try {\n`;
      component += `        setLoading(true);\n`;
      component += `        setError(null);\n`;
      component += `        // TODO: Implement async operation\n`;
      component += `      } catch (err) {\n`;
      component += `        setError(err instanceof Error ? err.message : 'An error occurred');\n`;
      component += `      } finally {\n`;
      component += `        setLoading(false);\n`;
      component += `      }\n`;
      component += `    };\n\n`;
      component += `    fetchData();\n`;
      component += `  }, []);\n\n`;
    }

    // Add handlers
    if (analysis.requirements.includes('state_management')) {
      component += `  const handleChange = useCallback((newValue: any) => {\n`;
      component += `    setValue(newValue);\n`;
      component += `    onChange?.(newValue);\n`;
      component += `  }, [onChange]);\n\n`;
    }

    // Add render logic
    component += `  return (\n`;
    component += `    <div className={className} {...props}>\n`;

    if (analysis.requirements.includes('state_management')) {
      component += `      {loading && <div>Loading...</div>}\n`;
      component += `      {error && <div className="error">Error: {error}</div>}\n`;
    }

    component += `      {children}\n`;
    component += `      {/* TODO: Implement component content */}\n`;
    component += `    </div>\n`;
    component += `  );\n`;
    component += `};\n\n`;
    component += `export default ${componentName};\n`;

    return component;
  }

  private extractComponentName(task: string): string | null {
    const patterns = [
      /create (\w+) component/i,
      /(\w+) component/i,
      /build (\w+) component/i
    ];

    for (const pattern of patterns) {
      const match = task.match(pattern);
      if (match) {
        return this.toPascalCase(match[1]);
      }
    }
    return null;
  }

  // React Hook Generation
  private generateReactHook(context: AgentContext, analysis: TaskAnalysis): string {
    const hookName = this.extractHookName(context.task) || 'useGeneratedHook';

    let hook = `interface ${hookName.charAt(0).toUpperCase() + hookName.slice(1)}Options {\n`;
    hook += `  initialValue?: any;\n`;
    hook += `  onError?: (error: Error) => void;\n`;
    hook += `}\n\n`;

    hook += `interface ${hookName.charAt(0).toUpperCase() + hookName.slice(1)}Return {\n`;
    hook += `  value: any;\n`;
    hook += `  setValue: (value: any) => void;\n`;
    hook += `  loading: boolean;\n`;
    hook += `  error: Error | null;\n`;
    hook += `  reset: () => void;\n`;
    hook += `}\n\n`;

    hook += `export const ${hookName} = (options: ${hookName.charAt(0).toUpperCase() + hookName.slice(1)}Options = {}): ${hookName.charAt(0).toUpperCase() + hookName.slice(1)}Return => {\n`;
    hook += `  const { initialValue, onError } = options;\n\n`;

    hook += `  const [value, setValue] = useState(initialValue);\n`;
    hook += `  const [loading, setLoading] = useState(false);\n`;
    hook += `  const [error, setError] = useState<Error | null>(null);\n\n`;

    hook += `  const reset = useCallback(() => {\n`;
    hook += `    setValue(initialValue);\n`;
    hook += `    setError(null);\n`;
    hook += `    setLoading(false);\n`;
    hook += `  }, [initialValue]);\n\n`;

    hook += `  const handleError = useCallback((err: Error) => {\n`;
    hook += `    setError(err);\n`;
    hook += `    onError?.(err);\n`;
    hook += `  }, [onError]);\n\n`;

    hook += `  return {\n`;
    hook += `    value,\n`;
    hook += `    setValue,\n`;
    hook += `    loading,\n`;
    hook += `    error,\n`;
    hook += `    reset\n`;
    hook += `  };\n`;
    hook += `};\n`;

    return hook;
  }

  private extractHookName(task: string): string | null {
    const patterns = [
      /create (\w+) hook/i,
      /(use\w+) hook/i,
      /(\w+) hook/i
    ];

    for (const pattern of patterns) {
      const match = task.match(pattern);
      if (match) {
        const name = match[1];
        return name.startsWith('use') ? name : `use${this.toPascalCase(name)}`;
      }
    }
    return null;
  }

  // API Endpoint Generation
  private generateApiEndpoint(context: AgentContext, analysis: TaskAnalysis): string {
    const routerName = this.extractRouterName(context.task) || 'generatedRouter';

    let api = `const router = express.Router();\n\n`;

    // Add middleware if needed
    if (analysis.requirements.includes('validation')) {
      api += `// Validation middleware\n`;
      api += `const validateRequest = (req: Request, res: Response, next: NextFunction) => {\n`;
      api += `  if (!req.body) {\n`;
      api += `    return res.status(400).json({ error: 'Request body is required' });\n`;
      api += `  }\n`;
      api += `  next();\n`;
      api += `};\n\n`;
    }

    // Add error handling middleware
    api += `// Error handling middleware\n`;
    api += `const handleError = (error: Error, req: Request, res: Response, next: NextFunction) => {\n`;
    api += `  console.error('API Error:', error);\n`;
    api += `  res.status(500).json({\n`;
    api += `    error: 'Internal server error',\n`;
    api += `    message: error.message\n`;
    api += `  });\n`;
    api += `};\n\n`;

    // Add main endpoint
    api += `// Main endpoint\n`;
    api += `router.get('/', async (req: Request, res: Response, next: NextFunction) => {\n`;
    api += `  try {\n`;
    api += `    const result = { message: 'Success', data: null };\n`;
    api += `    res.json(result);\n`;
    api += `  } catch (error) {\n`;
    api += `    next(error);\n`;
    api += `  }\n`;
    api += `});\n\n`;

    api += `router.post('/', validateRequest, async (req: Request, res: Response, next: NextFunction) => {\n`;
    api += `  try {\n`;
    api += `    const { body } = req;\n`;
    api += `    const result = { message: 'Created', data: body };\n`;
    api += `    res.status(201).json(result);\n`;
    api += `  } catch (error) {\n`;
    api += `    next(error);\n`;
    api += `  }\n`;
    api += `});\n\n`;

    api += `// Apply error handling\n`;
    api += `router.use(handleError);\n\n`;
    api += `export default router;\n`;

    return api;
  }

  private extractRouterName(task: string): string | null {
    const patterns = [
      /create (\w+) router/i,
      /(\w+) api/i,
      /(\w+) endpoint/i
    ];

    for (const pattern of patterns) {
      const match = task.match(pattern);
      if (match) {
        return this.toCamelCase(match[1]);
      }
    }
    return null;
  }

  // Service Generation
  private generateService(context: AgentContext, analysis: TaskAnalysis): string {
    const serviceName = this.extractServiceName(context.task) || 'GeneratedService';

    let service = `export interface ${serviceName}Config {\n`;
    service += `  apiUrl?: string;\n`;
    service += `  timeout?: number;\n`;
    service += `  retries?: number;\n`;
    service += `}\n\n`;

    service += `export class ${serviceName} {\n`;
    service += `  private config: ${serviceName}Config;\n`;
    service += `  private isInitialized = false;\n\n`;

    service += `  constructor(config: ${serviceName}Config = {}) {\n`;
    service += `    this.config = {\n`;
    service += `      timeout: 5000,\n`;
    service += `      retries: 3,\n`;
    service += `      ...config\n`;
    service += `    };\n`;
    service += `  }\n\n`;

    service += `  async initialize(): Promise<void> {\n`;
    service += `    if (this.isInitialized) return;\n`;
    service += `    \n`;
    service += `    try {\n`;
    service += `      this.isInitialized = true;\n`;
    service += `    } catch (error) {\n`;
    service += `      throw new Error(\`Failed to initialize ${serviceName}: \${error}\`);\n`;
    service += `    }\n`;
    service += `  }\n\n`;

    if (analysis.requirements.includes('async_operations')) {
      service += `  async process(data: any): Promise<any> {\n`;
      service += `    if (!this.isInitialized) {\n`;
      service += `      await this.initialize();\n`;
      service += `    }\n\n`;
      service += `    try {\n`;
      service += `      return { success: true, data };\n`;
      service += `    } catch (error) {\n`;
      service += `      throw new Error(\`Processing failed: \${error}\`);\n`;
      service += `    }\n`;
      service += `  }\n\n`;
    }

    service += `  getConfig(): ${serviceName}Config {\n`;
    service += `    return { ...this.config };\n`;
    service += `  }\n\n`;

    service += `  isReady(): boolean {\n`;
    service += `    return this.isInitialized;\n`;
    service += `  }\n`;
    service += `}\n\n`;

    service += `export const ${serviceName.toLowerCase()} = new ${serviceName}();\n`;

    return service;
  }

  private extractServiceName(task: string): string | null {
    const patterns = [
      /create (\w+) service/i,
      /(\w+) service/i,
      /build (\w+) service/i
    ];

    for (const pattern of patterns) {
      const match = task.match(pattern);
      if (match) {
        return this.toPascalCase(match[1]) + 'Service';
      }
    }
    return null;
  }

  // Utility Generation
  private generateUtility(context: AgentContext, analysis: TaskAnalysis): string {
    const utilityName = this.extractUtilityName(context.task) || 'generatedUtils';

    let utility = `/**\n`;
    utility += ` * ${utilityName} - Utility functions\n`;
    utility += ` */\n\n`;

    utility += `export const ${utilityName} = {\n`;
    utility += `  validate(input: any): { valid: boolean; errors: string[] } {\n`;
    utility += `    const errors: string[] = [];\n`;
    utility += `    \n`;
    utility += `    if (input == null) {\n`;
    utility += `      errors.push('Input is required');\n`;
    utility += `    }\n`;
    utility += `    \n`;
    utility += `    return { valid: errors.length === 0, errors };\n`;
    utility += `  },\n\n`;

    utility += `  format(data: any, options: { type?: string } = {}): string {\n`;
    utility += `    if (data == null) return '';\n`;
    utility += `    \n`;
    utility += `    switch (options.type) {\n`;
    utility += `      case 'date':\n`;
    utility += `        return new Date(data).toLocaleDateString();\n`;
    utility += `      case 'currency':\n`;
    utility += `        return new Intl.NumberFormat('en-US', {\n`;
    utility += `          style: 'currency',\n`;
    utility += `          currency: 'USD'\n`;
    utility += `        }).format(data);\n`;
    utility += `      default:\n`;
    utility += `        return String(data);\n`;
    utility += `    }\n`;
    utility += `  },\n\n`;

    if (analysis.requirements.includes('async_operations')) {
      utility += `  async retry<T>(\n`;
      utility += `    operation: () => Promise<T>,\n`;
      utility += `    maxRetries = 3,\n`;
      utility += `    delay = 1000\n`;
      utility += `  ): Promise<T> {\n`;
      utility += `    let lastError: Error;\n`;
      utility += `    \n`;
      utility += `    for (let attempt = 1; attempt <= maxRetries; attempt++) {\n`;
      utility += `      try {\n`;
      utility += `        return await operation();\n`;
      utility += `      } catch (error) {\n`;
      utility += `        lastError = error instanceof Error ? error : new Error(String(error));\n`;
      utility += `        \n`;
      utility += `        if (attempt < maxRetries) {\n`;
      utility += `          await new Promise(resolve => setTimeout(resolve, delay * attempt));\n`;
      utility += `        }\n`;
      utility += `      }\n`;
      utility += `    }\n`;
      utility += `    \n`;
      utility += `    throw lastError!;\n`;
      utility += `  },\n\n`;
    }

    utility += `  deepClone<T>(obj: T): T {\n`;
    utility += `    if (obj === null || typeof obj !== 'object') return obj;\n`;
    utility += `    if (obj instanceof Date) return new Date(obj.getTime()) as any;\n`;
    utility += `    if (obj instanceof Array) return obj.map(item => this.deepClone(item)) as any;\n`;
    utility += `    \n`;
    utility += `    const cloned = {} as T;\n`;
    utility += `    Object.keys(obj).forEach(key => {\n`;
    utility += `      (cloned as any)[key] = this.deepClone((obj as any)[key]);\n`;
    utility += `    });\n`;
    utility += `    \n`;
    utility += `    return cloned;\n`;
    utility += `  }\n`;
    utility += `};\n\n`;

    utility += `export default ${utilityName};\n`;

    return utility;
  }

  private extractUtilityName(task: string): string | null {
    const patterns = [
      /create (\w+) util/i,
      /(\w+) utility/i,
      /(\w+) helper/i
    ];

    for (const pattern of patterns) {
      const match = task.match(pattern);
      if (match) {
        return this.toCamelCase(match[1]) + 'Utils';
      }
    }
    return null;
  }

  // Form Handler Generation
  private generateFormHandler(context: AgentContext, analysis: TaskAnalysis): string {
    const formName = this.extractFormName(context.task) || 'GeneratedForm';

    let form = `interface ${formName}Data {\n`;
    form += `  [key: string]: any;\n`;
    form += `}\n\n`;

    form += `interface ${formName}Errors {\n`;
    form += `  [key: string]: string;\n`;
    form += `}\n\n`;

    form += `export const use${formName} = (initialData: Partial<${formName}Data> = {}) => {\n`;
    form += `  const [data, setData] = useState<${formName}Data>({ ...initialData } as ${formName}Data);\n`;
    form += `  const [errors, setErrors] = useState<${formName}Errors>({});\n`;
    form += `  const [isSubmitting, setIsSubmitting] = useState(false);\n`;
    form += `  const [isValid, setIsValid] = useState(false);\n\n`;

    form += `  const validate = useCallback((formData: ${formName}Data): ${formName}Errors => {\n`;
    form += `    const newErrors: ${formName}Errors = {};\n`;
    form += `    \n`;
    form += `    Object.keys(formData).forEach(key => {\n`;
    form += `      if (!formData[key]) {\n`;
    form += `        newErrors[key] = 'This field is required';\n`;
    form += `      }\n`;
    form += `    });\n`;
    form += `    \n`;
    form += `    return newErrors;\n`;
    form += `  }, []);\n\n`;

    form += `  const handleChange = useCallback((field: keyof ${formName}Data, value: any) => {\n`;
    form += `    setData(prev => ({ ...prev, [field]: value }));\n`;
    form += `    \n`;
    form += `    if (errors[field as string]) {\n`;
    form += `      setErrors(prev => ({ ...prev, [field]: '' }));\n`;
    form += `    }\n`;
    form += `  }, [errors]);\n\n`;

    form += `  const handleSubmit = useCallback(async (onSubmit: (data: ${formName}Data) => Promise<void>) => {\n`;
    form += `    const formErrors = validate(data);\n`;
    form += `    setErrors(formErrors);\n`;
    form += `    \n`;
    form += `    if (Object.keys(formErrors).length > 0) {\n`;
    form += `      return;\n`;
    form += `    }\n\n`;
    form += `    setIsSubmitting(true);\n`;
    form += `    try {\n`;
    form += `      await onSubmit(data);\n`;
    form += `    } catch (error) {\n`;
    form += `      console.error('Form submission error:', error);\n`;
    form += `    } finally {\n`;
    form += `      setIsSubmitting(false);\n`;
    form += `    }\n`;
    form += `  }, [data, validate]);\n\n`;

    form += `  const reset = useCallback(() => {\n`;
    form += `    setData({ ...initialData } as ${formName}Data);\n`;
    form += `    setErrors({});\n`;
    form += `    setIsSubmitting(false);\n`;
    form += `  }, [initialData]);\n\n`;

    form += `  useEffect(() => {\n`;
    form += `    const formErrors = validate(data);\n`;
    form += `    setIsValid(Object.keys(formErrors).length === 0);\n`;
    form += `  }, [data, validate]);\n\n`;

    form += `  return {\n`;
    form += `    data,\n`;
    form += `    errors,\n`;
    form += `    isSubmitting,\n`;
    form += `    isValid,\n`;
    form += `    handleChange,\n`;
    form += `    handleSubmit,\n`;
    form += `    reset\n`;
    form += `  };\n`;
    form += `};\n`;

    return form;
  }

  private extractFormName(task: string): string | null {
    const patterns = [
      /create (\w+) form/i,
      /(\w+) form/i,
      /build (\w+) form/i
    ];

    for (const pattern of patterns) {
      const match = task.match(pattern);
      if (match) {
        return this.toPascalCase(match[1]) + 'Form';
      }
    }
    return null;
  }

  // Database Operation Generation
  private generateDatabaseOperation(context: AgentContext, analysis: TaskAnalysis): string {
    const operationName = this.extractOperationName(context.task) || 'DatabaseOperation';

    let db = `interface ${operationName}Config {\n`;
    db += `  connectionString?: string;\n`;
    db += `  timeout?: number;\n`;
    db += `}\n\n`;

    db += `export class ${operationName} {\n`;
    db += `  private config: ${operationName}Config;\n`;
    db += `  private connection: any = null;\n\n`;

    db += `  constructor(config: ${operationName}Config = {}) {\n`;
    db += `    this.config = {\n`;
    db += `      timeout: 30000,\n`;
    db += `      ...config\n`;
    db += `    };\n`;
    db += `  }\n\n`;

    db += `  async connect(): Promise<void> {\n`;
    db += `    if (this.connection) return;\n`;
    db += `    \n`;
    db += `    try {\n`;
    db += `      console.log('Database connected');\n`;
    db += `    } catch (error) {\n`;
    db += `      throw new Error(\`Database connection failed: \${error}\`);\n`;
    db += `    }\n`;
    db += `  }\n\n`;

    db += `  async query(sql: string, params: any[] = []): Promise<any[]> {\n`;
    db += `    if (!this.connection) {\n`;
    db += `      await this.connect();\n`;
    db += `    }\n\n`;
    db += `    try {\n`;
    db += `      console.log('Executing query:', sql, params);\n`;
    db += `      return [];\n`;
    db += `    } catch (error) {\n`;
    db += `      throw new Error(\`Query execution failed: \${error}\`);\n`;
    db += `    }\n`;
    db += `  }\n\n`;

    db += `  async disconnect(): Promise<void> {\n`;
    db += `    if (this.connection) {\n`;
    db += `      try {\n`;
    db += `        this.connection = null;\n`;
    db += `        console.log('Database disconnected');\n`;
    db += `      } catch (error) {\n`;
    db += `        console.error('Error disconnecting from database:', error);\n`;
    db += `      }\n`;
    db += `    }\n`;
    db += `  }\n`;
    db += `}\n`;

    return db;
  }

  private extractOperationName(task: string): string | null {
    const patterns = [
      /create (\w+) operation/i,
      /(\w+) database/i,
      /(\w+) db/i
    ];

    for (const pattern of patterns) {
      const match = task.match(pattern);
      if (match) {
        return this.toPascalCase(match[1]) + 'Operation';
      }
    }
    return null;
  }
}
