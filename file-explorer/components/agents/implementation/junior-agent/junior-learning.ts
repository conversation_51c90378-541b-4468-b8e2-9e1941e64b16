/**
 * Junior Learning Module
 * Handles retry logic, pattern learning, and heuristic tracking
 */

import { AgentContext } from '../../agent-base';
import { TaskAnalysis, PatternResearch } from './junior-task-runner';

export interface LearningMetrics {
  taskType: string;
  complexity: string;
  attempts: number;
  success: boolean;
  executionTime: number;
  patterns: string[];
  errors: string[];
}

export class JuniorLearning {
  private learningHistory: LearningMetrics[] = [];
  private patternSuccessRates = new Map<string, { successes: number; total: number }>();
  private errorPatterns = new Map<string, number>();

  public recordTaskExecution(
    context: AgentContext,
    analysis: TaskAnalysis,
    attempts: number,
    success: boolean,
    executionTime: number,
    patterns: string[],
    errors: string[] = []
  ): void {
    const metrics: LearningMetrics = {
      taskType: analysis.type,
      complexity: analysis.complexity,
      attempts,
      success,
      executionTime,
      patterns,
      errors
    };

    this.learningHistory.push(metrics);
    this.updatePatternSuccessRates(patterns, success);
    this.updateErrorPatterns(errors);

    // Keep only last 1000 entries
    if (this.learningHistory.length > 1000) {
      this.learningHistory.shift();
    }
  }

  private updatePatternSuccessRates(patterns: string[], success: boolean): void {
    patterns.forEach(pattern => {
      const current = this.patternSuccessRates.get(pattern) || { successes: 0, total: 0 };
      current.total++;
      if (success) {
        current.successes++;
      }
      this.patternSuccessRates.set(pattern, current);
    });
  }

  private updateErrorPatterns(errors: string[]): void {
    errors.forEach(error => {
      const count = this.errorPatterns.get(error) || 0;
      this.errorPatterns.set(error, count + 1);
    });
  }

  public getRecommendedPatterns(taskType: string, complexity: string): string[] {
    const relevantHistory = this.learningHistory.filter(
      h => h.taskType === taskType && h.complexity === complexity && h.success
    );

    const patternCounts = new Map<string, number>();
    relevantHistory.forEach(h => {
      h.patterns.forEach(pattern => {
        patternCounts.set(pattern, (patternCounts.get(pattern) || 0) + 1);
      });
    });

    // Sort by frequency and success rate
    return Array.from(patternCounts.entries())
      .sort((a, b) => {
        const aSuccessRate = this.getPatternSuccessRate(a[0]);
        const bSuccessRate = this.getPatternSuccessRate(b[0]);
        
        if (aSuccessRate !== bSuccessRate) {
          return bSuccessRate - aSuccessRate;
        }
        return b[1] - a[1]; // Fall back to frequency
      })
      .slice(0, 5)
      .map(([pattern]) => pattern);
  }

  private getPatternSuccessRate(pattern: string): number {
    const stats = this.patternSuccessRates.get(pattern);
    if (!stats || stats.total === 0) return 0;
    return stats.successes / stats.total;
  }

  public getCommonErrors(taskType: string): string[] {
    const relevantHistory = this.learningHistory.filter(h => h.taskType === taskType && !h.success);
    
    const errorCounts = new Map<string, number>();
    relevantHistory.forEach(h => {
      h.errors.forEach(error => {
        errorCounts.set(error, (errorCounts.get(error) || 0) + 1);
      });
    });

    return Array.from(errorCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 3)
      .map(([error]) => error);
  }

  public shouldRetry(context: AgentContext, currentAttempt: number, error: string): boolean {
    // Don't retry if we've hit the max attempts
    if (currentAttempt >= 3) return false;

    // Don't retry for certain types of errors
    const nonRetryableErrors = [
      'configuration error',
      'invalid task',
      'missing dependencies',
      'permission denied'
    ];

    const errorLower = error.toLowerCase();
    if (nonRetryableErrors.some(pattern => errorLower.includes(pattern))) {
      return false;
    }

    // Check if this error pattern has a low success rate on retry
    const errorFrequency = this.errorPatterns.get(error) || 0;
    if (errorFrequency > 10) {
      // This error happens frequently, might not be worth retrying
      return false;
    }

    return true;
  }

  public getRetryStrategy(context: AgentContext, previousError: string): {
    approach: string;
    modifications: string[];
  } {
    const task = context.task.toLowerCase();
    
    // Analyze the error and suggest modifications
    const modifications: string[] = [];
    let approach = 'standard_retry';

    if (previousError.includes('timeout')) {
      approach = 'simplified_approach';
      modifications.push('Reduce complexity');
      modifications.push('Break into smaller steps');
    } else if (previousError.includes('validation')) {
      approach = 'enhanced_validation';
      modifications.push('Add more input validation');
      modifications.push('Check edge cases');
    } else if (previousError.includes('dependency')) {
      approach = 'alternative_implementation';
      modifications.push('Use different libraries');
      modifications.push('Implement from scratch');
    } else {
      // Generic retry with slight modifications
      modifications.push('Review task requirements');
      modifications.push('Simplify implementation');
    }

    return { approach, modifications };
  }

  public getPerformanceInsights(): {
    averageExecutionTime: number;
    successRate: number;
    mostSuccessfulPatterns: string[];
    commonFailureReasons: string[];
  } {
    if (this.learningHistory.length === 0) {
      return {
        averageExecutionTime: 0,
        successRate: 0,
        mostSuccessfulPatterns: [],
        commonFailureReasons: []
      };
    }

    const totalTime = this.learningHistory.reduce((sum, h) => sum + h.executionTime, 0);
    const successCount = this.learningHistory.filter(h => h.success).length;
    
    const averageExecutionTime = totalTime / this.learningHistory.length;
    const successRate = successCount / this.learningHistory.length;

    // Get most successful patterns
    const mostSuccessfulPatterns = Array.from(this.patternSuccessRates.entries())
      .filter(([, stats]) => stats.total >= 3) // Only patterns used at least 3 times
      .sort((a, b) => (b[1].successes / b[1].total) - (a[1].successes / a[1].total))
      .slice(0, 5)
      .map(([pattern]) => pattern);

    // Get common failure reasons
    const commonFailureReasons = Array.from(this.errorPatterns.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([error]) => error);

    return {
      averageExecutionTime,
      successRate,
      mostSuccessfulPatterns,
      commonFailureReasons
    };
  }

  public adaptToFeedback(feedback: {
    taskType: string;
    suggestion: string;
    priority: 'high' | 'medium' | 'low';
  }): void {
    // Store feedback for future task execution
    console.log(`Learning from feedback for ${feedback.taskType}: ${feedback.suggestion} (${feedback.priority})`);
    
    // This could be expanded to modify behavior based on feedback
    // For now, just log it for awareness
  }
}
