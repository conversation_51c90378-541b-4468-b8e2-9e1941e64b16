/**
 * Junior Agent - Streamlined Implementation
 * 
 * Reduced from 1719 lines to under 200 lines by extracting functionality into modular components.
 * This serves as the main interface that delegates to specialized modules.
 */

import { AgentBase, AgentConfig, AgentContext, AgentResponse } from '../agent-base';
import { JuniorAgentConfig } from './junior-agent/agent-config';
import { JuniorTaskRunner } from './junior-agent/junior-task-runner';
import { JuniorCommLayer } from './junior-agent/agent-comm-layer';
import { JuniorLearning } from './junior-agent/junior-learning';
import { JuniorUtils } from './junior-agent/junior-utils';

export class JuniorAgent extends AgentBase {
  private agentConfig: JuniorAgentConfig;
  private taskRunner: JuniorTaskRunner;
  private commLayer: JuniorCommLayer;
  private learning: JuniorLearning;
  private utils: JuniorUtils;

  constructor(config: AgentConfig) {
    super(config);
    
    // Initialize modular components
    this.agentConfig = new JuniorAgentConfig(config);
    this.commLayer = new JuniorCommLayer();
    this.learning = new JuniorLearning();
    this.utils = new JuniorUtils();
    this.taskRunner = new JuniorTaskRunner(
      this.agentConfig,
      this.commLayer,
      this.learning,
      this.utils
    );
  }

  public getCapabilities(): string[] {
    return this.agentConfig.getCapabilities();
  }

  public getSystemPrompt(): string {
    return this.agentConfig.getSystemPrompt();
  }

  public async execute(context: AgentContext): Promise<AgentResponse> {
    try {
      // Notify task start
      await this.commLayer.notifyTaskStart(context);

      // Execute task using modular task runner
      const result = await this.taskRunner.executeTask(context);

      // Record learning metrics
      if (result.success && result.metadata) {
        this.learning.recordTaskExecution(
          context,
          {
            type: result.metadata.taskType || 'unknown',
            complexity: result.metadata.complexity || 'moderate',
            domain: [],
            requirements: [],
            constraints: [],
            estimatedLines: 0
          },
          result.metadata.attempts || 1,
          result.success,
          result.executionTime || 0,
          result.metadata.patterns || [],
          result.error ? [result.error] : []
        );
      }

      // Notify completion
      if (result.success) {
        await this.commLayer.notifyTaskComplete(context, result);
      } else {
        await this.commLayer.notifyTaskError(context, result.error || 'Unknown error');
      }

      return result;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      
      // Notify error
      await this.commLayer.notifyTaskError(context, errorMessage);

      // Record failed execution
      this.learning.recordTaskExecution(
        context,
        {
          type: 'unknown',
          complexity: 'moderate',
          domain: [],
          requirements: [],
          constraints: [],
          estimatedLines: 0
        },
        1,
        false,
        0,
        [],
        [errorMessage]
      );

      return this.utils.createErrorResponse(errorMessage);
    }
  }

  // Additional utility methods that delegate to modular components
  public checkTaskComplexity(context: AgentContext): { suitable: boolean; reason?: string } {
    return this.agentConfig.checkTaskComplexity(context.task, context.files);
  }

  public getPerformanceInsights() {
    return this.learning.getPerformanceInsights();
  }

  public getRecommendedPatterns(taskType: string, complexity: string): string[] {
    return this.learning.getRecommendedPatterns(taskType, complexity);
  }

  public shouldRetry(context: AgentContext, currentAttempt: number, error: string): boolean {
    return this.learning.shouldRetry(context, currentAttempt, error);
  }

  public getRetryStrategy(context: AgentContext, previousError: string) {
    return this.learning.getRetryStrategy(context, previousError);
  }

  public async sendProgressUpdate(context: AgentContext, progress: number): Promise<void> {
    await this.commLayer.sendProgressUpdate(context, progress);
  }

  public async requestHelp(context: AgentContext, issue: string): Promise<void> {
    await this.commLayer.requestHelp(context, issue);
  }

  public async escalateToMidLevel(context: AgentContext, reason: string): Promise<void> {
    await this.commLayer.escalateToMidLevel(context, reason);
  }

  public adaptToFeedback(feedback: {
    taskType: string;
    suggestion: string;
    priority: 'high' | 'medium' | 'low';
  }): void {
    this.learning.adaptToFeedback(feedback);
  }

  // Configuration methods
  public getMaxRetryAttempts(): number {
    return this.agentConfig.getMaxRetryAttempts();
  }

  public validateLLMConfig(): void {
    this.agentConfig.validateLLMConfig();
  }

  // Utility methods
  public validateContext(context: AgentContext): { valid: boolean; error?: string } {
    return this.utils.validateContext(context);
  }

  public estimateTokens(context: AgentContext): number {
    return this.utils.estimateTokens(context);
  }

  // Override getId to return 'junior'
  public getId(): string {
    return 'junior';
  }

  // Override getName to return descriptive name
  public getName(): string {
    return 'Junior Developer Agent';
  }

  // Override getDescription
  public getDescription(): string {
    return 'Handles moderately complex single-file implementations with proper error handling and testing';
  }
}
