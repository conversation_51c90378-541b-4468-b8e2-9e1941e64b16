// components/agents/llm-integration-service.ts
import { LLMRequestService } from './llm-request-service';
import { SettingsManager } from '../settings/settings-manager';
import { getGlobalSettingsManager } from '../settings/global-settings';
import { getAllProviders, LLMProvider } from './llm-provider-registry';
import { costTracker } from '../../lib/cost-tracker';
import { alertManager } from '../../lib/alert-manager';

export class LLMIntegrationService {
  private static instance: LLMIntegrationService;
  private llmService: LLMRequestService;
  private settingsManager: SettingsManager;
  private initialized = false;

  private constructor() {
    this.llmService = LLMRequestService.getInstance();
    this.settingsManager = getGlobalSettingsManager(); // ✅ CRITICAL FIX: Use singleton instead of new instance
  }

  public static getInstance(): LLMIntegrationService {
    if (!LLMIntegrationService.instance) {
      LLMIntegrationService.instance = new LLMIntegrationService();
    }
    return LLMIntegrationService.instance;
  }

  public async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      // ✅ Wait for SettingsManager to fully load before accessing settings
      console.log('🔄 LLMIntegrationService: Waiting for SettingsManager to initialize...');
      await this.waitForSettingsManagerInitialization();

      // Load API keys and cost settings from settings and set them in LLM service
      const settings = this.settingsManager.getSettings();

      console.log('🔄 LLMIntegrationService: Initializing with settings:', {
        hasApiKeys: Object.keys(settings.apiKeys).length > 0,
        apiKeyProviders: Object.keys(settings.apiKeys),
        apiKeyValues: Object.keys(settings.apiKeys).map(provider => ({
          provider,
          hasKey: !!settings.apiKeys[provider],
          keyLength: settings.apiKeys[provider]?.length || 0
        })),
        costSettings: !!settings.cost,
        settingsManagerInitialized: this.settingsManager.isInitialized()
      });

      let loadedApiKeys = 0;
      getAllProviders().forEach(provider => {
        const apiKey = settings.apiKeys[provider];
        if (apiKey && apiKey.trim().length > 0) {
          this.llmService.setApiKey(provider, apiKey);
          loadedApiKeys++;
          console.log(`✅ LLMIntegrationService: API key loaded for ${provider} (length: ${apiKey.length})`);
        } else {
          console.log(`⚠️ LLMIntegrationService: No API key found for ${provider}`);
        }
      });

      if (loadedApiKeys === 0) {
        console.warn('⚠️ LLMIntegrationService: No API keys configured for any provider. Agent Chat will not function until API keys are added in Settings.');
        console.log('🔍 LLMIntegrationService: Debug - Raw settings.apiKeys:', settings.apiKeys);
      } else {
        console.log(`✅ LLMIntegrationService: ${loadedApiKeys} API key(s) loaded successfully`);
      }

      // ✅ Set cost settings for budget enforcement
      this.llmService.setCostSettings(settings.cost);
      console.log('✅ LLMIntegrationService: Cost settings loaded for budget enforcement');

      // ✅ Initialize cost tracker
      await costTracker.initialize();
      console.log('✅ LLMIntegrationService: Cost tracker initialized');

      // ✅ Initialize alert manager
      await alertManager.initialize();
      console.log('✅ LLMIntegrationService: Alert manager initialized');

      // Listen for settings changes to update API keys and cost settings
      this.settingsManager.onSettingsChange((newSettings) => {
        console.log('🔄 LLMIntegrationService: Settings changed, updating API keys and cost settings');
        this.updateApiKeys(newSettings.apiKeys);
        this.updateCostSettings(newSettings.cost);
      });

      this.initialized = true;
      console.log('✅ LLMIntegrationService: Initialized successfully');
    } catch (error) {
      console.error('❌ LLMIntegrationService: Failed to initialize:', error);
      throw error;
    }
  }

  private updateApiKeys(apiKeys: Record<string, string>): void {
    let updatedKeys = 0;
    getAllProviders().forEach(provider => {
      const apiKey = apiKeys[provider];
      if (apiKey) {
        this.llmService.setApiKey(provider, apiKey);
        updatedKeys++;
        console.log(`LLMIntegrationService: API key updated for ${provider}`);
      }
    });

    console.log(`✅ LLMIntegrationService: ${updatedKeys} API key(s) updated`);
    this.llmService.logApiKeyStatus();
  }

  public async reloadApiKeys(): Promise<void> {
    // ✅ Ensure SettingsManager is initialized before reloading
    await this.waitForSettingsManagerInitialization();
    const settings = this.settingsManager.getSettings();
    this.updateApiKeys(settings.apiKeys);
  }

  private updateCostSettings(costSettings: any): void {
    this.llmService.setCostSettings(costSettings);
    console.log('LLMIntegrationService: Cost settings updated for budget enforcement');
  }

  public getLLMService(): LLMRequestService {
    return this.llmService;
  }

  public getSettingsManager(): SettingsManager {
    return this.settingsManager;
  }

  public async validateAllApiKeys(): Promise<Record<LLMProvider, boolean>> {
    const results: Record<LLMProvider, boolean> = {} as Record<LLMProvider, boolean>;
    // ✅ Ensure SettingsManager is initialized before validating
    await this.waitForSettingsManagerInitialization();
    const settings = this.settingsManager.getSettings();

    for (const provider of getAllProviders()) {
      const apiKey = settings.apiKeys[provider];
      if (apiKey) {
        try {
          results[provider] = await this.llmService.validateApiKey(provider, apiKey);
        } catch (error) {
          console.error(`Failed to validate API key for ${provider}:`, error);
          results[provider] = false;
        }
      } else {
        results[provider] = false;
      }
    }

    return results;
  }

  public isInitialized(): boolean {
    return this.initialized;
  }

  /**
   * ✅ CRITICAL FIX: Non-blocking SettingsManager initialization check
   * Replaces polling loop with immediate check and graceful fallback
   */
  private async waitForSettingsManagerInitialization(): Promise<void> {
    console.log('🔄 LLMIntegrationService: Checking SettingsManager initialization...');

    // Since we're using the global singleton, it should already be initialized
    // or will initialize synchronously. No need for polling loops.
    if (!this.settingsManager.isInitialized()) {
      console.log('🔄 LLMIntegrationService: SettingsManager not initialized, attempting force reload...');
      try {
        await this.settingsManager.forceReloadSettings();
        console.log('✅ LLMIntegrationService: SettingsManager force reload completed');
      } catch (reloadError) {
        console.warn('⚠️ LLMIntegrationService: Force reload failed, proceeding with defaults:', reloadError);
      }
    } else {
      console.log('✅ LLMIntegrationService: SettingsManager already initialized');
    }
  }
}

// Export singleton instance
export const llmIntegration = LLMIntegrationService.getInstance();
