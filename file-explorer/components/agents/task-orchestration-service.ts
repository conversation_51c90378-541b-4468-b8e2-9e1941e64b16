// file-explorer/components/agents/task-orchestration-service.ts
// ✅ TASK 6.2: TaskOrchestrationService - Extract business logic from UI components

import { CompleteAgentManager } from './complete-agent-manager';

// ✅ Task Orchestration Interfaces
export interface TaskDecomposition {
  parentTaskId: string;
  subtasks: SubTask[];
  dependencies: TaskDependency[];
  estimatedDuration: number;
  complexity: 'low' | 'medium' | 'high';
}

export interface SubTask {
  id: string;
  description: string;
  agent: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  dependencies: string[];
  estimatedDuration: number;
  metadata?: {
    kanbanCardId?: string;
    [key: string]: any;
  };
}

export interface TaskDependency {
  taskId: string;
  dependsOn: string[];
  type: 'sequential' | 'parallel' | 'conditional';
}

export interface OrchestrationResult {
  parentTaskId: string;
  subtaskIds: string[];
  kanbanCardIds: string[];
  coordinationStats: {
    total: number;
    ready: number;
    waiting: number;
    completed: number;
    failed: number;
  };
  statusSummary: {
    successRate: number;
    averageCompletionTime: number;
    totalErrors: number;
  };
}

export interface OrchestrationCallbacks {
  onStatusUpdate?: (update: { taskId: string; status: string; progress?: number }) => void;
  onProgressUpdate?: (taskId: string, progress: number) => void;
  onKanbanUpdate?: (taskId: string, cardId: string, status: string) => void;
  onTaskReady?: (taskId: string, agentId: string) => void;
  onTaskStart?: (taskId: string, agentId: string) => void;
  onTaskComplete?: (taskId: string, agentId: string, result: any) => void;
  onTaskError?: (taskId: string, agentId: string, error: string, willRetry: boolean) => void;
  onTaskFailed?: (taskId: string, agentId: string, finalError: string) => void;
  onDependencyResolved?: (taskId: string, dependencyId: string) => void;
}

// ✅ TaskOrchestrationService Class
export class TaskOrchestrationService {
  private static instance: TaskOrchestrationService;
  private agentManager: CompleteAgentManager;
  private callbacks: OrchestrationCallbacks = {};

  private constructor(agentManager: CompleteAgentManager) {
    this.agentManager = agentManager;
  }

  public static getInstance(agentManager: CompleteAgentManager): TaskOrchestrationService {
    if (!TaskOrchestrationService.instance) {
      TaskOrchestrationService.instance = new TaskOrchestrationService(agentManager);
    }
    return TaskOrchestrationService.instance;
  }

  // ✅ Set orchestration callbacks
  public setCallbacks(callbacks: OrchestrationCallbacks): void {
    this.callbacks = { ...this.callbacks, ...callbacks };
  }

  // ✅ Orchestrate Micromanager Task
  public async orchestrateMicromanagerTask(task: string): Promise<OrchestrationResult> {
    try {
      console.log('TaskOrchestrationService: Starting task orchestration for:', task);

      // Import required services dynamically
      const { TaskOrchestrator } = await import('./task-orchestrator');
      const { KanbanTaskBridge } = await import('./kanban-task-bridge');
      const { AgentTaskCoordinator } = await import('./agent-task-coordinator');
      const { TaskStatusService } = await import('./task-status-service');

      // Step 1: Decompose the task
      const decomposition = TaskOrchestrator.decompose(task);
      console.log(`TaskOrchestrationService: Decomposed into ${decomposition.subtasks.length} subtasks`);

      // Step 2: Submit parent task to Micromanager
      const parentTaskId = await this.agentManager.submitTask(
        task,
        undefined,
        'high',
        {
          decomposition,
          isOrchestrationTask: true,
          originalTaskId: decomposition.parentTaskId
        }
      );

      // Step 3: Create Kanban cards for subtasks
      const cardResults = await KanbanTaskBridge.createCardsFromSubtasks(decomposition.subtasks);
      console.log(`TaskOrchestrationService: Created ${cardResults.success.length} Kanban cards`);

      // Step 4: Link Kanban cards to subtasks
      await this.linkKanbanCardsToSubtasks(decomposition.subtasks, cardResults.success);

      // Step 5: Set up coordination system
      const taskCoordinator = AgentTaskCoordinator.getInstance(this.agentManager);
      const statusService = TaskStatusService.getInstance();

      // Step 6: Configure coordination callbacks
      await this.setupCoordinationCallbacks(taskCoordinator, statusService, decomposition);

      // Step 7: Register and execute tasks
      await taskCoordinator.registerTasks(decomposition.subtasks);
      await this.dispatchTasksToAgents(taskCoordinator, cardResults.success);
      await taskCoordinator.executeCoordinatedTasks(decomposition.subtasks);

      // Step 8: Set created card IDs for completion tracking
      const createdCardIds = cardResults.success.map(card => card.id);
      TaskOrchestrator.setLastCreatedCardIds(createdCardIds);

      // Step 9: Get final stats
      const coordinationStats = taskCoordinator.getCoordinationStats();
      const statusSummary = statusService.getStatusSummary();

      const result: OrchestrationResult = {
        parentTaskId,
        subtaskIds: decomposition.subtasks.map(st => st.id),
        kanbanCardIds: createdCardIds,
        coordinationStats: {
          total: coordinationStats.total,
          ready: coordinationStats.ready,
          waiting: coordinationStats.waiting,
          completed: coordinationStats.completed || 0,
          failed: coordinationStats.failed || 0
        },
        statusSummary: {
          successRate: statusSummary.metrics.successRate,
          averageCompletionTime: statusSummary.metrics.averageCompletionTime || 0,
          totalErrors: statusSummary.metrics.totalErrors || 0
        }
      };

      console.log('TaskOrchestrationService: Orchestration complete:', result);
      return result;

    } catch (error) {
      console.error('TaskOrchestrationService: Orchestration failed:', error);
      throw error;
    }
  }

  // ✅ Link Kanban cards to subtasks
  private async linkKanbanCardsToSubtasks(subtasks: SubTask[], kanbanCards: any[]): Promise<void> {
    const { KanbanTaskBridge } = await import('./kanban-task-bridge');

    for (let i = 0; i < subtasks.length; i++) {
      const subtask = subtasks[i];
      const correspondingCard = kanbanCards[i];

      if (correspondingCard) {
        // Add Kanban card ID to subtask metadata
        subtask.metadata = {
          ...subtask.metadata,
          kanbanCardId: correspondingCard.id
        };

        // Link task to card for bidirectional reference
        await KanbanTaskBridge.linkTaskToCard(subtask.id, correspondingCard.id, subtask.agent);
      }
    }
  }

  // ✅ Setup coordination callbacks
  private async setupCoordinationCallbacks(
    taskCoordinator: any,
    statusService: any,
    decomposition: TaskDecomposition
  ): Promise<void> {
    // Configure status service callbacks
    statusService.setCallbacks({
      onStatusUpdate: (update: any) => {
        console.log(`📊 Status Update: Task ${update.taskId} -> ${update.status}${update.progress ? ` (${update.progress}%)` : ''}`);
        if (this.callbacks.onStatusUpdate) {
          this.callbacks.onStatusUpdate(update);
        }
      },
      onProgressUpdate: (taskId: string, progress: number) => {
        console.log(`📈 Progress: Task ${taskId} -> ${progress}%`);
        if (this.callbacks.onProgressUpdate) {
          this.callbacks.onProgressUpdate(taskId, progress);
        }
      },
      onKanbanUpdate: (taskId: string, cardId: string, status: string) => {
        console.log(`📋 Kanban: Card ${cardId} for task ${taskId} -> ${status}`);
        if (this.callbacks.onKanbanUpdate) {
          this.callbacks.onKanbanUpdate(taskId, cardId, status);
        }
      }
    });

    // Configure coordination callbacks
    taskCoordinator.setCallbacks({
      onTaskReady: (taskId: string, agentId: string) => {
        console.log(`🟢 Task ${taskId} is ready for execution on agent ${agentId}`);
        const subtask = decomposition.subtasks.find(st => st.id === taskId);
        statusService.updateTaskStatus(taskId, agentId, 'pending', {
          message: 'Task ready for execution',
          kanbanCardId: subtask?.metadata?.kanbanCardId
        });
        if (this.callbacks.onTaskReady) {
          this.callbacks.onTaskReady(taskId, agentId);
        }
      },
      onTaskStart: (taskId: string, agentId: string) => {
        console.log(`🚀 Task ${taskId} started on agent ${agentId}`);
        const subtask = decomposition.subtasks.find(st => st.id === taskId);
        statusService.updateTaskStatus(taskId, agentId, 'running', {
          progress: 10,
          message: 'Task execution started',
          kanbanCardId: subtask?.metadata?.kanbanCardId
        });
        if (this.callbacks.onTaskStart) {
          this.callbacks.onTaskStart(taskId, agentId);
        }
      },
      onTaskComplete: async (taskId: string, agentId: string, result: any) => {
        console.log(`✅ Task ${taskId} completed by agent ${agentId}`);
        const subtask = decomposition.subtasks.find(st => st.id === taskId);
        await statusService.reportCompletion(taskId, agentId, result, subtask?.metadata?.kanbanCardId);
        if (this.callbacks.onTaskComplete) {
          this.callbacks.onTaskComplete(taskId, agentId, result);
        }
      },
      onTaskError: async (taskId: string, agentId: string, error: string, willRetry: boolean) => {
        console.error(`❌ Task ${taskId} ${willRetry ? 'failed (will retry)' : 'failed permanently'} on agent ${agentId}: ${error}`);
        const subtask = decomposition.subtasks.find(st => st.id === taskId);
        await statusService.reportError(taskId, agentId, error, willRetry, subtask?.metadata?.kanbanCardId);
        if (this.callbacks.onTaskError) {
          this.callbacks.onTaskError(taskId, agentId, error, willRetry);
        }
      },
      onTaskFailed: async (taskId: string, agentId: string, finalError: string) => {
        console.error(`💀 Task ${taskId} failed permanently on agent ${agentId}: ${finalError}`);
        const subtask = decomposition.subtasks.find(st => st.id === taskId);
        await statusService.updateTaskStatus(taskId, agentId, 'failed', {
          message: `Final failure: ${finalError}`,
          kanbanCardId: subtask?.metadata?.kanbanCardId
        });
        if (this.callbacks.onTaskFailed) {
          this.callbacks.onTaskFailed(taskId, agentId, finalError);
        }
      },
      onDependencyResolved: (taskId: string, dependencyId: string) => {
        console.log(`🔗 Dependency resolved: Task ${taskId} dependency ${dependencyId} completed`);
        if (this.callbacks.onDependencyResolved) {
          this.callbacks.onDependencyResolved(taskId, dependencyId);
        }
      }
    });
  }

  // ✅ Dispatch tasks to agents
  private async dispatchTasksToAgents(taskCoordinator: any, kanbanCards: any[]): Promise<void> {
    console.log(`🎯 Dispatching ${kanbanCards.length} Kanban cards to assigned agents`);
    
    for (const card of kanbanCards) {
      try {
        await taskCoordinator.dispatchToAgent(card);
      } catch (error) {
        console.error(`❌ Failed to dispatch card ${card.id} to agent:`, error);
      }
    }
  }

  // ✅ Get orchestration statistics
  public getOrchestrationStats(): any {
    // This would return current orchestration statistics
    // Implementation depends on the specific requirements
    return {
      activeOrchestrations: 0,
      totalTasksOrchestrated: 0,
      successRate: 0,
      averageOrchestrationTime: 0
    };
  }

  // ✅ Cancel orchestration
  public async cancelOrchestration(parentTaskId: string): Promise<boolean> {
    try {
      console.log(`TaskOrchestrationService: Cancelling orchestration ${parentTaskId}`);
      
      // Implementation would cancel all related subtasks and cleanup
      // This is a placeholder for the actual cancellation logic
      
      return true;
    } catch (error) {
      console.error('TaskOrchestrationService: Failed to cancel orchestration:', error);
      return false;
    }
  }

  // ✅ Get orchestration status
  public async getOrchestrationStatus(parentTaskId: string): Promise<any> {
    try {
      // Implementation would return the current status of an orchestration
      // This is a placeholder for the actual status retrieval logic
      
      return {
        parentTaskId,
        status: 'unknown',
        progress: 0,
        subtasks: [],
        errors: []
      };
    } catch (error) {
      console.error('TaskOrchestrationService: Failed to get orchestration status:', error);
      return null;
    }
  }
}

// ✅ Export singleton factory function
export const createTaskOrchestrationService = (agentManager: CompleteAgentManager): TaskOrchestrationService => {
  return TaskOrchestrationService.getInstance(agentManager);
};
