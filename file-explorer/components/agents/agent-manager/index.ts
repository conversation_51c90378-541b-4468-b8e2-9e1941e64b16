// components/agents/agent-manager/index.ts
// Barrel export file for agent manager modules

// Board Agent Service
export { BoardAgentService, IBoardAgentService } from './board-agent-service';

// Agent Lifecycle Management
export { AgentLifecycle, IAgentLifecycle } from './agent-lifecycle';

// Task Assignment
export { TaskAssignment, ITaskAssignment } from './task-assignment';

// Task Execution
export { TaskExecution, ITaskExecution } from './task-execution';

// Task Queue Management
export { TaskQueue, ITaskQueue, QueueStatus } from './task-queue';

// Agent Utilities
export { AgentUtils, IAgentUtils } from './agent-utils';

// File Operations
export { FileOperations, IFileOperations } from './file-operations';

// Re-export types from parent module
export type { Agent, Task } from '../types';
