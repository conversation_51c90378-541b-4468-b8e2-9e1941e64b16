// components/agents/agent-manager/task-execution.ts

import { Agent, Task } from '../types';
import { agentIPCBridge } from '@/lib/agent-ipc-bridge';
import { logTaskExecutionSucceeded, logTaskExecutionFailed, logTaskExecutionSkipped } from '../../background/logger';

export interface ITaskExecution {
  executeTask(task: Task, agent: Agent): Promise<any>;
  processTaskResult(taskId: string, result: any, agentId: string): Promise<void>;
  handleTaskError(taskId: string, error: Error, agentId: string): Promise<void>;
  validateTaskOutput(result: any, task: Task): boolean;
}

export class TaskExecution implements ITaskExecution {
  async executeTask(task: Task, agent: Agent): Promise<any> {
    try {
      console.log(`Executing task ${task.id} with agent ${agent.id}`);

      // Validate task and agent
      if (!this.validateTaskExecution(task, agent)) {
        throw new Error('Task execution validation failed');
      }

      // Update agent status
      agent.status = 'busy';

      // Broadcast task execution start
      await agentIPCBridge.addMessage({
        agentId: agent.id,
        message: `Starting task execution: ${task.title}`,
        timestamp: Date.now(),
        type: 'info'
      });

      // Execute task based on type
      let result;
      switch (task.type) {
        case 'code-generation':
          result = await this.executeCodeGenerationTask(task, agent);
          break;
        case 'file-operation':
          result = await this.executeFileOperationTask(task, agent);
          break;
        case 'analysis':
          result = await this.executeAnalysisTask(task, agent);
          break;
        case 'testing':
          result = await this.executeTestingTask(task, agent);
          break;
        default:
          result = await this.executeGenericTask(task, agent);
      }

      // Process the result
      await this.processTaskResult(task.id!, result, agent.id);

      // Update agent status back to idle
      agent.status = 'idle';

      return result;
    } catch (error) {
      console.error(`Task execution failed for ${task.id}:`, error);
      await this.handleTaskError(task.id!, error as Error, agent.id);
      agent.status = 'idle';
      throw error;
    }
  }

  async processTaskResult(taskId: string, result: any, agentId: string): Promise<void> {
    try {
      console.log(`Processing result for task ${taskId} from agent ${agentId}`);

      // Validate the result
      if (!result || typeof result !== 'object') {
        throw new Error('Invalid task result format');
      }

      // Extract key information from result
      const { success, output, files, errors, metadata } = result;

      if (success) {
        // Log successful execution
        logTaskExecutionSucceeded(agentId, {
          taskId,
          outputGenerated: !!output,
          filesCreated: files ? files.length : 0,
          tokenUsage: metadata?.tokenUsage || 0,
          executionTime: metadata?.executionTime || 0,
          outputValidated: this.validateTaskOutput(result, { id: taskId } as Task)
        });

        // Broadcast success
        await agentIPCBridge.addMessage({
          agentId,
          message: `Task completed successfully: ${taskId}`,
          timestamp: Date.now(),
          type: 'success'
        });

        // Handle file outputs
        if (files && files.length > 0) {
          await this.handleFileOutputs(files, agentId);
        }
      } else {
        // Handle execution failure
        const errorMessage = errors ? errors.join(', ') : 'Unknown execution error';
        await this.handleTaskError(taskId, new Error(errorMessage), agentId);
      }

      console.log(`Task result processed for ${taskId}`);
    } catch (error) {
      console.error(`Failed to process task result for ${taskId}:`, error);
      await this.handleTaskError(taskId, error as Error, agentId);
    }
  }

  async handleTaskError(taskId: string, error: Error, agentId: string): Promise<void> {
    try {
      console.error(`Handling task error for ${taskId}:`, error);

      // Log failed execution
      logTaskExecutionFailed(agentId, {
        taskId,
        errorType: error.name || 'UnknownError',
        errorMessage: error.message,
        stackTrace: error.stack || '',
        recoverable: this.isRecoverableError(error),
        retryAttempted: false
      });

      // Broadcast error
      await agentIPCBridge.addMessage({
        agentId,
        message: `Task failed: ${taskId} - ${error.message}`,
        timestamp: Date.now(),
        type: 'error'
      });

      // Determine if task should be retried
      if (this.isRecoverableError(error)) {
        console.log(`Task ${taskId} marked for retry due to recoverable error`);
        // Note: Retry logic would be handled by the queue manager
      }
    } catch (handlingError) {
      console.error(`Failed to handle task error for ${taskId}:`, handlingError);
    }
  }

  validateTaskOutput(result: any, task: Task): boolean {
    try {
      // Basic validation
      if (!result || typeof result !== 'object') {
        return false;
      }

      // Check for required fields
      if (!result.hasOwnProperty('success')) {
        return false;
      }

      // Validate based on task type
      switch (task.type) {
        case 'code-generation':
          return this.validateCodeGenerationOutput(result);
        case 'file-operation':
          return this.validateFileOperationOutput(result);
        case 'analysis':
          return this.validateAnalysisOutput(result);
        default:
          return result.success === true;
      }
    } catch (error) {
      console.error('Failed to validate task output:', error);
      return false;
    }
  }

  private validateTaskExecution(task: Task, agent: Agent): boolean {
    // Check if task has required fields
    if (!task.id || !task.title || !task.type) {
      console.error('Task missing required fields');
      return false;
    }

    // Check if agent is available
    if (agent.status === 'busy') {
      console.error('Agent is busy and cannot execute task');
      return false;
    }

    // Check if agent has required capabilities
    if (task.requiredCapabilities) {
      const hasCapabilities = task.requiredCapabilities.every(cap =>
        agent.capabilities.includes(cap)
      );
      if (!hasCapabilities) {
        console.error('Agent lacks required capabilities for task');
        return false;
      }
    }

    return true;
  }

  private async executeCodeGenerationTask(task: Task, agent: Agent): Promise<any> {
    try {
      console.log(`Executing code generation task: ${task.title}`);
      
      // Simulate code generation execution
      const result = {
        success: true,
        output: `// Generated code for: ${task.title}\n// Agent: ${agent.name}\n// Timestamp: ${new Date().toISOString()}`,
        files: [`generated-${Date.now()}.ts`],
        metadata: {
          tokenUsage: 1500,
          executionTime: 3000,
          linesGenerated: 50
        }
      };

      return result;
    } catch (error) {
      console.error('Code generation task failed:', error);
      throw error;
    }
  }

  private async executeFileOperationTask(task: Task, agent: Agent): Promise<any> {
    try {
      console.log(`Executing file operation task: ${task.title}`);
      
      const result = {
        success: true,
        output: `File operation completed: ${task.description}`,
        files: task.context?.targetFiles || [],
        metadata: {
          operationType: task.context?.operation || 'unknown',
          executionTime: 1000
        }
      };

      return result;
    } catch (error) {
      console.error('File operation task failed:', error);
      throw error;
    }
  }

  private async executeAnalysisTask(task: Task, agent: Agent): Promise<any> {
    try {
      console.log(`Executing analysis task: ${task.title}`);
      
      const result = {
        success: true,
        output: `Analysis completed: ${task.description}`,
        analysis: {
          findings: [],
          recommendations: [],
          metrics: {}
        },
        metadata: {
          analysisType: task.context?.analysisType || 'general',
          executionTime: 2000
        }
      };

      return result;
    } catch (error) {
      console.error('Analysis task failed:', error);
      throw error;
    }
  }

  private async executeTestingTask(task: Task, agent: Agent): Promise<any> {
    try {
      console.log(`Executing testing task: ${task.title}`);
      
      const result = {
        success: true,
        output: `Tests executed: ${task.description}`,
        testResults: {
          passed: 0,
          failed: 0,
          skipped: 0
        },
        metadata: {
          testFramework: 'jest',
          executionTime: 5000
        }
      };

      return result;
    } catch (error) {
      console.error('Testing task failed:', error);
      throw error;
    }
  }

  private async executeGenericTask(task: Task, agent: Agent): Promise<any> {
    try {
      console.log(`Executing generic task: ${task.title}`);
      
      const result = {
        success: true,
        output: `Task completed: ${task.description}`,
        metadata: {
          taskType: task.type,
          executionTime: 1500
        }
      };

      return result;
    } catch (error) {
      console.error('Generic task failed:', error);
      throw error;
    }
  }

  private validateCodeGenerationOutput(result: any): boolean {
    return result.success && result.output && typeof result.output === 'string';
  }

  private validateFileOperationOutput(result: any): boolean {
    return result.success && result.files && Array.isArray(result.files);
  }

  private validateAnalysisOutput(result: any): boolean {
    return result.success && result.analysis && typeof result.analysis === 'object';
  }

  private isRecoverableError(error: Error): boolean {
    const recoverableErrors = [
      'NetworkError',
      'TimeoutError',
      'RateLimitError',
      'TemporaryError'
    ];
    
    return recoverableErrors.some(errorType => 
      error.name.includes(errorType) || error.message.includes(errorType)
    );
  }

  private async handleFileOutputs(files: string[], agentId: string): Promise<void> {
    try {
      // Broadcast file creation notifications
      for (const file of files) {
        await agentIPCBridge.addMessage({
          agentId,
          message: `Generated file: ${file}`,
          timestamp: Date.now(),
          type: 'info'
        });

        // Trigger file explorer refresh
        if (typeof window !== 'undefined' && window.electronAPI) {
          window.electronAPI.ipc.send('file-system-changed', { 
            type: 'create', 
            path: file 
          });
        }
      }
    } catch (error) {
      console.error('Failed to handle file outputs:', error);
    }
  }
}
