// components/agents/agent-manager/task-assignment.ts

import { Agent, Task } from '../types';
import { agentIPCBridge } from '@/lib/agent-ipc-bridge';
import { logTaskExecutionSkipped } from '../../background/logger';

export interface ITaskAssignment {
  submitTask(task: Task): Promise<string>;
  assignTaskToAgent(taskId: string, agentId: string): Promise<boolean>;
  enhanceTaskContext(task: Task): Promise<Task>;
  findBestAgentForTask(task: Task, agents: Agent[]): string | null;
  validateTaskRequirements(task: Task): boolean;
}

export class TaskAssignment implements ITaskAssignment {
  async submitTask(task: Task): Promise<string> {
    try {
      console.log('Submitting task:', task.title);

      // Validate task requirements
      if (!this.validateTaskRequirements(task)) {
        throw new Error('Task validation failed');
      }

      // Enhance task context
      const enhancedTask = await this.enhanceTaskContext(task);

      // Generate unique task ID if not provided
      const taskId = enhancedTask.id || `task-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      enhancedTask.id = taskId;

      // Broadcast task submission via IPC
      await agentIPCBridge.addMessage({
        agentId: 'system',
        message: `Task submitted: ${enhancedTask.title} (${taskId})`,
        timestamp: Date.now(),
        type: 'info'
      });

      console.log(`Task submitted successfully: ${taskId}`);
      return taskId;
    } catch (error) {
      console.error('Failed to submit task:', error);
      throw error;
    }
  }

  async assignTaskToAgent(taskId: string, agentId: string): Promise<boolean> {
    try {
      console.log(`Assigning task ${taskId} to agent ${agentId}`);

      // Broadcast task assignment via IPC
      await agentIPCBridge.addMessage({
        agentId,
        message: `Assigned task: ${taskId}`,
        timestamp: Date.now(),
        type: 'info'
      });

      // Notify other windows about task assignment
      if (typeof window !== 'undefined' && window.electronAPI) {
        window.electronAPI.ipc.send('agent-task-assigned', { agentId, taskId });
      }

      console.log(`Task ${taskId} assigned to agent ${agentId}`);
      return true;
    } catch (error) {
      console.error('Failed to assign task to agent:', error);
      return false;
    }
  }

  async enhanceTaskContext(task: Task): Promise<Task> {
    try {
      console.log('Enhancing task context for:', task.title);

      // Create enhanced task with additional context
      const enhancedTask: Task = {
        ...task,
        context: {
          ...task.context,
          timestamp: Date.now(),
          environment: 'development',
          projectPath: await this.getActiveProjectPath(),
          availableTools: this.getAvailableTools(),
          systemInfo: this.getSystemInfo()
        },
        metadata: {
          ...task.metadata,
          enhancedAt: new Date().toISOString(),
          enhancementVersion: '1.0'
        }
      };

      // Add file context if task involves file operations
      if (task.type === 'file-operation' || task.description?.includes('file')) {
        enhancedTask.context.fileContext = await this.getFileContext(task);
      }

      // Add code context if task involves coding
      if (task.type === 'code-generation' || task.description?.includes('code')) {
        enhancedTask.context.codeContext = await this.getCodeContext(task);
      }

      console.log('Task context enhanced successfully');
      return enhancedTask;
    } catch (error) {
      console.error('Failed to enhance task context:', error);
      // Return original task if enhancement fails
      return task;
    }
  }

  findBestAgentForTask(task: Task, agents: Agent[]): string | null {
    try {
      console.log('Finding best agent for task:', task.title);

      // Filter available agents
      const availableAgents = agents.filter(agent => 
        agent.status === 'idle' || agent.status === 'available'
      );

      if (availableAgents.length === 0) {
        console.warn('No available agents found');
        return null;
      }

      // Score agents based on task requirements
      const scoredAgents = availableAgents.map(agent => ({
        agent,
        score: this.calculateAgentScore(agent, task)
      }));

      // Sort by score (highest first)
      scoredAgents.sort((a, b) => b.score - a.score);

      const bestAgent = scoredAgents[0]?.agent;
      if (bestAgent) {
        console.log(`Best agent for task: ${bestAgent.name} (${bestAgent.id})`);
        return bestAgent.id;
      }

      return null;
    } catch (error) {
      console.error('Failed to find best agent for task:', error);
      return null;
    }
  }

  validateTaskRequirements(task: Task): boolean {
    try {
      // Check required fields
      if (!task.title || !task.description) {
        console.error('Task missing required fields (title, description)');
        return false;
      }

      // Check task type
      if (!task.type) {
        console.error('Task missing type');
        return false;
      }

      // Validate priority
      if (task.priority && !['low', 'medium', 'high', 'urgent'].includes(task.priority)) {
        console.error('Invalid task priority');
        return false;
      }

      // Check for active project if task requires it
      if (task.requiresProject && !this.hasActiveProject()) {
        console.error('Task requires active project but none is selected');
        
        // Log task execution skipped
        logTaskExecutionSkipped('system', {
          taskId: task.id || 'unknown',
          cause: 'no_active_project',
          reason: 'Task requires active project but none is selected',
          contextMissing: true,
          invalidTask: false
        });
        
        return false;
      }

      return true;
    } catch (error) {
      console.error('Failed to validate task requirements:', error);
      return false;
    }
  }

  private calculateAgentScore(agent: Agent, task: Task): number {
    let score = 0;

    // Base score for availability
    if (agent.status === 'idle') score += 10;
    else if (agent.status === 'available') score += 5;

    // Score based on capabilities match
    if (task.requiredCapabilities) {
      const matchingCapabilities = task.requiredCapabilities.filter(cap =>
        agent.capabilities.includes(cap)
      );
      score += matchingCapabilities.length * 5;
    }

    // Score based on role match
    if (task.preferredRole && agent.role === task.preferredRole) {
      score += 15;
    }

    // Score based on model capabilities
    if (task.requiresAdvancedModel && agent.model?.includes('claude-3-opus')) {
      score += 10;
    }

    return score;
  }

  private async getActiveProjectPath(): Promise<string | null> {
    try {
      const { activeProjectService } = await import('../../services/active-project-service');
      return activeProjectService.getActiveProjectPath();
    } catch (error) {
      console.error('Failed to get active project path:', error);
      return null;
    }
  }

  private getAvailableTools(): string[] {
    return [
      'file-operations',
      'terminal-commands',
      'code-analysis',
      'kanban-integration',
      'git-operations'
    ];
  }

  private getSystemInfo(): any {
    return {
      platform: typeof window !== 'undefined' ? navigator.platform : 'unknown',
      userAgent: typeof window !== 'undefined' ? navigator.userAgent : 'unknown',
      timestamp: Date.now()
    };
  }

  private async getFileContext(task: Task): Promise<any> {
    try {
      // Get file-related context for the task
      return {
        workingDirectory: await this.getActiveProjectPath(),
        fileExtensions: ['.ts', '.tsx', '.js', '.jsx', '.json'],
        encoding: 'utf-8'
      };
    } catch (error) {
      console.error('Failed to get file context:', error);
      return {};
    }
  }

  private async getCodeContext(task: Task): Promise<any> {
    try {
      // Get code-related context for the task
      return {
        language: 'typescript',
        framework: 'react',
        buildTool: 'next.js',
        packageManager: 'npm'
      };
    } catch (error) {
      console.error('Failed to get code context:', error);
      return {};
    }
  }

  private hasActiveProject(): boolean {
    try {
      // Check if there's an active project
      return typeof window !== 'undefined' && 
             window.localStorage && 
             !!window.localStorage.getItem('active-project-path');
    } catch (error) {
      console.error('Failed to check active project:', error);
      return false;
    }
  }
}
