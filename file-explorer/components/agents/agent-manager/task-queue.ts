// components/agents/agent-manager/task-queue.ts

import { Task } from '../types';
import { agentIPCBridge } from '@/lib/agent-ipc-bridge';
import { logTaskExecutionSkipped } from '../../background/logger';

export interface ITaskQueue {
  addTask(task: Task): Promise<void>;
  processQueue(): Promise<void>;
  retryFailedTask(taskId: string): Promise<boolean>;
  getQueueStatus(): QueueStatus;
  clearQueue(): void;
  pauseQueue(): void;
  resumeQueue(): void;
}

export interface QueueStatus {
  pending: number;
  processing: number;
  completed: number;
  failed: number;
  paused: boolean;
}

export class TaskQueue implements ITaskQueue {
  private taskQueue: Task[] = [];
  private activeTasks: Map<string, Task> = new Map();
  private completedTasks: Task[] = [];
  private failedTasks: Task[] = [];
  private isPaused: boolean = false;
  private isProcessing: boolean = false;
  private maxConcurrentTasks: number = 3;
  private retryAttempts: Map<string, number> = new Map();
  private maxRetries: number = 3;

  async addTask(task: Task): Promise<void> {
    try {
      console.log(`Adding task to queue: ${task.title} (${task.id})`);

      // Validate task before adding
      if (!this.validateTask(task)) {
        throw new Error('Invalid task cannot be added to queue');
      }

      // Set task status and timestamps
      task.status = 'pending';
      task.createdAt = task.createdAt || new Date().toISOString();
      task.updatedAt = new Date().toISOString();

      // Add to queue
      this.taskQueue.push(task);

      // Broadcast task added
      await agentIPCBridge.addMessage({
        agentId: 'system',
        message: `Task added to queue: ${task.title}`,
        timestamp: Date.now(),
        type: 'info'
      });

      // Start processing if not already running
      if (!this.isProcessing && !this.isPaused) {
        this.processQueue();
      }

      console.log(`Task added to queue successfully: ${task.id}`);
    } catch (error) {
      console.error('Failed to add task to queue:', error);
      throw error;
    }
  }

  async processQueue(): Promise<void> {
    if (this.isProcessing || this.isPaused) {
      return;
    }

    this.isProcessing = true;
    console.log('Starting queue processing...');

    try {
      while (this.taskQueue.length > 0 && !this.isPaused) {
        // Check if we can process more tasks
        if (this.activeTasks.size >= this.maxConcurrentTasks) {
          console.log('Max concurrent tasks reached, waiting...');
          await this.waitForTaskCompletion();
          continue;
        }

        // Get next task from queue
        const task = this.taskQueue.shift();
        if (!task) {
          break;
        }

        // Start processing task
        this.processTask(task);
      }
    } catch (error) {
      console.error('Queue processing error:', error);
    } finally {
      this.isProcessing = false;
      console.log('Queue processing completed');
    }
  }

  async retryFailedTask(taskId: string): Promise<boolean> {
    try {
      console.log(`Retrying failed task: ${taskId}`);

      // Find failed task
      const failedTaskIndex = this.failedTasks.findIndex(task => task.id === taskId);
      if (failedTaskIndex === -1) {
        console.warn(`Failed task ${taskId} not found`);
        return false;
      }

      const task = this.failedTasks[failedTaskIndex];

      // Check retry limit
      const currentRetries = this.retryAttempts.get(taskId) || 0;
      if (currentRetries >= this.maxRetries) {
        console.warn(`Task ${taskId} has exceeded max retry attempts`);
        return false;
      }

      // Remove from failed tasks and add back to queue
      this.failedTasks.splice(failedTaskIndex, 1);
      task.status = 'pending';
      task.updatedAt = new Date().toISOString();
      
      // Increment retry count
      this.retryAttempts.set(taskId, currentRetries + 1);

      // Add back to queue
      await this.addTask(task);

      // Broadcast retry
      await agentIPCBridge.addMessage({
        agentId: 'system',
        message: `Retrying task: ${task.title} (attempt ${currentRetries + 1}/${this.maxRetries})`,
        timestamp: Date.now(),
        type: 'info'
      });

      console.log(`Task ${taskId} queued for retry`);
      return true;
    } catch (error) {
      console.error(`Failed to retry task ${taskId}:`, error);
      return false;
    }
  }

  getQueueStatus(): QueueStatus {
    return {
      pending: this.taskQueue.length,
      processing: this.activeTasks.size,
      completed: this.completedTasks.length,
      failed: this.failedTasks.length,
      paused: this.isPaused
    };
  }

  clearQueue(): void {
    console.log('Clearing task queue...');
    
    // Clear all queues
    this.taskQueue = [];
    this.completedTasks = [];
    this.failedTasks = [];
    this.retryAttempts.clear();

    // Cancel active tasks
    this.activeTasks.forEach(task => {
      task.status = 'cancelled';
      task.updatedAt = new Date().toISOString();
    });
    this.activeTasks.clear();

    console.log('Task queue cleared');
  }

  pauseQueue(): void {
    console.log('Pausing task queue...');
    this.isPaused = true;
  }

  resumeQueue(): void {
    console.log('Resuming task queue...');
    this.isPaused = false;
    
    // Resume processing if there are pending tasks
    if (this.taskQueue.length > 0 && !this.isProcessing) {
      this.processQueue();
    }
  }

  private async processTask(task: Task): Promise<void> {
    try {
      console.log(`Processing task: ${task.title} (${task.id})`);

      // Add to active tasks
      task.status = 'processing';
      task.updatedAt = new Date().toISOString();
      this.activeTasks.set(task.id!, task);

      // Broadcast task processing start
      await agentIPCBridge.addMessage({
        agentId: 'system',
        message: `Processing task: ${task.title}`,
        timestamp: Date.now(),
        type: 'info'
      });

      // Simulate task processing (in real implementation, this would call the execution service)
      await this.simulateTaskExecution(task);

      // Move to completed tasks
      this.activeTasks.delete(task.id!);
      task.status = 'completed';
      task.completedAt = new Date().toISOString();
      task.updatedAt = new Date().toISOString();
      this.completedTasks.push(task);

      console.log(`Task completed: ${task.id}`);
    } catch (error) {
      console.error(`Task processing failed: ${task.id}`, error);
      await this.handleTaskFailure(task, error as Error);
    }
  }

  private async simulateTaskExecution(task: Task): Promise<void> {
    // Simulate execution time based on task complexity
    const executionTime = this.calculateExecutionTime(task);
    await new Promise(resolve => setTimeout(resolve, executionTime));

    // Simulate random failures for testing
    if (Math.random() < 0.1) { // 10% failure rate
      throw new Error('Simulated task execution failure');
    }
  }

  private calculateExecutionTime(task: Task): number {
    // Base execution time
    let time = 1000;

    // Adjust based on task type
    switch (task.type) {
      case 'code-generation':
        time = 3000;
        break;
      case 'analysis':
        time = 2000;
        break;
      case 'testing':
        time = 5000;
        break;
      case 'file-operation':
        time = 1000;
        break;
      default:
        time = 1500;
    }

    // Add random variation
    return time + Math.random() * 1000;
  }

  private async handleTaskFailure(task: Task, error: Error): Promise<void> {
    try {
      // Remove from active tasks
      this.activeTasks.delete(task.id!);

      // Update task status
      task.status = 'failed';
      task.error = error.message;
      task.updatedAt = new Date().toISOString();

      // Check if task should be retried
      const currentRetries = this.retryAttempts.get(task.id!) || 0;
      if (currentRetries < this.maxRetries && this.isRetryableError(error)) {
        console.log(`Task ${task.id} will be retried (attempt ${currentRetries + 1}/${this.maxRetries})`);
        
        // Add back to queue for retry
        this.retryAttempts.set(task.id!, currentRetries + 1);
        task.status = 'pending';
        this.taskQueue.unshift(task); // Add to front of queue for immediate retry
      } else {
        // Move to failed tasks
        this.failedTasks.push(task);
        
        // Log task execution skipped due to failure
        logTaskExecutionSkipped('system', {
          taskId: task.id!,
          cause: 'execution_failed',
          reason: `Task failed after ${currentRetries} retry attempts: ${error.message}`,
          contextMissing: false,
          invalidTask: false
        });
      }

      // Broadcast task failure
      await agentIPCBridge.addMessage({
        agentId: 'system',
        message: `Task failed: ${task.title} - ${error.message}`,
        timestamp: Date.now(),
        type: 'error'
      });
    } catch (handlingError) {
      console.error('Failed to handle task failure:', handlingError);
    }
  }

  private validateTask(task: Task): boolean {
    // Check required fields
    if (!task.id || !task.title || !task.type) {
      console.error('Task missing required fields');
      return false;
    }

    // Check for duplicate task IDs
    const isDuplicate = this.taskQueue.some(t => t.id === task.id) ||
                       this.activeTasks.has(task.id!) ||
                       this.completedTasks.some(t => t.id === task.id) ||
                       this.failedTasks.some(t => t.id === task.id);

    if (isDuplicate) {
      console.error(`Duplicate task ID: ${task.id}`);
      return false;
    }

    return true;
  }

  private isRetryableError(error: Error): boolean {
    const retryableErrors = [
      'NetworkError',
      'TimeoutError',
      'RateLimitError',
      'TemporaryError'
    ];
    
    return retryableErrors.some(errorType => 
      error.name.includes(errorType) || error.message.includes(errorType)
    );
  }

  private async waitForTaskCompletion(): Promise<void> {
    // Wait for at least one active task to complete
    return new Promise(resolve => {
      const checkInterval = setInterval(() => {
        if (this.activeTasks.size < this.maxConcurrentTasks) {
          clearInterval(checkInterval);
          resolve();
        }
      }, 100);
    });
  }
}
