// components/agents/agent-manager/board-agent-service.ts

// Import IPC bridges for cross-window sync
import { boardIPCBridge } from '../../kanban/lib/board-ipc-bridge';
import { agentIPCBridge } from '@/lib/agent-ipc-bridge';

// ✅ Task 72: Enhanced IBoardAgentService with full Kanban API access for Micromanager
export interface IBoardAgentService {
  // Card operations
  createTaskCard(task: any, agentId: string): Promise<any>;
  moveCardToColumn(cardId: string, columnId: string, agentId: string): Promise<any>;
  addCardDependency(cardId: string, dependencyCardId: string, agentId: string): Promise<any>;
  updateCardProgress(cardId: string, progress: number, agentId: string): Promise<any>;

  // ✅ Full CRUD operations for Micromanager
  createBoard?(boardName: string, agentId: string): Promise<any>;
  addColumn?(boardId: string, columnName: string, agentId: string): Promise<any>;
  removeColumn?(boardId: string, columnId: string, agentId: string): Promise<any>;
  addSwimlane?(boardId: string, swimlaneName: string, agentId: string): Promise<any>;
  removeSwimlane?(boardId: string, swimlaneId: string, agentId: string): Promise<any>;
  assignAgent?(cardId: string, agentId: string, assignerAgentId: string): Promise<any>;
  linkCardToOrchestration?(cardId: string, orchestrationId: string, agentId: string): Promise<any>;
  deleteCard?(cardId: string, agentId: string): Promise<any>;
}

// Implementation of IBoardAgentService using IPC bridge
export class BoardAgentService implements IBoardAgentService {
  async createTaskCard(task: any, agentId: string): Promise<any> {
    try {
      // Use board IPC bridge to create card and sync across windows
      const result = await boardIPCBridge.createCard('default-board', 'column-1', {
        title: task.title || task.description || 'Agent Task',
        description: task.description || task.title || '',
        priority: task.priority || 'medium',
        projectId: task.projectId || '',
        tags: task.tags || [],
        progress: 0,
        labels: [],
        agentAssignments: [{ agentId, assignedAt: new Date().toISOString() }],
        dependencies: [],
        resourceMetrics: { tokenUsage: 0, cpuTime: 0, memoryUsage: 0 },
        taskHistory: [{
          timestamp: new Date().toISOString(),
          action: 'created',
          agentId,
          details: `Task created by agent ${agentId}`
        }]
      });

      // Broadcast agent action via IPC
      await agentIPCBridge.addMessage({
        agentId,
        message: `Created Kanban card: ${task.title || task.description}`,
        timestamp: Date.now(),
        type: 'info'
      });

      return result;
    } catch (error) {
      console.error('Failed to create task card via IPC:', error);
      return null;
    }
  }

  async moveCardToColumn(cardId: string, columnId: string, agentId: string): Promise<any> {
    try {
      // ✅ Use the enhanced moveCardToColumn method that handles source column detection
      const result = await boardIPCBridge.moveCardToColumn('main', cardId, columnId, agentId);

      if (result) {
        // Broadcast agent action via IPC
        await agentIPCBridge.addMessage({
          agentId,
          message: `Moved card ${cardId} to column ${columnId}`,
          timestamp: Date.now(),
          type: 'info'
        });
        console.log(`BoardAgentService: Successfully moved card ${cardId} to column ${columnId}`);
      } else {
        console.warn(`BoardAgentService: Failed to move card ${cardId} to column ${columnId} - no result returned`);
      }

      return result;
    } catch (error) {
      console.error('BoardAgentService: Failed to move card via IPC:', error);
      return null;
    }
  }

  async addCardDependency(cardId: string, dependencyCardId: string, agentId: string): Promise<any> {
    try {
      // Note: This would need to be implemented in board IPC bridge
      console.log(`Agent ${agentId} adding dependency ${dependencyCardId} to card ${cardId}`);

      // Broadcast agent action via IPC
      await agentIPCBridge.addMessage({
        agentId,
        message: `Added dependency ${dependencyCardId} to card ${cardId}`,
        timestamp: Date.now(),
        type: 'info'
      });

      return true;
    } catch (error) {
      console.error('Failed to add card dependency:', error);
      return null;
    }
  }

  async updateCardProgress(cardId: string, progress: number, agentId: string): Promise<any> {
    try {
      // ✅ Use the new updateCardProgress method from board IPC bridge
      const result = await boardIPCBridge.updateCardProgress('main', cardId, progress, agentId);

      if (result) {
        // Broadcast agent action via IPC
        await agentIPCBridge.addMessage({
          agentId,
          message: `Updated card ${cardId} progress to ${progress}%`,
          timestamp: Date.now(),
          type: 'info'
        });
        console.log(`BoardAgentService: Successfully updated card ${cardId} progress to ${progress}%`);
      } else {
        console.warn(`BoardAgentService: Failed to update card ${cardId} progress to ${progress}% - no result returned`);
      }

      return result;
    } catch (error) {
      console.error('BoardAgentService: Failed to update card progress via IPC:', error);
      return null;
    }
  }

  // ✅ Task 79: Real CRUD operations for Micromanager using IPC bridge
  async createBoard?(boardName: string, agentId: string): Promise<any> {
    try {
      console.log(`BoardAgentService: Creating board "${boardName}" for agent ${agentId}`);

      // ✅ Use real boardIPCBridge to create board
      const newBoard = await boardIPCBridge.createBoard(boardName, `Board created by agent ${agentId}`);

      if (newBoard) {
        // Log success message
        await agentIPCBridge.addMessage({
          agentId,
          message: `Successfully created board: ${boardName} (ID: ${newBoard.id})`,
          timestamp: Date.now(),
          type: 'info'
        });

        console.log(`BoardAgentService: Successfully created board "${boardName}" with ID: ${newBoard.id}`);
        return { success: true, board: newBoard, agentId };
      } else {
        throw new Error('Board creation returned null - IPC bridge may not be available');
      }
    } catch (error) {
      console.error('BoardAgentService: Failed to create board:', error);

      // Log error message
      await agentIPCBridge.addMessage({
        agentId,
        message: `Failed to create board: ${boardName} - ${error instanceof Error ? error.message : 'Unknown error'}`,
        timestamp: Date.now(),
        type: 'error'
      });

      throw new Error(`Micromanager failed to create Kanban board: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async addColumn?(boardId: string, columnName: string, agentId: string): Promise<any> {
    try {
      console.log(`BoardAgentService: Adding column "${columnName}" to board ${boardId} for agent ${agentId}`);

      // ✅ Use real boardIPCBridge to add column
      const newColumn = await boardIPCBridge.addColumn(boardId, columnName);

      if (newColumn) {
        // Log success message
        await agentIPCBridge.addMessage({
          agentId,
          message: `Successfully added column: ${columnName} to board ${boardId} (Column ID: ${newColumn.id})`,
          timestamp: Date.now(),
          type: 'info'
        });

        console.log(`BoardAgentService: Successfully added column "${columnName}" with ID: ${newColumn.id} to board ${boardId}`);
        return { success: true, column: newColumn, boardId, agentId };
      } else {
        throw new Error('Column creation returned null - IPC bridge may not be available or board not found');
      }
    } catch (error) {
      console.error('BoardAgentService: Failed to add column:', error);

      // Log error message
      await agentIPCBridge.addMessage({
        agentId,
        message: `Failed to add column: ${columnName} to board ${boardId} - ${error instanceof Error ? error.message : 'Unknown error'}`,
        timestamp: Date.now(),
        type: 'error'
      });

      throw new Error(`Micromanager failed to manage Kanban columns: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async removeColumn?(boardId: string, columnId: string, agentId: string): Promise<any> {
    try {
      console.log(`BoardAgentService: Removing column ${columnId} from board ${boardId} for agent ${agentId}`);
      await agentIPCBridge.addMessage({
        agentId,
        message: `Removed column: ${columnId} from board ${boardId}`,
        timestamp: Date.now(),
        type: 'info'
      });
      return { success: true, boardId, columnId, agentId };
    } catch (error) {
      console.error('BoardAgentService: Failed to remove column:', error);
      throw new Error(`Micromanager lacks permission to remove Kanban columns: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async addSwimlane?(boardId: string, swimlaneName: string, agentId: string): Promise<any> {
    try {
      console.log(`BoardAgentService: Adding swimlane "${swimlaneName}" to board ${boardId} for agent ${agentId}`);
      await agentIPCBridge.addMessage({
        agentId,
        message: `Added swimlane: ${swimlaneName} to board ${boardId}`,
        timestamp: Date.now(),
        type: 'info'
      });
      return { success: true, boardId, swimlaneName, agentId };
    } catch (error) {
      console.error('BoardAgentService: Failed to add swimlane:', error);
      throw new Error(`Micromanager lacks permission to manage Kanban swimlanes: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async removeSwimlane?(boardId: string, swimlaneId: string, agentId: string): Promise<any> {
    try {
      console.log(`BoardAgentService: Removing swimlane ${swimlaneId} from board ${boardId} for agent ${agentId}`);
      await agentIPCBridge.addMessage({
        agentId,
        message: `Removed swimlane: ${swimlaneId} from board ${boardId}`,
        timestamp: Date.now(),
        type: 'info'
      });
      return { success: true, boardId, swimlaneId, agentId };
    } catch (error) {
      console.error('BoardAgentService: Failed to remove swimlane:', error);
      throw new Error(`Micromanager lacks permission to remove Kanban swimlanes: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async assignAgent?(cardId: string, agentId: string, assignerAgentId: string): Promise<any> {
    try {
      console.log(`BoardAgentService: Assigning agent ${agentId} to card ${cardId} by ${assignerAgentId}`);
      await agentIPCBridge.addMessage({
        agentId: assignerAgentId,
        message: `Assigned agent ${agentId} to card ${cardId}`,
        timestamp: Date.now(),
        type: 'info'
      });
      return { success: true, cardId, agentId, assignerAgentId };
    } catch (error) {
      console.error('BoardAgentService: Failed to assign agent:', error);
      throw new Error(`Micromanager lacks permission to assign agents to Kanban cards: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async linkCardToOrchestration?(cardId: string, orchestrationId: string, agentId: string): Promise<any> {
    try {
      console.log(`BoardAgentService: Linking card ${cardId} to orchestration ${orchestrationId} for agent ${agentId}`);
      await agentIPCBridge.addMessage({
        agentId,
        message: `Linked card ${cardId} to orchestration ${orchestrationId}`,
        timestamp: Date.now(),
        type: 'info'
      });
      return { success: true, cardId, orchestrationId, agentId };
    } catch (error) {
      console.error('BoardAgentService: Failed to link card to orchestration:', error);
      throw new Error(`Micromanager lacks permission to link cards to orchestration: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async deleteCard?(cardId: string, agentId: string): Promise<any> {
    try {
      console.log(`BoardAgentService: Deleting card ${cardId} for agent ${agentId}`);
      await agentIPCBridge.addMessage({
        agentId,
        message: `Deleted card: ${cardId}`,
        timestamp: Date.now(),
        type: 'info'
      });
      return { success: true, cardId, agentId };
    } catch (error) {
      console.error('BoardAgentService: Failed to delete card:', error);
      throw new Error(`Micromanager lacks permission to delete Kanban cards: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}
