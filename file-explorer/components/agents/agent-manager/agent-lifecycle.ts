// components/agents/agent-manager/agent-lifecycle.ts

import { Agent } from '../types';
import { agentIPCBridge } from '@/lib/agent-ipc-bridge';

export interface IAgentLifecycle {
  initializeAgents(): Promise<void>;
  createAgent(agentData: Partial<Agent>): Promise<Agent>;
  setupAgentMiddleware(): void;
  getAgents(): Agent[];
  getAgent(agentId: string): Agent | undefined;
  updateAgent(agentId: string, updates: Partial<Agent>): Promise<Agent | null>;
  deleteAgent(agentId: string): Promise<boolean>;
}

export class AgentLifecycle implements IAgentLifecycle {
  private agents: Agent[] = [];

  async initializeAgents(): Promise<void> {
    try {
      console.log('Initializing agents...');

      // Load agents from storage or create defaults
      const storedAgents = this.loadStoredAgents();
      if (storedAgents.length > 0) {
        this.agents = storedAgents;
        console.log(`Loaded ${storedAgents.length} agents from storage`);
      } else {
        // Create default agents if none exist
        await this.createDefaultAgents();
      }

      // Setup agent middleware
      this.setupAgentMiddleware();

      console.log('Agent initialization complete');
    } catch (error) {
      console.error('Failed to initialize agents:', error);
      throw error;
    }
  }

  async createAgent(agentData: Partial<Agent>): Promise<Agent> {
    try {
      const newAgent: Agent = {
        id: agentData.id || `agent-${Date.now()}`,
        name: agentData.name || 'Unnamed Agent',
        role: agentData.role || 'general',
        status: agentData.status || 'idle',
        capabilities: agentData.capabilities || [],
        model: agentData.model || 'claude-3-haiku-20240307',
        provider: agentData.provider || 'anthropic',
        systemPrompt: agentData.systemPrompt || '',
        temperature: agentData.temperature || 0.7,
        maxTokens: agentData.maxTokens || 4000,
        isDefault: agentData.isDefault || false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        ...agentData
      };

      this.agents.push(newAgent);
      this.saveAgents();

      // Broadcast agent creation via IPC
      await agentIPCBridge.addMessage({
        agentId: 'system',
        message: `Created new agent: ${newAgent.name} (${newAgent.id})`,
        timestamp: Date.now(),
        type: 'info'
      });

      console.log(`Created agent: ${newAgent.name} (${newAgent.id})`);
      return newAgent;
    } catch (error) {
      console.error('Failed to create agent:', error);
      throw error;
    }
  }

  setupAgentMiddleware(): void {
    try {
      // Setup IPC message handling for agent communication
      if (typeof window !== 'undefined' && window.electronAPI) {
        // Listen for agent status updates from other windows
        window.electronAPI.ipc.on('agent-status-update', (data: any) => {
          this.handleAgentStatusUpdate(data);
        });

        // Listen for agent task assignments
        window.electronAPI.ipc.on('agent-task-assigned', (data: any) => {
          this.handleAgentTaskAssignment(data);
        });
      }

      console.log('Agent middleware setup complete');
    } catch (error) {
      console.error('Failed to setup agent middleware:', error);
    }
  }

  getAgents(): Agent[] {
    return [...this.agents];
  }

  getAgent(agentId: string): Agent | undefined {
    return this.agents.find(agent => agent.id === agentId);
  }

  async updateAgent(agentId: string, updates: Partial<Agent>): Promise<Agent | null> {
    try {
      const agentIndex = this.agents.findIndex(agent => agent.id === agentId);
      if (agentIndex === -1) {
        console.warn(`Agent ${agentId} not found for update`);
        return null;
      }

      const updatedAgent = {
        ...this.agents[agentIndex],
        ...updates,
        updatedAt: new Date().toISOString()
      };

      this.agents[agentIndex] = updatedAgent;
      this.saveAgents();

      // Broadcast agent update via IPC
      await agentIPCBridge.addMessage({
        agentId: 'system',
        message: `Updated agent: ${updatedAgent.name} (${updatedAgent.id})`,
        timestamp: Date.now(),
        type: 'info'
      });

      console.log(`Updated agent: ${updatedAgent.name} (${updatedAgent.id})`);
      return updatedAgent;
    } catch (error) {
      console.error('Failed to update agent:', error);
      return null;
    }
  }

  async deleteAgent(agentId: string): Promise<boolean> {
    try {
      const agentIndex = this.agents.findIndex(agent => agent.id === agentId);
      if (agentIndex === -1) {
        console.warn(`Agent ${agentId} not found for deletion`);
        return false;
      }

      const agent = this.agents[agentIndex];
      
      // Prevent deletion of default agents
      if (agent.isDefault) {
        console.warn(`Cannot delete default agent: ${agent.name} (${agent.id})`);
        return false;
      }

      this.agents.splice(agentIndex, 1);
      this.saveAgents();

      // Broadcast agent deletion via IPC
      await agentIPCBridge.addMessage({
        agentId: 'system',
        message: `Deleted agent: ${agent.name} (${agent.id})`,
        timestamp: Date.now(),
        type: 'info'
      });

      console.log(`Deleted agent: ${agent.name} (${agent.id})`);
      return true;
    } catch (error) {
      console.error('Failed to delete agent:', error);
      return false;
    }
  }

  private loadStoredAgents(): Agent[] {
    try {
      if (typeof window !== 'undefined' && window.localStorage) {
        const stored = window.localStorage.getItem('synapse-agents');
        if (stored) {
          return JSON.parse(stored);
        }
      }
      return [];
    } catch (error) {
      console.error('Failed to load stored agents:', error);
      return [];
    }
  }

  private saveAgents(): void {
    try {
      if (typeof window !== 'undefined' && window.localStorage) {
        window.localStorage.setItem('synapse-agents', JSON.stringify(this.agents));
      }
    } catch (error) {
      console.error('Failed to save agents:', error);
    }
  }

  private async createDefaultAgents(): Promise<void> {
    const defaultAgents = [
      {
        id: 'junior-dev',
        name: 'Junior Developer',
        role: 'junior',
        capabilities: ['code-generation', 'testing', 'documentation'],
        systemPrompt: 'You are a junior developer focused on implementing features and writing tests.',
        isDefault: true
      },
      {
        id: 'senior-dev',
        name: 'Senior Developer',
        role: 'senior',
        capabilities: ['architecture', 'code-review', 'optimization'],
        systemPrompt: 'You are a senior developer focused on architecture and code quality.',
        isDefault: true
      },
      {
        id: 'micromanager',
        name: 'Micromanager',
        role: 'micromanager',
        capabilities: ['task-orchestration', 'agent-coordination', 'project-management'],
        systemPrompt: 'You are a micromanager responsible for coordinating tasks and managing other agents.',
        isDefault: true
      }
    ];

    for (const agentData of defaultAgents) {
      await this.createAgent(agentData);
    }

    console.log('Created default agents');
  }

  private handleAgentStatusUpdate(data: any): void {
    try {
      const { agentId, status } = data;
      const agent = this.getAgent(agentId);
      if (agent) {
        agent.status = status;
        agent.updatedAt = new Date().toISOString();
        this.saveAgents();
        console.log(`Updated agent ${agentId} status to ${status}`);
      }
    } catch (error) {
      console.error('Failed to handle agent status update:', error);
    }
  }

  private handleAgentTaskAssignment(data: any): void {
    try {
      const { agentId, taskId } = data;
      const agent = this.getAgent(agentId);
      if (agent) {
        agent.status = 'busy';
        agent.updatedAt = new Date().toISOString();
        this.saveAgents();
        console.log(`Agent ${agentId} assigned to task ${taskId}`);
      }
    } catch (error) {
      console.error('Failed to handle agent task assignment:', error);
    }
  }
}
