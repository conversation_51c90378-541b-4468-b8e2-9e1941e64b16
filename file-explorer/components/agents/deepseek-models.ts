// components/agents/deepseek-models.ts

export interface DeepSeekModelMetadata {
  id: string;
  label: string;
  description?: string;
  contextSize?: number;
  pricing?: {
    input: number;  // per 1K tokens
    output: number; // per 1K tokens
  };
  tags?: string[];
  releaseDate?: string;
}

/**
 * ✅ CURATED: DeepSeek models approved for Agent System use
 * Only includes models from the user's approved list
 */
export const DEEPSEEK_MODEL_METADATA: Record<string, DeepSeekModelMetadata> = {
  'deepseek-r1': {
    id: 'deepseek-r1',
    label: 'DeepSeek R1',
    description: 'Latest DeepSeek reasoning model with enhanced capabilities',
    contextSize: 65536,
    pricing: {
      input: 0.0020,
      output: 0.0040
    },
    tags: ['advanced-reasoning', 'latest', 'general-purpose', 'enhanced-reasoning'],
    releaseDate: '2025-01-15'
  },
  'deepseek-v3': {
    id: 'deepseek-v3',
    label: 'DeepSeek V3',
    description: 'Third generation DeepSeek model with improved performance',
    contextSize: 32768,
    pricing: {
      input: 0.0014,
      output: 0.0028
    },
    tags: ['advanced-reasoning', 'conversational', 'general-purpose', 'affordable'],
    releaseDate: '2024-12-15'
  }
};

/**
 * Get metadata for a specific DeepSeek model
 */
export function getDeepSeekModelMetadata(modelId: string): DeepSeekModelMetadata | null {
  return DEEPSEEK_MODEL_METADATA[modelId] || null;
}

/**
 * Check if a model has verified metadata
 */
export function hasDeepSeekModelMetadata(modelId: string): boolean {
  return modelId in DEEPSEEK_MODEL_METADATA;
}

/**
 * Get all available DeepSeek models with metadata
 */
export function getDeepSeekModelsWithMetadata(): DeepSeekModelMetadata[] {
  return Object.values(DEEPSEEK_MODEL_METADATA);
}

/**
 * Get model series for grouping
 */
export function getDeepSeekModelSeries(modelId: string): string {
  if (modelId.includes('coder')) {
    return 'DeepSeek Coder';
  }
  if (modelId.includes('chat')) {
    return 'DeepSeek Chat';
  }
  return 'DeepSeek';
}
