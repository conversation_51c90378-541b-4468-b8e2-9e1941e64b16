// file-explorer/components/agents/isolated-history-tab.tsx
// ✅ TASK 5.2: Real History Tab - Connected to actual agent execution history

"use client"

import React, { useEffect, useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import {
  History,
  RefreshCw,
  Search,
  Filter,
  CheckCircle,
  XCircle,
  Clock,
  Play,
  AlertTriangle,
  Calendar,
  User,
  FileText,
  HelpCircle,
  Info
} from 'lucide-react'
import { useSharedAgentState } from './shared-agent-state'
import { useRealTimeMetrics } from './real-time-metrics-provider'

interface IsolatedHistoryTabProps {
  // Add any props needed for the history tab
}

/**
 * ✅ TASK 5.2: Real History Tab Component
 *
 * Replaces static placeholder content with actual agent execution history
 * Connected to real task data and execution timeline
 */
const IsolatedHistoryTab = React.memo<IsolatedHistoryTabProps>(() => {
  const sharedState = useSharedAgentState()
  const realTimeMetrics = useRealTimeMetrics()
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [agentFilter, setAgentFilter] = useState<string>('all')
  const [sortBy, setSortBy] = useState<string>('newest')

  // ✅ Get filtered and sorted task history
  const getFilteredHistory = () => {
    let filteredTasks = [...sharedState.tasks]

    // Apply search filter
    if (searchTerm) {
      filteredTasks = filteredTasks.filter(task =>
        task.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        task.agentId.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // Apply status filter
    if (statusFilter !== 'all') {
      filteredTasks = filteredTasks.filter(task => task.status === statusFilter)
    }

    // Apply agent filter
    if (agentFilter !== 'all') {
      filteredTasks = filteredTasks.filter(task => task.agentId === agentFilter)
    }

    // Apply sorting
    filteredTasks.sort((a, b) => {
      switch (sortBy) {
        case 'newest':
          return b.createdAt - a.createdAt
        case 'oldest':
          return a.createdAt - b.createdAt
        case 'status':
          return a.status.localeCompare(b.status)
        case 'agent':
          return a.agentId.localeCompare(b.agentId)
        default:
          return b.createdAt - a.createdAt
      }
    })

    return filteredTasks
  }

  const filteredHistory = getFilteredHistory()
  const uniqueAgents = [...new Set(sharedState.tasks.map(task => task.agentId))]

  // ✅ Get status icon and color
  const getStatusDisplay = (status: string) => {
    switch (status) {
      case 'completed':
        return { icon: <CheckCircle className="h-4 w-4" />, color: 'text-green-600', bg: 'bg-green-50 dark:bg-green-950' }
      case 'failed':
        return { icon: <XCircle className="h-4 w-4" />, color: 'text-red-600', bg: 'bg-red-50 dark:bg-red-950' }
      case 'running':
        return { icon: <Play className="h-4 w-4" />, color: 'text-blue-600', bg: 'bg-blue-50 dark:bg-blue-950' }
      case 'pending':
        return { icon: <Clock className="h-4 w-4" />, color: 'text-yellow-600', bg: 'bg-yellow-50 dark:bg-yellow-950' }
      default:
        return { icon: <AlertTriangle className="h-4 w-4" />, color: 'text-gray-600', bg: 'bg-gray-50 dark:bg-gray-950' }
    }
  }

  // ✅ Format duration
  const formatDuration = (startTime: number, endTime?: number) => {
    const duration = (endTime || Date.now()) - startTime
    const seconds = Math.floor(duration / 1000)
    const minutes = Math.floor(seconds / 60)
    const hours = Math.floor(minutes / 60)

    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`
    } else {
      return `${seconds}s`
    }
  }

  // ✅ Get agent display name
  const getAgentDisplayName = (agentId: string) => {
    const agentNames: { [key: string]: string } = {
      'micromanager': '🤖 Micromanager',
      'intern': '1️⃣ Intern Agent',
      'junior': '2️⃣ Junior Agent',
      'midlevel': '3️⃣ MidLevel Agent',
      'senior': '4️⃣ Senior Agent',
      'researcher': '📘 Researcher Agent',
      'architect': '🏗️ Architect Agent',
      'designer': '🎨 Designer Agent',
      'tester': '🧪 Tester Agent'
    }
    return agentNames[agentId] || agentId
  }

  useEffect(() => {
    console.log('🔄 Real History Tab mounted')

    return () => {
      console.log('🧹 Real History Tab unmounted - cleaning up')
    }
  }, [])

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-border">
        <div className="flex items-center gap-2">
          <History className="h-5 w-5 text-muted-foreground" />
          <h2 className="text-lg font-semibold">Execution History</h2>
          <Tooltip>
            <TooltipTrigger asChild>
              <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
            </TooltipTrigger>
            <TooltipContent className="max-w-sm">
              <p className="font-medium mb-2">📚 Execution History</p>
              <p className="text-sm mb-2">Complete record of all agent task executions with real-time status updates.</p>
              <div className="text-xs space-y-1">
                <p>• <strong>Search:</strong> Find tasks by description or agent name</p>
                <p>• <strong>Filter:</strong> View by status, agent, or time period</p>
                <p>• <strong>Sort:</strong> Organize by date, status, or agent</p>
                <p>• <strong>Real-time:</strong> Live updates as tasks execute</p>
              </div>
            </TooltipContent>
          </Tooltip>
          <Badge variant="outline" className="text-xs">
            {filteredHistory.length} of {sharedState.tasks.length} tasks
          </Badge>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={() => {
            // ✅ INTEGRATION FIX: Refresh history data only, not entire application
            sharedState.refreshTasks();
          }}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Filters */}
      <div className="p-4 border-b border-border">
        <div className="flex flex-wrap items-center gap-4">
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="flex items-center gap-2">
                <Search className="h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search tasks..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-64"
                />
              </div>
            </TooltipTrigger>
            <TooltipContent>
              <p className="text-sm">Search by task description or agent name</p>
              <p className="text-xs text-muted-foreground">Real-time filtering as you type</p>
            </TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="failed">Failed</SelectItem>
                  <SelectItem value="running">Running</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                </SelectContent>
              </Select>
            </TooltipTrigger>
            <TooltipContent>
              <p className="text-sm">Filter tasks by execution status</p>
              <p className="text-xs text-muted-foreground">Shows only tasks with selected status</p>
            </TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <Select value={agentFilter} onValueChange={setAgentFilter}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Agent" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Agents</SelectItem>
                  {uniqueAgents.map(agent => (
                    <SelectItem key={agent} value={agent}>
                      {getAgentDisplayName(agent)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </TooltipTrigger>
            <TooltipContent>
              <p className="text-sm">Filter tasks by specific agent</p>
              <p className="text-xs text-muted-foreground">View execution history for individual agents</p>
            </TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="newest">Newest</SelectItem>
                  <SelectItem value="oldest">Oldest</SelectItem>
                  <SelectItem value="status">Status</SelectItem>
                  <SelectItem value="agent">Agent</SelectItem>
                </SelectContent>
              </Select>
            </TooltipTrigger>
            <TooltipContent>
              <p className="text-sm">Sort tasks by different criteria</p>
              <p className="text-xs text-muted-foreground">Organize history view for better analysis</p>
            </TooltipContent>
          </Tooltip>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-auto">
        {filteredHistory.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center space-y-4">
              <History className="h-12 w-12 text-muted-foreground mx-auto" />
              <div>
                <h3 className="text-lg font-medium">
                  {sharedState.tasks.length === 0 ? 'No execution history' : 'No matching tasks'}
                </h3>
                <p className="text-muted-foreground">
                  {sharedState.tasks.length === 0
                    ? 'Task execution history will appear here once agents start working.'
                    : 'Try adjusting your search or filter criteria.'}
                </p>
              </div>
            </div>
          </div>
        ) : (
          <div className="p-4 space-y-4">
            {/* Summary Stats */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
              <Tooltip>
                <TooltipTrigger asChild>
                  <Card className="cursor-help">
                    <CardContent className="p-4">
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-600" />
                        <div>
                          <p className="text-sm font-medium">Completed</p>
                          <p className="text-lg font-bold">
                            {filteredHistory.filter(t => t.status === 'completed').length}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TooltipTrigger>
                <TooltipContent>
                  <p className="text-sm">Tasks that finished successfully</p>
                  <p className="text-xs text-muted-foreground">Indicates system reliability and agent performance</p>
                </TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <Card className="cursor-help">
                    <CardContent className="p-4">
                      <div className="flex items-center gap-2">
                        <XCircle className="h-4 w-4 text-red-600" />
                        <div>
                          <p className="text-sm font-medium">Failed</p>
                          <p className="text-lg font-bold">
                            {filteredHistory.filter(t => t.status === 'failed').length}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TooltipTrigger>
                <TooltipContent>
                  <p className="text-sm">Tasks that encountered errors or failed to complete</p>
                  <p className="text-xs text-muted-foreground">Monitor for patterns to improve system reliability</p>
                </TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <Card className="cursor-help">
                    <CardContent className="p-4">
                      <div className="flex items-center gap-2">
                        <Play className="h-4 w-4 text-blue-600" />
                        <div>
                          <p className="text-sm font-medium">Running</p>
                          <p className="text-lg font-bold">
                            {filteredHistory.filter(t => t.status === 'running').length}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TooltipTrigger>
                <TooltipContent>
                  <p className="text-sm">Tasks currently being executed by agents</p>
                  <p className="text-xs text-muted-foreground">Real-time view of active agent work</p>
                </TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <Card className="cursor-help">
                    <CardContent className="p-4">
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4 text-yellow-600" />
                        <div>
                          <p className="text-sm font-medium">Pending</p>
                          <p className="text-lg font-bold">
                            {filteredHistory.filter(t => t.status === 'pending').length}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TooltipTrigger>
                <TooltipContent>
                  <p className="text-sm">Tasks waiting in queue to be assigned to agents</p>
                  <p className="text-xs text-muted-foreground">Shows system workload and capacity</p>
                </TooltipContent>
              </Tooltip>
            </div>

            {/* Task History List */}
            <div className="space-y-3">
              {filteredHistory.map((task) => {
                const statusDisplay = getStatusDisplay(task.status)
                const isRunning = task.status === 'running'
                const duration = formatDuration(task.createdAt, task.updatedAt)

                return (
                  <Card key={task.id} className={`transition-all hover:shadow-md ${statusDisplay.bg}`}>
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-3 mb-2">
                            <div className={`flex items-center gap-1 ${statusDisplay.color}`}>
                              {statusDisplay.icon}
                              <Badge variant={
                                task.status === 'completed' ? 'default' :
                                task.status === 'failed' ? 'destructive' :
                                task.status === 'running' ? 'secondary' : 'outline'
                              }>
                                {task.status}
                              </Badge>
                            </div>

                            <div className="flex items-center gap-2 text-sm text-muted-foreground">
                              <User className="h-3 w-3" />
                              <span>{getAgentDisplayName(task.agentId)}</span>
                            </div>

                            <div className="flex items-center gap-2 text-sm text-muted-foreground">
                              <Calendar className="h-3 w-3" />
                              <span>{new Date(task.createdAt).toLocaleString()}</span>
                            </div>

                            <div className="flex items-center gap-2 text-sm text-muted-foreground">
                              <Clock className="h-3 w-3" />
                              <span>{duration}</span>
                            </div>
                          </div>

                          <div className="mb-2">
                            <h4 className="font-medium text-sm mb-1">Task Description</h4>
                            <p className="text-sm text-muted-foreground break-words">
                              {task.description}
                            </p>
                          </div>

                          <div className="flex items-center gap-4 text-xs text-muted-foreground">
                            <div className="flex items-center gap-1">
                              <FileText className="h-3 w-3" />
                              <span>ID: {task.id}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <span>Priority: {task.priority}</span>
                            </div>
                            {task.status === 'running' && (
                              <div className="flex items-center gap-1 text-blue-600">
                                <div className="w-2 h-2 bg-blue-600 rounded-full animate-pulse" />
                                <span>Active</span>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )
              })}
            </div>

            {/* Load More / Pagination could go here */}
            {filteredHistory.length > 50 && (
              <div className="text-center py-4">
                <p className="text-sm text-muted-foreground">
                  Showing first 50 results. Use filters to narrow down the search.
                </p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  )
})

IsolatedHistoryTab.displayName = 'IsolatedHistoryTab'

export default IsolatedHistoryTab
