// file-explorer/components/agents/real-time-metrics-provider.tsx
// ✅ TASK 1.3: RealTimeMetricsProvider - React context for real-time agent metrics

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { AgentUIBridge, AgentStatus, ExecutionUpdate, SequentialWorkflowStatus } from './agent-ui-bridge';

// ✅ TASK 7.2: Import PerformanceOptimizer for optimized updates
import { usePerformanceOptimizer } from './performance-optimizer';

// ✅ RealTimeMetrics Interface
export interface RealTimeMetrics {
  systemHealthScore: number;
  activeAgents: number;
  queueLength: number;
  totalTasks: number;
  successfulTasks: number;
  averageResponseTime: number;
  totalTokensUsed: number;
  agentStatuses: AgentStatus[];
  lastUpdated: number;
  workflowStatus: SequentialWorkflowStatus;
  executionUpdates: ExecutionUpdate[];
}

// ✅ Context Definition
const RealTimeMetricsContext = createContext<RealTimeMetrics | null>(null);

// ✅ Provider Props Interface
interface RealTimeMetricsProviderProps {
  children: ReactNode;
}

// ✅ RealTimeMetricsProvider Component
export const RealTimeMetricsProvider: React.FC<RealTimeMetricsProviderProps> = ({ children }) => {
  // ✅ TASK 7.2: Use performance optimizer for efficient updates
  const performanceOptimizer = usePerformanceOptimizer();

  const [metrics, setMetrics] = useState<RealTimeMetrics>({
    systemHealthScore: 0,
    activeAgents: 0,
    queueLength: 0,
    totalTasks: 0,
    successfulTasks: 0,
    averageResponseTime: 0,
    totalTokensUsed: 0,
    agentStatuses: [],
    lastUpdated: Date.now(),
    workflowStatus: {
      isActive: false,
      currentAgent: null,
      currentTask: null,
      queueLength: 0,
      completedTasks: 0,
      totalTasks: 0
    },
    executionUpdates: []
  });

  useEffect(() => {
    console.log('RealTimeMetricsProvider: Initializing...');
    const agentUIBridge = AgentUIBridge.getInstance();

    // ✅ Subscribe to agent status updates
    const unsubscribeStatus = agentUIBridge.subscribeToAgentStatus((status: AgentStatus) => {
      console.log('RealTimeMetricsProvider: Received agent status update:', status);
      
      setMetrics(prev => {
        // Update or add agent status
        const updatedAgentStatuses = prev.agentStatuses.filter(agent => agent.agentId !== status.agentId);
        updatedAgentStatuses.push(status);

        // Calculate system metrics
        const systemHealthScore = updatedAgentStatuses.length > 0
          ? updatedAgentStatuses.reduce((acc, agent) => acc + agent.healthScore, 0) / updatedAgentStatuses.length
          : 0;

        const activeAgents = updatedAgentStatuses.filter(agent => agent.status === 'busy').length;
        const totalTokensUsed = updatedAgentStatuses.reduce((acc, agent) => acc + agent.tokensUsed, 0);
        const totalTasks = updatedAgentStatuses.reduce((acc, agent) => acc + agent.tasksCompleted, 0);

        return {
          ...prev,
          systemHealthScore,
          activeAgents,
          totalTokensUsed,
          totalTasks,
          agentStatuses: updatedAgentStatuses,
          lastUpdated: Date.now()
        };
      });
    });

    // ✅ Subscribe to execution updates
    const unsubscribeExecution = agentUIBridge.subscribeToExecutionUpdates((update: ExecutionUpdate) => {
      console.log('RealTimeMetricsProvider: Received execution update:', update);
      
      setMetrics(prev => {
        // Add execution update to history (keep last 50)
        const updatedExecutionUpdates = [...prev.executionUpdates, update].slice(-50);

        // Update average response time based on completion updates
        let averageResponseTime = prev.averageResponseTime;
        if (update.type === 'completion') {
          // Simple moving average calculation
          averageResponseTime = (prev.averageResponseTime * 0.8) + (2000 * 0.2); // Mock calculation
        }

        return {
          ...prev,
          averageResponseTime,
          executionUpdates: updatedExecutionUpdates,
          lastUpdated: Date.now()
        };
      });
    });

    // ✅ Subscribe to workflow status updates
    const unsubscribeWorkflow = agentUIBridge.subscribeToWorkflowStatus((workflowStatus: SequentialWorkflowStatus) => {
      console.log('RealTimeMetricsProvider: Received workflow status update:', workflowStatus);
      
      setMetrics(prev => ({
        ...prev,
        workflowStatus,
        queueLength: workflowStatus.queueLength,
        lastUpdated: Date.now()
      }));
    });

    // ✅ Initial data fetch
    const initialWorkflowStatus = agentUIBridge.getSequentialWorkflowStatus();
    setMetrics(prev => ({
      ...prev,
      workflowStatus: initialWorkflowStatus,
      queueLength: initialWorkflowStatus.queueLength
    }));

    console.log('RealTimeMetricsProvider: Subscriptions established');

    // ✅ Cleanup subscriptions on unmount
    return () => {
      console.log('RealTimeMetricsProvider: Cleaning up subscriptions...');
      unsubscribeStatus();
      unsubscribeExecution();
      unsubscribeWorkflow();
    };
  }, []);

  // ✅ TASK 7.2: Optimized periodic metrics calculation
  useEffect(() => {
    const config = performanceOptimizer.getConfig();
    const interval = setInterval(() => {
      // Use performance optimizer to queue the calculation
      performanceOptimizer.queueUpdate(
        'periodic-metrics',
        { type: 'periodic-calculation' },
        'low'
      );

      setMetrics(prev => {
        // Calculate success rate
        const completedTasks = prev.executionUpdates.filter(update => update.type === 'completion').length;
        const successfulTasks = completedTasks; // Simplified - all completions are successes for now

        const newMetrics = {
          ...prev,
          successfulTasks,
          lastUpdated: Date.now()
        };

        // Cache the calculated metrics
        performanceOptimizer.setCache('periodic-metrics', newMetrics);

        return newMetrics;
      });
    }, config.updateFrequency); // Use dynamic update frequency

    return () => clearInterval(interval);
  }, [performanceOptimizer]);

  return (
    <RealTimeMetricsContext.Provider value={metrics}>
      {children}
    </RealTimeMetricsContext.Provider>
  );
};

// ✅ useRealTimeMetrics Hook
export const useRealTimeMetrics = (): RealTimeMetrics => {
  const context = useContext(RealTimeMetricsContext);
  
  if (!context) {
    // Return default metrics if provider is not available
    console.warn('useRealTimeMetrics: Provider not found, returning default metrics');
    return {
      systemHealthScore: 0,
      activeAgents: 0,
      queueLength: 0,
      totalTasks: 0,
      successfulTasks: 0,
      averageResponseTime: 0,
      totalTokensUsed: 0,
      agentStatuses: [],
      lastUpdated: Date.now(),
      workflowStatus: {
        isActive: false,
        currentAgent: null,
        currentTask: null,
        queueLength: 0,
        completedTasks: 0,
        totalTasks: 0
      },
      executionUpdates: []
    };
  }
  
  return context;
};

// ✅ Additional Hooks for Specific Metrics
export const useAgentStatuses = (): AgentStatus[] => {
  const metrics = useRealTimeMetrics();
  return metrics.agentStatuses;
};

export const useSystemHealth = (): number => {
  const metrics = useRealTimeMetrics();
  return metrics.systemHealthScore;
};

export const useWorkflowStatus = (): SequentialWorkflowStatus => {
  const metrics = useRealTimeMetrics();
  return metrics.workflowStatus;
};

export const useExecutionUpdates = (): ExecutionUpdate[] => {
  const metrics = useRealTimeMetrics();
  return metrics.executionUpdates;
};

// ✅ Export context for advanced usage
export { RealTimeMetricsContext };
