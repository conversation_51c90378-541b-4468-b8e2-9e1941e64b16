// Integration Guide: Complete Agent System Setup
// File: components/agents/complete-integration.tsx

import React, { useState, useEffect } from 'react';
import { CompleteAgentManager } from './agent-manager-complete';
import { AgentIntegration } from './agent-integration';
import { getGlobalSettingsManager } from '../settings/global-settings';
import { SharedAgentStateProvider, useSharedAgentState } from './shared-agent-state';
import { useSystemSettings } from '../settings/settings-context';
import { useTimeout } from '../../lib/utils/use-timeout';
import { useDebug } from '../../lib/utils/use-debug';
import { useTelemetry } from '../../lib/utils/use-telemetry';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
// Removed Tabs import - using conditional mounting instead
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { AlertTriangle, CheckCircle, Activity, Brain, Play, RefreshCw, TrendingUp, HelpCircle, Info } from 'lucide-react';
import IsolatedHistoryTab from './isolated-history-tab';
import IsolatedAnalyticsTab from './isolated-analytics-tab';

import AgentExecutionTrace from './agent-execution-trace';
import RefactorPreview, { refactorService } from './refactor-service';

// ✅ TASK 2.1: Import RealTimeMetricsProvider for real agent data
import { RealTimeMetricsProvider, useRealTimeMetrics } from './real-time-metrics-provider';

// ✅ Import AgentUIBridge for real-time execution updates
import { AgentUIBridge } from './agent-ui-bridge';

// ✅ TASK 3.2: Import SequentialWorkflowPanel for sequential workflow UI
import { SequentialWorkflowPanel } from './sequential-workflow-panel';

// Monaco Integration available but not currently used

// ✅ TASK 6.1: Import AutoExecutionConfigPanel for advanced automation
import { AutoExecutionConfigPanel } from './auto-execution-config-panel';

// ✅ TASK 7.1: Import IntegrationTestSuite for comprehensive testing
import { IntegrationTestSuite } from './integration-test-suite';

interface CompleteSystemProps {
  className?: string;
}

// Wrapper component that provides shared state
export const CompleteAgentSystem: React.FC<CompleteSystemProps> = ({ className }) => {
  return (
    <SharedAgentStateProvider>
      <CompleteAgentSystemInner className={className} />
    </SharedAgentStateProvider>
  );
};

// Inner component that uses shared state
const CompleteAgentSystemInner: React.FC<CompleteSystemProps> = ({ className }) => {
  const sharedState = useSharedAgentState();
  const [agentManager] = useState(() => CompleteAgentManager.getInstance()); // ✅ CRITICAL FIX: Use singleton instead of new instance
  const [settingsManager] = useState(() => getGlobalSettingsManager());
  const [testModeEnabled, setTestModeEnabled] = useState(false);
  const [activeTab, setActiveTab] = useState("orchestrator");

  // ✅ Safe access to settings with fallback
  let systemSettings: any;
  let defaultTimeout: number;
  let maxConcurrentTasks: number;
  let withDefaultTimeout: any;
  let withCustomTimeout: any;

  try {
    const settingsContext = useSystemSettings();
    systemSettings = settingsContext.systemSettings;
    const timeoutUtils = useTimeout();
    defaultTimeout = timeoutUtils.defaultTimeout;
    maxConcurrentTasks = systemSettings.maxConcurrentTasks || 3;
    withDefaultTimeout = timeoutUtils.withDefaultTimeout;
    withCustomTimeout = timeoutUtils.withCustomTimeout;
  } catch (error) {
    console.warn('Settings context not available, using fallbacks:', error);
    // ✅ Fallback values when settings context is not available
    systemSettings = {
      defaultTimeout: 30000,
      maxConcurrentTasks: 3,
      debugMode: false,
      enableTelemetry: false
    };
    defaultTimeout = 30000;
    maxConcurrentTasks = 3;
    withDefaultTimeout = async (promise: Promise<any>, label: string) => {
      console.log(`⚠️ Using fallback timeout for: ${label}`);
      return promise; // No timeout when settings unavailable
    };
    withCustomTimeout = async (promise: Promise<any>, timeoutMs: number, label: string) => {
      console.log(`⚠️ Using fallback custom timeout for: ${label} (${timeoutMs}ms)`);
      return promise; // No timeout when settings unavailable
    };
  }

  // ✅ Update agent manager concurrency limit when settings change
  useEffect(() => {
    if (agentManager && maxConcurrentTasks) {
      agentManager.updateConcurrencyLimit(maxConcurrentTasks);
      console.log(`🔄 Updated agent manager concurrency limit to ${maxConcurrentTasks}`);
    }
  }, [agentManager, maxConcurrentTasks]);

  // ✅ Sync test mode with settings
  useEffect(() => {
    try {
      const settings = settingsManager.getSettings();
      setTestModeEnabled(settings.system.testModeEnabled || false);
    } catch (error) {
      console.warn('Failed to get test mode setting:', error);
      setTestModeEnabled(false);
    }
  }, [settingsManager]);

  // ✅ Initialize debug and telemetry hooks (they automatically sync with Electron when available)
  const debug = useDebug();
  const telemetry = useTelemetry();

  // ✅ TASK 2.1: Use real-time metrics instead of calculated values
  const realTimeMetrics = useRealTimeMetrics();

  // Combine real-time metrics with shared state data for backward compatibility
  const systemMetrics = {
    systemHealthScore: realTimeMetrics.systemHealthScore || (
      sharedState.agents.length > 0
        ? sharedState.agents.reduce((acc, agent) => {
            const healthScore = isNaN(agent.healthScore) ? 0 : (agent.healthScore || 0);
            return acc + healthScore;
          }, 0) / sharedState.agents.length
        : 0
    ),
    activeAgents: realTimeMetrics.activeAgents || sharedState.agents.filter(agent => agent.status === 'busy').length,
    queueLength: realTimeMetrics.queueLength || sharedState.tasks.filter(task => task.status === 'pending').length,
    totalTasks: realTimeMetrics.totalTasks || sharedState.tasks.length,
    successfulTasks: realTimeMetrics.successfulTasks || sharedState.tasks.filter(task => task.status === 'completed').length,
    averageResponseTime: realTimeMetrics.averageResponseTime || 0, // ✅ Removed hardcoded 2000 mock value
    totalTokensUsed: realTimeMetrics.totalTokensUsed || sharedState.agents.reduce((acc, agent) => {
      const tokensUsed = isNaN(agent.tokensUsed) ? 0 : (agent.tokensUsed || 0);
      return acc + tokensUsed;
    }, 0)
  };

  // ✅ TASK 2.1: Connect to real optimization service instead of mock data
  const optimizations = agentManager.getOptimizationSuggestions ? agentManager.getOptimizationSuggestions() : [];

  // Add real-time optimization suggestions based on agent performance
  const realTimeOptimizations = realTimeMetrics.agentStatuses
    .filter(agent => agent.healthScore < 80)
    .map(agent => ({
      type: 'performance',
      title: `Optimize ${agent.name}`,
      description: `Agent health is ${agent.healthScore}%. Consider reducing workload or checking for errors.`,
      impact: 'medium',
      agentId: agent.agentId
    }));

  const allOptimizations = [...optimizations, ...realTimeOptimizations];

  useEffect(() => {
    // Listen for system messages
    const handleMessage = (message: any) => {
      console.log('System message:', message);
      // Add message to shared state
      sharedState.addMessage({
        agentId: message.agentId || 'system',
        message: message.message || message.toString(),
        timestamp: Date.now(),
        type: message.type || 'info'
      });
    };

    agentManager.onMessage(handleMessage);

    return () => {
      agentManager.offMessage(handleMessage);
    };
  }, [agentManager, sharedState]);

  // ✅ Cleanup agent manager on unmount
  useEffect(() => {
    return () => {
      if (agentManager && typeof agentManager.shutdown === 'function') {
        agentManager.shutdown().catch(console.error);
      }
    };
  }, [agentManager]);

  const handleTaskSubmission = async (task: string) => {
    try {
      // ✅ Use extended timeout for micromanager tasks (LLM orchestration needs more time)
      const taskTimeout = sharedState.selectedAgent === 'micromanager' ? 90000 : defaultTimeout; // 90s for micromanager, default for others
      console.log(`🕐 Starting task submission with timeout: ${taskTimeout}ms for ${sharedState.selectedAgent}`);

      // ✅ TASK 4.1: Subscribe to execution updates for real-time progress
      const agentUIBridge = AgentUIBridge.getInstance();
      const unsubscribeExecution = agentUIBridge.subscribeToExecutionUpdates((update) => {
        console.log('🔄 Real-time execution update:', update);

        // Add execution progress message to shared state for immediate visibility
        sharedState.addMessage({
          agentId: update.agentId,
          message: `${update.type}: ${update.data.message || 'Processing...'}`,
          timestamp: update.timestamp,
          type: update.type === 'error' ? 'error' : 'info'
        });

        // Update task progress if available
        if (update.data.progress !== undefined) {
          console.log(`📈 Task progress: ${update.data.progress}% for agent ${update.agentId}`);
        }

        // Show file operations in real-time
        if (update.data.filePath) {
          sharedState.addMessage({
            agentId: update.agentId,
            message: `📁 ${update.type === 'code_generation' ? 'Generating' : 'Processing'}: ${update.data.filePath}`,
            timestamp: update.timestamp,
            type: 'info'
          });
        }
      });

      // ✅ Wrap task submission with appropriate timeout
      const result = await withCustomTimeout(
        (async () => {
          // Check if this is a Micromanager task that needs decomposition
          if (sharedState.selectedAgent === 'micromanager') {
            return await handleMicromanagerTask(task);
          } else {
            // Direct agent assignment for non-Micromanager tasks
            await sharedState.assignTask({
              agentId: sharedState.selectedAgent,
              description: task,
              status: 'pending',
              priority: 'medium'
            });

            const taskId = await agentManager.submitTask(task);
            console.log(`Task submitted with ID: ${taskId}`);
            return taskId;
          }
        })(),
        taskTimeout,
        `Task submission for ${sharedState.selectedAgent}`
      );

      // ✅ Clean up execution subscription after task completion
      setTimeout(() => {
        unsubscribeExecution();
        console.log('🧹 Cleaned up execution update subscription');
      }, 5000); // Keep listening for 5 seconds after completion

      return result;
    } catch (error) {
      console.error('Task submission failed:', error);
      throw error;
    }
  };

  // ✅ TASK 6.2: Refactored to use TaskOrchestrationService
  const handleMicromanagerTask = async (task: string) => {
    try {
      // Import TaskOrchestrationService
      const { TaskOrchestrationService } = await import('./task-orchestration-service');

      // Get orchestration service instance
      const orchestrationService = TaskOrchestrationService.getInstance(agentManager);

      // Set up orchestration callbacks to update shared state
      orchestrationService.setCallbacks({
        onStatusUpdate: (update) => {
          console.log(`📊 Status Update: Task ${update.taskId} -> ${update.status}${update.progress ? ` (${update.progress}%)` : ''}`);
          // Update shared state
          sharedState.updateTask(update.taskId, { status: update.status as any });
        },
        onProgressUpdate: (taskId, progress) => {
          console.log(`📈 Progress: Task ${taskId} -> ${progress}%`);
        },
        onKanbanUpdate: (taskId, cardId, status) => {
          console.log(`📋 Kanban: Card ${cardId} for task ${taskId} -> ${status}`);
        }
      });

      // Create parent task in shared state
      await sharedState.assignTask({
        agentId: 'micromanager',
        description: `[ORCHESTRATOR] ${task}`,
        status: 'pending',
        priority: 'high'
      });

      // Orchestrate the task using the service
      const orchestrationResult = await orchestrationService.orchestrateMicromanagerTask(task);

      // Add all subtasks to shared state
      const { TaskOrchestrator } = await import('./task-orchestrator');
      const decomposition = TaskOrchestrator.decompose(task);

      for (const subtask of decomposition.subtasks) {
        await sharedState.assignTask({
          agentId: subtask.agent,
          description: subtask.description,
          status: 'pending',
          priority: subtask.priority === 'urgent' ? 'high' : subtask.priority as 'high' | 'low' | 'medium'
        });
      }

      console.log(`Micromanager orchestration complete:`, orchestrationResult);
      console.log(`- Parent Task: ${orchestrationResult.parentTaskId}`);
      console.log(`- Total Tasks: ${orchestrationResult.coordinationStats.total}`);
      console.log(`- Kanban Cards: ${orchestrationResult.kanbanCardIds.length}`);
      console.log(`- Success Rate: ${orchestrationResult.statusSummary.successRate.toFixed(1)}%`);

      return orchestrationResult.parentTaskId;

    } catch (error) {
      console.error('Micromanager task orchestration failed:', error);
      throw error;
    }
  };

  const getSystemHealthColor = (health: number) => {
    if (health >= 80) return 'text-green-500';
    if (health >= 60) return 'text-yellow-500';
    return 'text-red-500';
  };

  const getSystemStatusIcon = (health: number) => {
    if (health >= 80) return <CheckCircle className="h-5 w-5 text-green-500" />;
    if (health >= 60) return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
    return <AlertTriangle className="h-5 w-5 text-red-500" />;
  };

  return (
    <TooltipProvider>
      <div className={`h-full bg-background ${className}`}>
        <div className="flex flex-col h-full">
          {/* System Header */}
          <div className="border-b border-border p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  {getSystemStatusIcon(systemMetrics.systemHealthScore || 0)}
                  <h1 className="text-2xl font-bold">Agent System</h1>
                  <Badge variant={(systemMetrics.systemHealthScore || 0) >= 80 ? 'default' : 'destructive'}>
                    {isNaN(systemMetrics.systemHealthScore) ? '0.0' : (systemMetrics.systemHealthScore || 0).toFixed(1)}% Health
                  </Badge>
                </div>
                <div className="flex items-center gap-4 text-sm text-muted-foreground">
                  <span>{systemMetrics.activeAgents} Active Agents</span>
                  <span>{systemMetrics.queueLength} Queued</span>
                  <span>{systemMetrics.totalTasks} Total Tasks</span>
                </div>
              </div>

            </div>
          </div>

        {/* Main Content */}
        <div className="flex-1 overflow-hidden">
          <div className="h-full flex flex-col">
              <div className="flex w-full mx-4 mt-4 h-10 items-center justify-start rounded-md bg-muted p-1 text-muted-foreground overflow-x-auto gap-1">
                <button
                  onClick={() => setActiveTab("orchestrator")}
                  className={`inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 ${
                    activeTab === "orchestrator" ? "bg-background text-foreground shadow-sm" : ""
                  }`}
                >
                  🤖 Orchestrator
                </button>
                <button
                  onClick={() => setActiveTab("agents")}
                  className={`inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 ${
                    activeTab === "agents" ? "bg-background text-foreground shadow-sm" : ""
                  }`}
                >
                  Agents
                </button>

                <button
                  onClick={() => setActiveTab("workflow")}
                  className={`inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 ${
                    activeTab === "workflow" ? "bg-background text-foreground shadow-sm" : ""
                  }`}
                >
                  🔄 Sequential
                </button>
                <button
                  onClick={() => setActiveTab("history")}
                  className={`inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 ${
                    activeTab === "history" ? "bg-background text-foreground shadow-sm" : ""
                  }`}
                >
                  History
                </button>
                <button
                  onClick={() => setActiveTab("analytics")}
                  className={`inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 ${
                    activeTab === "analytics" ? "bg-background text-foreground shadow-sm" : ""
                  }`}
                >
                  Analytics
                </button>

                <button
                  onClick={() => setActiveTab("optimization")}
                  className={`inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 ${
                    activeTab === "optimization" ? "bg-background text-foreground shadow-sm" : ""
                  }`}
                >
                  Optimization
                </button>

                <button
                  onClick={() => setActiveTab("debug")}
                  className={`inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 ${
                    activeTab === "debug" ? "bg-background text-foreground shadow-sm" : ""
                  }`}
                >
                  🔍 Debug
                </button>
                <button
                  onClick={() => setActiveTab("refactor")}
                  className={`inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 ${
                    activeTab === "refactor" ? "bg-background text-foreground shadow-sm" : ""
                  }`}
                >
                  🔧 Refactor
                </button>
                <button
                  onClick={() => setActiveTab("autoexec")}
                  className={`inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 ${
                    activeTab === "autoexec" ? "bg-background text-foreground shadow-sm" : ""
                  }`}
                >
                  ⚡ Auto-exec
                </button>
                <button
                  onClick={() => setActiveTab("testing")}
                  className={`inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 ${
                    activeTab === "testing" ? "bg-background text-foreground shadow-sm" : ""
                  }`}
                >
                  🧪 Testing
                </button>

              </div>

              {/* ✅ Conditional tab mounting - only active tab is mounted */}
              <div className="flex-1 mt-2 overflow-hidden min-h-0 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2">
                {activeTab === "orchestrator" && (
                  <div className="flex-1 p-4 overflow-auto h-full">
                    <AgentOrchestratorPanel onTaskSubmit={handleTaskSubmission} />
                  </div>
                )}

                {activeTab === "agents" && (
                  <div className="flex-1 p-4 overflow-auto h-full">
                    <AgentIntegration />
                  </div>
                )}



                {activeTab === "workflow" && (
                  <div className="flex-1 p-4 overflow-auto h-full">
                    <SequentialWorkflowPanel />
                  </div>
                )}

                {activeTab === "history" && (
                  <div className="flex-1 overflow-auto h-full">
                    <IsolatedHistoryTab />
                  </div>
                )}

                {activeTab === "analytics" && (
                  <div className="flex-1 overflow-auto h-full">
                    <IsolatedAnalyticsTab />
                  </div>
                )}



                {activeTab === "optimization" && (
                  <div className="flex-1 p-4 overflow-auto h-full">
                    <OptimizationPanel
                      optimizations={allOptimizations}
                      agentManager={agentManager}
                    />
                  </div>
                )}



                {activeTab === "debug" && (
                  <div className="flex-1 p-4 overflow-auto h-full">
                    <AgentExecutionTrace />
                  </div>
                )}

                {activeTab === "refactor" && (
                  <div className="flex-1 p-4 overflow-auto h-full">
                    <RefactorManagementPanel />
                  </div>
                )}

                {activeTab === "autoexec" && (
                  <div className="flex-1 p-4 overflow-auto h-full">
                    <AutoExecutionConfigPanel />
                  </div>
                )}

                {activeTab === "testing" && (
                  <div className="flex-1 p-4 overflow-auto h-full">
                    <IntegrationTestSuite />
                  </div>
                )}


              </div>
            </div>
        </div>
      </div>
    </div>
    </TooltipProvider>
  );
};

// ✅ TASK 4.3: Enhanced Task Progress Component (Performance Optimized)
const EnhancedTaskProgress: React.FC<{
  agentId: string;
  taskDescription: string;
  executionUpdates: any[];
}> = React.memo(({ agentId, taskDescription, executionUpdates }) => {
  const [currentPhase, setCurrentPhase] = useState<string>('Initializing');
  const [estimatedTimeRemaining, setEstimatedTimeRemaining] = useState<number | null>(null);
  const [startTime] = useState<number>(Date.now());

  // Calculate current phase based on execution updates
  useEffect(() => {
    const latestUpdate = executionUpdates[executionUpdates.length - 1];
    if (latestUpdate) {
      switch (latestUpdate.type) {
        case 'file_progress':
          setCurrentPhase('Analyzing & Planning');
          break;
        case 'code_generation':
          setCurrentPhase('Generating Code');
          break;
        case 'validation':
          setCurrentPhase('Validating & Testing');
          break;
        case 'completion':
          setCurrentPhase('Completed');
          break;
        default:
          setCurrentPhase('Processing');
      }
    }
  }, [executionUpdates]);

  // Calculate estimated time remaining
  useEffect(() => {
    const elapsedTime = Date.now() - startTime;
    const progressUpdates = executionUpdates.filter(u => u.data.progress !== undefined);

    if (progressUpdates.length > 0) {
      const latestProgress = progressUpdates[progressUpdates.length - 1].data.progress;
      if (latestProgress > 0 && latestProgress < 100) {
        const estimatedTotal = (elapsedTime / latestProgress) * 100;
        const remaining = estimatedTotal - elapsedTime;
        setEstimatedTimeRemaining(Math.max(0, remaining));
      }
    }
  }, [executionUpdates, startTime]);

  const phases = [
    { name: 'Initializing', icon: '🚀', completed: true },
    { name: 'Analyzing & Planning', icon: '🔍', completed: currentPhase !== 'Initializing' },
    { name: 'Generating Code', icon: '⚡', completed: ['Validating & Testing', 'Completed'].includes(currentPhase) },
    { name: 'Validating & Testing', icon: '✅', completed: currentPhase === 'Completed' },
    { name: 'Completed', icon: '🎉', completed: currentPhase === 'Completed' }
  ];

  return (
    <Card className="border-blue-200 dark:border-blue-800">
      <CardHeader className="pb-3">
        <CardTitle className="text-base flex items-center gap-2">
          <RefreshCw className="h-4 w-4 animate-spin text-blue-600" />
          {agentId} - Active Task
        </CardTitle>
        <CardDescription className="text-sm">
          {taskDescription}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Phase Progress */}
        <div className="space-y-3">
          <div className="flex justify-between text-sm">
            <span>Current Phase: <strong>{currentPhase}</strong></span>
            {estimatedTimeRemaining && (
              <span className="text-muted-foreground">
                ~{Math.round(estimatedTimeRemaining / 1000)}s remaining
              </span>
            )}
          </div>

          {/* Phase Timeline */}
          <div className="flex items-center gap-2 overflow-x-auto pb-2">
            {phases.map((phase, index) => (
              <div key={phase.name} className="flex items-center gap-2 flex-shrink-0">
                <div className={`flex items-center gap-1 px-2 py-1 rounded-md text-xs ${
                  phase.completed
                    ? 'bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300'
                    : phase.name === currentPhase
                    ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300'
                    : 'bg-gray-100 dark:bg-gray-800 text-gray-500'
                }`}>
                  <span>{phase.icon}</span>
                  <span>{phase.name}</span>
                </div>
                {index < phases.length - 1 && (
                  <div className={`w-4 h-0.5 ${
                    phase.completed ? 'bg-green-300' : 'bg-gray-300'
                  }`} />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Recent Updates */}
        {executionUpdates.length > 0 && (
          <div className="space-y-2">
            <div className="text-sm font-medium">Recent Activity</div>
            <div className="space-y-1 max-h-24 overflow-y-auto">
              {executionUpdates.slice(-3).reverse().map((update, index) => (
                <div key={index} className="text-xs text-muted-foreground flex items-center gap-2">
                  <div className="w-1 h-1 bg-blue-500 rounded-full flex-shrink-0" />
                  <span>{update.data.message || `${update.type} update`}</span>
                  <span className="ml-auto">
                    {new Date(update.timestamp).toLocaleTimeString()}
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
});

// ✅ PERFORMANCE OPTIMIZATION: Memoized Agent Orchestrator Panel Component
const AgentOrchestratorPanel: React.FC<{ onTaskSubmit: (task: string) => Promise<string> }> = React.memo(({ onTaskSubmit }) => {
  const sharedState = useSharedAgentState();
  const [taskInput, setTaskInput] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [lastTaskId, setLastTaskId] = useState<string | null>(null);

  // ✅ TASK 4.1: Real-time execution updates state
  const realTimeMetrics = useRealTimeMetrics();
  const [executionUpdates, setExecutionUpdates] = useState<any[]>([]);

  // Monaco integration available but not currently used in this component

  const handleSubmit = async () => {
    if (!taskInput.trim()) return;

    setIsSubmitting(true);
    try {
      const taskId = await onTaskSubmit(taskInput.trim());
      setLastTaskId(taskId);
      setTaskInput(''); // Clear input after successful submission
    } catch (error) {
      console.error('Task submission failed:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
      e.preventDefault();
      handleSubmit();
    }
  };

  const activeTasks = sharedState.getActiveTasks();
  const recentTasks = sharedState.tasks.slice(-5).reverse();

  // Group tasks by orchestration relationships for status display
  const orchestratorTasks = sharedState.tasks.filter(task =>
    task.description.startsWith('[ORCHESTRATOR]') || task.agentId === 'micromanager'
  );
  const subtasks = sharedState.tasks.filter(task =>
    !task.description.startsWith('[ORCHESTRATOR]') && task.agentId !== 'micromanager'
  );

  return (
    <div className="space-y-6">
      {/* Task Input Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5" />
            AI Agent Command Center
            <Tooltip>
              <TooltipTrigger asChild>
                <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
              </TooltipTrigger>
              <TooltipContent className="max-w-sm">
                <p className="font-medium mb-2">🤖 AI Agent Command Center</p>
                <p className="text-sm mb-2">This is the central hub for submitting tasks to your AI agent system.</p>
                <div className="text-xs space-y-1">
                  <p>• <strong>Micromanager:</strong> Automatically breaks down complex tasks</p>
                  <p>• <strong>Agent Selection:</strong> Choose specific agents for specialized tasks</p>
                  <p>• <strong>Real-time Updates:</strong> Monitor execution progress live</p>
                  <p>• <strong>Orchestration:</strong> Coordinates multiple agents automatically</p>
                </div>
              </TooltipContent>
            </Tooltip>
          </CardTitle>
            <CardDescription>
              Submit tasks to the AI agent system. The Micromanager will analyze, decompose, and orchestrate execution.
            </CardDescription>
          </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-3">
            <div className="flex gap-2">
              <Tooltip>
                <TooltipTrigger asChild>
                  <select
                    value={sharedState.selectedAgent}
                    onChange={(e) => sharedState.setSelectedAgent(e.target.value)}
                    className="px-3 py-2 border border-input rounded-md bg-background text-sm min-w-[200px]"
                  >
                    <option value="micromanager">🤖 Micromanager (Orchestrator)</option>
                    <option value="intern">1️⃣ Intern Agent</option>
                    <option value="junior">2️⃣ Junior Agent</option>
                    <option value="midlevel">3️⃣ MidLevel Agent</option>
                    <option value="senior">4️⃣ Senior Agent</option>
                    <option value="researcher">📘 Researcher Agent</option>
                    <option value="architect">🏗️ Architect Agent</option>
                    <option value="designer">🎨 Designer Agent</option>
                    <option value="tester">🧪 Tester Agent</option>
                  </select>
                </TooltipTrigger>
                <TooltipContent className="max-w-sm">
                  <p className="font-medium mb-2">🎯 Agent Selection Guide</p>
                  <div className="text-xs space-y-1">
                    <p>• <strong>Micromanager:</strong> Best for complex tasks requiring orchestration</p>
                    <p>• <strong>Intern/Junior:</strong> Simple tasks, basic operations</p>
                    <p>• <strong>MidLevel/Senior:</strong> Complex development tasks</p>
                    <p>• <strong>Researcher:</strong> Information gathering, analysis</p>
                    <p>• <strong>Architect:</strong> System design, planning</p>
                    <p>• <strong>Designer:</strong> UI/UX, visual tasks</p>
                    <p>• <strong>Tester:</strong> Quality assurance, testing</p>
                  </div>
                </TooltipContent>
              </Tooltip>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    onClick={handleSubmit}
                    disabled={isSubmitting || !taskInput.trim()}
                    className="min-w-[100px]"
                  >
                    {isSubmitting ? (
                      <>
                        <RefreshCw className="h-4 w-4 animate-spin mr-2" />
                        Processing...
                      </>
                    ) : (
                      <>
                        <Play className="h-4 w-4 mr-2" />
                        Execute
                      </>
                    )}
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p className="text-sm">Execute the task with the selected agent</p>
                  <p className="text-xs text-muted-foreground">Tip: Use Ctrl+Enter as a shortcut</p>
                </TooltipContent>
              </Tooltip>
            </div>
            <Textarea
              placeholder="Describe the task you want the AI agent to perform... (Ctrl+Enter to submit)"
              value={taskInput}
              onChange={(e) => setTaskInput(e.target.value)}
              onKeyDown={handleKeyPress}
              rows={4}
              className="resize-none"
            />
            <div className="text-xs text-muted-foreground">
              💡 Tip: Use Ctrl+Enter to quickly submit tasks. The Micromanager will automatically decompose complex tasks.
            </div>
          </div>

          {lastTaskId && (
            <div className="p-3 bg-green-50 dark:bg-green-950 border border-green-200 dark:border-green-800 rounded-md">
              <div className="flex items-center gap-2 text-green-700 dark:text-green-300">
                <CheckCircle className="h-4 w-4" />
                <span className="text-sm font-medium">Task submitted successfully!</span>
              </div>
              <div className="text-xs text-green-600 dark:text-green-400 mt-1">
                Task ID: {lastTaskId}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* ✅ TASK 4.3: Enhanced Task Progress for Active Tasks */}
      {realTimeMetrics.activeAgents > 0 && realTimeMetrics.executionUpdates.length > 0 && (
        <EnhancedTaskProgress
          agentId={realTimeMetrics.executionUpdates[realTimeMetrics.executionUpdates.length - 1]?.agentId || 'Unknown'}
          taskDescription={taskInput || 'Processing task...'}
          executionUpdates={realTimeMetrics.executionUpdates}
        />
      )}

      {/* ✅ TASK 4.1: Real-time Execution Updates */}
      {realTimeMetrics.executionUpdates.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Live Execution Updates
            </CardTitle>
            <CardDescription>Real-time updates from agent execution</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 max-h-48 overflow-y-auto">
              {realTimeMetrics.executionUpdates.slice(-10).reverse().map((update, index) => (
                <div key={index} className="flex items-start gap-3 p-2 border rounded-md">
                  <div className="flex-shrink-0 mt-1">
                    {update.type === 'completion' ? (
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    ) : update.type === 'error' ? (
                      <AlertTriangle className="h-4 w-4 text-red-600" />
                    ) : (
                      <RefreshCw className="h-4 w-4 text-blue-600 animate-spin" />
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="text-sm font-medium">{update.agentId}</span>
                      <Badge variant="outline" className="text-xs">
                        {update.type}
                      </Badge>
                      <span className="text-xs text-muted-foreground">
                        {new Date(update.timestamp).toLocaleTimeString()}
                      </span>
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {update.data.message || 'Processing...'}
                    </div>
                    {update.data.filePath && (
                      <div className="text-xs text-blue-600 mt-1 font-mono">
                        📁 {update.data.filePath}
                      </div>
                    )}
                    {update.data.progress !== undefined && (
                      <div className="mt-2">
                        <Progress value={update.data.progress} className="h-1" />
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Quick Status Overview */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2">
              Active Tasks
              <Tooltip>
                <TooltipTrigger asChild>
                  <Info className="h-4 w-4 text-muted-foreground cursor-help" />
                </TooltipTrigger>
                <TooltipContent>
                  <p className="text-sm">Tasks currently being executed by agents</p>
                  <p className="text-xs text-muted-foreground">Shows real-time execution status</p>
                </TooltipContent>
              </Tooltip>
            </CardTitle>
            <CardDescription>{activeTasks.length} currently executing</CardDescription>
          </CardHeader>
          <CardContent>
            {activeTasks.length === 0 ? (
              <p className="text-sm text-muted-foreground">No active tasks</p>
            ) : (
              <div className="space-y-2">
                {activeTasks.slice(0, 3).map(task => (
                  <div key={task.id} className="p-2 border rounded-md">
                    <div className="font-medium text-sm break-words">{task.description}</div>
                    <div className="text-xs text-muted-foreground">
                      Agent: {task.agentId} | Priority: {task.priority}
                    </div>
                  </div>
                ))}
                {activeTasks.length > 3 && (
                  <div className="text-xs text-muted-foreground text-center">
                    +{activeTasks.length - 3} more tasks
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2">
              Recent Tasks
              <Tooltip>
                <TooltipTrigger asChild>
                  <Info className="h-4 w-4 text-muted-foreground cursor-help" />
                </TooltipTrigger>
                <TooltipContent>
                  <p className="text-sm">Recently submitted tasks and their completion status</p>
                  <p className="text-xs text-muted-foreground">Helps track task history and success rates</p>
                </TooltipContent>
              </Tooltip>
            </CardTitle>
            <CardDescription>Last {recentTasks.length} submitted</CardDescription>
          </CardHeader>
          <CardContent>
            {recentTasks.length === 0 ? (
              <p className="text-sm text-muted-foreground">No recent tasks</p>
            ) : (
              <div className="space-y-2">
                {recentTasks.slice(0, 3).map(task => (
                  <div key={task.id} className="p-2 border rounded-md">
                    <div className="font-medium text-sm break-words">{task.description}</div>
                    <div className="text-xs text-muted-foreground flex items-center justify-between">
                      <span>Agent: {task.agentId}</span>
                      <Badge variant={
                        task.status === 'completed' ? 'default' :
                        task.status === 'failed' ? 'destructive' :
                        task.status === 'running' ? 'secondary' : 'outline'
                      }>
                        {task.status}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2">
              System Status
              <Tooltip>
                <TooltipTrigger asChild>
                  <Info className="h-4 w-4 text-muted-foreground cursor-help" />
                </TooltipTrigger>
                <TooltipContent className="max-w-sm">
                  <p className="text-sm font-medium mb-2">📊 System Status Overview</p>
                  <div className="text-xs space-y-1">
                    <p>• <strong>Active Agents:</strong> Number of agents currently available</p>
                    <p>• <strong>Queue Length:</strong> Tasks waiting to be processed</p>
                    <p>• <strong>Success Rate:</strong> Percentage of successfully completed tasks</p>
                    <p>• <strong>Kanban Cards:</strong> Tasks linked to project management</p>
                    <p>• <strong>Orchestrations:</strong> Complex multi-agent workflows</p>
                  </div>
                </TooltipContent>
              </Tooltip>
            </CardTitle>
            <CardDescription>Agent system & Kanban integration</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between text-sm">
                <span>Active Agents</span>
                <span className="font-medium">
                  {sharedState.agents.filter(a => a.status === 'busy' || a.status === 'idle').length}
                </span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Queue Length</span>
                <span className="font-medium">
                  {sharedState.tasks.filter(t => t.status === 'pending').length}
                </span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Success Rate</span>
                <span className="font-medium text-green-600">
                  {sharedState.tasks.length > 0 ?
                    Math.round((sharedState.tasks.filter(t => t.status === 'completed').length / sharedState.tasks.length) * 100) : 0
                  }%
                </span>
              </div>
              <div className="flex justify-between text-sm">
                <span>📋 Kanban Cards</span>
                <span className="font-medium text-blue-600">
                  {subtasks.length} linked
                </span>
              </div>
              <div className="flex justify-between text-sm">
                <span>🤖 Orchestrations</span>
                <span className="font-medium text-purple-600">
                  {orchestratorTasks.length} active
                </span>
              </div>
              <div className="flex justify-between text-sm">
                <span>🚀 Dispatched Tasks</span>
                <span className="font-medium text-orange-600">
                  {activeTasks.length} executing
                </span>
              </div>
              <div className="flex justify-between text-sm">
                <span>🔗 Dependencies</span>
                <span className="font-medium text-blue-600">
                  Coordinated
                </span>
              </div>
              <div className="flex justify-between text-sm">
                <span>📊 Success Rate</span>
                <span className="font-medium text-green-600">
                  Real-time
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
});

// TaskManagementPanel component removed - redundant with Agents tab functionality

// MetricsPanel component removed - redundant with Analytics tab functionality

// ✅ TASK 5.3: Enhanced Optimization Panel Component - Real performance analysis
const OptimizationPanel: React.FC<{
  optimizations: any[];
  agentManager: CompleteAgentManager;
}> = ({ optimizations, agentManager }) => {
  const realTimeMetrics = useRealTimeMetrics()
  const [realOptimizations, setRealOptimizations] = useState<any[]>([])
  const [isAnalyzing, setIsAnalyzing] = useState(false)

  // ✅ Generate real optimization suggestions based on performance data
  useEffect(() => {
    const generateRealOptimizations = () => {
      if (!realTimeMetrics) return

      const suggestions: any[] = []

      // Analyze system health
      if (realTimeMetrics.systemHealthScore < 70) {
        suggestions.push({
          id: 'system-health-low',
          priority: 'high',
          targetAgent: 'System',
          description: 'System health is below optimal threshold. Consider reviewing agent workloads and error rates.',
          expectedImpact: 0.25,
          effort: 6,
          category: 'performance',
          data: {
            suggestions: [
              'Review failed tasks and error patterns',
              'Consider redistributing workload across agents',
              'Check for resource constraints or bottlenecks'
            ]
          }
        })
      }

      // Analyze agent performance
      realTimeMetrics.agentStatuses.forEach(agent => {
        if (agent.healthScore < 60) {
          suggestions.push({
            id: `agent-health-${agent.agentId}`,
            priority: 'medium',
            targetAgent: agent.name,
            description: `${agent.name} health score is low (${agent.healthScore.toFixed(1)}%). Performance optimization needed.`,
            expectedImpact: 0.15,
            effort: 4,
            category: 'agent-specific',
            data: {
              suggestions: [
                'Review recent task failures and error patterns',
                'Consider adjusting task complexity or timeout settings',
                'Monitor token usage and response times'
              ]
            }
          })
        }

        if (agent.errorCount > 5) {
          suggestions.push({
            id: `agent-errors-${agent.agentId}`,
            priority: 'high',
            targetAgent: agent.name,
            description: `${agent.name} has ${agent.errorCount} errors. Error handling needs improvement.`,
            expectedImpact: 0.30,
            effort: 7,
            category: 'reliability',
            data: {
              suggestions: [
                'Implement better error recovery mechanisms',
                'Add input validation and sanitization',
                'Consider fallback strategies for common failures'
              ]
            }
          })
        }
      })

      // Analyze token usage efficiency
      const avgTokensPerTask = realTimeMetrics.totalTasks > 0 ?
        realTimeMetrics.totalTokensUsed / realTimeMetrics.totalTasks : 0

      if (avgTokensPerTask > 5000) {
        suggestions.push({
          id: 'token-efficiency',
          priority: 'medium',
          targetAgent: 'All Agents',
          description: `High token usage detected (${avgTokensPerTask.toFixed(0)} tokens/task). Cost optimization recommended.`,
          expectedImpact: 0.20,
          effort: 5,
          category: 'cost-optimization',
          data: {
            suggestions: [
              'Optimize prompt engineering for more concise responses',
              'Implement response caching for common queries',
              'Consider using more efficient models for simple tasks'
            ]
          }
        })
      }

      // Analyze response time
      if (realTimeMetrics.averageResponseTime > 30000) { // 30 seconds
        suggestions.push({
          id: 'response-time',
          priority: 'medium',
          targetAgent: 'System',
          description: `Average response time is high (${(realTimeMetrics.averageResponseTime / 1000).toFixed(1)}s). Performance tuning needed.`,
          expectedImpact: 0.18,
          effort: 6,
          category: 'performance',
          data: {
            suggestions: [
              'Implement parallel processing for independent tasks',
              'Optimize API call patterns and reduce latency',
              'Consider task complexity reduction or splitting'
            ]
          }
        })
      }

      // Success rate analysis
      const successRate = realTimeMetrics.totalTasks > 0 ?
        (realTimeMetrics.successfulTasks / realTimeMetrics.totalTasks) * 100 : 100

      if (successRate < 80) {
        suggestions.push({
          id: 'success-rate',
          priority: 'high',
          targetAgent: 'System',
          description: `Task success rate is low (${successRate.toFixed(1)}%). Reliability improvements needed.`,
          expectedImpact: 0.35,
          effort: 8,
          category: 'reliability',
          data: {
            suggestions: [
              'Implement comprehensive input validation',
              'Add retry mechanisms for transient failures',
              'Improve error handling and recovery strategies'
            ]
          }
        })
      }

      // If no issues found, add positive feedback
      if (suggestions.length === 0) {
        suggestions.push({
          id: 'system-optimal',
          priority: 'low',
          targetAgent: 'System',
          description: 'System is performing optimally! All metrics are within acceptable ranges.',
          expectedImpact: 0.05,
          effort: 1,
          category: 'maintenance',
          data: {
            suggestions: [
              'Continue monitoring performance metrics',
              'Consider implementing additional automation',
              'Document current best practices for future reference'
            ]
          }
        })
      }

      setRealOptimizations(suggestions)
    }

    generateRealOptimizations()

    // Update optimizations every 60 seconds
    const interval = setInterval(generateRealOptimizations, 60000)
    return () => clearInterval(interval)
  }, [realTimeMetrics])

  // ✅ Combine real optimizations with any existing mock optimizations
  const allSuggestions = [...realOptimizations, ...optimizations]

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Performance Optimization
            <Tooltip>
              <TooltipTrigger asChild>
                <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
              </TooltipTrigger>
              <TooltipContent className="max-w-sm">
                <p className="font-medium mb-2">⚡ Performance Optimization</p>
                <p className="text-sm mb-2">AI-powered analysis that identifies performance bottlenecks and suggests improvements.</p>
                <div className="text-xs space-y-1">
                  <p>• <strong>Real-time Analysis:</strong> Continuous monitoring of system metrics</p>
                  <p>• <strong>Priority Levels:</strong> High/Medium/Low impact recommendations</p>
                  <p>• <strong>Impact & Effort:</strong> Expected improvement vs implementation cost</p>
                  <p>• <strong>Categories:</strong> Performance, reliability, cost optimization</p>
                </div>
              </TooltipContent>
            </Tooltip>
          </CardTitle>
          <CardDescription>Real-time analysis and recommendations based on system performance</CardDescription>
        </CardHeader>
        <CardContent>
          {allSuggestions.length > 0 ? (
            <div className="space-y-4">
              {allSuggestions.map(opt => (
                <div key={opt.id} className="p-4 border rounded-md hover:bg-muted/50 transition-colors">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <Badge variant={opt.priority === 'high' ? 'destructive' : opt.priority === 'medium' ? 'default' : 'secondary'}>
                        {opt.priority}
                      </Badge>
                      <span className="font-medium">{opt.targetAgent}</span>
                      {opt.category && (
                        <Badge variant="outline" className="text-xs">
                          {opt.category}
                        </Badge>
                      )}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      Impact: {isNaN(opt.expectedImpact) ? '0' : (opt.expectedImpact * 100).toFixed(0)}% | Effort: {opt.effort || 0}/10
                    </div>
                  </div>
                  <p className="text-sm">{opt.description}</p>
                  {opt.data?.suggestions && (
                    <div className="mt-2">
                      <div className="text-xs font-medium text-muted-foreground mb-1">Recommendations:</div>
                      <ul className="text-xs text-muted-foreground list-disc list-inside">
                        {opt.data.suggestions.map((suggestion: string, index: number) => (
                          <li key={index}>{suggestion}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              <TrendingUp className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p>Analyzing system performance...</p>
              <p className="text-sm">Optimization suggestions will appear here</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Learning Patterns */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            Learning Patterns
            <Tooltip>
              <TooltipTrigger asChild>
                <Info className="h-4 w-4 text-muted-foreground cursor-help" />
              </TooltipTrigger>
              <TooltipContent className="max-w-sm">
                <p className="font-medium mb-2">🧠 Learning Patterns</p>
                <p className="text-sm mb-2">AI system learns from past executions to identify successful patterns and common failure modes.</p>
                <div className="text-xs space-y-1">
                  <p>• <strong>Success Patterns:</strong> Strategies that work well</p>
                  <p>• <strong>Failure Patterns:</strong> Common error scenarios to avoid</p>
                  <p>• <strong>Optimization:</strong> Performance improvement opportunities</p>
                  <p>• <strong>Frequency:</strong> How often pattern occurs</p>
                  <p>• <strong>Effectiveness:</strong> Success rate of pattern</p>
                </div>
              </TooltipContent>
            </Tooltip>
          </CardTitle>
          <CardDescription>Patterns identified by the learning system</CardDescription>
        </CardHeader>
        <CardContent>
          <LearningPatternsDisplay agentManager={agentManager} />
        </CardContent>
      </Card>
    </div>
  );
};

// Learning Patterns Display Component
const LearningPatternsDisplay: React.FC<{ agentManager: CompleteAgentManager }> = ({ agentManager }) => {
  const [patterns, setPatterns] = useState(agentManager.getLearningPatterns());
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  useEffect(() => {
    const interval = setInterval(() => {
      setPatterns(agentManager.getLearningPatterns());
    }, 10000);

    return () => clearInterval(interval);
  }, [agentManager]);

  const categories = ['all', 'success', 'failure', 'optimization', 'error_resolution'];
  const filteredPatterns = selectedCategory === 'all' ?
    patterns : patterns.filter(p => p.category === selectedCategory);

  return (
    <div className="space-y-4">
      <div className="flex gap-2">
        {categories.map(category => (
          <Button
            key={category}
            variant={selectedCategory === category ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedCategory(category)}
          >
            {category.charAt(0).toUpperCase() + category.slice(1)}
          </Button>
        ))}
      </div>

      <div className="space-y-3 max-h-64 overflow-auto">
        {filteredPatterns.slice(0, 10).map(pattern => (
          <div key={pattern.id} className="p-3 border rounded-md">
            <div className="flex items-center justify-between mb-1">
              <Badge variant={
                pattern.category === 'success' ? 'default' :
                pattern.category === 'failure' ? 'destructive' :
                'secondary'
              }>
                {pattern.category}
              </Badge>
              <div className="text-xs text-muted-foreground">
                Frequency: {pattern.frequency || 0} | Effectiveness: {isNaN(pattern.effectiveness) ? '0' : (pattern.effectiveness * 100).toFixed(0)}%
              </div>
            </div>
            <p className="text-sm font-medium mb-1">{pattern.pattern}</p>
            {pattern.recommendations.length > 0 && (
              <div className="text-xs text-muted-foreground">
                <strong>Recommendations:</strong> {pattern.recommendations.slice(0, 2).join(', ')}
              </div>
            )}
          </div>
        ))}
        {filteredPatterns.length === 0 && (
          <p className="text-sm text-muted-foreground">No patterns found for this category</p>
        )}
      </div>
    </div>
  );
};

// SystemPanel component removed - violates User Guidelines (mock functionality)

// Integration Instructions Component
export const IntegrationInstructions: React.FC = () => {
  return (
    <Card className="m-4">
      <CardHeader>
        <CardTitle>🚀 Agent System Integration Instructions</CardTitle>
        <CardDescription>How to integrate the complete agent system into your application</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="bg-blue-50 dark:bg-blue-950 p-4 rounded-md">
          <h3 className="font-semibold mb-2">📋 Integration Checklist</h3>
          <div className="space-y-2 text-sm">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span>✅ All agent implementations completed</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span>✅ Middleware components implemented</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span>✅ Complete Agent Manager with orchestration</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span>✅ Settings management system</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span>✅ Health monitoring and error resolution</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span>✅ Continuous learning system</span>
            </div>
          </div>
        </div>

        <div className="bg-yellow-50 dark:bg-yellow-950 p-4 rounded-md">
          <h3 className="font-semibold mb-2">⚠️ Integration Steps</h3>
          <ol className="text-sm space-y-2 list-decimal list-inside">
            <li>Copy all agent files to <code>components/agents/</code></li>
            <li>Copy middleware files to <code>components/middleware/</code></li>
            <li>Copy settings files to <code>components/settings/</code></li>
            <li>Update <code>components/agents/index.ts</code> with all exports</li>
            <li>Replace existing AgentManager with CompleteAgentManager</li>
            <li>Update main application to use CompleteAgentSystem component</li>
            <li>Configure API keys in settings</li>
            <li>Test system functionality with sample tasks</li>
          </ol>
        </div>

        <div className="bg-green-50 dark:bg-green-950 p-4 rounded-md">
          <h3 className="font-semibold mb-2">🎯 Usage Example</h3>
          <pre className="text-sm bg-background p-3 rounded border mt-2 overflow-auto">
{`// In your main application component
import { CompleteAgentSystem } from '@/components/agents/complete-integration';

export default function App() {
  return (
    <div className="h-screen">
      <CompleteAgentSystem />
    </div>
  );
}

// To submit tasks programmatically
const agentManager = CompleteAgentManager.getInstance(); // ✅ Use singleton pattern
const taskId = await agentManager.submitTask(
  "Create a React component for user authentication",
  ["auth.tsx", "types.ts"],
  "high"
);`}
          </pre>
        </div>

        <div className="bg-purple-50 dark:bg-purple-950 p-4 rounded-md">
          <h3 className="font-semibold mb-2">🔧 Key Features Available</h3>
          <ul className="text-sm space-y-1 list-disc list-inside">
            <li>🤖 9 specialized AI agents (Intern → Senior + Specialized)</li>
            <li>🧠 Intelligent task classification and routing</li>
            <li>📊 Real-time health monitoring and performance metrics</li>
            <li>🔄 Automatic error resolution with escalation</li>
            <li>📈 Continuous learning and optimization</li>
            <li>⚙️ Comprehensive settings management</li>
            <li>💰 Cost tracking and resource optimization</li>
            <li>🔒 Privacy-first architecture with local processing</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
};

// ✅ Task 66: Refactor Management Panel Component
const RefactorManagementPanel: React.FC = () => {
  const [refactorBatches, setRefactorBatches] = useState<any[]>([]);
  const [selectedBatch, setSelectedBatch] = useState<string | null>(null);

  // ✅ Subscribe to refactor service updates
  useEffect(() => {
    const unsubscribe = refactorService.subscribe(setRefactorBatches);
    setRefactorBatches(refactorService.getBatches());
    return unsubscribe;
  }, []);

  const handleConfirmRefactor = (batchId: string) => {
    refactorService.updateBatchStatus(batchId, 'applied');
    setSelectedBatch(null);
  };

  const handleRejectRefactor = (batchId: string) => {
    refactorService.updateBatchStatus(batchId, 'failed');
    setSelectedBatch(null);
  };

  const selectedBatchData = refactorBatches.find(batch => batch.id === selectedBatch);

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <RefreshCw className="h-5 w-5" />
            Refactor Management
            <Tooltip>
              <TooltipTrigger asChild>
                <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
              </TooltipTrigger>
              <TooltipContent className="max-w-sm">
                <p className="font-medium mb-2">🔄 Refactor Management</p>
                <p className="text-sm mb-2">Manage complex multi-file refactoring operations proposed by AI agents.</p>
                <div className="text-xs space-y-1">
                  <p>• <strong>Agent Proposals:</strong> Agents suggest code improvements and restructuring</p>
                  <p>• <strong>Preview:</strong> Review changes before applying them</p>
                  <p>• <strong>Batch Operations:</strong> Multiple file changes grouped together</p>
                  <p>• <strong>Safe Application:</strong> Controlled deployment of refactoring changes</p>
                  <p>• <strong>Status Tracking:</strong> Monitor refactoring operation progress</p>
                </div>
              </TooltipContent>
            </Tooltip>
          </CardTitle>
          <CardDescription>
            Manage multi-file refactoring operations proposed by agents
          </CardDescription>
        </CardHeader>
        <CardContent>
          {refactorBatches.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <RefreshCw className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p>No refactor operations pending</p>
              <p className="text-sm">Agents will propose refactors here when needed</p>
            </div>
          ) : (
            <div className="space-y-4">
              {refactorBatches.map(batch => (
                <div key={batch.id} className="p-4 border rounded-lg hover:bg-muted/50 transition-colors">
                  <div className="flex items-center justify-between mb-2">
                    <div>
                      <h3 className="font-medium">{batch.description}</h3>
                      <p className="text-sm text-muted-foreground">
                        {batch.operations.length} operations • Agent: {batch.agentId}
                      </p>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant={
                        batch.status === 'applied' ? 'default' :
                        batch.status === 'failed' ? 'destructive' :
                        batch.status === 'previewing' ? 'secondary' : 'outline'
                      }>
                        {batch.status}
                      </Badge>
                      {batch.status === 'pending' && (
                        <Button
                          size="sm"
                          onClick={() => setSelectedBatch(batch.id)}
                        >
                          Preview
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {selectedBatchData && (
        <RefactorPreview
          batch={selectedBatchData}
          onConfirm={handleConfirmRefactor}
          onReject={handleRejectRefactor}
        />
      )}
    </div>
  );
};

// ✅ TASK 2.1: Wrapped CompleteAgentSystem with RealTimeMetricsProvider
const CompleteAgentSystemWithMetrics: React.FC<{ className?: string }> = ({ className }) => {
  return (
    <RealTimeMetricsProvider>
      <CompleteAgentSystem className={className} />
    </RealTimeMetricsProvider>
  );
};

// Main export with real-time metrics integration
export default CompleteAgentSystemWithMetrics;