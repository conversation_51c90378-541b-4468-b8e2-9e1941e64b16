"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON><PERSON>, GitBranch, X } from "lucide-react"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { CodeFileIcon } from "@/components/file-sidebar/CodeFileIcon"
import { FileSystemItem } from "@/components/file-sidebar/types"
import MonacoEditor from "./monaco-editor"
import { useEditorState, useCreateTabFromFile } from "@/components/editor/editor-state-provider"

// Monaco Editor handles all code content and language detection

// AI suggestion component
const AiSuggestion = ({ visible }: { visible: boolean }) => {
  if (!visible) return null

  // Using a string template to avoid JSX parsing issues with arrow functions
  const codeSnippet = `
<span class="monaco-keyword">function</span> <span class="monaco-function">debounce</span>(fn, delay) {
  <span class="monaco-keyword">let</span> timer;
  <span class="monaco-keyword">return</span> <span class="monaco-keyword">function</span>(...args) {
    clearTimeout(timer);
    timer = setTimeout(() <span class="monaco-keyword">=&gt;</span> fn.apply(this, args), delay);
  };
}

<span class="monaco-comment">// Use debounced version of the scroll handler</span>
<span class="monaco-keyword">const</span> debouncedHandleScroll = <span class="monaco-function">debounce</span>(handleScroll, <span class="monaco-number">100</span>);
window.<span class="monaco-function">addEventListener</span>(<span class="monaco-string">'scroll'</span>, debouncedHandleScroll);
  `.trim()

  return (
    <div className="border-t border-editor-border p-4 bg-background/50">
      <div className="flex items-center mb-2">
        <Sparkles className="h-4 w-4 text-editor-highlight mr-2" />
        <span className="text-sm font-medium text-editor-highlight">AI Suggestion</span>
      </div>
      <p className="text-xs text-muted-foreground mb-3">
        Consider using a more efficient approach for event handling by implementing debounce:
      </p>
      <pre className="bg-accent p-2 rounded-md text-xs font-mono">
        <code className="monaco-syntax" dangerouslySetInnerHTML={{ __html: codeSnippet }} />
      </pre>
      <div className="flex justify-end mt-2">
        <Button variant="ghost" size="sm" className="text-xs h-7 px-2 text-muted-foreground hover:text-foreground">
          Ignore
        </Button>
        <Button
          size="sm"
          className="text-xs h-7 px-2 bg-editor-highlight text-editor-highlight-fg hover:bg-editor-highlight/90 ml-2"
        >
          Apply Suggestion
        </Button>
      </div>
    </div>
  )
}

// Tab component for open files
const EditorTab = ({
  file,
  active,
  onClick,
  onClose,
}: {
  file: any
  active: boolean
  onClick: () => void
  onClose: () => void
}) => {
  return (
    <div
      className={cn(
        "flex items-center h-9 px-3 border-r border-editor-border cursor-pointer group",
        active
          ? "bg-editor-tab-active-bg text-editor-tab-active-fg"
          : "bg-editor-tab-bg text-editor-tab-fg hover:bg-accent/50",
      )}
      onClick={onClick}
    >
      <CodeFileIcon extension={file.type} className="mr-2" />
      <span className="text-sm">{file.name}</span>
      <Button
        variant="ghost"
        size="icon"
        className="h-5 w-5 ml-2 opacity-0 group-hover:opacity-100"
        onClick={(e) => {
          e.stopPropagation()
          onClose()
        }}
      >
        <X className="h-3 w-3" />
      </Button>
    </div>
  )
}

export default function CodeEditor({ file }: { file: FileSystemItem | null }) {
  const { openTabs, activeTab, openTab, closeTab, switchToTab, updateTab } = useEditorState();
  const createTabFromFile = useCreateTabFromFile();
  const [showAiSuggestion, setShowAiSuggestion] = useState(false)

  // Add file to open tabs when a new file is selected
  useEffect(() => {
    if (file) {
      const tabData = createTabFromFile(file);
      openTab(tabData);
    }
  }, [file, openTab, createTabFromFile]);

  // Close a tab
  const handleCloseTab = async (tabId: string) => {
    await closeTab(tabId);
  }

  // Show AI suggestion after a delay for certain file types
  useEffect(() => {
    if (activeTab && activeTab.type && ["js", "jsx", "tsx"].includes(activeTab.type)) {
      const timer = setTimeout(() => {
        setShowAiSuggestion(true)
      }, 2000)
      return () => clearTimeout(timer)
    } else {
      setShowAiSuggestion(false)
    }
  }, [activeTab])

  return (
    <div className="flex flex-col h-full bg-editor-bg text-editor-fg">
      {/* Tabs for open files */}
      <div className="flex border-b border-editor-border overflow-x-auto hide-scrollbar">
        {openTabs.map((tab) => (
          <EditorTab
            key={tab.id}
            file={tab}
            active={activeTab ? activeTab.id === tab.id : false}
            onClick={() => switchToTab(tab.id)}
            onClose={() => handleCloseTab(tab.id)}
          />
        ))}
      </div>

      {/* Editor content */}
      <div className="flex-1 overflow-hidden">
        {activeTab ? (
          <div className="h-full flex flex-col">
            <div className="flex-1">
              <MonacoEditor
                file={activeTab}
                onContentChange={(content) => {
                  // Update the tab content and mark as dirty
                  if (activeTab) {
                    updateTab(activeTab.id, {
                      content,
                      isDirty: content !== (activeTab.content || '')
                    });
                  }
                }}
              />
            </div>
            <AiSuggestion visible={showAiSuggestion} />
          </div>
        ) : (
          <div className="h-full flex items-center justify-center text-muted-foreground">
            <div className="text-center">
              <p>No file selected</p>
              <p className="text-sm mt-2">Select a file from the sidebar to start editing</p>
            </div>
          </div>
        )}
      </div>

      {/* Status bar */}
      <div className="h-6 border-t border-editor-border bg-editor-statusbar-bg flex items-center px-4 text-xs text-editor-statusbar-fg">
        <div className="flex items-center">
          <GitBranch className="h-3 w-3 mr-1" />
          <span>main</span>
        </div>
        <div className="ml-4 flex items-center">
          {activeTab && (
            <>
              <span>{activeTab.type?.toUpperCase() || 'UNKNOWN'}</span>
              <span className="mx-2">•</span>
              <span>UTF-8</span>
              {activeTab.isDirty && (
                <>
                  <span className="mx-2">•</span>
                  <span className="text-orange-500">Modified</span>
                </>
              )}
            </>
          )}
        </div>
        <div className="ml-auto flex items-center space-x-4">
          <span>Ln 1, Col 1</span>
          <span>Spaces: 2</span>
        </div>
      </div>
    </div>
  )
}
