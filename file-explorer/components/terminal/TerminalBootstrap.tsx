import { useEffect, useRef, useState } from 'react';
import { terminalEventBus } from './terminal-event-bus';
import { getGlobalSettingsManager } from '../settings/global-settings';
import { TerminalSettings } from '../settings/settings-manager';

// ✅ Fix SSR: Dynamic imports for browser-only xterm.js modules
let Terminal: any;
let FitAddon: any;
let WebLinksAddon: any;

// ✅ Load xterm modules only on client side
const loadXtermModules = async () => {
  if (typeof window === 'undefined') return false;

  try {
    const [terminalModule, fitModule, webLinksModule] = await Promise.all([
      import('@xterm/xterm'),
      import('@xterm/addon-fit'),
      import('@xterm/addon-web-links')
    ]);

    Terminal = terminalModule.Terminal;
    FitAddon = fitModule.FitAddon;
    WebLinksAddon = webLinksModule.WebLinksAddon;

    // Import CSS dynamically
    await import('@xterm/xterm/css/xterm.css');

    return true;
  } catch (error) {
    console.error('Failed to load xterm modules:', error);
    return false;
  }
};

interface TerminalBootstrapProps {
  className?: string;
  onReady?: (terminal: Terminal) => void;
  // ✅ Task 98: Logging callbacks
  onInput?: (input: string) => void;
  onOutput?: (output: string) => void;
  sessionId?: string;
}

export default function TerminalBootstrap({
  className = '',
  onReady,
  onInput,
  onOutput,
  sessionId
}: TerminalBootstrapProps) {
  const terminalRef = useRef<HTMLDivElement>(null);
  const terminalInstance = useRef<any | null>(null);
  const fitAddon = useRef<any | null>(null);
  const webLinksAddon = useRef<any | null>(null);
  const [isReady, setIsReady] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [loadError, setLoadError] = useState<string | null>(null);
  const cleanupRef = useRef<(() => void) | null>(null);
  const [terminalSettings, setTerminalSettings] = useState<TerminalSettings | null>(null);

  // ✅ SSR Guard: Only render on client side
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  // ✅ Load terminal settings
  useEffect(() => {
    if (typeof window !== 'undefined') {
      try {
        const settingsManager = getGlobalSettingsManager();
        const settings = settingsManager.getSettings();
        setTerminalSettings(settings.terminal);

        // Listen for settings changes
        const handleSettingsChange = (newSettings: any) => {
          setTerminalSettings(newSettings.terminal);
        };

        settingsManager.onSettingsChange(handleSettingsChange);
        return () => settingsManager.offSettingsChange(handleSettingsChange);
      } catch (error) {
        console.warn('⚠️ Failed to load terminal settings:', error);
        // Use default settings
        setTerminalSettings({
          theme: 'system',
          fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
          fontSize: 13,
          shell: process.platform === 'win32' ? 'powershell.exe' : 'bash',
          cols: 80,
          rows: 24,
          scrollback: 1000,
          cursorBlink: true,
          lineHeight: 1.2
        });
      }
    }
  }, []);

  useEffect(() => {
    // ✅ SURGICAL FIX: Enhanced safety checks to prevent premature initialization
    if (!isMounted || !terminalRef.current || terminalInstance.current || !terminalSettings) {
      console.log('🔍 TerminalBootstrap: Skipping initialization', {
        isMounted,
        hasTerminalRef: !!terminalRef.current,
        hasInstance: !!terminalInstance.current,
        hasSettings: !!terminalSettings
      });
      return;
    }

    // ✅ Additional check: Ensure DOM element is properly attached
    if (!terminalRef.current.isConnected) {
      console.warn('⚠️ TerminalBootstrap: DOM element not connected, delaying initialization');
      return;
    }

    // ✅ SURGICAL FIX: Add cancellation mechanism to prevent race conditions
    let isCancelled = false;

    const initializeTerminal = async () => {
      try {
        // ✅ SURGICAL FIX: Check cancellation before starting
        if (isCancelled) return;

        setIsLoading(true);
        setLoadError(null);

        // ✅ Load xterm modules first
        const modulesLoaded = await loadXtermModules();
        if (!modulesLoaded || isCancelled) {
          throw new Error('Failed to load terminal modules');
        }

        // ✅ Get theme colors based on settings
        const getThemeColors = () => {
          if (terminalSettings.theme === 'light') {
            return {
              background: '#ffffff',
              foreground: '#000000',
              cursor: '#000000',
              selectionBackground: '#e0e0e0',
            };
          } else if (terminalSettings.theme === 'dark') {
            return {
              background: '#1a1a1a',
              foreground: '#ffffff',
              cursor: '#ffffff',
              selectionBackground: '#3a3a3a',
            };
          } else {
            // System theme - use dark as default, could be enhanced to detect system theme
            return {
              background: '#1a1a1a',
              foreground: '#ffffff',
              cursor: '#ffffff',
              selectionBackground: '#3a3a3a',
            };
          }
        };

        // Initialize terminal with settings-based configuration
        const terminal = new Terminal({
          cursorBlink: terminalSettings.cursorBlink,
          fontSize: terminalSettings.fontSize,
          fontFamily: terminalSettings.fontFamily,
          lineHeight: terminalSettings.lineHeight,
          theme: getThemeColors(),
          cols: terminalSettings.cols,
          rows: terminalSettings.rows,
          scrollback: terminalSettings.scrollback,
          allowTransparency: false,
          convertEol: true,
        });

        // Initialize addons
        const fit = new FitAddon();
        const webLinks = new WebLinksAddon();

        // Load addons
        terminal.loadAddon(fit);
        terminal.loadAddon(webLinks);

        // Store references
        terminalInstance.current = terminal;
        fitAddon.current = fit;
        webLinksAddon.current = webLinks;

        // ✅ SURGICAL FIX: Enhanced DOM validation with cancellation checks
        if (isCancelled) return;

        if (!terminalRef.current) {
          throw new Error('Terminal container element not found - DOM not ready');
        }

        // ✅ Additional validation: Ensure element is properly attached to DOM
        if (!terminalRef.current.isConnected) {
          throw new Error('Terminal container element not connected to DOM');
        }

        // ✅ SURGICAL FIX: Double-check after async operations
        if (isCancelled || !terminalRef.current) {
          throw new Error('Component unmounted during initialization');
        }

        // ✅ Additional validation: Ensure element has dimensions
        const containerRect = terminalRef.current.getBoundingClientRect();
        if (containerRect.width === 0 || containerRect.height === 0) {
          console.warn('⚠️ TerminalBootstrap: Container has zero dimensions, retrying...');

          // ✅ SURGICAL FIX: Enhanced retry mechanism with multiple strategies
          let retryCount = 0;
          const maxRetries = 3;

          while (retryCount < maxRetries && !isCancelled) {
            // Wait for layout to complete using multiple timing strategies
            await new Promise(resolve => {
              requestAnimationFrame(() => {
                setTimeout(resolve, 50 * (retryCount + 1)); // Progressive delay
              });
            });

            // Re-check element existence after delay
            if (isCancelled || !terminalRef.current || !terminalRef.current.isConnected) {
              throw new Error('Terminal container element lost during retry');
            }

            const retryRect = terminalRef.current.getBoundingClientRect();
            if (retryRect.width > 0 && retryRect.height > 0) {
              console.log(`✅ TerminalBootstrap: Container dimensions recovered after ${retryCount + 1} retries`);
              break;
            }

            retryCount++;
            if (retryCount >= maxRetries) {
              throw new Error(`Terminal container has zero dimensions after ${maxRetries} retries`);
            }
          }
        }

        console.log('✅ TerminalBootstrap: Opening terminal in DOM element', {
          width: containerRect.width,
          height: containerRect.height,
          element: terminalRef.current
        });

        // Open terminal in DOM
        terminal.open(terminalRef.current);

        // Fit terminal to container
        fit.fit();

        // Focus terminal
        terminal.focus();

        // Check if we have access to the terminal API
        if (typeof window !== 'undefined' && window.electronAPI?.terminal) {
          // Create PTY process with terminal settings
          const terminalId = await window.electronAPI.terminal.create({
            shell: terminalSettings.shell,
            cols: terminalSettings.cols,
            rows: terminalSettings.rows
          });
          console.log('✅ TerminalBootstrap: PTY created with ID:', terminalId);

          // Set up data listener for PTY output
          const cleanup = window.electronAPI.terminal.listen(terminalId, (data, exit) => {
            if (exit) {
              // Terminal process exited
              console.log('✅ TerminalBootstrap: Terminal process exited:', exit);
              const exitMessage = `\r\n[Process exited with code ${exit.exitCode}]`;
              terminal.writeln(exitMessage);
              // ✅ Task 98: Log process exit
              onOutput?.(exitMessage);
              setIsReady(false);
            } else if (data) {
              // Write PTY output to terminal
              terminal.write(data);
              // ✅ Task 98: Log terminal output
              onOutput?.(data);
            }
          });

          // Set up input handler to send data to PTY
          terminal.onData((data) => {
            window.electronAPI.terminal.write(terminalId, data);
            // ✅ Task 98: Log user input
            onInput?.(data);
          });

          // ✅ Task 103: Enhanced terminal resize handling with debouncing
          let resizeTimeout: NodeJS.Timeout;
          terminal.onResize(({ cols, rows }) => {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(() => {
              window.electronAPI.terminal.resize(terminalId, cols, rows);
              console.log(`✅ TerminalBootstrap: PTY resized to ${cols}x${rows}`);
            }, 100);
          });

          // ✅ Task 103: Enhanced resize handling with multiple triggers and improved timing
          const handleResize = () => {
            if (fit && terminalRef.current && terminal) {
              // Use requestAnimationFrame for smooth resize
              requestAnimationFrame(() => {
                try {
                  fit.fit();
                  // Ensure terminal stays focused after resize
                  if (document.activeElement === terminalRef.current || terminalRef.current?.contains(document.activeElement)) {
                    terminal.focus();
                  }
                  console.log(`✅ TerminalBootstrap: Terminal fitted to container`);
                } catch (error) {
                  console.warn('⚠️ TerminalBootstrap: Resize fit failed:', error);
                }
              });
            }
          };

          // Listen to multiple resize events
          window.addEventListener('resize', handleResize);

          // ✅ Task 103: Enhanced ResizeObserver for accurate container resize detection
          let resizeObserver: ResizeObserver | null = null;
          if (window.ResizeObserver && terminalRef.current) {
            resizeObserver = new ResizeObserver((entries) => {
              for (const entry of entries) {
                // Check if the size actually changed to avoid unnecessary fits
                const { width, height } = entry.contentRect;
                if (width > 0 && height > 0) {
                  console.log(`✅ TerminalBootstrap: Container resized to ${width}x${height}`);
                  handleResize();
                }
              }
            });
            resizeObserver.observe(terminalRef.current);
            console.log(`✅ TerminalBootstrap: ResizeObserver attached to terminal container`);
          }

          // ✅ Task 92: Set up agent output listeners
          const agentOutputUnsubscribe = terminalEventBus.on('agent-output', ({ output, agentName, command, success }) => {
            if (terminal) {
              // Format agent output with colors
              const prefix = agentName ? `[${agentName}]` : '[Agent]';
              const statusColor = success ? '\x1b[1;32m' : '\x1b[1;31m'; // Green for success, red for failure
              const resetColor = '\x1b[0m';

              terminal.writeln(`${statusColor}${prefix}:${resetColor} ${output}`);
            }
          });

          const systemMessageUnsubscribe = terminalEventBus.on('system-message', ({ message, type }) => {
            if (terminal) {
              // Format system messages with colors based on type
              let color = '\x1b[1;37m'; // Default white
              switch (type) {
                case 'info':
                  color = '\x1b[1;36m'; // Cyan
                  break;
                case 'success':
                  color = '\x1b[1;32m'; // Green
                  break;
                case 'warning':
                  color = '\x1b[1;33m'; // Yellow
                  break;
                case 'error':
                  color = '\x1b[1;31m'; // Red
                  break;
              }
              const resetColor = '\x1b[0m';

              terminal.writeln(`${color}${message}${resetColor}`);
            }
          });

          const clearTerminalUnsubscribe = terminalEventBus.on('clear-terminal', () => {
            if (terminal) {
              terminal.clear();
            }
          });

          // Mark as ready
          setIsReady(true);
          setIsLoading(false);
          onReady?.(terminal);

          console.log('✅ TerminalBootstrap: Real shell connected successfully');

          // Store cleanup function
          const cleanupFunction = () => {
            clearTimeout(resizeTimeout);
            window.removeEventListener('resize', handleResize);
            resizeObserver?.disconnect();
            cleanup?.();
            // ✅ Task 92: Cleanup event listeners
            agentOutputUnsubscribe();
            systemMessageUnsubscribe();
            clearTerminalUnsubscribe();
            window.electronAPI.terminal?.dispose(terminalId);
            if (terminalInstance.current) {
              terminalInstance.current.dispose();
              terminalInstance.current = null;
            }
            fitAddon.current = null;
            webLinksAddon.current = null;
            setIsReady(false);
            console.log('✅ TerminalBootstrap: PTY terminal cleaned up');
          };

          cleanupRef.current = cleanupFunction;
          return cleanupFunction;
        } else {
          // Fallback to echo mode if terminal API is not available
          console.warn('⚠️ TerminalBootstrap: Terminal API not available, using echo mode');

          terminal.writeln('🟡 Terminal API not available - Echo mode only');
          terminal.writeln('📝 Type anything to test echo functionality...');
          terminal.write('\r\n$ ');

          // Simple echo functionality for testing
          terminal.onData((data: string) => {
            // ✅ Task 98: Log input in echo mode
            onInput?.(data);

            if (data === '\r') {
              const output = '\r\n$ ';
              terminal.write(output);
              // ✅ Task 98: Log echo output
              onOutput?.(output);
            } else if (data === '\u007F') {
              const output = '\b \b';
              terminal.write(output);
              onOutput?.(output);
            } else {
              terminal.write(data);
              onOutput?.(data);
            }
          });

          // Handle resize events
          const handleResize = () => {
            if (fit) {
              fit.fit();
            }
          };

          window.addEventListener('resize', handleResize);

          // ✅ Task 92: Set up agent output listeners for echo mode too
          const agentOutputUnsubscribe = terminalEventBus.on('agent-output', ({ output, agentName, command, success }) => {
            if (terminal) {
              const prefix = agentName ? `[${agentName}]` : '[Agent]';
              const statusColor = success ? '\x1b[1;32m' : '\x1b[1;31m';
              const resetColor = '\x1b[0m';

              terminal.writeln(`${statusColor}${prefix}:${resetColor} ${output}`);
            }
          });

          const systemMessageUnsubscribe = terminalEventBus.on('system-message', ({ message, type }) => {
            if (terminal) {
              let color = '\x1b[1;37m';
              switch (type) {
                case 'info': color = '\x1b[1;36m'; break;
                case 'success': color = '\x1b[1;32m'; break;
                case 'warning': color = '\x1b[1;33m'; break;
                case 'error': color = '\x1b[1;31m'; break;
              }
              const resetColor = '\x1b[0m';

              terminal.writeln(`${color}${message}${resetColor}`);
            }
          });

          const clearTerminalUnsubscribe = terminalEventBus.on('clear-terminal', () => {
            if (terminal) {
              terminal.clear();
            }
          });

          // Mark as ready
          setIsReady(true);
          setIsLoading(false);
          onReady?.(terminal);

          console.log('✅ TerminalBootstrap: Echo mode initialized');

          // Store cleanup function for echo mode
          const cleanupFunction = () => {
            window.removeEventListener('resize', handleResize);
            // ✅ Task 92: Cleanup event listeners for echo mode
            agentOutputUnsubscribe();
            systemMessageUnsubscribe();
            clearTerminalUnsubscribe();
            if (terminalInstance.current) {
              terminalInstance.current.dispose();
              terminalInstance.current = null;
            }
            fitAddon.current = null;
            webLinksAddon.current = null;
            setIsReady(false);
            console.log('✅ TerminalBootstrap: Echo mode cleaned up');
          };

          cleanupRef.current = cleanupFunction;
          return cleanupFunction;
        }
      } catch (error) {
        console.error('❌ TerminalBootstrap: Initialization failed:', error);
        setIsReady(false);
        setIsLoading(false);
        setLoadError(error instanceof Error ? error.message : 'Failed to initialize terminal');
      }
    };

    initializeTerminal();

    // ✅ SURGICAL FIX: Cleanup function to cancel initialization if component unmounts
    return () => {
      isCancelled = true;
    };
  }, [isMounted, onReady, terminalSettings]);

  // ✅ Task 103: Enhanced global resize effect with improved debouncing
  useEffect(() => {
    let resizeTimeoutId: NodeJS.Timeout;

    const handleGlobalResize = () => {
      if (fitAddon.current && terminalInstance.current) {
        // Clear previous timeout to debounce
        clearTimeout(resizeTimeoutId);

        // Use requestAnimationFrame for smooth resize handling
        resizeTimeoutId = setTimeout(() => {
          requestAnimationFrame(() => {
            try {
              fitAddon.current?.fit();
              // Only focus if terminal was previously focused
              if (document.activeElement === terminalRef.current ||
                  terminalRef.current?.contains(document.activeElement)) {
                terminalInstance.current?.focus();
              }
              console.log(`✅ TerminalBootstrap: Global resize handled`);
            } catch (error) {
              console.warn('⚠️ TerminalBootstrap: Global resize failed:', error);
            }
          });
        }, 100);
      }
    };

    window.addEventListener('resize', handleGlobalResize);

    // ✅ Task 103: Enhanced initial fit with proper timing
    const initialFit = setTimeout(() => {
      if (fitAddon.current && terminalRef.current) {
        requestAnimationFrame(() => {
          try {
            fitAddon.current.fit();
            console.log(`✅ TerminalBootstrap: Initial fit completed`);
          } catch (error) {
            console.warn('⚠️ TerminalBootstrap: Initial fit failed:', error);
          }
        });
      }
    }, 150);

    return () => {
      window.removeEventListener('resize', handleGlobalResize);
      clearTimeout(resizeTimeoutId);
      clearTimeout(initialFit);
    };
  }, []);

  // Handle container click to focus terminal
  const handleContainerClick = () => {
    if (terminalInstance.current) {
      terminalInstance.current.focus();
    }
  };

  // ✅ SSR Guard: Don't render anything on server side
  if (!isMounted) {
    return (
      <div className={`terminal-bootstrap-container relative ${className}`}>
        <div className="w-full h-full min-h-[400px] bg-black text-white overflow-hidden rounded-md border border-gray-700 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
            <p className="text-sm text-gray-300">Loading Terminal...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`terminal-bootstrap-container relative ${className}`}>
      <div
        ref={terminalRef}
        id="terminal-container"
        className="w-full h-full min-h-[400px] bg-black text-white overflow-hidden rounded-md border border-gray-700 focus:border-blue-500 transition-colors"
        tabIndex={0}
        onClick={handleContainerClick}
        style={{
          fontFamily: terminalSettings?.fontFamily || 'Monaco, Menlo, "Ubuntu Mono", monospace',
          fontSize: `${terminalSettings?.fontSize || 13}px`,
          lineHeight: terminalSettings?.lineHeight || 1.2,
          width: '100%',
          height: '100%',
        }}
      />
      {(isLoading || !isReady) && !loadError && (
        <div className="absolute inset-0 flex items-center justify-center bg-black text-white rounded-md">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
            <p className="text-sm text-gray-300">
              {isLoading ? 'Loading Terminal Modules...' : 'Initializing Terminal...'}
            </p>
          </div>
        </div>
      )}
      {loadError && (
        <div className="absolute inset-0 flex items-center justify-center bg-black text-white rounded-md">
          <div className="text-center">
            <div className="text-red-500 text-2xl mb-2">⚠️</div>
            <p className="text-sm text-red-400 mb-2">Terminal Load Error</p>
            <p className="text-xs text-gray-400">{loadError}</p>
          </div>
        </div>
      )}
    </div>
  );
}
