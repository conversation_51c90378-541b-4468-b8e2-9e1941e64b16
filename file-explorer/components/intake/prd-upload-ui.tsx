// components/intake/prd-upload-ui.tsx
import React, { useState, useRef, useCallback, useEffect } from 'react';
import { Button } from '../ui/button';
import { Textarea } from '../ui/textarea';
import { Upload, FileText, Loader2, CheckCircle, XCircle, FileWarning } from 'lucide-react';
import { useToast } from '../ui/use-toast';
import { prdIntakeService, PRDValidationResult, PRDParseResult } from './prd-intake-service';
import { claudeTaskmasterService } from '../../services/claude-taskmaster-service';
import { getGlobalSettingsManager } from '../settings/global-settings';
import { Card, CardContent } from '../ui/card';

interface PRDUploadUIProps {
  onPRDUploaded: (filePath: string, validation: PRDValidationResult, content?: string) => void;
  onPRDParsed: (result: PRDParseResult) => void;
  onValidationChange: (isValid: boolean) => void;
  projectPath?: string;
  className?: string;
  // ✅ ENHANCED: Direct API key props to avoid timing issues
  anthropicApiKey?: string;
  perplexityApiKey?: string;
  // ✅ NEW: Option to disable parsing functionality
  disableParsing?: boolean;
}

interface ParseProgress {
  step: string;
  message: string;
  completed: boolean;
  error?: string;
}

interface ParseState {
  isActive: boolean;
  currentStep: number;
  steps: ParseProgress[];
  startTime?: number;
}

export const PRDUploadUI: React.FC<PRDUploadUIProps> = ({
  onPRDUploaded,
  onPRDParsed,
  onValidationChange,
  projectPath,
  className = '',
  anthropicApiKey,
  perplexityApiKey,
  disableParsing = false
}) => {
  const { toast } = useToast();
  const [prdContent, setPrdContent] = useState('');
  const [isUploading, setIsUploading] = useState(false);
  const [isParsing, setIsParsing] = useState(false);
  const [validationResult, setValidationResult] = useState<PRDValidationResult | null>(null);
  const [parseState, setParseState] = useState<ParseState>({
    isActive: false,
    currentStep: 0,
    steps: []
  });
  const [claudeTaskmasterStatus, setClaudeTaskmasterStatus] = useState<{ isInstalled: boolean }>({
    isInstalled: false
  });
  const fileInputRef = useRef<HTMLInputElement>(null);

  const validate = useCallback(async (content: string) => {
    const result = prdIntakeService.validatePRD(content);
    setValidationResult(result);
    onValidationChange(result.isValid);

    // If content is valid and we have a project path, save the PRD file
    if (result.isValid && content.trim() && projectPath) {
      try {
        // Save PRD file to scripts directory (as per memory requirement)
        const prdFilePath = `${projectPath}/scripts/prd.txt`;

        // Try to save PRD content to scripts directory
        // First try createFile, if it fails due to existing file, use saveFile to overwrite
        let saveResult = await window.electronAPI.createFile(prdFilePath, content);

        if (!saveResult.success && saveResult.error === 'File already exists') {
          // File exists, overwrite it using saveFile
          console.log('📝 PRD file exists, updating content...');
          saveResult = await window.electronAPI.saveFile(prdFilePath, content);
        }

        if (saveResult.success) {
          console.log('✅ PRD file saved to scripts directory:', prdFilePath);
          // Call the onPRDUploaded callback with the file path, validation result, and content
          onPRDUploaded(prdFilePath, result, content);
        } else {
          console.warn('⚠️ Failed to save PRD file:', saveResult.error);
          // Still call onPRDUploaded but with a fallback path
          onPRDUploaded(`${projectPath}/scripts/prd.txt`, result, content);
        }
      } catch (error) {
        console.warn('⚠️ Error saving PRD file:', error);
        // Still call onPRDUploaded but with a fallback path
        onPRDUploaded(`${projectPath}/scripts/prd.txt`, result, content);
      }
    }
  }, [onValidationChange, onPRDUploaded, projectPath]);

  const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setIsUploading(true);
      const reader = new FileReader();
      reader.onload = async (event) => {
        const content = event.target?.result as string;
        setPrdContent(content);
        await validate(content);
        setIsUploading(false);
        toast({ title: "PRD File Loaded", description: `${file.name} content has been loaded into the text area.` });
      };
      reader.readAsText(file);
    }
  }, [toast, validate]);

  const handleContentChange = useCallback(async (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newContent = e.target.value;
    setPrdContent(newContent);
    await validate(newContent);
  }, [validate]);

  // ✅ Progress tracking helpers
  const initializeProgress = useCallback(() => {
    const steps: ParseProgress[] = [
      { step: 'validation', message: 'Validating PRD content...', completed: false },
      { step: 'environment', message: 'Checking environment...', completed: false },
      { step: 'installation', message: 'Verifying Claude Taskmaster installation...', completed: false },
      { step: 'apikeys', message: 'Checking API keys...', completed: false },
      { step: 'configuration', message: 'Configuring Claude Taskmaster...', completed: false },
      { step: 'saving', message: 'Saving PRD file to scripts directory...', completed: false },
      { step: 'parsing', message: 'Parsing PRD with Claude Taskmaster...', completed: false },
      { step: 'generation', message: 'Generating task files...', completed: false },
      { step: 'completion', message: 'Finalizing...', completed: false }
    ];

    setParseState({
      isActive: true,
      currentStep: 0,
      steps,
      startTime: Date.now()
    });
  }, []);

  const updateProgress = useCallback((stepIndex: number, completed: boolean, error?: string) => {
    setParseState(prev => ({
      ...prev,
      currentStep: completed ? stepIndex + 1 : stepIndex,
      steps: prev.steps.map((step, index) =>
        index === stepIndex
          ? { ...step, completed, error }
          : step
      )
    }));
  }, []);

  const completeProgress = useCallback((success: boolean) => {
    setParseState(prev => ({
      ...prev,
      isActive: false,
      currentStep: success ? prev.steps.length : prev.currentStep
    }));
  }, []);

  const handleParse = useCallback(async () => {
    console.log('🚀 [PRD Upload] Parse button clicked!');
    console.log('🚀 [PRD Upload] Validation result:', validationResult);
    console.log('🚀 [PRD Upload] PRD content length:', prdContent?.length);
    console.log('🚀 [PRD Upload] Project path:', projectPath);

    // Step 0: Validation
    initializeProgress();
    updateProgress(0, false);

    if (!validationResult?.isValid || !prdContent) {
      console.log('❌ [PRD Upload] Parse blocked - validation failed');
      console.log('❌ [PRD Upload] Validation details:', { isValid: validationResult?.isValid, hasContent: !!prdContent });
      updateProgress(0, false, "Invalid PRD content");
      completeProgress(false);
      toast({
        title: "Invalid PRD",
        description: "Please provide a valid PRD before parsing.",
        variant: "destructive",
      });
      return;
    }

    if (!projectPath) {
      updateProgress(0, false, "No project path provided");
      completeProgress(false);
      toast({
        title: "No Project Path",
        description: "Project path is required for Claude Taskmaster integration.",
        variant: "destructive",
      });
      return;
    }

    updateProgress(0, true);
    setIsParsing(true);

    try {
      // Step 1: Check environment
      updateProgress(1, false);
      const isElectronAvailable = typeof window !== 'undefined' &&
                                 window.electronAPI?.taskmaster !== undefined;

      if (!isElectronAvailable) {
        console.log('⚠️ Running in web mode - using fallback PRD parsing');
        updateProgress(1, true);

        // Skip to parsing step for web mode
        updateProgress(6, false);

        // Fallback parsing for web mode
        await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate processing time

        // Generate mock tasks based on content analysis
        const words = prdContent.split(/\s+/).length;
        const tasksGenerated = Math.max(3, Math.floor(words / 100));

        updateProgress(6, true);
        updateProgress(8, true); // Mark completion
        setIsParsing(false);
        completeProgress(true);

        toast({
          title: "PRD Parsed (Web Mode)",
          description: `Generated ${tasksGenerated} tasks using fallback parsing. Use desktop app for full Claude Taskmaster integration.`,
        });

        onPRDParsed({
          success: true,
          tasksGenerated,
          filePath: `${projectPath}/scripts/prd.txt`
        });
        return;
      }

      updateProgress(1, true);

      // Step 2: Check installation
      updateProgress(2, false);
      let installCheck;
      try {
        // Reduced timeout since we improved the check methods
        installCheck = await Promise.race([
          claudeTaskmasterService.checkInstallation(),
          new Promise((_, reject) => setTimeout(() => reject(new Error('Installation check timeout')), 15000))
        ]);
      } catch (error) {
        // If installation check times out, assume not installed and try to install
        console.warn('⚠️ Installation check timed out, assuming not installed');
        updateProgress(2, false, "Installation check timed out - attempting installation anyway");
        installCheck = {
          installed: false,
          error: 'Installation check timed out - will attempt installation'
        };
      }

      if (!installCheck.installed) {
        updateProgress(2, false, "Installing Claude Taskmaster (this may take up to 90 seconds)...");

        // Show progress updates during installation
        const progressInterval = setInterval(() => {
          updateProgress(2, false, `Installing Claude Taskmaster... (${Math.floor((Date.now() - (parseState.startTime || Date.now())) / 1000)}s elapsed)`);
        }, 5000);

        // Try to install Claude Taskmaster
        try {
          const installResult = await Promise.race([
            claudeTaskmasterService.installTaskmaster(),
            new Promise((_, reject) => setTimeout(() => reject(new Error('Installation timeout')), 90000)) // Increased to 90 seconds
          ]);

          clearInterval(progressInterval);

          if (!installResult.success) {
            updateProgress(2, false, installResult.error || "Installation failed");
            completeProgress(false);
            toast({
              title: "Installation Failed",
              description: installResult.error || "Failed to install Claude Taskmaster.",
              variant: "destructive",
            });
            setIsParsing(false);
            return;
          }

          updateProgress(2, true);
          setClaudeTaskmasterStatus({ isInstalled: true });
          toast({
            title: "Claude Taskmaster Installed",
            description: "Successfully installed Claude Taskmaster.",
          });
        } catch (error) {
          clearInterval(progressInterval);
          updateProgress(2, false, "Installation failed - will use fallback parsing");

          // Show installation failure but continue with fallback
          toast({
            title: "Claude Taskmaster Installation Failed",
            description: "Will use fallback parsing method. For better results, install manually: sudo npm install -g task-master-ai",
            variant: "destructive",
          });

          // Continue with fallback parsing instead of stopping
          console.warn('⚠️ Claude Taskmaster installation failed, will use fallback parsing');

          // Mark Claude Taskmaster as not installed so we skip to fallback
          setClaudeTaskmasterStatus({ isInstalled: false });
        }
      } else {
        updateProgress(2, true);
        setClaudeTaskmasterStatus({ isInstalled: true });
      }

      // Step 3: Check API keys
      updateProgress(3, false, "Checking API keys...");

      // ✅ ENHANCED: Longer delay to ensure settings are saved from previous step
      console.log('🔍 [PRD Upload] Waiting for settings from wizard...');
      await new Promise(resolve => setTimeout(resolve, 750));

      // ✅ ENHANCED: Use props first, then fall back to settings manager
      const settingsManager = getGlobalSettingsManager();
      let finalAnthropicApiKey = anthropicApiKey || settingsManager.getApiKey('anthropic');
      let finalPerplexityApiKey = perplexityApiKey || settingsManager.getApiKey('perplexity');

      console.log('🔍 [PRD Upload] API Key Check - Raw values:', {
        anthropic: finalAnthropicApiKey,
        perplexity: finalPerplexityApiKey
      });

      console.log('🔍 [PRD Upload] API Key Check - Masked:', {
        anthropic: finalAnthropicApiKey ? '***' + finalAnthropicApiKey.slice(-4) : 'missing',
        perplexity: finalPerplexityApiKey ? '***' + finalPerplexityApiKey.slice(-4) : 'missing'
      });

      console.log('🔍 [PRD Upload] API Key Source:', {
        anthropic: anthropicApiKey ? 'props' : 'settings',
        perplexity: perplexityApiKey ? 'props' : 'settings'
      });

      // ✅ ENHANCED: Debug all settings with more detail
      const allSettings = settingsManager.getSettings();
      console.log('🔍 [PRD Upload] All API keys in settings:', allSettings.apiKeys);
      console.log('🔍 [PRD Upload] Settings manager initialized:', settingsManager.isInitialized());

      // ✅ CRITICAL: Additional verification
      if (!finalAnthropicApiKey && !finalPerplexityApiKey) {
        console.error('❌ [PRD Upload] NO API KEYS FOUND! This indicates a settings persistence issue.');
        console.log('🔍 [PRD Upload] Attempting to force reload settings...');

        // Try to force reload settings
        await settingsManager.forceReloadSettings();
        const retryAnthropicKey = settingsManager.getApiKey('anthropic');
        const retryPerplexityKey = settingsManager.getApiKey('perplexity');

        console.log('🔍 [PRD Upload] Retry API Key Check after force reload:', {
          anthropic: retryAnthropicKey ? '***' + retryAnthropicKey.slice(-4) : 'still missing',
          perplexity: retryPerplexityKey ? '***' + retryPerplexityKey.slice(-4) : 'still missing'
        });

        // Update the variables if we found keys
        if (retryAnthropicKey) finalAnthropicApiKey = retryAnthropicKey;
        if (retryPerplexityKey) finalPerplexityApiKey = retryPerplexityKey;
      }

      if (!finalAnthropicApiKey) {
        updateProgress(3, false, "Missing Anthropic API key - falling back to basic parsing");
        console.warn('⚠️ Anthropic API key not found, will use fallback parsing');
        setClaudeTaskmasterStatus({ isInstalled: false }); // Force fallback mode
      } else if (!finalPerplexityApiKey) {
        updateProgress(3, false, "Missing Perplexity API key - falling back to basic parsing");
        console.warn('⚠️ Perplexity API key not found, will use fallback parsing');
        setClaudeTaskmasterStatus({ isInstalled: false }); // Force fallback mode
      } else {
        console.log('[PRD Parser] ✅ API keys found, proceeding with Claude Taskmaster');
        updateProgress(3, true);
      }

      // Step 4: Configure Claude Taskmaster (skip if not installed)
      updateProgress(4, false);
      let configResult = { success: true }; // Default to success for fallback mode

      if (claudeTaskmasterStatus.isInstalled) {
        try {
          // First validate CLI functionality
          updateProgress(4, false, "Validating Claude Taskmaster CLI...");
          const cliValidation = await claudeTaskmasterService.validateCLI(projectPath);

          if (!cliValidation.isWorking) {
            console.warn('⚠️ Claude Taskmaster CLI validation failed:', cliValidation.error);
            updateProgress(4, false, "CLI validation failed - using fallback");
            setClaudeTaskmasterStatus({ isInstalled: false });
            configResult = { success: true }; // Continue with fallback
          } else {
            updateProgress(4, false, "Configuring Claude Taskmaster...");
            console.log('[PRD Upload] Configuring Claude Taskmaster with API keys:', {
              anthropic: finalAnthropicApiKey ? '***' + finalAnthropicApiKey.slice(-4) : 'missing',
              perplexity: finalPerplexityApiKey ? '***' + finalPerplexityApiKey.slice(-4) : 'missing'
            });
            configResult = await Promise.race([
              claudeTaskmasterService.configureTaskmaster(projectPath, finalAnthropicApiKey, finalPerplexityApiKey),
              new Promise((_, reject) => setTimeout(() => reject(new Error('Configuration timeout')), 15000))
            ]);

            if (!configResult.success) {
              updateProgress(4, false, "Configuration failed - using fallback");
              console.warn('⚠️ Claude Taskmaster configuration failed, using fallback parsing');
              setClaudeTaskmasterStatus({ isInstalled: false }); // Fall back to basic parsing
              configResult = { success: true }; // Continue with fallback
            }
          }
        } catch (error) {
          updateProgress(4, false, "Configuration timed out - using fallback");
          console.warn('⚠️ Claude Taskmaster configuration failed, using fallback parsing');
          setClaudeTaskmasterStatus({ isInstalled: false }); // Fall back to basic parsing
          configResult = { success: true }; // Continue with fallback
        }
      } else {
        updateProgress(4, false, "Skipping configuration - using fallback parsing");
      }

      updateProgress(4, true);

      // Step 5: Ensure PRD file is saved to scripts directory
      updateProgress(5, false, "Saving PRD file to scripts directory...");
      try {
        const prdFilePath = `${projectPath}/scripts/prd.txt`;

        // Try to save PRD content to scripts directory
        let saveResult = await window.electronAPI.createFile(prdFilePath, prdContent);

        if (!saveResult.success && saveResult.error === 'File already exists') {
          // File exists, overwrite it using saveFile
          saveResult = await window.electronAPI.saveFile(prdFilePath, prdContent);
        }

        if (!saveResult.success) {
          throw new Error(`Failed to save PRD file: ${saveResult.error}`);
        }

        console.log('✅ PRD file saved to scripts directory before parsing:', prdFilePath);
        updateProgress(5, true);
      } catch (error) {
        updateProgress(5, false, `Failed to save PRD file: ${error instanceof Error ? error.message : 'Unknown error'}`);
        console.error('❌ Failed to save PRD file before parsing:', error);
        // Continue anyway - the Claude Taskmaster service will save it to .taskmaster/docs/
      }

      // Step 6: Parse PRD (with Claude Taskmaster or fallback)
      updateProgress(6, false);
      let result;

      if (claudeTaskmasterStatus.isInstalled) {
        // Try Claude Taskmaster parsing
        try {
          result = await Promise.race([
            claudeTaskmasterService.parsePRD(projectPath, prdContent, 'prd.txt'),
            new Promise((_, reject) => setTimeout(() => reject(new Error('Parsing timeout')), 30000))
          ]);
        } catch (error) {
          updateProgress(6, false, "Claude Taskmaster parsing timed out - using fallback");
          console.warn('⚠️ Claude Taskmaster parsing timed out, using fallback parsing');
          setClaudeTaskmasterStatus({ isInstalled: false }); // Fall back to basic parsing
          result = { success: false, error: "Timeout - will use fallback" };
        }
      } else {
        // Use fallback parsing immediately
        updateProgress(6, false, "Using fallback parsing method");
        await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate processing time

        const words = prdContent.split(/\s+/).length;
        const tasksGenerated = Math.max(3, Math.floor(words / 100));

        result = {
          success: true,
          tasksGenerated,
          tasksFilePath: `${projectPath}/scripts/prd.txt`,
          fallbackMode: true
        };
      }

      updateProgress(6, true);

      if (result.success) {
        // Step 7: Generate task files (only for Claude Taskmaster)
        updateProgress(7, false);
        if (claudeTaskmasterStatus.isInstalled && !result.fallbackMode) {
          try {
            await Promise.race([
              claudeTaskmasterService.generateTaskFiles(projectPath),
              new Promise((_, reject) => setTimeout(() => reject(new Error('Task generation timeout')), 15000))
            ]);
            updateProgress(7, true);
          } catch (error) {
            // Task generation timeout is not critical - we can still proceed
            updateProgress(7, false, "Task generation timed out (non-critical)");
            console.warn('⚠️ Task generation timed out, but parsing was successful');
          }
        } else {
          // Skip task generation for fallback mode
          updateProgress(7, true, "Skipping task generation (fallback mode)");
        }

        // Step 8: Complete
        updateProgress(8, true);
        setIsParsing(false);
        completeProgress(true);

        const parsingMethod = result.fallbackMode ? "fallback parsing" : "Claude Taskmaster";
        toast({
          title: "PRD Parsed Successfully",
          description: `Generated ${result.tasksGenerated} tasks using ${parsingMethod}.`,
        });

        onPRDParsed({
          success: true,
          tasksGenerated: result.tasksGenerated,
          filePath: result.tasksFilePath,
          fallbackMode: result.fallbackMode
        });
      } else {
        // If Claude Taskmaster parsing fails, offer fallback
        updateProgress(6, false, result.error || "Parsing failed");

        // Try fallback parsing
        updateProgress(7, false);
        console.log('⚠️ Claude Taskmaster parsing failed, using fallback parsing');

        // Simulate fallback processing
        await new Promise(resolve => setTimeout(resolve, 2000));

        const words = prdContent.split(/\s+/).length;
        const tasksGenerated = Math.max(3, Math.floor(words / 100));

        updateProgress(7, true);
        updateProgress(8, true);
        setIsParsing(false);
        completeProgress(true);

        toast({
          title: "PRD Parsed (Fallback Mode)",
          description: `Claude Taskmaster failed, but generated ${tasksGenerated} tasks using fallback parsing. Some features may be limited.`,
          variant: "default",
        });

        onPRDParsed({
          success: true,
          tasksGenerated,
          filePath: `${projectPath}/scripts/prd.txt`,
          fallbackMode: true
        });
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unexpected error";
      updateProgress(parseState.currentStep, false, errorMessage);
      setIsParsing(false);
      completeProgress(false);

      toast({
        title: "Unexpected Error",
        description: errorMessage,
        variant: "destructive",
      });
      onPRDParsed({
        success: false,
        error: errorMessage
      });
    }
  }, [validationResult, prdContent, projectPath, toast, onPRDParsed, initializeProgress, updateProgress, completeProgress, claudeTaskmasterStatus.isInstalled, parseState.startTime]);

  useEffect(() => {
    // Initial validation for empty content - simplified to avoid circular dependency
    const result = prdIntakeService.validatePRD('');
    setValidationResult(result);
    onValidationChange(result.isValid);
  }, [onValidationChange]); // Only depend on onValidationChange for initial validation

  const renderProgressPanel = () => {
    if (!parseState.isActive && parseState.steps.length === 0) return null;

    const elapsedTime = parseState.startTime ? Math.floor((Date.now() - parseState.startTime) / 1000) : 0;

    return (
      <Card>
        <CardContent className="p-4 space-y-3">
          <div className="flex justify-between items-center">
            <h4 className="font-semibold flex items-center gap-2">
              <Loader2 className={`h-4 w-4 ${parseState.isActive ? 'animate-spin' : ''}`} />
              Parsing Progress
            </h4>
            <div className="text-sm text-gray-500">
              {elapsedTime}s elapsed
            </div>
          </div>

          <div className="space-y-2">
            {parseState.steps.map((step, index) => (
              <div key={step.step} className="flex items-center gap-2 text-sm">
                {step.completed ? (
                  <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0" />
                ) : step.error ? (
                  <XCircle className="h-4 w-4 text-red-500 flex-shrink-0" />
                ) : index === parseState.currentStep ? (
                  <Loader2 className="h-4 w-4 text-blue-500 animate-spin flex-shrink-0" />
                ) : (
                  <div className="h-4 w-4 rounded-full border-2 border-gray-300 flex-shrink-0" />
                )}
                <span className={`${step.completed ? 'text-green-600' : step.error ? 'text-red-600' : index === parseState.currentStep ? 'text-blue-600 font-medium' : 'text-gray-500'}`}>
                  {step.message}
                </span>
                {step.error && (
                  <span className="text-xs text-red-500 ml-auto">
                    {step.error}
                  </span>
                )}
              </div>
            ))}
          </div>

          {!parseState.isActive && (
            <div className="pt-2 border-t">
              <p className="text-sm text-gray-600">
                {parseState.steps.every(s => s.completed) ? '✅ Parsing completed successfully' : '❌ Parsing failed'}
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  const renderValidationStatus = () => {
    if (!validationResult) return null;

    const scoreColor = validationResult.score < 40 ? 'text-red-500' : validationResult.score < 70 ? 'text-yellow-500' : 'text-green-500';

    return (
      <Card>
        <CardContent className="p-4 space-y-3">
          <div className="flex justify-between items-center">
            <h4 className="font-semibold">PRD Validation Status</h4>
            <div className={`text-lg font-bold ${scoreColor}`}>Score: {validationResult.score}/100</div>
          </div>

          {validationResult.issues.length > 0 && (
            <div>
              <h5 className="font-medium text-red-600 flex items-center gap-1"><XCircle className="h-4 w-4" /> Issues:</h5>
              <ul className="list-disc pl-5 text-sm text-red-500">
                {validationResult.issues.map((issue, i) => <li key={i}>{issue}</li>)}
              </ul>
            </div>
          )}

          {validationResult.suggestions.length > 0 && (
            <div>
              <h5 className="font-medium text-yellow-600 flex items-center gap-1"><FileWarning className="h-4 w-4" /> Suggestions:</h5>
              <ul className="list-disc pl-5 text-sm text-yellow-500">
                {validationResult.suggestions.map((s, i) => <li key={i}>{s}</li>)}
              </ul>
            </div>
          )}

          {validationResult.isValid && validationResult.issues.length === 0 && (
             <p className="text-sm text-green-600 font-medium flex items-center gap-1">
                <CheckCircle className="h-4 w-4" /> PRD meets quality standards. Ready to parse.
             </p>
          )}

        </CardContent>
      </Card>
    );
  };

  return (
    <div className={`${className} grid grid-cols-1 md:grid-cols-2 gap-6`}>
      <div className="space-y-4">
        <div className="flex flex-col gap-2">
          <label className="font-medium flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Project Requirements Document (PRD)
          </label>
          <Textarea
            value={prdContent}
            onChange={handleContentChange}
            placeholder="Paste your PRD content here, or upload a file."
            rows={15}
            className="resize-y"
            disabled={isParsing}
          />
        </div>
        <div className="flex gap-2 items-center">
          <input
            type="file"
            accept=".txt,.md"
            ref={fileInputRef}
            style={{ display: 'none' }}
            onChange={handleFileSelect}
            disabled={isUploading || isParsing}
          />
          <Button
            type="button"
            variant="outline"
            onClick={() => fileInputRef.current?.click()}
            disabled={isUploading || isParsing}
          >
            {isUploading ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : <Upload className="h-4 w-4 mr-2" />}
            Upload PRD File
          </Button>
          {!disableParsing && (
            <Button
              type="button"
              onClick={handleParse}
              disabled={!validationResult?.isValid || isParsing || !prdContent}
            >
              {isParsing ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : null}
              Parse PRD with Claude Taskmaster
            </Button>
          )}
        </div>
      </div>
      <div className="space-y-4">
        {renderValidationStatus()}
        {!disableParsing && renderProgressPanel()}
      </div>
    </div>
  );
};