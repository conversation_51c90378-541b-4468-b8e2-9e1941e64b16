// components/intake/prd-intake-service.ts

export interface PRDValidationResult {
  isValid: boolean;
  score: number;
  issues: string[];
  suggestions: string[];
}

export interface PRDParseResult {
  success: boolean;
  tasksGenerated?: number;
  error?: string;
  filePath?: string;
  fallbackMode?: boolean;
}

class PRDIntakeService {
  validatePRD(content: string): PRDValidationResult {
    const issues: string[] = [];
    const suggestions: string[] = [];
    let score = 0;

    // Basic validation checks
    if (!content || content.trim().length === 0) {
      issues.push("PRD content is empty");
      return {
        isValid: false,
        score: 0,
        issues,
        suggestions: ["Please provide PRD content"]
      };
    }

    // Check for minimum length
    if (content.length < 100) {
      issues.push("PRD content is too short");
      score -= 20;
    } else {
      score += 20;
    }

    // Check for common PRD sections
    const sections = [
      { name: "objective", patterns: ["objective", "goal", "purpose"], weight: 15 },
      { name: "requirements", patterns: ["requirement", "feature", "functionality"], weight: 20 },
      { name: "scope", patterns: ["scope", "boundary", "limitation"], weight: 10 },
      { name: "acceptance criteria", patterns: ["acceptance", "criteria", "definition of done"], weight: 15 },
      { name: "timeline", patterns: ["timeline", "deadline", "schedule", "milestone"], weight: 10 },
      { name: "stakeholders", patterns: ["stakeholder", "user", "customer", "audience"], weight: 10 }
    ];

    const contentLower = content.toLowerCase();
    
    sections.forEach(section => {
      const hasSection = section.patterns.some(pattern => contentLower.includes(pattern));
      if (hasSection) {
        score += section.weight;
      } else {
        suggestions.push(`Consider adding ${section.name} section`);
      }
    });

    // Check for structure indicators
    const hasHeaders = /^#+\s/m.test(content) || /^[A-Z][^a-z]*:$/m.test(content);
    if (hasHeaders) {
      score += 10;
    } else {
      suggestions.push("Consider using headers to structure your PRD");
    }

    // Ensure score is within bounds
    score = Math.max(0, Math.min(100, score));

    const isValid = score >= 60 && issues.length === 0;

    return {
      isValid,
      score,
      issues,
      suggestions
    };
  }

  async savePRDAndParse(content: string, projectPath?: string): Promise<PRDParseResult> {
    try {
      // This method is deprecated - PRD parsing is now handled directly by the PRD upload UI
      // using the Claude Taskmaster service. This is kept for backward compatibility.
      console.warn('PRDIntakeService.savePRDAndParse is deprecated. Use Claude Taskmaster service directly.');

      // Simulate parsing logic for backward compatibility
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Mock task generation based on content analysis
      const words = content.split(/\s+/).length;
      const tasksGenerated = Math.max(1, Math.floor(words / 50));

      return {
        success: true,
        tasksGenerated,
        filePath: projectPath ? `${projectPath}/prd.md` : 'prd.md'
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }
}

export const prdIntakeService = new PRDIntakeService();
