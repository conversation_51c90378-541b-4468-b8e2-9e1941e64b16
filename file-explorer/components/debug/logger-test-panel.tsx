/**
 * LOGGER TEST PANEL
 * Debug component to test and verify the logging system
 */

'use client';

import React, { useState, useEffect } from 'react';
import { 
  synapseLogger, 
  logInfo, 
  logDebug, 
  logError,
  logMicromanagerEvent,
  logKanbanEvent,
  logTaskmasterEvent,
  logAgentEvent
} from '../../services/logger';
import { useLoggerInitContext } from '../services/logger-init-provider';

export function LoggerTestPanel() {
  const { isInitialized, isInitializing, error } = useLoggerInitContext();
  const [testResults, setTestResults] = useState<string[]>([]);
  const [recentLogs, setRecentLogs] = useState<any[]>([]);

  const addResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const runBasicTests = async () => {
    try {
      addResult('🧪 Starting basic logging tests...');

      // Test basic logging functions
      logInfo('TestSystem', 'BasicLogTest', {
        testId: 'basic-001',
        message: 'Testing basic INFO logging'
      });
      addResult('✅ Basic INFO log test completed');

      logDebug('TestSystem', 'DebugLogTest', {
        testId: 'debug-001',
        details: { key: 'value', number: 42, array: [1, 2, 3] }
      });
      addResult('✅ Basic DEBUG log test completed');

      logError('TestSystem', 'ErrorLogTest', {
        testId: 'error-001',
        error: 'This is a test error message',
        severity: 'low'
      });
      addResult('✅ Basic ERROR log test completed');

      // Update recent logs display
      const logs = synapseLogger.getRecentLogs(10);
      setRecentLogs(logs);
      addResult(`📊 Retrieved ${logs.length} recent log entries`);

    } catch (error) {
      addResult(`❌ Basic tests failed: ${error}`);
    }
  };

  const runSpecializedTests = async () => {
    try {
      addResult('🎯 Starting specialized component tests...');

      // Test Micromanager logging
      logMicromanagerEvent('TestTaskDecomposition', {
        taskId: 'test-task-001',
        subtasks: 3,
        complexity: 'moderate',
        estimatedTime: '1h'
      });
      addResult('✅ Micromanager logging test completed');

      // Test Kanban logging
      logKanbanEvent('TestCardCreation', {
        cardId: 'test-card-001',
        title: 'Test Card',
        boardId: 'test-board',
        columnId: 'test-column'
      });
      addResult('✅ Kanban logging test completed');

      // Test Taskmaster logging
      logTaskmasterEvent('TestOrchestrationStart', {
        tasksCount: 5,
        testMode: true,
        timestamp: new Date().toISOString()
      });
      addResult('✅ Taskmaster logging test completed');

      // Test Agent logging
      logAgentEvent('test-agent', 'TestTaskExecution', {
        taskId: 'test-task-002',
        duration: '30m',
        tokensUsed: 1500
      });
      addResult('✅ Agent logging test completed');

      // Update recent logs display
      const logs = synapseLogger.getRecentLogs(15);
      setRecentLogs(logs);
      addResult(`📊 Retrieved ${logs.length} recent log entries after specialized tests`);

    } catch (error) {
      addResult(`❌ Specialized tests failed: ${error}`);
    }
  };

  const clearResults = () => {
    setTestResults([]);
    setRecentLogs([]);
  };

  const clearLogBuffer = () => {
    synapseLogger.clearBuffer();
    setRecentLogs([]);
    addResult('🗑️ Log buffer cleared');
  };

  if (!isInitialized && !isInitializing) {
    return (
      <div className="p-4 border border-red-300 bg-red-50 rounded-md">
        <h3 className="text-lg font-semibold text-red-800 mb-2">Logger Not Initialized</h3>
        <p className="text-red-700">The logging system is not initialized yet.</p>
        {error && <p className="text-red-600 mt-2">Error: {error}</p>}
      </div>
    );
  }

  if (isInitializing) {
    return (
      <div className="p-4 border border-blue-300 bg-blue-50 rounded-md">
        <h3 className="text-lg font-semibold text-blue-800 mb-2">Logger Initializing</h3>
        <p className="text-blue-700">Please wait while the logging system initializes...</p>
      </div>
    );
  }

  return (
    <div className="p-4 border border-gray-300 rounded-md space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Synapse Logger Test Panel</h3>
        <div className="flex items-center space-x-2">
          <span className="px-2 py-1 bg-green-100 text-green-800 rounded text-sm">
            ✅ Logger Ready
          </span>
        </div>
      </div>

      <div className="flex space-x-2">
        <button
          onClick={runBasicTests}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Run Basic Tests
        </button>
        <button
          onClick={runSpecializedTests}
          className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600"
        >
          Run Specialized Tests
        </button>
        <button
          onClick={clearResults}
          className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
        >
          Clear Results
        </button>
        <button
          onClick={clearLogBuffer}
          className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
        >
          Clear Log Buffer
        </button>
      </div>

      {testResults.length > 0 && (
        <div className="space-y-2">
          <h4 className="font-semibold">Test Results:</h4>
          <div className="bg-gray-100 p-3 rounded max-h-40 overflow-y-auto">
            {testResults.map((result, index) => (
              <div key={index} className="text-sm font-mono">
                {result}
              </div>
            ))}
          </div>
        </div>
      )}

      {recentLogs.length > 0 && (
        <div className="space-y-2">
          <h4 className="font-semibold">Recent Log Entries ({recentLogs.length}):</h4>
          <div className="bg-black text-green-400 p-3 rounded max-h-60 overflow-y-auto font-mono text-xs">
            {recentLogs.map((log, index) => (
              <div key={index} className="mb-1">
                <span className="text-gray-400">[{log.level}]</span>{' '}
                <span className="text-yellow-400">{log.timestamp}</span>{' '}
                <span className="text-cyan-400">{log.component}</span>{' '}
                <span className="text-green-400">{log.event}</span>
                {Object.keys(log.data).length > 0 && (
                  <div className="ml-4 text-gray-300">
                    {JSON.stringify(log.data, null, 2)}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
