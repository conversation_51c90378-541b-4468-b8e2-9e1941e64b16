// components/debug/StreamReplayDebugger.tsx

"use client"

import React, { useState, useEffect, useMemo, useCallback, useRef } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'
import { Slider } from '@/components/ui/slider'
import {
  Play,
  Pause,
  SkipForward,
  RotateCcw,
  Clock,
  Zap,
  DollarSign,
  Brain,
  ChevronRight,
  Activity
} from 'lucide-react'
// Using CSS animations instead of framer-motion for lighter bundle

import { useStreamReplayStore } from './stream-replay-store'
import type { StreamSession, ReplayState } from './stream-replay-store'

interface StreamReplayDebuggerProps {
  className?: string
}

export default function StreamReplayDebugger({ className }: StreamReplayDebuggerProps) {
  // Development mode check
  if (process.env.NODE_ENV === 'production') {
    return null;
  }

  const {
    sessions,
    selectedSessionId,
    replayState,
    selectSession,
    startReplay,
    pauseReplay,
    stopReplay,
    restartReplay,
    skipToEnd,
    setPlaybackSpeed,
    clearSessions
  } = useStreamReplayStore()

  const [currentChunkIndex, setCurrentChunkIndex] = useState(0)
  const [displayedContent, setDisplayedContent] = useState('')
  const replayTimerRef = useRef<NodeJS.Timeout | null>(null)

  const selectedSession = useMemo(() =>
    sessions.find(s => s.id === selectedSessionId),
    [sessions, selectedSessionId]
  )

  // Replay engine
  useEffect(() => {
    if (!selectedSession || replayState.status !== 'playing') {
      if (replayTimerRef.current) {
        clearTimeout(replayTimerRef.current)
        replayTimerRef.current = null
      }
      return
    }

    const playNextChunk = () => {
      if (currentChunkIndex >= selectedSession.chunks.length) {
        stopReplay()
        return
      }

      const chunk = selectedSession.chunks[currentChunkIndex]
      setDisplayedContent(prev => prev + chunk.delta)
      setCurrentChunkIndex(prev => prev + 1)

      // Calculate delay based on playback speed and original timing
      const baseDelay = chunk.originalDelay || 50 // Default 50ms between chunks
      const adjustedDelay = baseDelay / replayState.playbackSpeed

      replayTimerRef.current = setTimeout(playNextChunk, adjustedDelay)
    }

    replayTimerRef.current = setTimeout(playNextChunk, 100) // Initial delay

    return () => {
      if (replayTimerRef.current) {
        clearTimeout(replayTimerRef.current)
        replayTimerRef.current = null
      }
    }
  }, [selectedSession, replayState.status, currentChunkIndex, replayState.playbackSpeed, stopReplay])

  // Reset display when session changes
  useEffect(() => {
    if (selectedSession && replayState.status === 'stopped') {
      setCurrentChunkIndex(0)
      setDisplayedContent('')
    }
  }, [selectedSession, replayState.status])

  const handlePlay = useCallback(() => {
    if (!selectedSession) return

    if (replayState.status === 'paused') {
      startReplay()
    } else if (replayState.status === 'stopped') {
      setCurrentChunkIndex(0)
      setDisplayedContent('')
      startReplay()
    }
  }, [selectedSession, replayState.status, startReplay])

  const handlePause = useCallback(() => {
    pauseReplay()
  }, [pauseReplay])

  const handleRestart = useCallback(() => {
    setCurrentChunkIndex(0)
    setDisplayedContent('')
    restartReplay()
  }, [restartReplay])

  const handleSkipToEnd = useCallback(() => {
    if (!selectedSession) return

    const fullContent = selectedSession.chunks.reduce((acc, chunk) => acc + chunk.delta, '')
    setDisplayedContent(fullContent)
    setCurrentChunkIndex(selectedSession.chunks.length)
    skipToEnd()
  }, [selectedSession, skipToEnd])

  const formatTimestamp = (timestamp: number): string => {
    return new Date(timestamp).toLocaleTimeString()
  }

  const formatDuration = (ms: number): string => {
    if (ms < 1000) return `${ms}ms`
    return `${(ms / 1000).toFixed(1)}s`
  }

  const formatCost = (cost: number): string => {
    return `$${cost.toFixed(4)}`
  }

  const getProgressPercentage = (): number => {
    if (!selectedSession || selectedSession.chunks.length === 0) return 0
    return (currentChunkIndex / selectedSession.chunks.length) * 100
  }

  return (
    <div className={`flex h-full ${className}`}>
      {/* Left Pane - Session List */}
      <div className="w-[30%] border-r border-border bg-background">
        <Card className="h-full rounded-none border-0">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <Activity className="h-4 w-4" />
                Stream Sessions
              </CardTitle>
              <div className="flex items-center gap-1">
                <Badge variant="secondary" className="text-xs">
                  {sessions.length}
                </Badge>
                <Button
                  onClick={clearSessions}
                  size="sm"
                  variant="ghost"
                  className="h-6 w-6 p-0"
                  title="Clear all sessions"
                >
                  <RotateCcw className="h-3 w-3" />
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-0">
            <ScrollArea className="h-[calc(100vh-8rem)]">
              <div className="space-y-1 p-3">
                {sessions.length === 0 ? (
                  <div className="text-center text-muted-foreground text-sm py-8">
                    No stream sessions recorded yet.
                    <br />
                    Start an agent conversation to see streams here.
                  </div>
                ) : (
                  sessions.map((session) => (
                    <div
                      key={session.id}
                      className={`p-3 rounded-lg border cursor-pointer transition-all duration-200 ease-in-out hover:scale-[1.02] ${
                        selectedSessionId === session.id
                          ? 'bg-accent border-accent-foreground/20'
                          : 'bg-card hover:bg-accent/50 border-border'
                      }`}
                      onClick={() => selectSession(session.id)}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <Brain className="h-3 w-3 text-blue-500" />
                            <span className="text-sm font-medium break-words">
                              {session.agentName}
                            </span>
                          </div>
                          <div className="text-xs text-muted-foreground space-y-1">
                            <div className="flex items-center gap-1">
                              <Clock className="h-3 w-3" />
                              {formatTimestamp(session.timestamp)}
                            </div>
                            <div className="flex items-center gap-2">
                              <Badge variant="outline" className="text-xs px-1 py-0">
                                {session.model}
                              </Badge>
                              <span className="flex items-center gap-1">
                                <Zap className="h-3 w-3" />
                                {session.tokens}
                              </span>
                              {session.cost > 0 && (
                                <span className="flex items-center gap-1">
                                  <DollarSign className="h-3 w-3" />
                                  {formatCost(session.cost)}
                                </span>
                              )}
                            </div>
                          </div>
                        </div>
                        {selectedSessionId === session.id && (
                          <ChevronRight className="h-4 w-4 text-accent-foreground" />
                        )}
                      </div>
                    </div>
                  ))
                )}
              </div>
            </ScrollArea>
          </CardContent>
        </Card>
      </div>

      {/* Right Pane - Replay Panel */}
      <div className="flex-1 bg-background">
        <Card className="h-full rounded-none border-0">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm font-medium">
                {selectedSession ? `Replaying: ${selectedSession.agentName}` : 'Select a session to replay'}
              </CardTitle>
              {selectedSession && (
                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                  <span>{currentChunkIndex} / {selectedSession.chunks.length} chunks</span>
                  <Badge variant="outline" className="text-xs">
                    {formatDuration(selectedSession.duration)}
                  </Badge>
                </div>
              )}
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            {selectedSession ? (
              <>
                {/* Playback Controls */}
                <div className="flex items-center gap-2">
                  <Button
                    onClick={handlePlay}
                    size="sm"
                    variant={replayState.status === 'playing' ? 'secondary' : 'default'}
                    disabled={replayState.status === 'playing'}
                  >
                    <Play className="h-4 w-4 mr-1" />
                    Play
                  </Button>
                  <Button
                    onClick={handlePause}
                    size="sm"
                    variant="outline"
                    disabled={replayState.status !== 'playing'}
                  >
                    <Pause className="h-4 w-4 mr-1" />
                    Pause
                  </Button>
                  <Button
                    onClick={handleRestart}
                    size="sm"
                    variant="outline"
                  >
                    <RotateCcw className="h-4 w-4 mr-1" />
                    Restart
                  </Button>
                  <Button
                    onClick={handleSkipToEnd}
                    size="sm"
                    variant="outline"
                  >
                    <SkipForward className="h-4 w-4 mr-1" />
                    Skip to End
                  </Button>

                  <Separator orientation="vertical" className="h-6" />

                  <div className="flex items-center gap-2 min-w-[120px]">
                    <span className="text-xs text-muted-foreground">Speed:</span>
                    <Slider
                      value={[replayState.playbackSpeed]}
                      onValueChange={([speed]) => setPlaybackSpeed(speed)}
                      min={0.25}
                      max={4}
                      step={0.25}
                      className="flex-1"
                    />
                    <span className="text-xs font-mono w-8">
                      {replayState.playbackSpeed}x
                    </span>
                  </div>
                </div>

                {/* Progress Bar */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-xs text-muted-foreground">
                    <span>Progress</span>
                    <span>{getProgressPercentage().toFixed(1)}%</span>
                  </div>
                  <div className="w-full bg-secondary rounded-full h-2">
                    <div
                      className="bg-primary h-2 rounded-full transition-all duration-200 ease-out"
                      style={{ width: `${getProgressPercentage()}%` }}
                    />
                  </div>
                </div>

                <Separator />

                {/* Stream Output */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Stream Output</span>
                    <Badge variant="outline" className="text-xs">
                      {replayState.status}
                    </Badge>
                  </div>
                  <ScrollArea className="h-[400px] w-full border rounded-lg p-4 bg-muted/30">
                    <div className="font-mono text-sm whitespace-pre-wrap">
                      {displayedContent && (
                        <div className="animate-in fade-in duration-200">
                          {displayedContent}
                          {replayState.status === 'playing' && (
                            <span className="inline-block w-2 h-4 bg-primary ml-1 animate-pulse" />
                          )}
                        </div>
                      )}
                    </div>
                  </ScrollArea>
                </div>
              </>
            ) : (
              <div className="flex items-center justify-center h-[500px] text-muted-foreground">
                <div className="text-center">
                  <Activity className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p className="text-lg font-medium mb-2">No Session Selected</p>
                  <p className="text-sm">
                    Choose a stream session from the left panel to start replaying
                  </p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
