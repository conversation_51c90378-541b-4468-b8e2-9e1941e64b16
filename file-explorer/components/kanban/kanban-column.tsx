"use client"

import { useDraggable, useDroppable } from "@dnd-kit/core"
import { Card, Column, CardType, Agent } from "./board-context"
import { KanbanCard } from "./kanban-card"
import { Button } from "@/components/ui/button"
import { Plus, MoreHorizontal, GripVertical, Edit, Trash2 } from "lucide-react"
import { ScrollArea } from "@/components/ui/scroll-area"
import { CSS } from "@dnd-kit/utilities"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { useState } from "react"
import { Input } from "@/components/ui/input";

interface KanbanColumnProps {
  column: Column
  onAddCard: () => void
  onDeleteColumn: () => void
  onEditColumn: (newTitle: string) => void
  onCardUpdate: (card: Card) => void
  onDeleteCard: (cardId: string) => void
  isBoardOver?: boolean
  isTargetForColumnDrop?: boolean
  isBoardDragging?: boolean
  cardTypes: CardType[]
  agents: Agent[]
}

export function KanbanColumn({
  column,
  onAddCard,
  onDeleteColumn,
  onEditColumn,
  onCardUpdate,
  onDeleteCard,
  isBoardOver,
  isTargetForColumnDrop,
  isBoardDragging,
  cardTypes,
  agents,
}: KanbanColumnProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editTitle, setEditTitle] = useState(column.title);
  const { setNodeRef: setCardDropZoneRef, isOver: isOverCardDrop } = useDroppable({
    id: column.id,
    data: {
      type: "column",
      columnId: column.id,
    },
  });

  const {
    attributes: columnDragAttributes,
    listeners: columnDragListeners,
    setNodeRef: setDraggableColumnRef,
    transform: columnTransform,
    isDragging: isThisColumnDragging,
  } = useDraggable({
    id: column.id,
    data: {
      type: "column",
      column: column,
    },
  });

  const columnStyle = columnTransform
    ? {
        transform: CSS.Translate.toString(columnTransform),
        zIndex: isThisColumnDragging ? 100 : "auto",
        opacity: isThisColumnDragging ? 0.8 : 1,
      }
    : {};

  const highlightForCardDrop = isOverCardDrop || (isBoardOver && !isThisColumnDragging);
  const highlightForColumnReorder = isTargetForColumnDrop && !isThisColumnDragging;

  const handleEditSave = () => {
    if (editTitle.trim()) {
      onEditColumn(editTitle);
      setIsEditing(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleEditSave();
    } else if (e.key === "Escape") {
      setEditTitle(column.title);
      setIsEditing(false);
    }
  };

  return (
    <div
      ref={setDraggableColumnRef}
      style={columnStyle}
      className={`flex flex-col w-72 shrink-0 rounded-md border bg-card relative
        ${highlightForCardDrop ? "ring-2 ring-primary border-primary" : "border-border"}
        ${highlightForColumnReorder ? "ring-2 ring-blue-500 border-blue-500" : ""}
        ${isThisColumnDragging ? "shadow-xl" : "shadow-sm"}`}
    >
      <div className="p-3 border-b border-border flex items-center justify-between">
        {/* Only the grip icon is draggable */}
        <div
          className="cursor-grab p-1 hover:bg-muted rounded-sm"
          {...columnDragAttributes}
          {...columnDragListeners}
          // Prevent this from interfering with other interactions
          onClick={(e) => e.stopPropagation()}
          // Add aria label for accessibility
          aria-label="Drag column"
        >
          <GripVertical className="activity-bar-icon text-muted-foreground" />
        </div>

        {isEditing ? (
          <div className="flex-1 px-1">
            <Input
              value={editTitle}
              onChange={(e) => setEditTitle(e.target.value)}
              onBlur={handleEditSave}
              onKeyDown={handleKeyDown}
              className="h-7 text-sm"
              autoFocus
            />
          </div>
        ) : (
          <div className="font-medium break-words flex-1">{column.title}</div>
        )}

        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="icon"
            className="h-7 w-7"
            onClick={(e) => {
              e.stopPropagation();
              onAddCard();
            }}
          >
            <Plus className="sidebar-icon" />
          </Button>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="h-7 w-7"
                onClick={(e) => {
                  e.stopPropagation();
                }}
              >
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-[160px]">
              <DropdownMenuItem onClick={() => setIsEditing(true)}>
                <Edit className="mr-2 h-4 w-4" />
                Edit Column
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={onDeleteColumn}
                className="text-destructive focus:text-destructive"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete Column
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
      <ScrollArea
        ref={setCardDropZoneRef}
        className="p-2 flex-1"
        // Ensure scroll area doesn't block pointer events
        style={{ pointerEvents: 'auto' }}
      >
        <div
          className="space-y-2 min-h-[50px]"
          // Ensure this container doesn't block pointer events
          style={{ pointerEvents: 'auto' }}
        >
          {column.cards.length === 0 ? (
            <div
              className="p-4 text-center text-muted-foreground text-sm"
              // Make empty state clickable for adding cards
              onClick={(e) => {
                e.stopPropagation();
                onAddCard();
              }}
              style={{ cursor: 'pointer' }}
            >
              Drop cards here or click +
            </div>
          ) : (
            column.cards.map((card) => (
              <KanbanCard
                key={card.id}
                card={card}
                cardTypes={cardTypes}
                columnId={column.id}
                onCardUpdate={onCardUpdate}
                onDeleteCard={onDeleteCard}
              />
            ))
          )}
        </div>
      </ScrollArea>
    </div>
  )
}