"use client"

import React, { useState, useRef, use<PERSON>emo, useEffect } from "react"
import {
  DndContext,
  DragOverlay,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  closestCenter,
  rectIntersection,
  type DragStartEvent,
  type DragOverEvent,
  type DragEndEvent,
} from "@dnd-kit/core"
import { CreateCardDialog } from "./create-card-dialog"
import { EditSwimlaneDialog } from "./edit-swimlane-dialog"
import { useDialog } from "@/components/dialogs"
import { CreateBoardDialog } from "@/components/dialogs/CreateBoardDialog"
import { CreateColumnDialog } from "@/components/dialogs/CreateColumnDialog"
import { CreateSwimlaneDialog } from "@/components/dialogs/CreateSwimlaneDialog"
import { BoardSettingsDialog } from "@/components/dialogs/BoardSettingsDialog"
import { AgentManagementDialog } from "@/components/dialogs/AgentManagementDialog"
import { Kanban<PERSON>wimlane } from "./kanban-swimlane"
import { KanbanLegend } from "./kanban-legend"
import { useBoard, Card, Column, Swimlane, CardType, Agent } from "./board-context" // Import types from board-context
import { useSearch } from "./search-provider" // Updated import path
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { LayoutGrid, ChevronRight, Cpu } from "lucide-react"
import { KanbanColumn } from "./kanban-column"
import { KanbanCard } from "./kanban-card"
import { useToast } from "@/hooks/use-toast" // Updated import path
import { Button } from "@/components/ui/button"
import { LegendEditDialog } from "./legend-edit-dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

interface KanbanBoardProps {
  boardId?: string
}

export function KanbanBoard({ boardId }: KanbanBoardProps) {
  const {
    activeBoard,
    addBoard: addBoardToContext,
    updateBoard,
    addColumn: addColumnToContext,
    updateColumn: updateColumnInContext,
    deleteColumn: deleteColumnFromContext,
    addSwimlane: addSwimlaneToContext,
    updateSwimlane: updateSwimlaneInContext,
    deleteSwimlane: deleteSwimlaneFromContext,
    toggleSwimlaneExpansion: toggleSwimlaneExpansionInContext,
    addCardToColumn: addCardToContext,
    updateCardInColumn: updateCardInContext,
    deleteCardFromColumn: deleteCardFromContext,
    moveCard: moveCardInContext,
    moveColumn: moveColumnInContext, // Added for column drag
    updateCardTypes,
    updateAgents,
  } = useBoard()

  const { searchQuery } = useSearch()
  const { toast } = useToast()
  const { openDialog, closeDialog } = useDialog()

  // Local UI state
  const [activeCardId, setActiveCardId] = useState<string | null>(null);
  const [activeColumnIdForDnd, setActiveColumnIdForDnd] = useState<string | null>(null); // Renamed from activeDndColumnId for clarity
  const [activeSwimlaneIdForDnd, setActiveSwimlaneIdForDnd] = useState<string | null>(null); // Renamed for clarity

  const [isDragging, setIsDragging] = useState(false);
  const [draggedColumn, setDraggedColumn] = useState<Column | null>(null);
  const [dragOverColumnTargetId, setDragOverColumnTargetId] = useState<string | null>(null); // For column reorder preview
  // Dialog states are managed here as in the original full component
  const [showCreateCardDialog, setShowCreateCardDialog] = useState(false)
  const [showEditSwimlaneDialog, setShowEditSwimlaneDialog] = useState(false)
  const [selectedSwimlane, setSelectedSwimlane] = useState<Swimlane | null>(null)
  const [viewMode, setViewMode] = useState<"swimlanes" | "columns">("swimlanes")
  const [targetColumnId, setTargetColumnId] = useState<string | null>(null)
  const [targetSwimlaneId, setTargetSwimlaneId] = useState<string | undefined>(undefined)
  const [isCreateCardOpen, setIsCreateCardOpen] = useState(false)
  const [showLegendEditDialog, setShowLegendEditDialog] = useState(false)
  const [activeSwimlaneForColumnView, setActiveSwimlaneForColumnView] = useState<string | null>(null)

  const dragOverlayRef = useRef(null)

  // Get data from context
  const columns = useMemo(() => activeBoard?.columns || [], [activeBoard])
  const swimlanes = useMemo(() => activeBoard?.swimlanes || [], [activeBoard])
  const cardTypes = useMemo(() => activeBoard?.cardTypes || [], [activeBoard])
  const agents = useMemo(() => activeBoard?.agents || [], [activeBoard])

  // ✅ SURGICAL FIX: Remove activeSwimlaneForColumnView from dependencies to prevent infinite loop
  useEffect(() => {
    if (swimlanes.length > 0) {
      // Use a ref to check current value without causing re-renders
      setActiveSwimlaneForColumnView(current => {
        const currentActiveIsValid = current && swimlanes.some(s => s.id === current);
        if (!currentActiveIsValid) {
          // If current is null, undefined, or not found in current swimlanes,
          // set it to the ID of the first swimlane.
          return swimlanes[0].id;
        }
        // If currentActiveIsValid is true, keep the current value unchanged.
        return current;
      });
    } else {
      // No swimlanes exist on the current board.
      setActiveSwimlaneForColumnView(null);
    }
  }, [swimlanes]); // Only depend on swimlanes to prevent infinite loop

  const filteredColumns = useMemo(() => {
    let filtered = columns
    if (searchQuery && searchQuery.trim() !== "") {
      const searchLower = searchQuery.toLowerCase().trim()
      filtered = columns.map((column) => {
        const filteredCards = column.cards.filter((card) => {
          const titleMatch = card.title?.toLowerCase().includes(searchLower) || false
          const descMatch = card.description?.toLowerCase().includes(searchLower) || false
          const projectMatch = card.projectId?.toLowerCase().includes(searchLower) || false
          const tagsMatch = card.tags?.some((tag) => tag.toLowerCase().includes(searchLower)) || false
          const assigneeMatch = card.assignee?.toLowerCase().includes(searchLower) || false
          return titleMatch || descMatch || projectMatch || tagsMatch || assigneeMatch
        })
        return { ...column, cards: filteredCards }
      })
    }

    if (viewMode === "columns" && activeSwimlaneForColumnView) {
      filtered = filtered.map((column) => ({
        ...column,
        cards: column.cards.filter((card) => card.swimlaneId === activeSwimlaneForColumnView),
      }))
    }
    return filtered
  }, [columns, searchQuery, viewMode, activeSwimlaneForColumnView])

  const sensors = useSensors(
    useSensor(PointerSensor, {
      // Configure pointer sensor with more forgiving activation constraints
      activationConstraint: {
        // Only start dragging after moving 8px (helps distinguish clicks from drags)
        distance: 8,
        // Add a small delay to prevent accidental drags
        delay: 100,
        // Allow tolerance for slight movement
        tolerance: 5,
      }
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: closestCenter,
    }),
  )

  const getCardsForSwimlaneAndColumn = (swimlaneId: string, columnId: string): Card[] => {
    return (
      filteredColumns
        .find((column) => column.id === columnId)
        ?.cards.filter((card) => card.swimlaneId === swimlaneId) || []
    )
  }

  const handleDragStart = (event: DragStartEvent) => {
    // Clear any lingering drag states first
    setActiveCardId(null);
    setActiveColumnIdForDnd(null);
    setActiveSwimlaneIdForDnd(null);
    setDraggedColumn(null);
    setDragOverColumnTargetId(null);

    // Now set the dragging state
    setIsDragging(true);
    const { active } = event;

    if (active.data?.current?.type === "card") {
      setActiveCardId(active.id as string);
      setDraggedColumn(null);
      setActiveColumnIdForDnd(active.data.current.columnId); // Store card's original column
    } else if (active.data?.current?.type === "column") {
      setActiveColumnIdForDnd(active.id as string); // This is the ID of the column being dragged
      setDraggedColumn(active.data.current.column as Column);
      setActiveCardId(null);
    }
  };

  const handleDragOver = (event: DragOverEvent) => {
    const { active, over } = event;
    if (!over) {
      setDragOverColumnTargetId(null);
      return;
    }

    const activeType = active.data?.current?.type;
    const overData = over.data?.current;
    const overType = overData?.type;

    if (activeType === "card") {
      if (overType === "column") {
        setActiveColumnIdForDnd(over.id as string); // Target column for card
        // Crucially, set the swimlane ID for column view drops
        setActiveSwimlaneIdForDnd(activeSwimlaneForColumnView || (swimlanes[0]?.id ?? "default-swimlane-fallback"));
      } else if (overType === "swimlane-column") {
        setActiveColumnIdForDnd(overData.columnId as string);
        setActiveSwimlaneIdForDnd(overData.swimlaneId as string);
      } else if (overType === "card" && overData.card) { // Hovering card over another card
        setActiveColumnIdForDnd(overData.card.columnId);
        setActiveSwimlaneIdForDnd(overData.card.swimlaneId);
      }
      setDragOverColumnTargetId(null); // Not dragging a column
    } else if (activeType === "column") {
      if (overType === "column" && active.id !== over.id) {
        setDragOverColumnTargetId(over.id as string); // Column `active.id` is over column `over.id`
      } else {
        setDragOverColumnTargetId(null); // Not over a valid column target or over itself
      }
    }
  };

  const handleDragCancel = () => {
    // Ensure all drag-related states are reset
    setIsDragging(false)
    setActiveColumnIdForDnd(null)
    setActiveSwimlaneIdForDnd(null)
    setActiveCardId(null)
    setDraggedColumn(null)
    setDragOverColumnTargetId(null)

    // Add a small delay to ensure UI updates properly
    setTimeout(() => {
      // Double-check that dragging state is fully cleared
      setIsDragging(false)
    }, 50)
  }

  const handleDragEnd = (event: DragEndEvent) => {
    // Immediately set dragging to false to restore interactivity
    setIsDragging(false);
    const { active, over } = event;

    const activeType = active.data?.current?.type;

    // Capture current DND states before resetting
    const dndCardId = activeCardId;
    const dndDestinationColumnId = activeColumnIdForDnd;
    const dndDestinationSwimlaneId = activeSwimlaneIdForDnd;
    const dndDraggedColumnId = active.data.current?.type === 'column' ? active.id as string : null;

    // Reset DND states
    setActiveCardId(null);
    setActiveColumnIdForDnd(null);
    setActiveSwimlaneIdForDnd(null);
    setDraggedColumn(null);
    setDragOverColumnTargetId(null);

    // Add a small delay to ensure UI updates properly
    setTimeout(() => {
      // Double-check that dragging state is fully cleared
      setIsDragging(false);
    }, 50);

    if (!over || !activeBoard) { // Removed !active.data.current as active is always present if drag started
      return;
    }

    if (activeType === "card" && dndCardId) {
      const draggedCard = active.data.current?.card as Card | undefined;
      if (!draggedCard) return;

      const sourceColumnId = draggedCard.columnId;
      // Use the DND state captured *before* reset
      const destinationColumnId = dndDestinationColumnId;
      let destinationSwimlaneId = dndDestinationSwimlaneId;

      // If dropping in column view, and target was a column, ensure swimlaneId is from activeSwimlaneForColumnView
      if (over.data.current?.type === "column" && viewMode === "columns") {
          destinationSwimlaneId = activeSwimlaneForColumnView || (swimlanes[0]?.id ?? "default-swimlane-fallback");
      }

      if (!destinationColumnId || !destinationSwimlaneId) {
        console.warn("Card drag ended: No valid destination column/swimlane.");
        return;
      }
      if (destinationColumnId === sourceColumnId && destinationSwimlaneId === draggedCard.swimlaneId) {
        return; // No change
      }
      moveCardInContext(activeBoard.id, dndCardId, sourceColumnId, destinationColumnId, destinationSwimlaneId);
    } else if (activeType === "column" && dndDraggedColumnId) {
      // If over.id is the column itself, or not a column, it's not a valid reorder drop
      const targetColumnIdForReorder = over.data.current?.type === 'column' ? over.id as string : null;
      if (dndDraggedColumnId !== targetColumnIdForReorder) { // Cannot drop a column onto itself
          moveColumnInContext(activeBoard.id, dndDraggedColumnId, targetColumnIdForReorder);
      }
    }
  };

  const handleItemUpdate = (updatedCard: Card) => { // Signature changed to accept only Card
    if (!activeBoard) return;
    // The updatedCard should contain its original columnId unless it was part of a move.
    // For CardDetailView updates, columnId should remain the same.
    if (updatedCard.columnId) {
      updateCardInContext(activeBoard.id, updatedCard);
    } else {
      // This fallback should ideally not be needed if CardDetailView passes columnId
      console.warn("KanbanBoard: Card update attempt without columnId on card object.", updatedCard);
      // Attempt to find column if not on card (less ideal)
      let foundColumnId: string | undefined;
      for (const col of activeBoard.columns) {
        if (col.cards.some((c) => c.id === updatedCard.id)) {
          foundColumnId = col.id;
          break;
        }
      }
      if (foundColumnId) {
         updateCardInContext(activeBoard.id, { ...updatedCard, columnId: foundColumnId });
      } else {
          toast({ title: "Error", description: "Could not update card, column not found.", variant: "destructive"});
      }
    }
  };

  const handleAddBoard = (name: string, description?: string) => {
    addBoardToContext(name, description)
    closeDialog('create-board')
  }

  const openCreateBoardDialog = () => {
    openDialog('create-board',
      <CreateBoardDialog
        onCreateBoard={handleAddBoard}
        onCancel={() => closeDialog('create-board')}
      />,
      {
        size: 'md',
        position: 'center',
        closable: true
      }
    )
  }

  const handleAddColumn = (title: string) => {
    if (activeBoard) {
      addColumnToContext(activeBoard.id, title)
      closeDialog('create-column')
    }
  }

  const openCreateColumnDialog = () => {
    openDialog('create-column',
      <CreateColumnDialog
        onCreateColumn={handleAddColumn}
        onCancel={() => closeDialog('create-column')}
      />,
      {
        size: 'md',
        position: 'center',
        closable: true
      }
    )
  }

  const handleDeleteColumn = (columnId: string) => {
    if (activeBoard) {
      deleteColumnFromContext(activeBoard.id, columnId)
    }
  }

  const handleEditColumn = (columnId: string, newTitle: string) => {
    if (activeBoard) {
      const columnToUpdate = activeBoard.columns.find((c) => c.id === columnId)
      if (columnToUpdate) {
        updateColumnInContext(activeBoard.id, { ...columnToUpdate, title: newTitle })
      }
    }
  }

  const handleAddSwimlane = (title: string) => {
    if (activeBoard) {
      addSwimlaneToContext(activeBoard.id, title)
      closeDialog('create-swimlane')
    }
  }

  const openCreateSwimlaneDialog = () => {
    openDialog('create-swimlane',
      <CreateSwimlaneDialog
        onCreateSwimlane={handleAddSwimlane}
        onCancel={() => closeDialog('create-swimlane')}
      />,
      {
        size: 'md',
        position: 'center',
        closable: true
      }
    )
  }

  const handleBoardSettingsSave = (boardId: string, name: string, description: string) => {
    if (activeBoard) {
      updateBoard(boardId, name, description);
      closeDialog('board-settings');
    }
  }

  const openBoardSettingsDialog = () => {
    if (activeBoard) {
      openDialog('board-settings',
        <BoardSettingsDialog
          boardData={{
            id: activeBoard.id,
            name: activeBoard.name,
            description: activeBoard.description
          }}
          onSave={handleBoardSettingsSave}
          onCancel={() => closeDialog('board-settings')}
        />,
        {
          size: 'md',
          position: 'center',
          closable: true
        }
      )
    }
  }

  const handleAgentUpdate = (updatedAgents: any[]) => {
    if (activeBoard) {
      updateAgents(activeBoard.id, updatedAgents);
    }
  }

  const openAgentManagementDialog = () => {
    if (activeBoard) {
      openDialog('agent-management',
        <AgentManagementDialog
          agents={activeBoard.agents || []}
          onUpdateAgents={handleAgentUpdate}
          onCancel={() => closeDialog('agent-management')}
        />,
        {
          size: 'lg',
          position: 'center',
          closable: true
        }
      )
    }
  }

  const handleUpdateSwimlane = (id: string, title: string) => {
    if (activeBoard) {
      const swimlaneToUpdate = activeBoard.swimlanes.find((s) => s.id === id)
      if (swimlaneToUpdate) {
        updateSwimlaneInContext(activeBoard.id, { ...swimlaneToUpdate, title })
      }
    }
  }

  const handleToggleSwimlaneExpansion = (id: string) => {
    if (activeBoard) {
      toggleSwimlaneExpansionInContext(activeBoard.id, id)
    }
  }

  const handleEditSwimlane = (swimlane: Swimlane) => {
    setSelectedSwimlane(swimlane)
    setShowEditSwimlaneDialog(true)
  }

  const openCreateCardDialog = (columnId: string, swimlaneId?: string) => {
    setTargetColumnId(columnId)
    // Ensure we always have a valid swimlaneId
    const validSwimlaneId = swimlaneId || activeSwimlaneForColumnView || swimlanes[0]?.id || "default"
    setTargetSwimlaneId(validSwimlaneId)
    setIsCreateCardOpen(true)
  }

  const handleAddCard = (cardData: Omit<Card, "id" | "columnId" | "createdAt" | "updatedAt">) => {
    if (activeBoard && targetColumnId && targetSwimlaneId) {
      const cardWithSwimlane = { ...cardData, swimlaneId: targetSwimlaneId };
      addCardToContext(activeBoard.id, targetColumnId, cardWithSwimlane);
    } else {
      console.warn("handleAddCard: Pre-conditions not met. Card creation aborted.", {
        activeBoard: !!activeBoard,
        targetColumnId,
        targetSwimlaneId,
      });
      toast({
        title: "Cannot Create Card",
        description: "Required information for creating the card is missing. Please try again or refresh the board.",
        variant: "destructive",
      });
    }
  }

  const internalHandleDeleteCard = (columnId: string, cardId: string) => {
    if (activeBoard) {
      deleteCardFromContext(activeBoard.id, columnId, cardId);
    }
  };

  const handleSwimlaneChangeForColumnView = (swimlaneId: string) => {
    setActiveSwimlaneForColumnView(swimlaneId)
  }

  if (!activeBoard) {
    return <div className="p-8 text-center">No board selected or loading...</div>
  }

  const activeCardForOverlay = activeCardId
    ? columns.flatMap(col => col.cards).find(card => card.id === activeCardId)
    : null;

  // Custom collision detection that combines multiple algorithms
  const customCollisionDetection = (args: any) => {
    // For column dragging, use rectIntersection which is better for horizontal sorting
    if (args.active.data.current?.type === 'column') {
      return rectIntersection(args);
    }
    // For cards, use the default closestCenter
    return closestCenter(args);
  };

  // Add an effect to ensure drag state is reset when component unmounts
  useEffect(() => {
    return () => {
      // Cleanup function to reset all drag states when component unmounts
      setIsDragging(false);
      setActiveCardId(null);
      setActiveColumnIdForDnd(null);
      setActiveSwimlaneIdForDnd(null);
      setDraggedColumn(null);
      setDragOverColumnTargetId(null);
    };
  }, []);

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={customCollisionDetection}
      onDragStart={handleDragStart}
      onDragOver={handleDragOver}
      onDragEnd={handleDragEnd}
      onDragCancel={handleDragCancel}
      // ✅ SURGICAL FIX: Optimize DndContext configuration to prevent freezing
      autoScroll={true}
      measuring={{
        droppable: {
          strategy: 'whenNecessary', // Changed from 'always' to prevent performance issues
        },
      }}
    >
      <div className="flex flex-col h-full">
        <div className="flex items-center justify-between p-4 border-b border-border">
          <h2 className="text-xl font-semibold tracking-tight">{activeBoard.name}</h2>
          <div className="flex gap-2">
            <Tabs value={viewMode} onValueChange={(value) => setViewMode(value as "swimlanes" | "columns")}>
              <TabsList>
                <TabsTrigger value="swimlanes" className="flex items-center gap-1">
                  <LayoutGrid className="h-4 w-4" />
                  Swimlanes
                </TabsTrigger>
                <TabsTrigger value="columns" className="flex items-center gap-1">
                  <ChevronRight className="h-4 w-4" />
                  Columns
                </TabsTrigger>
              </TabsList>
            </Tabs>

            {viewMode === "columns" && swimlanes.length > 0 && (
              <Select value={activeSwimlaneForColumnView || swimlanes[0]?.id} onValueChange={handleSwimlaneChangeForColumnView}>
                <SelectTrigger className="w-[180px] h-9">
                  <SelectValue placeholder="Select swimlane" />
                </SelectTrigger>
                <SelectContent>
                  {swimlanes.map((swimlane) => (
                    <SelectItem key={swimlane.id} value={swimlane.id}>
                      {swimlane.title}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          </div>
          <div className="flex items-center space-x-2">
            <KanbanLegend cardTypes={cardTypes} onEdit={() => setShowLegendEditDialog(true)} />
            <Button variant="outline" size="sm" onClick={openAgentManagementDialog}>
              <Cpu className="h-4 w-4 mr-2" />
              Manage Agents
            </Button>
            <Button variant="outline" size="sm" onClick={openCreateBoardDialog}>
              Add Board
            </Button>
            <Button variant="outline" size="sm" onClick={openBoardSettingsDialog}>
              Board Settings
            </Button>
            <Button variant="outline" size="sm" onClick={openCreateColumnDialog}>
              Add Column
            </Button>
            <Button variant="outline" size="sm" onClick={openCreateSwimlaneDialog}>
              Add Swimlane
            </Button>
          </div>
        </div>

        {/* Main content area, handles overall board scrolling */}
        <div className="flex-1 overflow-auto">
          <div className={`p-4 ${isDragging ? "cursor-grabbing" : ""}`}>
            {viewMode === "swimlanes" ? (
              <div className="flex flex-col gap-4 min-h-full"> {/* Use min-h-full to ensure content pushes scrollbar */}
                {swimlanes.map((swimlane) => (
                  <KanbanSwimlane
                    key={swimlane.id}
                    swimlane={swimlane}
                    columns={filteredColumns}
                    onAddCard={(columnId) => openCreateCardDialog(columnId, swimlane.id)}
                    onCardUpdate={handleItemUpdate}
                    onDeleteCard={internalHandleDeleteCard}
                    onToggleExpansion={() => handleToggleSwimlaneExpansion(swimlane.id)}
                    onEditSwimlane={() => handleEditSwimlane(swimlane)}
                    cardTypes={cardTypes}
                    agents={agents}
                    boardActiveColumnId={activeColumnIdForDnd}
                    boardActiveSwimlaneId={activeSwimlaneIdForDnd}
                  />
                ))}
              </div>
            ) : (
              <div className="flex gap-4 h-full"> {/* Ensure columns take full height, overflow handled by parent */}
                {filteredColumns.map((column) => (
                  <KanbanColumn
                    key={column.id}
                    column={column}
                    onAddCard={() => openCreateCardDialog(column.id, activeSwimlaneForColumnView || swimlanes[0]?.id || "default")}
                    onDeleteColumn={() => handleDeleteColumn(column.id)}
                    onEditColumn={(newTitle) => handleEditColumn(column.id, newTitle)}
                    onCardUpdate={handleItemUpdate}
                    onDeleteCard={(cardId) => internalHandleDeleteCard(column.id, cardId)}
                    cardTypes={cardTypes}
                    agents={agents}
                    isBoardOver={activeColumnIdForDnd === column.id && activeSwimlaneIdForDnd === (activeSwimlaneForColumnView || swimlanes[0]?.id) && activeCardId !== null}
                    isColumnDropTarget={draggedColumn !== null && column.id === dragOverColumnTargetId && draggedColumn.id !== column.id}
                    isBoardDragging={isDragging}
                  />
                ))}
              </div>
            )}
          </div>
        </div>

        <DragOverlay ref={dragOverlayRef} style={{
          // Only show overlay when actually dragging something
          display: (activeCardForOverlay || draggedColumn) ? 'block' : 'none',
          // Ensure overlay doesn't block interactions when not dragging
          pointerEvents: isDragging ? 'auto' : 'none',
          // Set a reasonable z-index that won't block other UI
          zIndex: isDragging ? 1000 : -1
        }}>
          {activeCardForOverlay ? (
             <KanbanCard
                key={activeCardForOverlay.id}
                card={activeCardForOverlay}
                cardTypes={cardTypes}
                isDragOverlay
                columnId={activeCardForOverlay.columnId}
              />
          ) : draggedColumn ? (
            <KanbanColumn
              column={draggedColumn}
              onAddCard={() => {}}
              onDeleteColumn={() => {}}
              onEditColumn={() => {}}
              onCardUpdate={() => {}}
              onDeleteCard={() => {}}
              cardTypes={cardTypes}
              agents={agents}
              isBoardDragging={true} // Indicate it's a drag overlay version
            />
          ) : null}
        </DragOverlay>
      </div>

      <CreateCardDialog
        open={isCreateCardOpen}
        onOpenChange={setIsCreateCardOpen}
        onCreateCard={(cardData) => {
            handleAddCard(cardData);
            setIsCreateCardOpen(false);
        }}
        cardTypes={cardTypes}
      />





      <EditSwimlaneDialog
        open={showEditSwimlaneDialog}
        onOpenChange={setShowEditSwimlaneDialog}
        onUpdateSwimlane={handleUpdateSwimlane}
        swimlane={selectedSwimlane}
      />


      <LegendEditDialog
        open={showLegendEditDialog}
        onOpenChange={setShowLegendEditDialog}
        cardTypes={cardTypes}
        onSave={(updatedCardTypes) => {
          if (activeBoard) {
            updateCardTypes(activeBoard.id, updatedCardTypes)
          }
        }}
      />
    </DndContext>
  )
}

// Add default export for dynamic imports
export default KanbanBoard