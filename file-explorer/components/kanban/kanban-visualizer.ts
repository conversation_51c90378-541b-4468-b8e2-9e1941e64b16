// ARCHITECTURE RESTORATION: Passive Kanban Visualizer
// This component only handles UI visualization - NO business logic or orchestration

import { boardIPCBridge } from './lib/board-ipc-bridge';
import { Card } from './board-context';
import {
  logKanbanEvent,
  logKanbanDebug,
  logKanbanError,
  logCardMovementWithReason,
  logCardMarkedDoneWithoutOutput,
  logSilentExecutionFailure
} from '../../services/logger';

export interface VisualizationRequest {
  taskId: string;
  title: string;
  description: string;
  assignedAgentId: string;
  priority: 'low' | 'medium' | 'high';
  tags?: string[];
  metadata?: any;
}

export interface VisualizationResult {
  success: boolean;
  cardId?: string;
  error?: string;
}

/**
 * ARCHITECTURE RESTORATION: Pure visualization component
 * - NO task orchestration
 * - NO agent assignment logic  
 * - NO business logic
 * - ONLY passive UI updates
 */
export class KanbanVisualizer {
  private static instance: KanbanVisualizer;

  private constructor() {}

  public static getInstance(): KanbanVisualizer {
    if (!KanbanVisualizer.instance) {
      KanbanVisualizer.instance = new KanbanVisualizer();
    }
    return KanbanVisualizer.instance;
  }

  /**
   * Create a passive visualization card for a task
   * This is ONLY for UI display - no orchestration logic
   */
  public async createVisualizationCard(request: VisualizationRequest): Promise<VisualizationResult> {
    try {
      console.log(`🎨 KanbanVisualizer: Creating visualization card for task "${request.taskId}"`);

      logKanbanEvent('CardCreationStarted', {
        taskId: request.taskId,
        title: request.title,
        assignedAgentId: request.assignedAgentId,
        priority: request.priority,
        tags: request.tags,
        source: request.metadata?.source
      });

      // Use main board for all visualizations (like Agent System)
      const boardId = 'main';
      const columnId = 'column-1'; // Always start in backlog

      const cardData = {
        title: request.title,
        description: request.description,
        priority: request.priority,
        columnId,
        swimlaneId: null, // Let board assign default swimlane
        projectId: request.taskId,
        tags: request.tags || [],
        labels: [`source:${request.metadata?.source || 'unknown'}`],
        progress: 0,
        assignee: this.getAgentDisplayName(request.assignedAgentId),
        assignedAgentId: request.assignedAgentId,
        agentAssignments: [{
          agentId: request.assignedAgentId,
          agentType: 'AI',
          assignmentTime: new Date().toISOString(),
          role: 'primary',
          status: 'assigned'
        }],
        dependencies: [],
        resourceMetrics: {
          tokenUsage: 0,
          cpuTime: 0,
          memoryUsage: 0
        },
        taskHistory: [{
          timestamp: new Date().toISOString(),
          action: 'created',
          agentId: 'kanban-visualizer',
          details: `Visualization card created for task: ${request.taskId}`
        }]
      };

      const card = await boardIPCBridge.createCard(boardId, columnId, cardData as any);

      if (card) {
        console.log(`✅ KanbanVisualizer: Visualization card created: "${card.title}" (${card.id})`);

        logKanbanEvent('CardCreated', {
          cardId: card.id,
          title: card.title,
          boardId,
          columnId,
          assignedAgentId: request.assignedAgentId,
          taskId: request.taskId
        });

        return {
          success: true,
          cardId: card.id
        };
      } else {
        logKanbanError('CardCreationFailed', {
          taskId: request.taskId,
          error: 'Failed to create visualization card'
        });

        return {
          success: false,
          error: 'Failed to create visualization card'
        };
      }

    } catch (error) {
      console.error(`❌ KanbanVisualizer: Failed to create visualization card:`, error);

      logKanbanError('CardCreationError', {
        taskId: request.taskId,
        error: error instanceof Error ? error.message : 'Unknown visualization error',
        stack: error instanceof Error ? error.stack : undefined
      });

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown visualization error'
      };
    }
  }

  /**
   * Update card status for visualization purposes only
   */
  public async updateCardStatus(cardId: string, columnId: string, reason?: string, triggeredBy?: string, agentAction?: string): Promise<VisualizationResult> {
    try {
      console.log(`🎨 KanbanVisualizer: Updating card "${cardId}" to column "${columnId}"`);

      // ✅ MANDATORY UPGRADE: Get current card state to track movement
      const currentCard = await this.getCurrentCardState(cardId);
      const fromColumn = currentCard?.columnId || 'unknown';

      logKanbanEvent('CardStatusUpdateStarted', {
        cardId,
        newColumnId: columnId,
        boardId: 'main',
        fromColumn,
        reason: reason || 'status_update_requested',
        triggeredBy: triggeredBy || 'system'
      });

      const success = await boardIPCBridge.moveCard('main', cardId, columnId);

      if (success) {
        console.log(`✅ KanbanVisualizer: Card status updated successfully`);

        // ✅ MANDATORY UPGRADE: Log card movement with detailed reason and detect silent failures
        logCardMovementWithReason({
          cardId,
          taskId: currentCard?.taskId || 'unknown',
          fromColumn,
          toColumn: columnId,
          reason: reason || 'status_update_requested',
          triggeredBy: triggeredBy || 'system',
          agentAction: agentAction || 'card_status_update',
          hasOutput: false, // Will be updated by agent execution logging
          outputFiles: []
        });

        // ✅ MANDATORY UPGRADE: Detect if card moved to "Done" without execution output
        if (columnId === 'column-6' && !agentAction) { // column-6 is typically "Done"
          logCardMarkedDoneWithoutOutput({
            cardId,
            taskId: currentCard?.taskId || 'unknown',
            agentId: currentCard?.assignedAgentId || 'unknown',
            columnId,
            timestamp: Date.now()
          });

          // Also log as silent execution failure
          logSilentExecutionFailure({
            cardId,
            taskId: currentCard?.taskId || 'unknown',
            agentId: currentCard?.assignedAgentId || 'unknown',
            reason: 'Card moved to Done column without agent execution output',
            columnTransition: { from: fromColumn, to: columnId }
          });
        }

        logKanbanEvent('CardStatusUpdated', {
          cardId,
          newColumnId: columnId,
          boardId: 'main',
          fromColumn,
          reason: reason || 'status_update_requested'
        });

        return { success: true };
      } else {
        logKanbanError('CardStatusUpdateFailed', {
          cardId,
          columnId,
          error: 'Failed to update card status',
          fromColumn,
          reason: reason || 'status_update_requested'
        });

        return {
          success: false,
          error: 'Failed to update card status'
        };
      }

    } catch (error) {
      console.error(`❌ KanbanVisualizer: Failed to update card status:`, error);

      logKanbanError('CardStatusUpdateError', {
        cardId,
        columnId,
        error: error instanceof Error ? error.message : 'Unknown update error',
        stack: error instanceof Error ? error.stack : undefined
      });

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown update error'
      };
    }
  }

  /**
   * ✅ MANDATORY UPGRADE: Get current card state for movement tracking
   */
  private async getCurrentCardState(cardId: string): Promise<{ columnId: string; taskId?: string; assignedAgentId?: string } | null> {
    try {
      const boardState = await boardIPCBridge.getBoardState('main');
      if (!boardState || !boardState.columns) {
        return null;
      }

      for (const column of boardState.columns) {
        const card = column.cards.find((c: any) => c.id === cardId);
        if (card) {
          return {
            columnId: column.id,
            taskId: card.taskId || card.metadata?.taskId,
            assignedAgentId: card.assignedAgentId
          };
        }
      }

      return null;
    } catch (error) {
      console.warn(`Failed to get current state for card ${cardId}:`, error);
      return null;
    }
  }

  /**
   * Get agent display name for UI purposes
   */
  private getAgentDisplayName(agentId: string): string {
    const agentNames: Record<string, string> = {
      'intern': 'Intern Agent',
      'junior': 'Junior Developer',
      'midlevel': 'Mid-Level Developer', 
      'senior': 'Senior Developer',
      'architect': 'System Architect',
      'designer': 'UI/UX Designer',
      'tester': 'QA Tester',
      'researcher': 'Research Agent',
      'micromanager': 'Micromanager'
    };

    return agentNames[agentId] || `${agentId} Agent`;
  }
}

// Export singleton instance
export const kanbanVisualizer = KanbanVisualizer.getInstance();
