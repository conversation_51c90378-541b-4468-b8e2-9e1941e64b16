// components/history/HistoryCard.tsx

"use client"

import { useState } from "react"
import { cn } from "@/lib/utils"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader } from "@/components/ui/card"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@radix-ui/react-collapsible"
import {
  ChevronDown,
  ChevronRight,
  Clock,
  MessageSquare,
  Activity,
  DollarSign,
  Zap,
  Calendar,
  User
} from "lucide-react"
import type { AgentHistoryEntry } from "@/types/agent-history"
import { STATUS_CONFIG, ACTION_TYPE_CONFIG, AGENT_TYPE_NAMES } from "@/types/agent-history"

interface HistoryCardProps {
  entry: AgentHistoryEntry
  className?: string
}

export default function HistoryCard({ entry, className }: HistoryCardProps) {
  const [isExpanded, setIsExpanded] = useState(false)

  const statusConfig = STATUS_CONFIG[entry.status]
  const agentName = AGENT_TYPE_NAMES[entry.agentType as keyof typeof AGENT_TYPE_NAMES] || entry.agentType

  const formatDuration = (ms: number) => {
    if (ms < 1000) return `${ms}ms`
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`
    return `${(ms / 60000).toFixed(1)}m`
  }

  const formatTimestamp = (timestamp: number) => {
    const date = new Date(timestamp)
    const now = new Date()
    const diffMs = now.getTime() - timestamp
    const diffHours = diffMs / (1000 * 60 * 60)
    const diffDays = diffMs / (1000 * 60 * 60 * 24)

    if (diffHours < 1) {
      const diffMinutes = Math.floor(diffMs / (1000 * 60))
      return `${diffMinutes}m ago`
    } else if (diffHours < 24) {
      return `${Math.floor(diffHours)}h ago`
    } else if (diffDays < 7) {
      return `${Math.floor(diffDays)}d ago`
    } else {
      return date.toLocaleDateString()
    }
  }

  const getAgentIcon = (agentType: string) => {
    const icons: Record<string, string> = {
      micromanager: "🧠",
      intern: "🛠️",
      junior: "📦",
      midlevel: "⚙️",
      senior: "🧱",
      architect: "🏗️",
      designer: "🎨",
      tester: "🧪",
      researcher: "📘"
    }
    return icons[agentType] || "🤖"
  }

  return (
    <Card className={cn("transition-all duration-200 hover:shadow-md", className)}>
      <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
        <CollapsibleTrigger asChild>
          <CardHeader className="cursor-pointer hover:bg-accent/50 transition-colors">
            <div className="flex items-start justify-between">
              <div className="flex items-start gap-3 flex-1 min-w-0">
                {/* Agent Icon */}
                <div className="flex-shrink-0 mt-1">
                  <span className="text-lg">{getAgentIcon(entry.agentType)}</span>
                </div>

                {/* Main Content */}
                <div className="flex-1 min-w-0 space-y-2">
                  {/* Header Row */}
                  <div className="flex items-center gap-2 flex-wrap">
                    <Badge
                      variant="outline"
                      className={cn("text-xs", statusConfig.color, statusConfig.bgColor, statusConfig.borderColor)}
                    >
                      {statusConfig.icon} {statusConfig.label}
                    </Badge>

                    <Badge variant="outline" className="text-xs">
                      <User className="h-3 w-3 mr-1" />
                      {agentName}
                    </Badge>

                    <span className="text-xs text-muted-foreground flex items-center gap-1">
                      <Calendar className="h-3 w-3" />
                      {formatTimestamp(entry.timestamp)}
                    </span>
                  </div>

                  {/* Task Description */}
                  <h3 className="font-medium text-sm leading-tight break-words">
                    {entry.taskDescription}
                  </h3>

                  {/* Metrics Row */}
                  <div className="flex items-center gap-4 text-xs text-muted-foreground">
                    {entry.duration && (
                      <span className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        {formatDuration(entry.duration)}
                      </span>
                    )}

                    {entry.tokensUsed && (
                      <span className="flex items-center gap-1">
                        <Zap className="h-3 w-3" />
                        {entry.tokensUsed.toLocaleString()} tokens
                      </span>
                    )}

                    {entry.cost && (
                      <span className="flex items-center gap-1">
                        <DollarSign className="h-3 w-3" />
                        ${entry.cost.toFixed(4)}
                      </span>
                    )}

                    {entry.actionsTaken.length > 0 && (
                      <span className="flex items-center gap-1">
                        <Activity className="h-3 w-3" />
                        {entry.actionsTaken.length} actions
                      </span>
                    )}

                    {entry.messages.length > 0 && (
                      <span className="flex items-center gap-1">
                        <MessageSquare className="h-3 w-3" />
                        {entry.messages.length} messages
                      </span>
                    )}
                  </div>
                </div>
              </div>

              {/* Expand Button */}
              <Button variant="ghost" size="sm" className="h-6 w-6 p-0 flex-shrink-0">
                {isExpanded ? (
                  <ChevronDown className="h-3 w-3" />
                ) : (
                  <ChevronRight className="h-3 w-3" />
                )}
              </Button>
            </div>
          </CardHeader>
        </CollapsibleTrigger>

        <CollapsibleContent>
          <CardContent className="pt-0 space-y-4">
            {/* Result Summary */}
            <div className="space-y-2">
              <h4 className="text-sm font-medium">Result</h4>
              <p className="text-sm text-muted-foreground bg-accent/30 rounded p-2">
                {entry.resultSummary}
              </p>
            </div>

            {/* Actions Taken */}
            {entry.actionsTaken.length > 0 && (
              <div className="space-y-2">
                <h4 className="text-sm font-medium">Actions Taken</h4>
                <div className="space-y-1">
                  {entry.actionsTaken.map((action) => {
                    const actionConfig = ACTION_TYPE_CONFIG[action.type]
                    return (
                      <div key={action.id} className="flex items-start gap-2 text-sm">
                        <span className={cn("text-xs mt-0.5", actionConfig.color)}>
                          {actionConfig.icon}
                        </span>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2">
                            <span className="font-medium">{actionConfig.label}</span>
                            <Badge
                              variant="outline"
                              className={cn(
                                "text-xs h-4",
                                action.result === 'success' ? "text-green-600 bg-green-50 border-green-200" :
                                action.result === 'error' ? "text-red-600 bg-red-50 border-red-200" :
                                "text-yellow-600 bg-yellow-50 border-yellow-200"
                              )}
                            >
                              {action.result}
                            </Badge>
                          </div>
                          <p className="text-xs text-muted-foreground mt-1">
                            {action.description}
                          </p>
                          {action.target && (
                            <p className="text-xs text-muted-foreground font-mono">
                              {action.target}
                            </p>
                          )}
                        </div>
                      </div>
                    )
                  })}
                </div>
              </div>
            )}

            {/* Messages */}
            {entry.messages.length > 0 && (
              <div className="space-y-2">
                <h4 className="text-sm font-medium">Messages</h4>
                <div className="space-y-2 max-h-40 overflow-y-auto">
                  {entry.messages.map((message) => (
                    <div key={message.id} className="text-sm border-l-2 border-accent pl-3">
                      <div className="flex items-center gap-2 mb-1">
                        <Badge variant="outline" className="text-xs h-4">
                          {message.role}
                        </Badge>
                        <span className="text-xs text-muted-foreground">
                          {new Date(message.timestamp).toLocaleTimeString()}
                        </span>
                      </div>
                      <p className="text-muted-foreground">{message.content}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Metadata */}
            {entry.metadata && Object.keys(entry.metadata).length > 0 && (
              <div className="space-y-2">
                <h4 className="text-sm font-medium">Metadata</h4>
                <div className="text-xs font-mono bg-accent/30 rounded p-2 overflow-x-auto">
                  <pre>{JSON.stringify(entry.metadata, null, 2)}</pre>
                </div>
              </div>
            )}
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  )
}
