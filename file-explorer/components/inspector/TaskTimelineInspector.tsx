"use client"

import React, { useState, useEffect, useMemo, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  Clock,
  Filter,
  ZoomIn,
  ZoomOut,
  Play,
  Pause,
  RotateCcw,
  Download,
  CheckCircle,
  XCircle,
  Loader,
  User,
  Calendar,
  Timer
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { But<PERSON> } from '@/components/ui/button'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { Slider } from '@/components/ui/slider'
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'

// Import task data services
import { TaskStatusService } from '@/components/agents/task-status-service'
import { AgentEventsService } from '@/services/agent-events'

interface TimelineTask {
  id: string
  agentId: string
  agentName: string
  agentType: string
  taskDescription: string
  startedAt: number
  completedAt?: number
  status: 'running' | 'completed' | 'failed' | 'pending'
  executionTime?: number
  tokensUsed?: number
  cost?: number
  modelUsed?: string
  kanbanCardId?: string
  metadata?: Record<string, any>
}

interface TimelineFilters {
  agentType: string
  status: string
  minDuration: number
  maxDuration: number
  showOnlyErrors: boolean
  showOnlySuccess: boolean
}

interface TimelineViewport {
  startTime: number
  endTime: number
  pixelsPerSecond: number
  zoomLevel: number
}

const ZOOM_LEVELS = [
  { label: '10s', seconds: 10, pixelsPerSecond: 20 },
  { label: '30s', seconds: 30, pixelsPerSecond: 10 },
  { label: '1m', seconds: 60, pixelsPerSecond: 5 },
  { label: '5m', seconds: 300, pixelsPerSecond: 1 },
  { label: '15m', seconds: 900, pixelsPerSecond: 0.5 },
  { label: '1h', seconds: 3600, pixelsPerSecond: 0.1 }
]

const AGENT_COLORS = {
  micromanager: '#8B5CF6', // purple
  intern: '#10B981', // emerald
  junior: '#3B82F6', // blue
  midlevel: '#F59E0B', // amber
  senior: '#EF4444', // red
  architect: '#6366F1', // indigo
  designer: '#EC4899', // pink
  tester: '#84CC16', // lime
  researcher: '#06B6D4' // cyan
} as const

const STATUS_COLORS = {
  running: '#3B82F6', // blue
  completed: '#10B981', // green
  failed: '#EF4444', // red
  pending: '#6B7280' // gray
} as const

// Timeline Grid Component
interface TimelineGridProps {
  tasks: TimelineTask[]
  tasksByAgent: Record<string, TimelineTask[]>
  viewport: TimelineViewport
  onTaskSelect: (task: TimelineTask) => void
  selectedTask: TimelineTask | null
}

const TimelineGrid: React.FC<TimelineGridProps> = ({
  tasks,
  tasksByAgent,
  viewport,
  onTaskSelect,
  selectedTask
}) => {
  const formatTime = (timestamp: number) => {
    // Use a consistent format to avoid hydration mismatches
    const date = new Date(timestamp)
    const hours = date.getHours().toString().padStart(2, '0')
    const minutes = date.getMinutes().toString().padStart(2, '0')
    const seconds = date.getSeconds().toString().padStart(2, '0')
    return `${hours}:${minutes}:${seconds}`
  }

  const getTaskPosition = (task: TimelineTask) => {
    const startOffset = (task.startedAt - viewport.startTime) * viewport.pixelsPerSecond / 1000
    const duration = task.completedAt ? task.completedAt - task.startedAt : Date.now() - task.startedAt
    const width = Math.max(duration * viewport.pixelsPerSecond / 1000, 4) // Minimum 4px width

    return { left: startOffset, width }
  }

  const timelineWidth = (viewport.endTime - viewport.startTime) * viewport.pixelsPerSecond / 1000

  return (
    <div className="relative">
      {/* Time axis */}
      <div className="sticky top-0 z-10 bg-background border-b border-border">
        <div className="h-8 relative" style={{ width: timelineWidth }}>
          {/* Time markers */}
          {Array.from({ length: Math.ceil(timelineWidth / 100) }, (_, i) => {
            const timeOffset = (i * 100) / viewport.pixelsPerSecond * 1000
            const timestamp = viewport.startTime + timeOffset
            return (
              <div
                key={i}
                className="absolute top-0 h-full border-l border-border/50 text-xs text-muted-foreground pl-1 flex items-center"
                style={{ left: i * 100 }}
              >
                {formatTime(timestamp)}
              </div>
            )
          })}

          {/* Current time line */}
          {Date.now() >= viewport.startTime && Date.now() <= viewport.endTime && (
            <div
              className="absolute top-0 h-full border-l-2 border-red-500 z-20"
              style={{ left: (Date.now() - viewport.startTime) * viewport.pixelsPerSecond / 1000 }}
            >
              <div className="absolute -top-1 -left-1 w-2 h-2 bg-red-500 rounded-full" />
            </div>
          )}
        </div>
      </div>

      {/* Agent rows */}
      <div className="space-y-1 p-2">
        {Object.entries(tasksByAgent).map(([agentId, agentTasks]) => (
          <div key={agentId} className="relative">
            {/* Agent row background */}
            <div className="h-8 bg-muted/10 rounded-md relative" style={{ width: timelineWidth }}>
              {/* Task bars */}
              {agentTasks.map(task => {
                const position = getTaskPosition(task)
                const isSelected = selectedTask?.id === task.id

                return (
                  <TooltipProvider key={task.id}>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <motion.div
                          initial={{ opacity: 0, scaleX: 0 }}
                          animate={{ opacity: 1, scaleX: 1 }}
                          transition={{ duration: 0.3 }}
                          className={cn(
                            "absolute top-1 h-6 rounded-sm cursor-pointer border-2 transition-all",
                            isSelected ? "border-white shadow-lg z-10" : "border-transparent",
                            "hover:border-white/50 hover:shadow-md"
                          )}
                          style={{
                            left: position.left,
                            width: position.width,
                            backgroundColor: STATUS_COLORS[task.status],
                            opacity: task.status === 'running' ? 0.8 : 1
                          }}
                          onClick={() => onTaskSelect(task)}
                        >
                          {/* Task status indicator */}
                          <div className="absolute right-1 top-1">
                            {task.status === 'running' && <Loader className="h-2 w-2 animate-spin text-white" />}
                            {task.status === 'completed' && <CheckCircle className="h-2 w-2 text-white" />}
                            {task.status === 'failed' && <XCircle className="h-2 w-2 text-white" />}
                          </div>
                        </motion.div>
                      </TooltipTrigger>
                      <TooltipContent side="top" className="max-w-xs">
                        <div className="space-y-1">
                          <div className="font-medium">{task.agentName}</div>
                          <div className="text-xs text-muted-foreground">{task.taskDescription}</div>
                          <div className="text-xs">
                            Started: {formatTime(task.startedAt)}
                            {task.completedAt && (
                              <> • Completed: {formatTime(task.completedAt)}</>
                            )}
                          </div>
                          {task.executionTime && (
                            <div className="text-xs">Duration: {(task.executionTime / 1000).toFixed(1)}s</div>
                          )}
                          {task.tokensUsed && (
                            <div className="text-xs">Tokens: {task.tokensUsed.toLocaleString()}</div>
                          )}
                          <Badge variant="outline" className="text-xs">
                            {task.status}
                          </Badge>
                        </div>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                )
              })}
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

// Task Details Panel Component
interface TaskDetailsPanelProps {
  task: TimelineTask
  onClose: () => void
}

const TaskDetailsPanel: React.FC<TaskDetailsPanelProps> = ({ task, onClose }) => {
  const formatDuration = (ms: number) => {
    const seconds = Math.floor(ms / 1000)
    const minutes = Math.floor(seconds / 60)
    const hours = Math.floor(minutes / 60)

    if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`
    } else {
      return `${seconds}s`
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 20 }}
      className="border-t border-border bg-muted/20 p-4"
    >
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold">Task Details</h3>
        <Button variant="ghost" size="icon" onClick={onClose}>
          <XCircle className="h-4 w-4" />
        </Button>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm">Basic Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex items-center space-x-2">
              <User className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">{task.agentName}</span>
              <Badge variant="outline" className="text-xs">
                {task.agentType}
              </Badge>
            </div>
            <div className="flex items-center space-x-2">
              <div
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: STATUS_COLORS[task.status] }}
              />
              <span className="text-sm capitalize">{task.status}</span>
            </div>
            <div className="text-sm text-muted-foreground">
              {task.taskDescription}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm">Timing</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex items-center space-x-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">Started: {new Date(task.startedAt).toLocaleString()}</span>
            </div>
            {task.completedAt && (
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">Completed: {new Date(task.completedAt).toLocaleString()}</span>
              </div>
            )}
            {task.executionTime && (
              <div className="flex items-center space-x-2">
                <Timer className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">Duration: {formatDuration(task.executionTime)}</span>
              </div>
            )}
          </CardContent>
        </Card>

        {(task.tokensUsed || task.cost || task.modelUsed) && (
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm">Resource Usage</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              {task.tokensUsed && (
                <div className="text-sm">
                  <span className="text-muted-foreground">Tokens:</span> {task.tokensUsed.toLocaleString()}
                </div>
              )}
              {task.cost && (
                <div className="text-sm">
                  <span className="text-muted-foreground">Cost:</span> ${task.cost.toFixed(4)}
                </div>
              )}
              {task.modelUsed && (
                <div className="text-sm">
                  <span className="text-muted-foreground">Model:</span> {task.modelUsed}
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {task.kanbanCardId && (
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm">Integration</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-sm">
                <span className="text-muted-foreground">Kanban Card:</span> {task.kanbanCardId}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </motion.div>
  )
}

export default function TaskTimelineInspector() {
  const [tasks, setTasks] = useState<TimelineTask[]>([])
  const [filters, setFilters] = useState<TimelineFilters>({
    agentType: 'all',
    status: 'all',
    minDuration: 0,
    maxDuration: 300,
    showOnlyErrors: false,
    showOnlySuccess: false
  })
  const [viewport, setViewport] = useState<TimelineViewport>({
    startTime: Date.now() - 300000, // 5 minutes ago
    endTime: Date.now(),
    pixelsPerSecond: 5,
    zoomLevel: 2
  })
  const [isLive, setIsLive] = useState(true)
  const [selectedTask, setSelectedTask] = useState<TimelineTask | null>(null)

  // Services
  const [taskStatusService] = useState(() => new TaskStatusService())
  const [agentEventsService] = useState(() => new AgentEventsService())

  // ✅ Broadcast timeline updates to other windows
  const broadcastTimelineUpdate = useCallback((updateData: {
    tasks?: TimelineTask[];
    statusData?: any;
    recentActivity?: any[];
  }) => {
    if (typeof window !== 'undefined' && window.electronAPI?.ipc) {
      window.electronAPI.ipc.send('timeline-data-update', {
        ...updateData,
        timestamp: Date.now()
      })
      console.log('TaskTimelineInspector: Broadcasting timeline update to other windows')
    }
  }, [])

  // ✅ Broadcast individual task updates
  const broadcastTaskUpdate = useCallback((taskUpdate: {
    taskId: string;
    agentId: string;
    status: string;
    metadata?: any;
  }) => {
    if (typeof window !== 'undefined' && window.electronAPI?.ipc) {
      window.electronAPI.ipc.send('timeline-task-update', {
        ...taskUpdate,
        timestamp: Date.now()
      })
      console.log('TaskTimelineInspector: Broadcasting task update to other windows:', taskUpdate)
    }
  }, [])

  // ✅ Broadcast agent activity
  const broadcastAgentActivity = useCallback((activityData: any) => {
    if (typeof window !== 'undefined' && window.electronAPI?.ipc) {
      window.electronAPI.ipc.send('timeline-agent-activity', {
        ...activityData,
        timestamp: Date.now()
      })
      console.log('TaskTimelineInspector: Broadcasting agent activity to other windows:', activityData)
    }
  }, [])

  // Load task data
  const loadTaskData = useCallback(async () => {
    try {
      // Get current task statuses
      const statusData = taskStatusService.exportStatusData()

      // Get recent agent activity
      const recentActivity = agentEventsService.getRecentActivity(100)

      // Convert to timeline tasks
      const timelineTasks: TimelineTask[] = []

      // Process current statuses
      Object.entries(statusData.currentStatuses).forEach(([taskId, status]) => {
        const history = statusData.statusHistory[taskId] || []
        const startEvent = history.find(h => h.status === 'running')
        const endEvent = history.find(h => h.status === 'completed' || h.status === 'failed')

        if (startEvent) {
          timelineTasks.push({
            id: taskId,
            agentId: status.agentId,
            agentName: getAgentName(status.agentId),
            agentType: getAgentType(status.agentId),
            taskDescription: status.message || 'Task execution',
            startedAt: startEvent.timestamp,
            completedAt: endEvent?.timestamp,
            status: status.status as any,
            executionTime: endEvent ? endEvent.timestamp - startEvent.timestamp : undefined,
            tokensUsed: status.metadata?.tokensUsed,
            cost: status.metadata?.cost,
            modelUsed: status.metadata?.modelUsed,
            kanbanCardId: status.metadata?.kanbanCardId,
            metadata: status.metadata
          })
        }
      })

      // Process agent activity events
      recentActivity.forEach(event => {
        if (event.type === 'task_started' || event.type === 'task_completed') {
          const existingTask = timelineTasks.find(t => t.id === event.taskId)
          if (!existingTask && event.taskId) {
            timelineTasks.push({
              id: event.taskId,
              agentId: event.agentId,
              agentName: event.agentName || getAgentName(event.agentId),
              agentType: event.agentType,
              taskDescription: event.message,
              startedAt: event.timestamp,
              completedAt: event.type === 'task_completed' ? event.timestamp : undefined,
              status: event.type === 'task_completed' ? 'completed' : 'running',
              metadata: event.data
            })
          }
        }
      })

      // Sort by start time
      timelineTasks.sort((a, b) => a.startedAt - b.startedAt)

      setTasks(timelineTasks)

      // ✅ Broadcast timeline update to other windows if we have data
      if (timelineTasks.length > 0) {
        broadcastTimelineUpdate({
          tasks: timelineTasks,
          statusData,
          recentActivity
        })
      }

      // ✅ Always request data from shared state to ensure synchronization
      if (typeof window !== 'undefined' && window.electronAPI?.ipc) {
        console.log('TaskTimelineInspector: Requesting timeline data from shared state...')
        try {
          window.electronAPI.ipc.send('request-timeline-data', {
            requestId: `timeline-${Date.now()}`,
            timestamp: Date.now()
          })
        } catch (error) {
          console.warn('TaskTimelineInspector: Failed to request data from shared state:', error)
        }
      }
    } catch (error) {
      console.error('Failed to load task data:', error)
    }
  }, [taskStatusService, agentEventsService, broadcastTimelineUpdate])

  // Helper functions
  const getAgentName = (agentId: string): string => {
    const names: Record<string, string> = {
      micromanager: 'Micromanager',
      intern: 'Intern Agent',
      junior: 'Junior Agent',
      midlevel: 'Mid-level Agent',
      senior: 'Senior Agent',
      architect: 'Architect Agent',
      designer: 'Designer Agent',
      tester: 'Tester Agent',
      researcher: 'Researcher Agent'
    }
    return names[agentId] || agentId
  }

  const getAgentType = (agentId: string): string => {
    return agentId.replace(/Agent$/, '').toLowerCase()
  }

  // Filter tasks based on current filters
  const filteredTasks = useMemo(() => {
    return tasks.filter(task => {
      // Agent type filter
      if (filters.agentType !== 'all' && task.agentType !== filters.agentType) {
        return false
      }

      // Status filter
      if (filters.status !== 'all' && task.status !== filters.status) {
        return false
      }

      // Duration filter
      if (task.executionTime) {
        const durationSeconds = task.executionTime / 1000
        if (durationSeconds < filters.minDuration || durationSeconds > filters.maxDuration) {
          return false
        }
      }

      // Error/success filters
      if (filters.showOnlyErrors && task.status !== 'failed') {
        return false
      }
      if (filters.showOnlySuccess && task.status !== 'completed') {
        return false
      }

      return true
    })
  }, [tasks, filters])

  // Group tasks by agent
  const tasksByAgent = useMemo(() => {
    const groups: Record<string, TimelineTask[]> = {}
    filteredTasks.forEach(task => {
      if (!groups[task.agentId]) {
        groups[task.agentId] = []
      }
      groups[task.agentId].push(task)
    })
    return groups
  }, [filteredTasks])

  // Auto-refresh in live mode
  useEffect(() => {
    if (isLive) {
      const interval = setInterval(() => {
        loadTaskData()
        // Update viewport to show current time
        setViewport(prev => ({
          ...prev,
          startTime: Date.now() - (ZOOM_LEVELS[prev.zoomLevel].seconds * 1000),
          endTime: Date.now()
        }))
      }, 2000)

      return () => clearInterval(interval)
    }
  }, [isLive, loadTaskData])

  // Initial load
  useEffect(() => {
    loadTaskData()
  }, [loadTaskData])

  // ✅ Enhanced cross-window synchronization for real-time updates
  useEffect(() => {
    if (typeof window !== 'undefined' && window.electronAPI?.ipc) {
      try {
        // Listen for timeline data requests from other windows
        const handleTimelineDataRequest = (requestEvent: { requestId: string; timestamp: number }) => {
          console.log('TaskTimelineInspector: Received timeline data request from other window')

          // Send our current task data if we have any
          if (tasks.length > 0) {
            const statusData = taskStatusService.exportStatusData()
            const recentActivity = agentEventsService.getRecentActivity(100)

            window.electronAPI.ipc.send('timeline-data-response', {
              requestId: requestEvent.requestId,
              tasks: tasks,
              statusData: statusData,
              recentActivity: recentActivity,
              timestamp: Date.now()
            })
            console.log('TaskTimelineInspector: Sent timeline data to requesting window')
          }
        }

        // ✅ Listen for real-time timeline data updates from other windows
        const handleTimelineDataUpdate = (updateData: {
          tasks?: TimelineTask[];
          statusData?: any;
          recentActivity?: any[];
          timestamp: number;
        }) => {
          console.log('TaskTimelineInspector: Received real-time timeline update from other window')

          if (updateData.tasks && updateData.tasks.length > 0) {
            // Merge new tasks with existing ones, avoiding duplicates
            setTasks(prevTasks => {
              const existingIds = new Set(prevTasks.map(t => t.id))
              const newTasks = updateData.tasks!.filter(t => !existingIds.has(t.id))
              const mergedTasks = [...prevTasks, ...newTasks]

              // Sort by start time
              mergedTasks.sort((a, b) => a.startedAt - b.startedAt)
              return mergedTasks
            })
          }

          // Update local services with received data
          if (updateData.statusData) {
            Object.entries(updateData.statusData.currentStatuses || {}).forEach(([taskId, status]: [string, any]) => {
              taskStatusService.updateTaskStatus(
                taskId,
                status.agentId,
                status.status,
                {
                  progress: status.progress,
                  message: status.message,
                  metadata: status.metadata
                }
              )
            })
          }

          if (updateData.recentActivity) {
            updateData.recentActivity.forEach((activity: any) => {
              agentEventsService.emitAgentActivity(activity)
            })
          }
        }

        // ✅ Listen for individual task updates
        const handleTaskUpdate = (taskUpdate: {
          taskId: string;
          agentId: string;
          status: string;
          metadata?: any;
          timestamp: number;
        }) => {
          console.log('TaskTimelineInspector: Received task update:', taskUpdate)

          // Update existing task or create new one
          setTasks(prevTasks => {
            const existingTaskIndex = prevTasks.findIndex(t => t.id === taskUpdate.taskId)

            if (existingTaskIndex >= 0) {
              // Update existing task
              const updatedTasks = [...prevTasks]
              updatedTasks[existingTaskIndex] = {
                ...updatedTasks[existingTaskIndex],
                status: taskUpdate.status as any,
                completedAt: taskUpdate.status === 'completed' ? taskUpdate.timestamp : undefined,
                metadata: { ...updatedTasks[existingTaskIndex].metadata, ...taskUpdate.metadata }
              }
              return updatedTasks
            } else {
              // Create new task if it doesn't exist
              const newTask: TimelineTask = {
                id: taskUpdate.taskId,
                agentId: taskUpdate.agentId,
                agentName: getAgentName(taskUpdate.agentId),
                agentType: getAgentType(taskUpdate.agentId),
                taskDescription: taskUpdate.metadata?.message || 'Task execution',
                startedAt: taskUpdate.timestamp,
                completedAt: taskUpdate.status === 'completed' ? taskUpdate.timestamp : undefined,
                status: taskUpdate.status as any,
                metadata: taskUpdate.metadata
              }
              return [...prevTasks, newTask].sort((a, b) => a.startedAt - b.startedAt)
            }
          })

          // Update local task status service
          taskStatusService.updateTaskStatus(
            taskUpdate.taskId,
            taskUpdate.agentId,
            taskUpdate.status,
            {
              message: taskUpdate.metadata?.message,
              metadata: taskUpdate.metadata
            }
          )
        }

        // ✅ Listen for agent activity updates
        const handleAgentActivity = (activityData: any) => {
          console.log('TaskTimelineInspector: Received agent activity:', activityData)

          // Add to local agent events service
          agentEventsService.emitAgentActivity(activityData)

          // Trigger data reload to incorporate new activity
          loadTaskData()
        }

        // Listen for timeline data responses
        const handleTimelineDataResponse = (responseEvent: {
          requestId: string;
          tasks: TimelineTask[];
          statusData: any;
          recentActivity: any[];
          timestamp: number;
        }) => {
          console.log('TaskTimelineInspector: Received timeline data from other window:', responseEvent.tasks.length, 'tasks')

          // Update our local data with the received data
          if (responseEvent.tasks.length > 0) {
            setTasks(responseEvent.tasks)

            // Also update our local services with the received data
            try {
              // Import the status data into our local service
              if (responseEvent.statusData) {
                Object.entries(responseEvent.statusData.currentStatuses).forEach(([taskId, status]: [string, any]) => {
                  taskStatusService.updateTaskStatus(
                    taskId,
                    status.agentId,
                    status.status,
                    {
                      progress: status.progress,
                      message: status.message,
                      metadata: status.metadata
                    }
                  )
                })
              }

              // Import activity data into our local service
              if (responseEvent.recentActivity) {
                responseEvent.recentActivity.forEach((activity: any) => {
                  agentEventsService.emitAgentActivity(activity)
                })
              }
            } catch (error) {
              console.error('TaskTimelineInspector: Failed to import data from other window:', error)
            }
          }
        }

        // Register IPC listeners and get cleanup functions
        const cleanupRequestListener = window.electronAPI.ipc.on('request-timeline-data', handleTimelineDataRequest)
        const cleanupResponseListener = window.electronAPI.ipc.on('timeline-data-response', handleTimelineDataResponse)
        const cleanupUpdateListener = window.electronAPI.ipc.on('timeline-data-update', handleTimelineDataUpdate)
        const cleanupTaskUpdateListener = window.electronAPI.ipc.on('timeline-task-update', handleTaskUpdate)
        const cleanupActivityListener = window.electronAPI.ipc.on('timeline-agent-activity', handleAgentActivity)

        // Return cleanup function
        return () => {
          if (cleanupRequestListener) cleanupRequestListener()
          if (cleanupResponseListener) cleanupResponseListener()
          if (cleanupUpdateListener) cleanupUpdateListener()
          if (cleanupTaskUpdateListener) cleanupTaskUpdateListener()
          if (cleanupActivityListener) cleanupActivityListener()
        }
      } catch (error) {
        console.warn('TaskTimelineInspector: Failed to set up cross-window synchronization:', error)
        return () => {} // Return empty cleanup function on error
      }
    }
  }, [tasks, taskStatusService, agentEventsService])

  return (
    <div className="h-full flex flex-col bg-background">
      {/* Header with controls */}
      <div className="border-b border-border p-4 space-y-4">
        {/* Title section - separate line */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Clock className="h-5 w-5 text-primary" />
            <h2 className="text-lg font-semibold">Task Timeline Inspector</h2>
            <Badge variant="outline" className="text-xs">
              {filteredTasks.length} tasks
            </Badge>
          </div>
        </div>

        {/* Control buttons - separate line */}
        <div className="flex items-center justify-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsLive(!isLive)}
            className={cn(isLive && "bg-green-500/10 border-green-500/20")}
          >
            {isLive ? <Pause className="h-4 w-4 mr-1" /> : <Play className="h-4 w-4 mr-1" />}
            {isLive ? 'Live' : 'Paused'}
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={loadTaskData}
          >
            <RotateCcw className="h-4 w-4 mr-1" />
            Refresh
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              const data = JSON.stringify(filteredTasks, null, 2)
              const blob = new Blob([data], { type: 'application/json' })
              const url = URL.createObjectURL(blob)
              const a = document.createElement('a')
              a.href = url
              a.download = `task-timeline-${Date.now()}.json`
              a.click()
              URL.revokeObjectURL(url)
            }}
          >
            <Download className="h-4 w-4 mr-1" />
            Export
          </Button>
        </div>

        {/* Filters */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
          <div className="space-y-2">
            <Label className="text-xs">Agent Type</Label>
            <Select value={filters.agentType} onValueChange={(value) => setFilters(prev => ({ ...prev, agentType: value }))}>
              <SelectTrigger className="h-8">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Agents</SelectItem>
                <SelectItem value="micromanager">Micromanager</SelectItem>
                <SelectItem value="intern">Intern</SelectItem>
                <SelectItem value="junior">Junior</SelectItem>
                <SelectItem value="midlevel">Mid-level</SelectItem>
                <SelectItem value="senior">Senior</SelectItem>
                <SelectItem value="architect">Architect</SelectItem>
                <SelectItem value="designer">Designer</SelectItem>
                <SelectItem value="tester">Tester</SelectItem>
                <SelectItem value="researcher">Researcher</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label className="text-xs">Status</Label>
            <Select value={filters.status} onValueChange={(value) => setFilters(prev => ({ ...prev, status: value }))}>
              <SelectTrigger className="h-8">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="running">Running</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="failed">Failed</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label className="text-xs">Zoom Level</Label>
            <Select
              value={viewport.zoomLevel.toString()}
              onValueChange={(value) => {
                const zoomLevel = parseInt(value)
                const zoom = ZOOM_LEVELS[zoomLevel]
                setViewport(prev => ({
                  ...prev,
                  zoomLevel,
                  pixelsPerSecond: zoom.pixelsPerSecond,
                  startTime: prev.endTime - (zoom.seconds * 1000)
                }))
              }}
            >
              <SelectTrigger className="h-8">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {ZOOM_LEVELS.map((zoom, index) => (
                  <SelectItem key={index} value={index.toString()}>
                    {zoom.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Quick Filters - separate line */}
        <div className="space-y-2">
          <Label className="text-xs">Quick Filters</Label>
          <div className="flex items-center justify-center space-x-6">
            <div className="flex items-center space-x-2">
              <Switch
                id="timeline-errors-only"
                checked={filters.showOnlyErrors}
                onCheckedChange={(checked) => setFilters(prev => ({ ...prev, showOnlyErrors: checked, showOnlySuccess: checked ? false : prev.showOnlySuccess }))}
              />
              <Label htmlFor="timeline-errors-only" className="text-sm">Errors Only</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="timeline-success-only"
                checked={filters.showOnlySuccess}
                onCheckedChange={(checked) => setFilters(prev => ({ ...prev, showOnlySuccess: checked, showOnlyErrors: checked ? false : prev.showOnlyErrors }))}
              />
              <Label htmlFor="timeline-success-only" className="text-sm">Success Only</Label>
            </div>
          </div>
        </div>
      </div>

      {/* Timeline content */}
      <div className="flex-1 flex overflow-hidden">
        {/* Agent list sidebar */}
        <div className="w-48 border-r border-border bg-muted/20">
          <div className="p-2 border-b border-border">
            <h3 className="text-sm font-medium">Agents</h3>
          </div>
          <ScrollArea className="h-full">
            <div className="p-2 space-y-1">
              {Object.keys(tasksByAgent).map(agentId => (
                <div key={agentId} className="flex items-center space-x-2 p-2 rounded-md hover:bg-muted/50">
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: AGENT_COLORS[agentId as keyof typeof AGENT_COLORS] || '#6B7280' }}
                  />
                  <span className="text-sm break-words">{getAgentName(agentId)}</span>
                  <Badge variant="secondary" className="text-xs ml-auto">
                    {tasksByAgent[agentId].length}
                  </Badge>
                </div>
              ))}
            </div>
          </ScrollArea>
        </div>

        {/* Timeline grid */}
        <div className="flex-1 overflow-auto">
          <TimelineGrid
            tasks={filteredTasks}
            tasksByAgent={tasksByAgent}
            viewport={viewport}
            onTaskSelect={setSelectedTask}
            selectedTask={selectedTask}
          />
        </div>
      </div>

      {/* Task details panel */}
      {selectedTask && (
        <TaskDetailsPanel
          task={selectedTask}
          onClose={() => setSelectedTask(null)}
        />
      )}
    </div>
  )
}
