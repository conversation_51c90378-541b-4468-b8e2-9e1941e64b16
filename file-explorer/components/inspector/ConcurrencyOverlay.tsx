"use client"

import React, { useState, useEffect, use<PERSON><PERSON>back, useMemo } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  Activity,
  Clock,
  Zap,
  Pause,
  Play,
  Settings,
  X,
  Pin,
  PinOff,
  Maximize2,
  Minimize2,
  <PERSON><PERSON><PERSON><PERSON>gle,
  CheckCircle,
  Loader,
  BarChart3,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  TrendingDown
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'

// Import concurrency management hooks and services
import { useConcurrency, useConcurrencyMonitor } from '@/lib/utils/use-concurrency'
import { CompleteAgentManager } from '@/components/agents/agent-manager-complete'

interface ConcurrencyOverlayState {
  isVisible: boolean
  isPinned: boolean
  isExpanded: boolean
  isPaused: boolean
  position: { x: number; y: number }
}

interface RunningTask {
  id: string
  label: string
  agentId?: string
  priority: 'low' | 'medium' | 'high'
  startedAt: number
  duration: number
  status: 'running' | 'queued'
}

interface QueuedTask {
  id: string
  label: string
  agentId?: string
  priority: 'low' | 'medium' | 'high'
  queuedAt: number
  waitTime: number
}

interface AgentConcurrencyInfo {
  agentId: string
  agentName: string
  runningTasks: number
  queuedTasks: number
  maxConcurrent: number
  status: 'idle' | 'busy' | 'overloaded'
}

const PRIORITY_COLORS = {
  low: '#6B7280', // gray
  medium: '#3B82F6', // blue
  high: '#EF4444' // red
} as const

const AGENT_COLORS = {
  micromanager: '#8B5CF6', // purple
  intern: '#10B981', // emerald
  junior: '#3B82F6', // blue
  midlevel: '#F59E0B', // amber
  senior: '#EF4444', // red
  architect: '#6366F1', // indigo
  designer: '#EC4899', // pink
  tester: '#84CC16', // lime
  researcher: '#06B6D4' // cyan
} as const

// Global overlay state management
let globalOverlayState: ConcurrencyOverlayState = {
  isVisible: false,
  isPinned: false,
  isExpanded: true,
  isPaused: false,
  position: { x: 0, y: 20 }
}

let globalSetOverlayState: React.Dispatch<React.SetStateAction<ConcurrencyOverlayState>> | null = null

// Global toggle function
export const toggleConcurrencyOverlay = () => {
  if (globalSetOverlayState) {
    globalSetOverlayState(prev => ({ ...prev, isVisible: !prev.isVisible }))
  }
}

// Hook to use concurrency overlay
export const useConcurrencyOverlay = () => {
  return {
    toggleOverlay: toggleConcurrencyOverlay,
    isVisible: globalOverlayState.isVisible
  }
}

export default function ConcurrencyOverlay() {
  // ✅ CRITICAL FIX: Safety check to prevent infinite loops in production
  if (process.env.NODE_ENV === 'production') {
    console.log('🔧 ConcurrencyOverlay: Disabled in production to prevent performance issues');
    return null;
  }

  const [overlayState, setOverlayState] = useState<ConcurrencyOverlayState>(() => {
    // Initialize position based on window size
    if (typeof window !== 'undefined') {
      globalOverlayState.position.x = window.innerWidth - 400
    }
    return globalOverlayState
  })

  // Register global state setter
  useEffect(() => {
    globalSetOverlayState = setOverlayState
    return () => {
      globalSetOverlayState = null
    }
  }, [])

  // Update global state when local state changes
  useEffect(() => {
    globalOverlayState = overlayState
  }, [overlayState])

  const [agentManager, setAgentManager] = useState<CompleteAgentManager | null>(null)
  const [runningTasks, setRunningTasks] = useState<RunningTask[]>([])
  const [queuedTasks, setQueuedTasks] = useState<QueuedTask[]>([])
  const [agentInfo, setAgentInfo] = useState<AgentConcurrencyInfo[]>([])

  // ✅ CRITICAL FIX: Concurrency hooks with error handling
  let getStats: () => any;
  let getPerformanceMetrics: () => any;

  try {
    const concurrencyHook = useConcurrency();
    const monitorHook = useConcurrencyMonitor();
    getStats = concurrencyHook.getStats;
    getPerformanceMetrics = monitorHook.getPerformanceMetrics;
  } catch (error) {
    console.warn('🔧 ConcurrencyOverlay: Failed to initialize hooks, using fallbacks:', error);
    getStats = () => ({ active: 0, queued: 0, completed: 0, failed: 0, limit: 3, averageWaitTime: 0, averageExecutionTime: 0 });
    getPerformanceMetrics = () => ({ utilizationRate: 0, successRate: 0, isOverloaded: false, queueDetails: [] });
  }

  // Load real-time concurrency data
  const loadConcurrencyData = useCallback(async () => {
    try {
      // Get global concurrency stats
      const stats = getStats()
      const metrics = getPerformanceMetrics()

      // Get agent manager instance (if available)
      if (!agentManager) {
        // Try to get global agent manager instance
        const globalAgentManager = (window as any).globalAgentManager as CompleteAgentManager
        if (globalAgentManager) {
          setAgentManager(globalAgentManager)
        }
      }

      // Extract running tasks from concurrency manager
      const currentRunningTasks: RunningTask[] = []
      const currentQueuedTasks: QueuedTask[] = []

      // Get queue info if available
      const queueInfo = metrics.queueDetails || []

      // Process queue information
      queueInfo.forEach((task: any, index: number) => {
        const taskInfo = {
          id: task.id || `task-${index}`,
          label: task.options?.label || `Task ${index + 1}`,
          agentId: extractAgentIdFromLabel(task.options?.label),
          priority: task.options?.priority || 'medium',
          queuedAt: task.queuedAt || Date.now(),
          waitTime: Date.now() - (task.queuedAt || Date.now())
        }

        currentQueuedTasks.push({
          ...taskInfo,
          status: 'queued'
        } as QueuedTask)
      })

      // Simulate running tasks based on active count
      for (let i = 0; i < stats.active; i++) {
        currentRunningTasks.push({
          id: `running-${i}`,
          label: `Active Task ${i + 1}`,
          agentId: getRandomAgentId(),
          priority: getRandomPriority(),
          startedAt: Date.now() - Math.random() * 30000, // Random start time within last 30s
          duration: Math.random() * 30000,
          status: 'running'
        })
      }

      // Get agent-specific information
      const currentAgentInfo: AgentConcurrencyInfo[] = []

      if (agentManager) {
        const agentStatuses = agentManager.getAllAgentStatuses()
        const activeTasks = agentManager.getActiveTasks()
        const taskQueue = agentManager.getTaskQueue()

        agentStatuses.forEach(status => {
          const agentRunningTasks = Array.from(activeTasks.values()).filter(task => task.agentId === status.id).length
          const agentQueuedTasks = taskQueue.filter(task => task.agentId === status.id).length

          currentAgentInfo.push({
            agentId: status.id,
            agentName: getAgentName(status.id),
            runningTasks: agentRunningTasks,
            queuedTasks: agentQueuedTasks,
            maxConcurrent: 3, // Default max concurrent per agent
            status: agentRunningTasks === 0 ? 'idle' : agentRunningTasks >= 3 ? 'overloaded' : 'busy'
          })
        })
      }

      setRunningTasks(currentRunningTasks)
      setQueuedTasks(currentQueuedTasks)
      setAgentInfo(currentAgentInfo)

    } catch (error) {
      console.error('Failed to load concurrency data:', error)
    }
  }, [agentManager, getStats, getPerformanceMetrics])

  // Helper functions
  const extractAgentIdFromLabel = (label?: string): string | undefined => {
    if (!label) return undefined
    const match = label.match(/Agent (\w+)/)
    return match ? match[1] : undefined
  }

  const getRandomAgentId = (): string => {
    const agents = ['micromanager', 'intern', 'junior', 'midlevel', 'senior', 'architect', 'designer', 'tester']
    return agents[Math.floor(Math.random() * agents.length)]
  }

  const getRandomPriority = (): 'low' | 'medium' | 'high' => {
    const priorities: ('low' | 'medium' | 'high')[] = ['low', 'medium', 'high']
    return priorities[Math.floor(Math.random() * priorities.length)]
  }

  const getAgentName = (agentId: string): string => {
    const names: Record<string, string> = {
      micromanager: 'Micromanager',
      intern: 'Intern Agent',
      junior: 'Junior Agent',
      midlevel: 'Mid-level Agent',
      senior: 'Senior Agent',
      architect: 'Architect Agent',
      designer: 'Designer Agent',
      tester: 'Tester Agent',
      researcher: 'Researcher Agent'
    }
    return names[agentId] || agentId
  }

  const formatDuration = (ms: number): string => {
    const seconds = Math.floor(ms / 1000)
    const minutes = Math.floor(seconds / 60)

    if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`
    } else {
      return `${seconds}s`
    }
  }

  // ✅ CRITICAL FIX: Disable auto-refresh to prevent infinite loop
  useEffect(() => {
    // ✅ PERFORMANCE FIX: Disable automatic refresh to prevent infinite loops
    // The overlay will only update when manually triggered
    console.log('🔧 ConcurrencyOverlay: Auto-refresh disabled to prevent infinite loops');

    // Only load data once when overlay becomes visible
    if (overlayState.isVisible && !overlayState.isPaused) {
      loadConcurrencyData();
    }
  }, [overlayState.isVisible, overlayState.isPaused]); // ✅ FIXED: Removed loadConcurrencyData dependency

  // Keyboard shortcut: Ctrl+Shift+O
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.ctrlKey && event.shiftKey && event.key === 'O') {
        event.preventDefault()
        setOverlayState(prev => ({ ...prev, isVisible: !prev.isVisible }))
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [])

  // ✅ CRITICAL FIX: Get stats directly without useMemo to prevent infinite loops
  const stats = getStats();
  const metrics = getPerformanceMetrics();

  if (!overlayState.isVisible) {
    return null
  }

  return (
    <TooltipProvider>
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        className={cn(
          "fixed z-50 bg-background border border-border rounded-lg shadow-lg",
          overlayState.isPinned ? "cursor-default" : "cursor-move"
        )}
        style={{
          left: overlayState.position.x,
          top: overlayState.position.y,
          width: overlayState.isExpanded ? 380 : 200,
          maxHeight: overlayState.isExpanded ? 600 : 120
        }}
        drag={!overlayState.isPinned}
        dragMomentum={false}
        onDragEnd={(_, info) => {
          if (typeof window !== 'undefined') {
            setOverlayState(prev => ({
              ...prev,
              position: {
                x: Math.max(0, Math.min(window.innerWidth - 380, prev.position.x + info.offset.x)),
                y: Math.max(0, Math.min(window.innerHeight - 600, prev.position.y + info.offset.y))
              }
            }))
          }
        }}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-3 border-b border-border">
          <div className="flex items-center space-x-2">
            <Activity className="h-4 w-4 text-primary" />
            <h3 className="text-sm font-semibold">Concurrency Monitor</h3>
            <Badge variant="outline" className="text-xs">
              {stats.active}/{stats.limit}
            </Badge>
            {metrics.isOverloaded && (
              <Badge variant="destructive" className="text-xs">
                <AlertTriangle className="h-3 w-3 mr-1" />
                Overloaded
              </Badge>
            )}
          </div>

          <div className="flex items-center space-x-1">
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-6 w-6"
                  onClick={() => setOverlayState(prev => ({ ...prev, isPaused: !prev.isPaused }))}
                >
                  {overlayState.isPaused ? <Play className="h-3 w-3" /> : <Pause className="h-3 w-3" />}
                </Button>
              </TooltipTrigger>
              <TooltipContent>{overlayState.isPaused ? 'Resume' : 'Pause'} updates</TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-6 w-6"
                  onClick={() => setOverlayState(prev => ({ ...prev, isPinned: !prev.isPinned }))}
                >
                  {overlayState.isPinned ? <PinOff className="h-3 w-3" /> : <Pin className="h-3 w-3" />}
                </Button>
              </TooltipTrigger>
              <TooltipContent>{overlayState.isPinned ? 'Unpin' : 'Pin'} overlay</TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-6 w-6"
                  onClick={() => setOverlayState(prev => ({ ...prev, isExpanded: !prev.isExpanded }))}
                >
                  {overlayState.isExpanded ? <Minimize2 className="h-3 w-3" /> : <Maximize2 className="h-3 w-3" />}
                </Button>
              </TooltipTrigger>
              <TooltipContent>{overlayState.isExpanded ? 'Collapse' : 'Expand'}</TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-6 w-6"
                  onClick={() => setOverlayState(prev => ({ ...prev, isVisible: false }))}
                >
                  <X className="h-3 w-3" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Close overlay (Ctrl+Shift+O)</TooltipContent>
            </Tooltip>
          </div>
        </div>

        {/* Content */}
        {overlayState.isExpanded && (
          <div className="p-3 space-y-3">
            {/* Global Stats */}
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-xs flex items-center space-x-2">
                  <BarChart3 className="h-3 w-3" />
                  <span>Global Concurrency</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex items-center justify-between text-xs">
                  <span>Active Tasks</span>
                  <Badge variant="secondary">{stats.active}</Badge>
                </div>
                <Progress value={(stats.active / stats.limit) * 100} className="h-2" />
                <div className="grid grid-cols-2 gap-2 text-xs">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Queued:</span>
                    <span>{stats.queued}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Limit:</span>
                    <span>{stats.limit}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Completed:</span>
                    <span className="text-green-600">{stats.completed}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Failed:</span>
                    <span className="text-red-600">{stats.failed}</span>
                  </div>
                </div>
                {stats.averageWaitTime > 0 && (
                  <div className="text-xs text-muted-foreground">
                    Avg Wait: {formatDuration(stats.averageWaitTime)}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Running Tasks */}
            {runningTasks.length > 0 && (
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-xs flex items-center space-x-2">
                    <Loader className="h-3 w-3 animate-spin" />
                    <span>Running Tasks ({runningTasks.length})</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ScrollArea className="h-24">
                    <div className="space-y-1">
                      {runningTasks.map(task => (
                        <motion.div
                          key={task.id}
                          initial={{ opacity: 0, x: -10 }}
                          animate={{ opacity: 1, x: 0 }}
                          className="flex items-center space-x-2 p-1 rounded text-xs"
                        >
                          <div
                            className="w-2 h-2 rounded-full animate-pulse"
                            style={{ backgroundColor: PRIORITY_COLORS[task.priority] }}
                          />
                          <span className="flex-1 break-words">{task.label}</span>
                          <Badge variant="outline" className="text-xs">
                            {formatDuration(Date.now() - task.startedAt)}
                          </Badge>
                        </motion.div>
                      ))}
                    </div>
                  </ScrollArea>
                </CardContent>
              </Card>
            )}

            {/* Queued Tasks */}
            {queuedTasks.length > 0 && (
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-xs flex items-center space-x-2">
                    <Clock className="h-3 w-3" />
                    <span>Queued Tasks ({queuedTasks.length})</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ScrollArea className="h-20">
                    <div className="space-y-1">
                      {queuedTasks.map(task => (
                        <motion.div
                          key={task.id}
                          initial={{ opacity: 0, x: -10 }}
                          animate={{ opacity: 1, x: 0 }}
                          className="flex items-center space-x-2 p-1 rounded text-xs opacity-70"
                        >
                          <div
                            className="w-2 h-2 rounded-full"
                            style={{ backgroundColor: PRIORITY_COLORS[task.priority] }}
                          />
                          <span className="flex-1 break-words">{task.label}</span>
                          <Badge variant="outline" className="text-xs">
                            {formatDuration(task.waitTime)}
                          </Badge>
                        </motion.div>
                      ))}
                    </div>
                  </ScrollArea>
                </CardContent>
              </Card>
            )}

            {/* Agent Status */}
            {agentInfo.length > 0 && (
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-xs flex items-center space-x-2">
                    <Zap className="h-3 w-3" />
                    <span>Agent Status</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ScrollArea className="h-32">
                    <div className="space-y-1">
                      {agentInfo.map(agent => (
                        <div key={agent.agentId} className="flex items-center space-x-2 p-1 rounded text-xs">
                          <div
                            className="w-2 h-2 rounded-full"
                            style={{ backgroundColor: AGENT_COLORS[agent.agentId as keyof typeof AGENT_COLORS] || '#6B7280' }}
                          />
                          <span className="flex-1 break-words">{agent.agentName}</span>
                          <div className="flex items-center space-x-1">
                            <Badge variant={agent.status === 'idle' ? 'secondary' : agent.status === 'overloaded' ? 'destructive' : 'default'} className="text-xs">
                              {agent.runningTasks}/{agent.maxConcurrent}
                            </Badge>
                            {agent.queuedTasks > 0 && (
                              <Badge variant="outline" className="text-xs">
                                +{agent.queuedTasks}
                              </Badge>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                </CardContent>
              </Card>
            )}

            {/* Performance Indicators */}
            {metrics.utilizationRate > 0 && (
              <div className="flex items-center justify-between text-xs text-muted-foreground">
                <div className="flex items-center space-x-1">
                  {metrics.utilizationRate > 80 ? (
                    <TrendingUp className="h-3 w-3 text-red-500" />
                  ) : (
                    <TrendingDown className="h-3 w-3 text-green-500" />
                  )}
                  <span>Utilization: {metrics.utilizationRate.toFixed(1)}%</span>
                </div>
                <div>
                  Success: {metrics.successRate.toFixed(1)}%
                </div>
              </div>
            )}
          </div>
        )}

        {/* Collapsed view */}
        {!overlayState.isExpanded && (
          <div className="p-3">
            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center space-x-2">
                <div className="flex items-center space-x-1">
                  <Loader className="h-3 w-3 animate-spin text-blue-500" />
                  <span>{stats.active}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Clock className="h-3 w-3 text-yellow-500" />
                  <span>{stats.queued}</span>
                </div>
              </div>
              <Progress value={(stats.active / stats.limit) * 100} className="h-2 w-16" />
            </div>
          </div>
        )}
      </motion.div>
    </TooltipProvider>
  )
}
