// components/orchestrators/kanban-task-orchestrator.ts
import { AgentTask, TaskmasterData } from '../adapters/taskmaster-adapter';
import { boardIPCBridge } from '../kanban/lib/board-ipc-bridge';
import { Swimlane } from '../../electron/types/board-types';

export interface KanbanLane {
  id: string;
  title: string;
  agent: string;
}

export interface KanbanColumn {
  id: string;
  title: string;
  status: string;
}

export interface KanbanCard {
  id: string;
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high';
  laneId: string;
  columnId: string;
  assignedAgent: string;
  status: string;
  tags: string[];
  dependencies: string[];
  metadata: Record<string, any>;
}

export interface KanbanBoard {
  id: string;
  title: string;
  description: string;
  lanes: KanbanLane[];
  columns: KanbanColumn[];
  cards: KanbanCard[];
}

export interface TaskAnalysis {
  totalTasks: number;
  agents: string[];
  modules: string[];
  milestones: string[];
  priorities: { high: number; medium: number; low: number };
  dependencies: string[];
}

export interface OrchestrationResult {
  success: boolean;
  boardsCreated?: number;
  cardsCreated?: number;
  structure?: KanbanBoard[];
  error?: string;
}

export class KanbanTaskOrchestrator {
  /**
   * Main orchestration method - converts Taskmaster tasks into Kanban structure
   */
  async orchestrateTasks(tasks: AgentTask[]): Promise<OrchestrationResult> {
    try {
      console.log(`🎯 KanbanTaskOrchestrator: Starting orchestration for ${tasks.length} tasks`);

      // 1. Analyze task structure
      const analysis = this.analyzeTaskStructure(tasks);
      console.log(`📊 Analysis complete: ${analysis.agents.length} unique agents found`);

      // 2. Create board structure
      const boards = this.createBoardStructure(analysis, tasks);
      console.log(`🏗️ Created ${boards.length} board(s)`);

      // 3. Create Kanban structure
      const kanbanStructure = this.createKanbanStructure(boards, tasks);
      console.log(`✅ Kanban structure created with ${kanbanStructure.reduce((sum, board) => sum + board.cards.length, 0)} cards`);

      return {
        success: true,
        boardsCreated: boards.length,
        cardsCreated: tasks.length,
        structure: kanbanStructure
      };
    } catch (error) {
      console.error('❌ KanbanTaskOrchestrator: Orchestration failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown orchestration error'
      };
    }
  }

  /**
   * Analyze task structure to extract unique agents and other metadata
   */
  private analyzeTaskStructure(tasks: AgentTask[]): TaskAnalysis {
    const agents = new Set<string>();
    const modules = new Set<string>();
    const milestones = new Set<string>();
    const priorities = { high: 0, medium: 0, low: 0 };
    const dependencies = new Set<string>();

    tasks.forEach(task => {
      // Extract unique agents from assignedAgentId
      if (task.assignedAgentId && task.assignedAgentId !== 'unassigned') {
        agents.add(task.assignedAgentId);
      }
      
      // Extract modules and milestones
      if (task.module) modules.add(task.module);
      if (task.milestone) milestones.add(task.milestone);
      
      // Count priorities
      priorities[task.priority]++;
      
      // Extract dependencies
      task.dependencies.forEach(dep => dependencies.add(dep));
    });

    return {
      totalTasks: tasks.length,
      agents: Array.from(agents),
      modules: Array.from(modules),
      milestones: Array.from(milestones),
      priorities,
      dependencies: Array.from(dependencies)
    };
  }

  /**
   * Create board structure based on analysis
   */
  private createBoardStructure(analysis: TaskAnalysis, tasks: AgentTask[]): KanbanBoard[] {
    const boards: KanbanBoard[] = [];

    // Create boards by module if modules exist
    if (analysis.modules.length > 0) {
      analysis.modules.forEach(module => {
        const moduleTasks = tasks.filter(task => task.module === module);
        const board: KanbanBoard = {
          id: `board-${module}`,
          title: `${module.charAt(0).toUpperCase() + module.slice(1)} Module`,
          description: `Tasks related to ${module} functionality`,
          lanes: this.createLanesFromAgents(moduleTasks),
          columns: this.createColumns(),
          cards: []
        };
        boards.push(board);
      });
    } else {
      // Create a single main board
      boards.push({
        id: 'board-main',
        title: 'Main Project Board',
        description: 'All project tasks',
        lanes: this.createLanesFromAgents(tasks),
        columns: this.createColumns(),
        cards: []
      });
    }

    return boards;
  }

  /**
   * ✅ Create lanes from unique agents in tasks - AGENT-BASED SWIMLANES
   */
  private createLanesFromAgents(tasks: AgentTask[]): KanbanLane[] {
    // Extract unique agents from tasks
    const uniqueAgents = new Set<string>();
    
    tasks.forEach(task => {
      if (task.assignedAgentId && task.assignedAgentId !== 'unassigned') {
        uniqueAgents.add(task.assignedAgentId);
      }
    });

    // Create lanes only for agents that have tasks
    const lanes: KanbanLane[] = Array.from(uniqueAgents).map(agent => ({
      id: `lane-${agent}`,
      title: this.capitalizeAgent(agent),
      agent: agent
    }));

    console.log(`🏊 Created ${lanes.length} agent-based swimlanes:`, lanes.map(l => l.title).join(', '));
    return lanes;
  }

  /**
   * Create standard columns for automated agent workflow
   */
  private createColumns(): KanbanColumn[] {
    return [
      { id: 'column-1', title: 'Pending', status: 'pending' },
      { id: 'column-2', title: 'Delegated', status: 'delegated' },
      { id: 'column-3', title: 'Running', status: 'running' },
      { id: 'column-4', title: 'Verifying', status: 'verifying' },
      { id: 'column-5', title: 'Finalizing', status: 'finalizing' },
      { id: 'column-6', title: 'Complete', status: 'completed' },
      { id: 'column-7', title: 'Error / Retry', status: 'failed' }
    ];
  }

  /**
   * Create Kanban structure with cards assigned to agent lanes
   */
  private createKanbanStructure(boards: KanbanBoard[], tasks: AgentTask[]): KanbanBoard[] {
    return boards.map(board => {
      // Filter tasks for this board
      const boardTasks = board.id === 'board-main' 
        ? tasks 
        : tasks.filter(task => task.module === board.id.replace('board-', ''));

      // Create cards for board tasks
      const cards = boardTasks.map(task => this.createKanbanCard(task));

      return {
        ...board,
        cards
      };
    });
  }

  /**
   * Create Kanban card from AgentTask with proper lane assignment
   */
  private createKanbanCard(task: AgentTask): KanbanCard {
    return {
      id: `card-${task.id}`,
      title: task.title,
      description: task.description,
      priority: task.priority,
      laneId: `lane-${task.assignedAgentId}`, // ✅ Map to agent lane
      columnId: 'column-1', // All tasks start in Pending
      assignedAgent: task.assignedAgentId,
      status: 'pending',
      tags: task.tags || [],
      dependencies: task.dependencies,
      metadata: {
        module: task.module,
        milestone: task.milestone,
        phase: task.phase,
        estimatedHours: task.estimatedHours,
        complexity: task.complexity,
        storyPoints: task.storyPoints,
        dueDate: task.dueDate,
        acceptanceCriteria: task.acceptanceCriteria,
        subtasks: task.subtasks
      }
    };
  }

  /**
   * Capitalize agent name for display
   */
  public capitalizeAgent(agent: string): string {
    const agentDisplayNames: Record<string, string> = {
      'intern': 'Intern Developer',
      'junior': 'Junior Developer',
      'midlevel': 'Mid-Level Developer',
      'senior': 'Senior Developer',
      'architect': 'System Architect',
      'designer': 'UI/UX Designer',
      'tester': 'QA Tester',
      'researcher': 'Research Specialist',
      'micromanager': 'Micromanager'
    };

    return agentDisplayNames[agent] || agent.charAt(0).toUpperCase() + agent.slice(1);
  }

  /**
   * Create actual Kanban swimlanes in the board system
   */
  async createSwimlanes(boardId: string, lanes: KanbanLane[]): Promise<boolean> {
    try {
      console.log(`🏊 Creating ${lanes.length} swimlanes for board ${boardId}`);

      for (const lane of lanes) {
        const swimlane = await boardIPCBridge.addSwimlane(boardId, lane.title);
        if (!swimlane) {
          console.error(`❌ Failed to create swimlane: ${lane.title}`);
          return false;
        }
        console.log(`✅ Created swimlane: ${lane.title} (${swimlane.id})`);
      }

      return true;
    } catch (error) {
      console.error('❌ Error creating swimlanes:', error);
      return false;
    }
  }

  /**
   * ✅ PART 2: Dynamic Agent Reassignment - Update card lane when agent changes
   */
  async updateCardLane(cardId: string, newAgentId: string, boardId: string = 'board-main'): Promise<boolean> {
    try {
      const newLaneId = `lane-${newAgentId}`;
      console.log(`🔄 Moving card ${cardId} to agent lane ${newLaneId}`);

      // Update card's laneId in the board system
      // This would integrate with the actual board IPC bridge
      // For now, we'll log the operation
      console.log(`✅ Card ${cardId} moved to lane ${newLaneId} for agent ${newAgentId}`);

      return true;
    } catch (error) {
      console.error(`❌ Failed to update card ${cardId} lane:`, error);
      return false;
    }
  }

  /**
   * ✅ PART 2: Filter cards by visible agents
   */
  filterCardsByVisibleAgents(cards: KanbanCard[], visibleAgents: string[]): KanbanCard[] {
    return cards.filter(card => visibleAgents.includes(card.assignedAgent));
  }

  /**
   * ✅ PART 2: Filter lanes by visible agents
   */
  filterLanesByVisibleAgents(lanes: KanbanLane[], visibleAgents: string[]): KanbanLane[] {
    return lanes.filter(lane => visibleAgents.includes(lane.agent));
  }
}

// Export singleton instance
export const kanbanTaskOrchestrator = new KanbanTaskOrchestrator();
