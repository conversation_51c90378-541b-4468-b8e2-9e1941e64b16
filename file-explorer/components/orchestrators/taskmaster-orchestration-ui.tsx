// components/orchestrators/taskmaster-orchestration-ui.tsx
import React, { useState, useCallback, useMemo } from 'react';
import { Button } from '../ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Alert, AlertDescription } from '../ui/alert';
import { Progress } from '../ui/progress';
import { Checkbox } from '../ui/checkbox';
import {
  Play,
  RefreshCw,
  CheckCircle,
  AlertTriangle,
  XCircle,
  Loader2,
  FileText,
  Users,
  Layers,
  Target,
  Clock,
  Zap,
  Settings,
  Eye,
  EyeOff,
  Filter,
  Square,
  Pause
} from 'lucide-react';
import { kanbanTaskOrchestrator, OrchestrationResult } from './kanban-task-orchestrator';
import { taskmasterAdapter, TaskmasterData } from '../adapters/taskmaster-adapter';
import { micromanagerAgent } from '../agents/micromanager-agent';
import { AgentContext } from '../agents/agent-base';
import { activeProjectService } from '../../services/active-project-service';
import { taskStateService, TaskState, sequentialExecutionController } from '../../services/task-state-service';
import { liveCodingService } from '../../services/live-coding-service';
import { completionVerificationService } from '../../services/completion-verification-service';
import { automaticExecutionService, AutoExecutionStatus } from '../../services/automatic-execution-service';
import UserConfirmationDialog, { TaskCompletionReport, UserDecision } from '../workflow/user-confirmation-dialog';
import {
  logTaskmasterEvent,
  logTaskmasterDebug,
  logTaskmasterError
} from '../../services/logger';

// Enhanced orchestration step tracking
interface OrchestrationStep {
  id: string;
  title: string;
  description: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  startTime?: number;
  endTime?: number;
  error?: string;
  subSteps?: OrchestrationSubStep[];
}

interface OrchestrationSubStep {
  id: string;
  title: string;
  description: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  startTime?: number;
  endTime?: number;
  error?: string;
}

interface OrchestrationState {
  isActive: boolean;
  steps: OrchestrationStep[];
  startTime?: number;
  endTime?: number;
}

// ARCHITECTURE RESTORATION: Taskmaster now only submits tasks to Micromanager
interface TaskmasterOrchestrationUIProps {
  onTasksSubmittedToMicromanager: (result: { success: boolean; tasksSubmitted: number; error?: string }) => void;
  onCancel?: () => void;
  className?: string;
  forceShowSequentialWorkflow?: boolean; // ✅ NEW: Force show Sequential Workflow Control panel
}

export const TaskmasterOrchestrationUI: React.FC<TaskmasterOrchestrationUIProps> = ({
  onTasksSubmittedToMicromanager,
  onCancel,
  className = '',
  forceShowSequentialWorkflow = false
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [hasTasksFile, setHasTasksFile] = useState<boolean | null>(null);
  const [tasksData, setTasksData] = useState<any | null>(null); // ARCHITECTURE RESTORATION: Remove TaskmasterData dependency
  const [submissionResult, setSubmissionResult] = useState<{ success: boolean; tasksSubmitted: number; error?: string } | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [orchestrationState, setOrchestrationState] = useState<OrchestrationState>({
    isActive: false,
    steps: []
  });

  // ✅ PART 1: Agent Lane Toggling State
  const [visibleAgents, setVisibleAgents] = useState<string[]>([]);
  const [availableAgents, setAvailableAgents] = useState<string[]>([]);
  const [orchestrationResult, setOrchestrationResult] = useState<OrchestrationResult | null>(null);
  const [sequentialWorkflowReady, setSequentialWorkflowReady] = useState(false);
  // ✅ PHASE 3: User confirmation dialog state
  const [confirmationDialog, setConfirmationDialog] = useState<{
    isOpen: boolean;
    report: TaskCompletionReport | null;
    isLoading: boolean;
  }>({
    isOpen: false,
    report: null,
    isLoading: false
  });
  // ✅ PHASE 4: Automatic execution state
  const [autoExecutionStatus, setAutoExecutionStatus] = useState<AutoExecutionStatus>(
    automaticExecutionService.getStatus()
  );
  const [autoExecutionEnabled, setAutoExecutionEnabled] = useState(false);

  // ARCHITECTURE RESTORATION: New steps for Micromanager delegation
  const createOrchestrationSteps = useCallback((): OrchestrationStep[] => [
    {
      id: 'validate',
      title: 'Validate Tasks Data',
      description: 'Verifying tasks file and data structure',
      status: 'pending',
      subSteps: [
        {
          id: 'check-file',
          title: 'Check Tasks File',
          description: 'Verifying tasks.json file exists and is readable',
          status: 'pending'
        },
        {
          id: 'parse-data',
          title: 'Parse Task Data',
          description: 'Loading and validating task structure from Taskmaster',
          status: 'pending'
        },
        {
          id: 'validate-structure',
          title: 'Validate Task Structure',
          description: 'Ensuring tasks conform to Synapse architecture requirements',
          status: 'pending'
        }
      ]
    },
    {
      id: 'prepare',
      title: 'Prepare for Micromanager',
      description: 'Converting Taskmaster tasks for Micromanager ingestion',
      status: 'pending',
      subSteps: [
        {
          id: 'normalize-tasks',
          title: 'Normalize Task Format',
          description: 'Converting Taskmaster format to Synapse agent context format',
          status: 'pending'
        },
        {
          id: 'create-contexts',
          title: 'Create Agent Contexts',
          description: 'Preparing individual task contexts for Micromanager delegation',
          status: 'pending'
        },
        {
          id: 'validate-contexts',
          title: 'Validate Contexts',
          description: 'Ensuring all contexts meet Synapse architecture requirements',
          status: 'pending'
        }
      ]
    },
    {
      id: 'orchestrate',
      title: 'Create Kanban Structure',
      description: 'Creating agent-based Kanban boards with swimlanes',
      status: 'pending',
      subSteps: [
        {
          id: 'analyze-agents',
          title: 'Analyze Agent Assignments',
          description: 'Extracting unique agents from task assignments',
          status: 'pending'
        },
        {
          id: 'create-swimlanes',
          title: 'Create Agent Swimlanes',
          description: 'Creating swimlanes grouped by assigned agent roles',
          status: 'pending'
        },
        {
          id: 'create-boards',
          title: 'Create Kanban Boards',
          description: 'Setting up Kanban boards with agent-based structure',
          status: 'pending'
        },
        {
          id: 'assign-cards',
          title: 'Assign Task Cards',
          description: 'Placing tasks in appropriate agent swimlanes',
          status: 'pending'
        }
      ]
    },
    {
      id: 'delegate',
      title: 'Delegate to Micromanager',
      description: 'Submitting tasks to Micromanager for proper orchestration',
      status: 'pending',
      subSteps: [
        {
          id: 'initialize-micromanager',
          title: 'Initialize Micromanager',
          description: 'Ensuring Micromanager agent is ready for task delegation',
          status: 'pending'
        },
        {
          id: 'submit-tasks',
          title: 'Submit Tasks',
          description: 'Sending task contexts to Micromanager for analysis and delegation',
          status: 'pending'
        },
        {
          id: 'verify-delegation',
          title: 'Verify Delegation',
          description: 'Confirming Micromanager has accepted and processed all tasks',
          status: 'pending'
        }
      ]
    },
    {
      id: 'sequential',
      title: 'Initialize Sequential Workflow',
      description: 'Setting up controlled sequential execution with Micromanager',
      status: 'pending',
      subSteps: [
        {
          id: 'initialize-workflow',
          title: 'Initialize Workflow',
          description: 'Setting up sequential execution controller and task queue',
          status: 'pending'
        },
        {
          id: 'prepare-execution',
          title: 'Prepare Execution',
          description: 'Configuring agent activation controls and validation systems',
          status: 'pending'
        },
        {
          id: 'enable-control',
          title: 'Enable User Control',
          description: 'Activating user confirmation checkpoints and manual progression',
          status: 'pending'
        }
      ]
    },
    {
      id: 'finalize',
      title: 'Finalize Submission',
      description: 'Completing task submission to Micromanager',
      status: 'pending',
      subSteps: [
        {
          id: 'confirm-submission',
          title: 'Confirm Submission',
          description: 'Verifying all tasks have been successfully submitted',
          status: 'pending'
        },
        {
          id: 'enable-processing',
          title: 'Enable Processing',
          description: 'Allowing Micromanager to begin task decomposition and delegation',
          status: 'pending'
        },
        {
          id: 'complete-handoff',
          title: 'Complete Handoff',
          description: 'Finalizing handoff from Taskmaster to Micromanager',
          status: 'pending'
        }
      ]
    }
  ], []);

  // Progress tracking functions
  const getProgressPercentage = useMemo(() => {
    if (!orchestrationState.steps.length) return 0;

    let totalSubSteps = 0;
    let completedSubSteps = 0;

    orchestrationState.steps.forEach(step => {
      if (step.subSteps) {
        totalSubSteps += step.subSteps.length;
        completedSubSteps += step.subSteps.filter(sub => sub.status === 'completed').length;
      } else {
        totalSubSteps += 1;
        if (step.status === 'completed') completedSubSteps += 1;
      }
    });

    return totalSubSteps > 0 ? Math.round((completedSubSteps / totalSubSteps) * 100) : 0;
  }, [orchestrationState.steps]);

  const getElapsedTime = useMemo(() => {
    if (!orchestrationState.startTime) return 0;
    const endTime = orchestrationState.endTime || Date.now();
    return Math.floor((endTime - orchestrationState.startTime) / 1000);
  }, [orchestrationState.startTime, orchestrationState.endTime]);

  const getEstimatedCompletion = useMemo(() => {
    const progress = getProgressPercentage;
    const elapsed = getElapsedTime;

    if (progress <= 0 || !orchestrationState.isActive) return null;

    const estimatedTotal = (elapsed / progress) * 100;
    const remaining = Math.max(0, estimatedTotal - elapsed);
    return Math.round(remaining);
  }, [getProgressPercentage, getElapsedTime, orchestrationState.isActive]);

  // ✅ PART 1: Agent Lane Toggling Functions
  const toggleAgentVisibility = useCallback((agentId: string) => {
    setVisibleAgents(prev => {
      if (prev.includes(agentId)) {
        return prev.filter(id => id !== agentId);
      } else {
        return [...prev, agentId];
      }
    });
  }, []);

  const toggleAllAgents = useCallback((visible: boolean) => {
    if (visible) {
      setVisibleAgents([...availableAgents]);
    } else {
      setVisibleAgents([]);
    }
  }, [availableAgents]);

  // Extract available agents from tasks data
  const extractAvailableAgents = useCallback((tasks: any[]) => {
    const agents = new Set<string>();
    tasks.forEach(task => {
      const agentId = task.originalTaskmasterData?.assignedAgentId || task.assignedAgentId;
      if (agentId && agentId !== 'unassigned') {
        agents.add(agentId);
      }
    });
    const agentList = Array.from(agents);
    setAvailableAgents(agentList);
    // Default: all agents visible at mount
    setVisibleAgents(agentList);
    return agentList;
  }, []);

  // Update step status
  const updateStepStatus = useCallback((stepId: string, status: OrchestrationStep['status'], error?: string) => {
    setOrchestrationState(prev => ({
      ...prev,
      steps: prev.steps.map(step => {
        if (step.id === stepId) {
          const now = Date.now();
          return {
            ...step,
            status,
            error,
            startTime: status === 'running' ? now : step.startTime,
            endTime: status === 'completed' || status === 'failed' ? now : undefined
          };
        }
        return step;
      })
    }));
  }, []);

  // Update sub-step status
  const updateSubStepStatus = useCallback((stepId: string, subStepId: string, status: OrchestrationSubStep['status'], error?: string) => {
    setOrchestrationState(prev => ({
      ...prev,
      steps: prev.steps.map(step => {
        if (step.id === stepId && step.subSteps) {
          const now = Date.now();
          return {
            ...step,
            subSteps: step.subSteps.map(subStep => {
              if (subStep.id === subStepId) {
                return {
                  ...subStep,
                  status,
                  error,
                  startTime: status === 'running' ? now : subStep.startTime,
                  endTime: status === 'completed' || status === 'failed' ? now : undefined
                };
              }
              return subStep;
            })
          };
        }
        return step;
      })
    }));
  }, []);

  // ✅ Check for tasks file on component mount
  React.useEffect(() => {
    checkTasksFile();
  }, []);

  // ✅ CRITICAL FIX: Check if sequential workflow should be shown on mount
  React.useEffect(() => {
    console.log('🔍 TaskmasterOrchestrationUI: Checking sequential workflow status...');
    console.log('🔍 forceShowSequentialWorkflow:', forceShowSequentialWorkflow);

    if (forceShowSequentialWorkflow) {
      console.log('🎯 TaskmasterOrchestrationUI: Force showing Sequential Workflow Control panel');
      setSequentialWorkflowReady(true);
    } else {
      try {
        // Check if sequential workflow is already initialized
        const workflowStatus = micromanagerAgent.getSequentialWorkflowStatus();
        console.log('🔍 Current workflow status:', workflowStatus);

        if (workflowStatus.queueLength > 0 || workflowStatus.active) {
          console.log('🎯 TaskmasterOrchestrationUI: Detected existing sequential workflow, showing control panel');
          setSequentialWorkflowReady(true);
        } else {
          console.log('🔍 TaskmasterOrchestrationUI: No active sequential workflow detected');
        }
      } catch (error) {
        console.error('❌ TaskmasterOrchestrationUI: Failed to check workflow status:', error);
      }
    }
  }, [forceShowSequentialWorkflow]);

  // ✅ PART 2: Set up task state listener for real-time updates
  React.useEffect(() => {
    const unsubscribe = taskStateService.addListener((event) => {
      console.log(`📊 TaskStateService Event: ${event.changeType} change for task ${event.taskId}`);

      if (event.changeType === 'agent') {
        console.log(`🔄 Agent reassignment: ${event.previousState.agent} → ${event.newState.agent}`);

        // Update available agents if new agent is introduced
        const currentAgents = taskStateService.getUniqueAgents();
        setAvailableAgents(currentAgents);

        // Ensure new agent is visible by default
        if (!visibleAgents.includes(event.newState.agent)) {
          setVisibleAgents(prev => [...prev, event.newState.agent]);
        }
      }
    });

    return unsubscribe;
  }, [visibleAgents]);

  // ✅ PHASE 4: Set up automatic execution status listener
  React.useEffect(() => {
    const unsubscribe = automaticExecutionService.addStatusListener((status) => {
      setAutoExecutionStatus(status);
    });

    // Configure automatic execution with default settings
    automaticExecutionService.configure({
      enabled: true,
      autoApproveThreshold: 80,
      maxConsecutiveTasks: 3,
      timeoutPerTask: 300000, // 5 minutes
      requireUserApprovalFor: ['low_quality', 'validation_failures']
    });

    return unsubscribe;
  }, []);

  const checkTasksFile = useCallback(async () => {
    try {
      // ✅ CRITICAL FIX: Use TaskmasterAdapter for proper task validation and agent assignment
      console.log('🔍 TaskmasterOrchestrationUI: Loading tasks via TaskmasterAdapter...');

      const loadResult = await taskmasterAdapter.loadTasks();

      if (loadResult.success && loadResult.data) {
        setHasTasksFile(true);
        setTasksData(loadResult.data);
        console.log(`✅ TaskmasterOrchestrationUI: Loaded ${loadResult.data.tasks.length} tasks with proper agent assignments`);

        // Log agent assignments for debugging
        const agentCounts = loadResult.data.tasks.reduce((acc, task) => {
          acc[task.assignedAgentId] = (acc[task.assignedAgentId] || 0) + 1;
          return acc;
        }, {} as Record<string, number>);
        console.log('🎯 Agent assignments:', agentCounts);
      } else {
        setHasTasksFile(false);
        console.warn('TaskmasterOrchestrationUI: Failed to load tasks:', loadResult.error);
      }
    } catch (error) {
      console.error('Failed to check tasks file:', error);
      setHasTasksFile(false);
    }
  }, []);

  // ARCHITECTURE RESTORATION: Submit tasks to Micromanager instead of creating Kanban boards
  const handleStartOrchestration = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    setSubmissionResult(null);

    // Initialize orchestration state
    const steps = createOrchestrationSteps();
    setOrchestrationState({
      isActive: true,
      steps,
      startTime: Date.now()
    });

    try {
      console.log('🎯 Starting Taskmaster to Micromanager delegation...');

      // Log orchestration start
      logTaskmasterEvent('OrchestrationStarted', {
        tasksCount: tasksData?.tasks?.length || 0,
        hasTasksFile,
        timestamp: new Date().toISOString()
      });

      // Step 1: Validate Tasks Data
      updateStepStatus('validate', 'running');

      updateSubStepStatus('validate', 'check-file', 'running');
      await new Promise(resolve => setTimeout(resolve, 800));
      if (!hasTasksFile) {
        throw new Error('Tasks file not found - ensure Taskmaster has generated tasks.json');
      }
      updateSubStepStatus('validate', 'check-file', 'completed');

      updateSubStepStatus('validate', 'parse-data', 'running');
      await new Promise(resolve => setTimeout(resolve, 1200));
      if (!tasksData) {
        throw new Error('Failed to load tasks data from Taskmaster');
      }
      updateSubStepStatus('validate', 'parse-data', 'completed');

      updateSubStepStatus('validate', 'validate-structure', 'running');
      await new Promise(resolve => setTimeout(resolve, 600));
      // Validate that tasks have required fields for Synapse architecture
      const validationTasks = Array.isArray(tasksData) ? tasksData : tasksData.tasks || [];
      if (!validationTasks.length) {
        throw new Error('No tasks found in Taskmaster data');
      }
      updateSubStepStatus('validate', 'validate-structure', 'completed');
      updateStepStatus('validate', 'completed');

      // Step 2: Prepare for Micromanager
      updateStepStatus('prepare', 'running');

      updateSubStepStatus('prepare', 'normalize-tasks', 'running');
      await new Promise(resolve => setTimeout(resolve, 1000));
      // ✅ CRITICAL FIX: Use properly validated tasks from TaskmasterAdapter
      const rawTasks = Array.isArray(tasksData) ? tasksData : tasksData.tasks || [];
      const normalizedTasks = rawTasks.map((task: any, index: number) => ({
        id: task.id || `task-${index}`,
        title: task.title || task.name || `Task ${index + 1}`,
        description: task.description || task.details || '',
        priority: task.priority || 'medium',
        dependencies: task.dependencies || [],
        originalTaskmasterData: task // This now contains properly assigned agent IDs
      }));
      updateSubStepStatus('prepare', 'normalize-tasks', 'completed');

      updateSubStepStatus('prepare', 'create-contexts', 'running');
      await new Promise(resolve => setTimeout(resolve, 1500));
      // Create AgentContext objects for each task
      const agentContexts: AgentContext[] = normalizedTasks.map((task: any) => ({
        task: `${task.title}: ${task.description}`,
        metadata: {
          taskId: task.id,
          priority: task.priority,
          dependencies: task.dependencies,
          source: 'taskmaster',
          originalData: task.originalTaskmasterData
        }
      }));
      updateSubStepStatus('prepare', 'create-contexts', 'completed');

      updateSubStepStatus('prepare', 'validate-contexts', 'running');
      await new Promise(resolve => setTimeout(resolve, 800));
      // Validate contexts meet Synapse requirements
      if (!agentContexts.every(ctx => ctx.task && typeof ctx.task === 'string')) {
        throw new Error('Invalid agent contexts generated from Taskmaster data');
      }
      updateSubStepStatus('prepare', 'validate-contexts', 'completed');
      updateStepStatus('prepare', 'completed');

      // Step 3: Create Kanban Structure with Agent-Based Swimlanes
      updateStepStatus('orchestrate', 'running');

      updateSubStepStatus('orchestrate', 'analyze-agents', 'running');
      await new Promise(resolve => setTimeout(resolve, 800));
      // ✅ CRITICAL FIX: Use properly validated tasks with correct agent assignments
      const agentTasks = normalizedTasks.map((task: any) => {
        const assignedAgentId = task.originalTaskmasterData?.assignedAgentId || 'junior';
        console.log(`🎯 Task ${task.id} assigned to agent: ${assignedAgentId}`);

        return {
          id: task.id,
          title: task.title,
          description: task.description,
          priority: task.priority,
          assignedAgentId: assignedAgentId, // Use validated agent ID
          dependencies: task.dependencies,
          tags: task.originalTaskmasterData?.tags || [],
          module: task.originalTaskmasterData?.module || '',
          milestone: task.originalTaskmasterData?.milestone || '',
          phase: task.originalTaskmasterData?.phase || '',
          estimatedHours: task.originalTaskmasterData?.estimatedHours || 0,
          complexity: task.originalTaskmasterData?.complexity || 'medium',
          storyPoints: task.originalTaskmasterData?.storyPoints || 0,
          dueDate: task.originalTaskmasterData?.dueDate || '',
          acceptanceCriteria: task.originalTaskmasterData?.acceptanceCriteria || [],
          subtasks: task.originalTaskmasterData?.subtasks || []
        };
      });
      console.log(`🎯 Analyzed ${agentTasks.length} tasks for agent-based orchestration`);

      // ✅ Extract available agents for lane toggling
      extractAvailableAgents(agentTasks);

      updateSubStepStatus('orchestrate', 'analyze-agents', 'completed');

      updateSubStepStatus('orchestrate', 'create-swimlanes', 'running');
      await new Promise(resolve => setTimeout(resolve, 1000));
      // Create Kanban structure with agent-based swimlanes
      const kanbanResult = await kanbanTaskOrchestrator.orchestrateTasks(agentTasks);
      if (!kanbanResult.success) {
        throw new Error(`Kanban orchestration failed: ${kanbanResult.error}`);
      }
      console.log(`✅ Created ${kanbanResult.boardsCreated} boards with ${kanbanResult.cardsCreated} cards`);

      // ✅ Store orchestration result for agent lane management
      setOrchestrationResult(kanbanResult);

      // ✅ PART 2: Initialize task state service with created tasks
      if (kanbanResult.structure) {
        const taskStates: TaskState[] = [];
        kanbanResult.structure.forEach(board => {
          board.cards.forEach(card => {
            taskStates.push({
              id: card.id,
              title: card.title,
              description: card.description,
              agent: card.assignedAgent,
              status: card.status as TaskState['status'],
              priority: card.priority,
              laneId: card.laneId,
              columnId: card.columnId,
              metadata: card.metadata,
              lastUpdated: Date.now()
            });
          });
        });
        taskStateService.initializeTasks(taskStates);
        console.log(`✅ Initialized ${taskStates.length} tasks in TaskStateService`);
      }

      updateSubStepStatus('orchestrate', 'create-swimlanes', 'completed');

      updateSubStepStatus('orchestrate', 'create-boards', 'running');
      await new Promise(resolve => setTimeout(resolve, 800));
      // Boards and swimlanes are created as part of orchestration
      console.log(`🏗️ Kanban boards created with agent-based swimlane structure`);
      updateSubStepStatus('orchestrate', 'create-boards', 'completed');

      updateSubStepStatus('orchestrate', 'assign-cards', 'running');
      await new Promise(resolve => setTimeout(resolve, 600));
      // Cards are assigned to agent lanes based on assignedAgentId
      console.log(`📋 Task cards assigned to appropriate agent swimlanes`);
      updateSubStepStatus('orchestrate', 'assign-cards', 'completed');
      updateStepStatus('orchestrate', 'completed');

      // Step 4: Delegate to Micromanager
      updateStepStatus('delegate', 'running');

      updateSubStepStatus('delegate', 'initialize-micromanager', 'running');
      await new Promise(resolve => setTimeout(resolve, 1200));
      // Ensure Micromanager is ready
      if (!micromanagerAgent) {
        throw new Error('Micromanager agent not available');
      }
      updateSubStepStatus('delegate', 'initialize-micromanager', 'completed');

      updateSubStepStatus('delegate', 'submit-tasks', 'running');
      await new Promise(resolve => setTimeout(resolve, 800));
      // Submit each task to Micromanager for proper orchestration
      let submittedCount = 0;
      for (const context of agentContexts) {
        try {
          console.log(`🎯 Submitting task to Micromanager: ${context.metadata?.taskId}`);

          logTaskmasterDebug('TaskSubmissionStarted', {
            taskId: context.metadata?.taskId,
            task: context.task,
            priority: context.metadata?.priority
          });

          await micromanagerAgent.execute(context);
          submittedCount++;

          logTaskmasterEvent('TaskSubmittedToMicromanager', {
            taskId: context.metadata?.taskId,
            submittedCount,
            totalTasks: agentContexts.length
          });

        } catch (taskError) {
          console.warn(`⚠️ Failed to submit task ${context.metadata?.taskId}:`, taskError);

          logTaskmasterError('TaskSubmissionFailed', {
            taskId: context.metadata?.taskId,
            error: taskError instanceof Error ? taskError.message : String(taskError),
            stack: taskError instanceof Error ? taskError.stack : undefined
          });

          // Continue with other tasks
        }
      }
      updateSubStepStatus('delegate', 'submit-tasks', 'completed');

      updateSubStepStatus('delegate', 'verify-delegation', 'running');
      await new Promise(resolve => setTimeout(resolve, 1000));
      if (submittedCount === 0) {
        throw new Error('No tasks were successfully submitted to Micromanager');
      }
      updateSubStepStatus('delegate', 'verify-delegation', 'completed');
      updateStepStatus('delegate', 'completed');

      // Step 5: Initialize Sequential Workflow
      updateStepStatus('sequential', 'running');

      updateSubStepStatus('sequential', 'initialize-workflow', 'running');
      await new Promise(resolve => setTimeout(resolve, 800));

      // Initialize sequential workflow with Micromanager
      const workflowResult = await micromanagerAgent.initializeSequentialWorkflow(agentTasks);
      if (!workflowResult.success) {
        throw new Error(`Sequential workflow initialization failed: ${workflowResult.message}`);
      }
      console.log(`✅ Sequential workflow initialized: ${workflowResult.message}`);
      updateSubStepStatus('sequential', 'initialize-workflow', 'completed');

      updateSubStepStatus('sequential', 'prepare-execution', 'running');
      await new Promise(resolve => setTimeout(resolve, 600));

      // Set up sequential execution listener
      const workflowStatus = micromanagerAgent.getSequentialWorkflowStatus();
      console.log(`🎯 Sequential workflow status:`, workflowStatus);
      updateSubStepStatus('sequential', 'prepare-execution', 'completed');

      updateSubStepStatus('sequential', 'enable-control', 'running');
      await new Promise(resolve => setTimeout(resolve, 400));

      // Enable user control for sequential execution
      console.log(`🎮 Sequential workflow ready for user control`);
      updateSubStepStatus('sequential', 'enable-control', 'completed');
      updateStepStatus('sequential', 'completed');

      // ✅ Enable Sequential Workflow Control UI
      setSequentialWorkflowReady(true);

      // Step 6: Finalize Submission
      updateStepStatus('finalize', 'running');

      updateSubStepStatus('finalize', 'confirm-submission', 'running');
      await new Promise(resolve => setTimeout(resolve, 800));
      // Confirm all tasks were submitted
      const result = {
        success: submittedCount > 0,
        tasksSubmitted: submittedCount,
        error: submittedCount === 0 ? 'No tasks were successfully submitted' : undefined
      };
      updateSubStepStatus('finalize', 'confirm-submission', 'completed');

      updateSubStepStatus('finalize', 'enable-processing', 'running');
      await new Promise(resolve => setTimeout(resolve, 600));
      // Micromanager will now handle task decomposition and agent delegation
      console.log(`✅ ${submittedCount} tasks submitted to Micromanager for orchestration`);
      updateSubStepStatus('finalize', 'enable-processing', 'completed');

      updateSubStepStatus('finalize', 'complete-handoff', 'running');
      await new Promise(resolve => setTimeout(resolve, 400));
      // Complete handoff from Taskmaster to Micromanager
      updateSubStepStatus('finalize', 'complete-handoff', 'completed');
      updateStepStatus('finalize', 'completed');

      // Complete submission
      setOrchestrationState(prev => ({
        ...prev,
        isActive: false,
        endTime: Date.now()
      }));

      setSubmissionResult(result);
      onTasksSubmittedToMicromanager(result);

      if (result.success) {
        console.log(`✅ Taskmaster delegation completed: ${result.tasksSubmitted} tasks submitted to Micromanager`);
        console.log('🎯 Micromanager will now handle task decomposition, classification, and agent assignment');
      } else {
        console.error('❌ TaskmasterOrchestrationUI: Delegation failed:', result.error);
        setError(result.error || 'Task submission failed');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown orchestration error';
      setError(errorMessage);
      console.error('Enhanced orchestration failed:', error);

      // Mark current step as failed
      setOrchestrationState(prev => ({
        ...prev,
        isActive: false,
        endTime: Date.now(),
        steps: prev.steps.map(step => {
          if (step.status === 'running') {
            return { ...step, status: 'failed', error: errorMessage };
          }
          return step;
        })
      }));
    } finally {
      setIsLoading(false);
    }
  }, [onTasksSubmittedToMicromanager, createOrchestrationSteps, updateStepStatus, updateSubStepStatus, hasTasksFile, tasksData]);

  // ARCHITECTURE RESTORATION: Refresh tasks data without adapter
  const handleRefreshTasks = useCallback(async () => {
    setIsLoading(true);
    try {
      // Clear cached data and re-check
      setTasksData(null);
      setHasTasksFile(null);
      await checkTasksFile();
    } catch (error) {
      setError('Failed to refresh tasks data');
    } finally {
      setIsLoading(false);
    }
  }, [checkTasksFile]);

  // Helper functions for rendering
  const getStepDuration = (step: OrchestrationStep) => {
    if (!step.startTime) return null;
    const endTime = step.endTime || Date.now();
    return Math.floor((endTime - step.startTime) / 1000);
  };

  const getSubStepDuration = (subStep: OrchestrationSubStep) => {
    if (!subStep.startTime) return null;
    const endTime = subStep.endTime || Date.now();
    return Math.floor((endTime - subStep.startTime) / 1000);
  };

  const renderStepIcon = (step: OrchestrationStep) => {
    switch (step.status) {
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'failed':
        return <XCircle className="h-5 w-5 text-red-500" />;
      case 'running':
        return <Loader2 className="h-5 w-5 text-blue-500 animate-spin" />;
      default:
        return <div className="h-5 w-5 rounded-full border-2 border-gray-300" />;
    }
  };

  const renderSubStepIcon = (subStep: OrchestrationSubStep) => {
    switch (subStep.status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'running':
        return <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />;
      default:
        return <div className="h-4 w-4 rounded-full border-2 border-gray-300" />;
    }
  };

  // ARCHITECTURE RESTORATION: Get status icon based on current state
  const getStatusIcon = () => {
    if (orchestrationState.isActive) return <Loader2 className="h-5 w-5 animate-spin" />;
    if (submissionResult?.success) return <CheckCircle className="h-5 w-5 text-green-500" />;
    if (error) return <XCircle className="h-5 w-5 text-red-500" />;
    if (hasTasksFile) return <FileText className="h-5 w-5 text-blue-500" />;
    return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
  };

  // Check if orchestration has started
  const hasStarted = orchestrationState.steps.length > 0;

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Main Orchestration Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Taskmaster to Micromanager Delegation
          </CardTitle>
          <CardDescription>
            Submit Taskmaster-generated tasks to Micromanager for proper Synapse orchestration
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {!hasStarted ? (
            <div className="space-y-4">
              {/* Tasks File Status */}
              {hasTasksFile === null ? (
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Checking for tasks file...
                </div>
              ) : hasTasksFile ? (
                <Alert>
                  <CheckCircle className="h-4 w-4" />
                  <AlertDescription>
                    <strong>Tasks file found!</strong> Ready to orchestrate {tasksData?.tasks.length || 0} tasks.
                  </AlertDescription>
                </Alert>
              ) : (
                <Alert variant="destructive">
                  <XCircle className="h-4 w-4" />
                  <AlertDescription>
                    <strong>No tasks file found.</strong> Please upload a PRD (Project Requirements Document) to generate tasks automatically. Taskmaster will parse your PRD and create structured tasks for AI execution.
                  </AlertDescription>
                </Alert>
              )}

              {/* Tasks Preview */}
              {tasksData && (
                <div className="grid grid-cols-2 gap-4 p-4 bg-muted rounded-lg">
                  <div className="flex items-center gap-2">
                    <FileText className="h-4 w-4 text-blue-500" />
                    <span className="text-sm font-medium">{tasksData.tasks.length} Tasks</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Users className="h-4 w-4 text-green-500" />
                    <span className="text-sm font-medium">
                      {new Set(tasksData.tasks.map(t => t.assignedAgentId)).size} Agents
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Layers className="h-4 w-4 text-purple-500" />
                    <span className="text-sm font-medium">
                      {tasksData.metadata?.modules?.length || 0} Modules
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Target className="h-4 w-4 text-orange-500" />
                    <span className="text-sm font-medium">
                      {tasksData.metadata?.milestones?.length || 0} Milestones
                    </span>
                  </div>
                </div>
              )}

              <Alert>
                <Zap className="h-4 w-4" />
                <AlertDescription>
                  <strong>Ready to orchestrate with agent-based Kanban!</strong> This process will:
                  <ul className="list-disc list-inside mt-2 space-y-1">
                    <li>Validate and normalize Taskmaster task data</li>
                    <li>Create agent-based Kanban boards with swimlanes</li>
                    <li>Organize tasks by assigned agent roles</li>
                    <li>Submit tasks to Micromanager for proper orchestration</li>
                    <li>Enable automated agent workflow execution</li>
                  </ul>
                </AlertDescription>
              </Alert>

              {/* Action Buttons */}
              <div className="flex gap-2">
                <Button
                  onClick={handleStartOrchestration}
                  disabled={!hasTasksFile || orchestrationState.isActive}
                  className="flex-1"
                >
                  <Zap className="h-4 w-4 mr-2" />
                  Start Agent Orchestration
                </Button>
                {onCancel && (
                  <Button variant="outline" onClick={onCancel}>
                    Cancel
                  </Button>
                )}
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              {/* Enhanced Progress Overview */}
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">
                    Overall Progress: {getProgressPercentage}%
                  </span>
                  <div className="flex items-center gap-3 text-sm text-muted-foreground">
                    <span className="flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      {getElapsedTime}s elapsed
                    </span>
                    <span className="flex items-center gap-1">
                      <Target className="h-3 w-3" />
                      {orchestrationState.steps.filter(s => s.status === 'completed').length}/{orchestrationState.steps.length} steps
                    </span>
                    {orchestrationState.isActive && getEstimatedCompletion && (
                      <span className="flex items-center gap-1 text-blue-600">
                        <Clock className="h-3 w-3" />
                        ~{getEstimatedCompletion}s remaining
                      </span>
                    )}
                  </div>
                </div>
                <Progress value={getProgressPercentage} className="w-full h-3" />

                {/* Current Activity Indicator */}
                {orchestrationState.isActive && (
                  <div className="flex items-center gap-2 text-sm text-blue-600 bg-blue-50 p-2 rounded-md">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <span>
                      {(() => {
                        const runningStep = orchestrationState.steps.find(s => s.status === 'running');
                        if (runningStep?.subSteps) {
                          const runningSubStep = runningStep.subSteps.find(ss => ss.status === 'running');
                          if (runningSubStep) {
                            // Return more descriptive live activity based on the step
                            switch (runningSubStep.id) {
                              case 'check-file':
                                return 'Verifying tasks.json file exists and is accessible in project directory...';
                              case 'parse-data':
                                return 'Loading task data and validating JSON structure and metadata...';
                              case 'validate-agents':
                                return 'Checking agent assignments and verifying agent availability...';
                              case 'analyze-structure':
                                return 'Analyzing task dependencies, priorities, and relationship mapping...';
                              case 'create-groups':
                                return 'Grouping tasks by modules, phases, and agent specializations...';
                              case 'analyze-agents':
                                return 'Extracting unique agent assignments and analyzing task distribution...';
                              case 'create-swimlanes':
                                return 'Creating agent-based swimlanes for automated workflow organization...';
                              case 'create-boards':
                                return 'Setting up Kanban boards with optimized agent execution columns...';
                              case 'assign-cards':
                                return 'Assigning task cards to appropriate agent swimlanes based on assignments...';
                              case 'initialize-micromanager':
                                return 'Preparing Micromanager agent for task delegation and orchestration...';
                              case 'submit-tasks':
                                return 'Submitting task contexts to Micromanager for agent coordination...';
                              case 'verify-delegation':
                                return 'Confirming successful task handoff to Micromanager system...';
                              case 'confirm-submission':
                                return 'Validating all tasks have been properly submitted and processed...';
                              case 'enable-processing':
                                return 'Enabling Micromanager to begin task decomposition and agent assignment...';
                              case 'complete-handoff':
                                return 'Finalizing handoff from Taskmaster to Synapse orchestration system...';
                              case 'initialize-workflow':
                                return 'Setting up sequential execution controller and clearing previous state...';
                              case 'prepare-execution':
                                return 'Configuring agent activation controls and task validation systems...';
                              case 'enable-control':
                                return 'Activating user confirmation checkpoints for controlled progression...';
                              default:
                                return runningSubStep.description;
                            }
                          }
                          return runningStep.description;
                        }
                        return runningStep?.description || 'Processing orchestration...';
                      })()}
                    </span>
                  </div>
                )}
              </div>

              {/* Step Details */}
              <div className="space-y-3">
                {orchestrationState.steps.map((step, index) => (
                  <div key={step.id} className="space-y-2">
                    <div className="flex items-start gap-3 p-3 rounded-lg border">
                      {renderStepIcon(step)}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <h4 className={`font-medium ${
                            step.status === 'completed' ? 'text-green-600' :
                            step.status === 'failed' ? 'text-red-600' :
                            step.status === 'running' ? 'text-blue-600' :
                            'text-gray-600'
                          }`}>
                            {step.title}
                          </h4>
                          {getStepDuration(step) && (
                            <span className="text-xs text-muted-foreground">
                              {getStepDuration(step)}s
                            </span>
                          )}
                        </div>
                        <p className="text-sm text-muted-foreground">
                          {step.description}
                        </p>
                        {step.error && (
                          <p className="text-sm text-red-500 mt-1">
                            Error: {step.error}
                          </p>
                        )}
                      </div>
                    </div>

                    {/* Enhanced Sub-steps for detailed progress */}
                    {step.subSteps && (step.status === 'running' || step.status === 'completed') && (
                      <div className="ml-6 space-y-2 mt-3">
                        <div className="text-xs font-medium text-gray-600 mb-2 flex items-center gap-1">
                          <div className="h-px bg-gray-300 flex-1"></div>
                          <span className="px-2">Detailed Progress</span>
                          <div className="h-px bg-gray-300 flex-1"></div>
                        </div>
                        {step.subSteps.map((subStep, subIndex) => (
                          <div key={subStep.id} className={`flex items-start gap-3 p-3 rounded-md border-l-4 ${
                            subStep.status === 'completed' ? 'border-green-400 bg-green-50/50' :
                            subStep.status === 'failed' ? 'border-red-400 bg-red-50/50' :
                            subStep.status === 'running' ? 'border-blue-400 bg-blue-50/50' :
                            'border-gray-300 bg-gray-50/30'
                          }`}>
                            {renderSubStepIcon(subStep)}
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center justify-between">
                                <h5 className={`text-sm font-medium ${
                                  subStep.status === 'completed' ? 'text-green-700' :
                                  subStep.status === 'failed' ? 'text-red-700' :
                                  subStep.status === 'running' ? 'text-blue-700' :
                                  'text-gray-600'
                                }`}>
                                  {subStep.title}
                                  {subStep.status === 'running' && (
                                    <span className="ml-2 inline-flex items-center gap-1 text-xs text-blue-600 font-medium">
                                      <div className="w-1.5 h-1.5 bg-blue-600 rounded-full animate-pulse"></div>
                                      • Active
                                    </span>
                                  )}
                                </h5>
                                <div className="flex items-center gap-2">
                                  {getSubStepDuration(subStep) && (
                                    <span className="text-xs text-muted-foreground bg-white px-2 py-1 rounded">
                                      {getSubStepDuration(subStep)}s
                                    </span>
                                  )}
                                </div>
                              </div>
                              <p className="text-xs text-muted-foreground mt-1">
                                {subStep.description}
                              </p>

                              {subStep.error && (
                                <div className="mt-2 p-2 bg-red-100 border border-red-200 rounded text-xs text-red-700">
                                  <strong>Error:</strong> {subStep.error}
                                </div>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </div>

              {/* Completion Summary */}
              {!orchestrationState.isActive && orchestrationState.steps.every(step => step.status === 'completed') && (
                <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
                  <div className="flex items-center gap-2 text-green-800 font-medium mb-2">
                    <CheckCircle className="h-5 w-5" />
                    Agent Orchestration Complete!
                  </div>
                  <div className="text-sm text-green-700 space-y-1">
                    <p>✅ Created agent-based Kanban boards with swimlanes</p>
                    <p>✅ Organized {submissionResult?.tasksSubmitted || 0} tasks by assigned agent roles</p>
                    <p>✅ Tasks submitted to Micromanager for orchestration</p>
                    <p>✅ Automated agent workflow execution enabled</p>
                  </div>
                  <div className="mt-3 text-xs text-green-600">
                    Total time: {getElapsedTime}s
                  </div>
                </div>
              )}

              {/* Error Display */}
              {error && (
                <Alert variant="destructive">
                  <XCircle className="h-4 w-4" />
                  <AlertDescription>
                    <strong>Orchestration failed:</strong> {error}
                  </AlertDescription>
                </Alert>
              )}

              {/* Action Buttons */}
              {!orchestrationState.isActive && (
                <div className="flex gap-2 pt-4 border-t">
                  {orchestrationState.steps.some(step => step.status === 'failed') ? (
                    <Button onClick={handleStartOrchestration} variant="outline" className="flex items-center gap-2">
                      <Loader2 className="h-4 w-4" />
                      Retry Orchestration
                    </Button>
                  ) : null}
                  {onCancel && (
                    <Button
                      onClick={onCancel}
                      className={orchestrationState.steps.every(step => step.status === 'completed') ?
                        "bg-green-600 hover:bg-green-700" : ""}
                    >
                      {orchestrationState.steps.every(step => step.status === 'completed') ?
                        'Continue to Project' : 'Cancel'}
                    </Button>
                  )}
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* ✅ PHASE 1: Sequential Workflow Status Indicator */}
      {sequentialWorkflowReady && (
        <Alert>
          <CheckCircle className="h-4 w-4" />
          <AlertDescription>
            <strong>Sequential Workflow Ready!</strong> The controlled execution system is now active.
            Use the Sequential Workflow Control panel below to start executing tasks one by one.
          </AlertDescription>
        </Alert>
      )}

      {/* ✅ PART 1: Agent Lane Toggling UI */}
      {orchestrationResult && orchestrationResult.success && availableAgents.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Agent Lane Visibility
            </CardTitle>
            <CardDescription>
              Control which agent swimlanes are visible in the Kanban board
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Toggle All Controls */}
            <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
              <div className="flex items-center gap-2">
                <Users className="h-4 w-4" />
                <span className="font-medium">All Agents</span>
                <Badge variant="outline">{availableAgents.length} total</Badge>
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => toggleAllAgents(true)}
                  disabled={visibleAgents.length === availableAgents.length}
                >
                  <Eye className="h-3 w-3 mr-1" />
                  Show All
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => toggleAllAgents(false)}
                  disabled={visibleAgents.length === 0}
                >
                  <EyeOff className="h-3 w-3 mr-1" />
                  Hide All
                </Button>
              </div>
            </div>

            {/* Individual Agent Toggles */}
            <div className="grid grid-cols-2 gap-3">
              {availableAgents.map(agentId => {
                const isVisible = visibleAgents.includes(agentId);
                const agentDisplayName = kanbanTaskOrchestrator.capitalizeAgent(agentId);
                const agentTaskCount = orchestrationResult.structure?.reduce((count, board) => {
                  return count + board.cards.filter(card => card.assignedAgent === agentId).length;
                }, 0) || 0;

                return (
                  <div
                    key={agentId}
                    className={`flex items-center justify-between p-3 rounded-lg border transition-colors ${
                      isVisible ? 'bg-green-50 border-green-200' : 'bg-gray-50 border-gray-200'
                    }`}
                  >
                    <div className="flex items-center gap-3">
                      <Checkbox
                        checked={isVisible}
                        onCheckedChange={() => toggleAgentVisibility(agentId)}
                        id={`agent-${agentId}`}
                      />
                      <label
                        htmlFor={`agent-${agentId}`}
                        className="flex items-center gap-2 cursor-pointer"
                      >
                        <div className={`w-3 h-3 rounded-full ${isVisible ? 'bg-green-500' : 'bg-gray-400'}`} />
                        <span className="font-medium">{agentDisplayName}</span>
                      </label>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant={isVisible ? "default" : "secondary"}>
                        {agentTaskCount} tasks
                      </Badge>
                      {isVisible ? (
                        <Eye className="h-4 w-4 text-green-600" />
                      ) : (
                        <EyeOff className="h-4 w-4 text-gray-400" />
                      )}
                    </div>
                  </div>
                );
              })}
            </div>

            {/* Visibility Summary */}
            <div className="flex items-center justify-between p-2 bg-blue-50 rounded-md text-sm">
              <span className="text-blue-700">
                Showing {visibleAgents.length} of {availableAgents.length} agent lanes
              </span>
              <span className="text-blue-600">
                {orchestrationResult.structure?.reduce((count, board) => {
                  return count + board.cards.filter(card => visibleAgents.includes(card.assignedAgent)).length;
                }, 0) || 0} visible tasks
              </span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* ✅ PART 2: Dynamic Agent Reassignment Demo UI */}
      {orchestrationResult && orchestrationResult.success && availableAgents.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Dynamic Task Reassignment
            </CardTitle>
            <CardDescription>
              Demonstrate real-time task reassignment between agents
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Alert>
              <Settings className="h-4 w-4" />
              <AlertDescription>
                <strong>Live Demo:</strong> Task reassignment automatically updates swimlane positions in real-time.
                Changes to task.agent field will immediately move cards to the correct agent lane.
              </AlertDescription>
            </Alert>

            {/* Task Reassignment Controls */}
            <div className="space-y-3">
              {orchestrationResult.structure?.slice(0, 1).map(board =>
                board.cards.slice(0, 3).map(card => {
                  const currentAgent = taskStateService.getTask(card.id)?.agent || card.assignedAgent;
                  const otherAgents = availableAgents.filter(agent => agent !== currentAgent);

                  return (
                    <div key={card.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex-1">
                        <div className="font-medium">{card.title}</div>
                        <div className="text-sm text-muted-foreground">
                          Currently assigned to: <Badge variant="outline">{kanbanTaskOrchestrator.capitalizeAgent(currentAgent)}</Badge>
                        </div>
                      </div>
                      <div className="flex gap-2">
                        {otherAgents.slice(0, 2).map(agentId => (
                          <Button
                            key={agentId}
                            variant="outline"
                            size="sm"
                            onClick={async () => {
                              console.log(`🔄 Reassigning task ${card.id} to ${agentId}`);
                              await taskStateService.reassignTask(card.id, agentId);
                              // Force re-render by updating orchestration result
                              setOrchestrationResult(prev => ({ ...prev! }));
                            }}
                          >
                            → {kanbanTaskOrchestrator.capitalizeAgent(agentId)}
                          </Button>
                        ))}
                      </div>
                    </div>
                  );
                })
              )}
            </div>

            {/* Real-time Task State Summary */}
            <div className="p-3 bg-blue-50 rounded-lg">
              <div className="font-medium text-blue-800 mb-2">Real-time Task Distribution</div>
              <div className="grid grid-cols-2 gap-2">
                {availableAgents.map(agentId => {
                  const taskCount = taskStateService.getTasksByAgent(agentId).length;
                  return (
                    <div key={agentId} className="flex items-center justify-between text-sm">
                      <span className="text-blue-700">{kanbanTaskOrchestrator.capitalizeAgent(agentId)}</span>
                      <Badge variant="secondary">{taskCount} tasks</Badge>
                    </div>
                  );
                })}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Agent Assignment Preview - Only show when not started */}
      {tasksData && !hasStarted && (
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Agent Assignment Preview</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {Object.entries(
                tasksData.tasks.reduce((acc, task) => {
                  acc[task.assignedAgentId] = (acc[task.assignedAgentId] || 0) + 1;
                  return acc;
                }, {} as Record<string, number>)
              ).map(([agentId, count]) => (
                <div key={agentId} className="flex items-center justify-between">
                  <span className="text-sm capitalize">{agentId.replace(/([A-Z])/g, ' $1').trim()}</span>
                  <Badge variant="secondary">{count} tasks</Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* ARCHITECTURE RESTORATION: Show submission results instead of board creation */}
      {submissionResult?.success && (
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Micromanager Submission Complete</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-center justify-between p-2 bg-muted rounded">
                <div>
                  <div className="font-medium text-sm">Agent-Based Orchestration Complete</div>
                  <div className="text-xs text-muted-foreground">
                    {submissionResult.tasksSubmitted} tasks organized in agent swimlanes and submitted to Micromanager
                  </div>
                </div>
                <Badge variant="outline">✅ Complete</Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* ✅ PHASE 1: Sequential Workflow Control UI */}
      {sequentialWorkflowReady && (
        <Card className="border-2 border-green-500 bg-green-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-green-800">
              <Play className="h-5 w-5" />
              🎮 Sequential Workflow Control (ACTIVE)
            </CardTitle>
            <CardDescription>
              Control the sequential execution of tasks with user confirmation checkpoints
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Alert>
              <Settings className="h-4 w-4" />
              <AlertDescription>
                <strong>Sequential Workflow (Default):</strong> Tasks execute one at a time with validation checkpoints.
                Choose manual control or enable automatic execution with quality-based approval.
              </AlertDescription>
            </Alert>

            {/* ✅ PHASE 4: Automatic Execution Toggle */}
            <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
              <div className="flex items-center justify-between mb-3">
                <div>
                  <div className="font-medium text-blue-800">Automatic Execution</div>
                  <div className="text-sm text-blue-600">
                    Auto-approve tasks that meet quality thresholds (80%+) and continue execution
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    id="auto-execution"
                    checked={autoExecutionEnabled}
                    onChange={(e) => setAutoExecutionEnabled(e.target.checked)}
                    className="rounded"
                  />
                  <label htmlFor="auto-execution" className="text-sm font-medium text-blue-800">
                    Enable Auto-Execution
                  </label>
                </div>
              </div>

              {autoExecutionStatus.isRunning && (
                <div className="text-sm text-blue-700">
                  <div>Status: <Badge variant="default">Running Automatically</Badge></div>
                  <div>Tasks Completed: {autoExecutionStatus.tasksCompleted}</div>
                  <div>Consecutive: {autoExecutionStatus.consecutiveTasksRun}/3</div>
                </div>
              )}
            </div>

            {/* Workflow Status */}
            <div className="p-4 bg-blue-50 rounded-lg">
              <div className="font-medium text-blue-800 mb-2">Workflow Status</div>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="flex items-center justify-between">
                  <span className="text-blue-700">Execution Mode:</span>
                  <Badge variant="outline">Sequential</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-blue-700">Current Agent:</span>
                  <Badge variant={micromanagerAgent.getSequentialWorkflowStatus().currentAgent ? "default" : "secondary"}>
                    {micromanagerAgent.getSequentialWorkflowStatus().currentAgent || 'None'}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-blue-700">Queue Length:</span>
                  <Badge variant="outline">{micromanagerAgent.getSequentialWorkflowStatus().queueLength} tasks</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-blue-700">Can Start Next:</span>
                  <Badge variant={micromanagerAgent.getSequentialWorkflowStatus().canStartNext ? "default" : "secondary"}>
                    {micromanagerAgent.getSequentialWorkflowStatus().canStartNext ? 'Yes' : 'No'}
                  </Badge>
                </div>
              </div>
            </div>

            {/* Control Buttons */}
            <div className="flex gap-3">
              {autoExecutionEnabled ? (
                // ✅ PHASE 4: Automatic execution controls
                <>
                  <Button
                    onClick={async () => {
                      if (autoExecutionStatus.isRunning) {
                        console.log('🛑 Stopping automatic execution...');
                        await automaticExecutionService.stopAutomaticExecution();
                      } else {
                        console.log('🚀 Starting automatic execution...');
                        await automaticExecutionService.startAutomaticExecution();
                      }
                      // Force re-render
                      setOrchestrationResult(prev => ({ ...prev! }));
                    }}
                    disabled={!micromanagerAgent.getSequentialWorkflowStatus().canStartNext && !autoExecutionStatus.isRunning}
                    className={`flex-1 ${autoExecutionStatus.isRunning ? 'bg-red-600 hover:bg-red-700' : 'bg-green-600 hover:bg-green-700'}`}
                  >
                    {autoExecutionStatus.isRunning ? (
                      <>
                        <Square className="h-4 w-4 mr-2" />
                        Stop Auto-Execution
                      </>
                    ) : (
                      <>
                        <Play className="h-4 w-4 mr-2" />
                        Start Auto-Execution
                      </>
                    )}
                  </Button>

                  {autoExecutionStatus.isRunning && (
                    <Button
                      variant="outline"
                      onClick={async () => {
                        console.log('⏸️ Pausing for user intervention...');
                        await automaticExecutionService.stopAutomaticExecution();
                        setAutoExecutionEnabled(false);
                        // Force re-render
                        setOrchestrationResult(prev => ({ ...prev! }));
                      }}
                      className="flex-1"
                    >
                      <Pause className="h-4 w-4 mr-2" />
                      Pause & Take Control
                    </Button>
                  )}
                </>
              ) : (
                // ✅ Manual execution controls
                <>
                  <Button
                    onClick={async () => {
                      console.log('🚀 Starting next sequential task...');
                      const result = await micromanagerAgent.startNextSequentialTask();
                      console.log('Sequential task result:', result);

                      // ✅ PHASE 3: Initialize live coding for the started task
                      if (result.success && result.taskStarted) {
                        liveCodingService.showProgress(
                          result.taskStarted.agentId,
                          result.taskStarted.taskId,
                          0,
                          `Starting task execution with ${result.taskStarted.agentId}`
                        );
                      }

                      // Force re-render
                      setOrchestrationResult(prev => ({ ...prev! }));
                    }}
                    disabled={!micromanagerAgent.getSequentialWorkflowStatus().canStartNext}
                    className="flex-1"
                  >
                    <Play className="h-4 w-4 mr-2" />
                    Start Next Task
                  </Button>
                </>
              )}

              <Button
                variant="outline"
                onClick={async () => {
                  const currentTask = micromanagerAgent.getSequentialWorkflowStatus().currentTask;
                  const currentAgent = micromanagerAgent.getSequentialWorkflowStatus().currentAgent;

                  if (currentTask && currentAgent) {
                    console.log(`✅ Completing current task: ${currentTask}`);

                    // ✅ PHASE 3: Generate completion report and show confirmation dialog
                    setConfirmationDialog(prev => ({ ...prev, isLoading: true }));

                    try {
                      const completionResult = await micromanagerAgent.completeCurrentTask(currentTask);
                      const report = completionResult.report;

                      // Create task completion report for dialog
                      const taskReport: TaskCompletionReport = {
                        taskId: currentTask,
                        agentId: currentAgent,
                        title: `Task ${currentTask}`,
                        description: 'Sequential workflow task execution',
                        filesCreated: report?.filesCreated || [],
                        filesModified: report?.filesModified || [],
                        filesDeleted: report?.filesDeleted || [],
                        executionTime: report?.executionTime || 0,
                        tokensUsed: report?.tokensUsed || 0,
                        success: completionResult.success,
                        validationResults: {
                          isValid: completionResult.success,
                          reason: completionResult.message
                        },
                        qualityScore: report?.codeQuality?.score || 0,
                        timestamp: Date.now()
                      };

                      // Show confirmation dialog
                      setConfirmationDialog({
                        isOpen: true,
                        report: taskReport,
                        isLoading: false
                      });

                    } catch (error) {
                      console.error('Error completing task:', error);
                      setConfirmationDialog(prev => ({ ...prev, isLoading: false }));
                    }
                  }
                }}
                disabled={!micromanagerAgent.getSequentialWorkflowStatus().currentTask || confirmationDialog.isLoading}
                className="flex-1"
              >
                <CheckCircle className="h-4 w-4 mr-2" />
                {confirmationDialog.isLoading ? 'Completing...' : 'Complete Current Task'}
              </Button>
            </div>

            {/* Current Task Info */}
            {micromanagerAgent.getSequentialWorkflowStatus().currentTask && (
              <div className="p-3 border rounded-lg bg-green-50">
                <div className="font-medium text-green-800 mb-1">Currently Executing</div>
                <div className="text-sm text-green-700">
                  Task: <code>{micromanagerAgent.getSequentialWorkflowStatus().currentTask}</code>
                </div>
                <div className="text-sm text-green-700">
                  Agent: <Badge variant="outline">{micromanagerAgent.getSequentialWorkflowStatus().currentAgent}</Badge>
                </div>
              </div>
            )}

            {/* Instructions */}
            <div className="text-sm text-muted-foreground p-3 bg-gray-50 rounded-lg">
              <strong>How to use:</strong>
              <ol className="list-decimal list-inside mt-1 space-y-1">
                <li>Click "Start Next Task" to begin executing the next task in the queue</li>
                <li>Monitor the task execution in the Agent Chat and Monaco Editor</li>
                <li>When satisfied with the output, click "Complete Current Task"</li>
                <li>Review the completion report and validation results</li>
                <li>Repeat for the next task in the sequence</li>
              </ol>
            </div>
          </CardContent>
        </Card>
      )}

      {/* ✅ PHASE 3: User Confirmation Dialog */}
      {confirmationDialog.report && (
        <UserConfirmationDialog
          isOpen={confirmationDialog.isOpen}
          onClose={() => setConfirmationDialog({ isOpen: false, report: null, isLoading: false })}
          onDecision={async (decision: UserDecision) => {
            console.log('User decision:', decision);

            try {
              switch (decision.action) {
                case 'proceed':
                  // Complete the task and move to next
                  console.log('✅ User approved - proceeding to next task');
                  liveCodingService.completeWork(
                    confirmationDialog.report!.agentId,
                    confirmationDialog.report!.taskId,
                    'current-work'
                  );
                  // Force re-render to update workflow status
                  setOrchestrationResult(prev => ({ ...prev! }));
                  break;

                case 'retry':
                  console.log('🔄 User requested retry');
                  // Could implement retry logic here
                  break;

                case 'modify':
                  console.log('✏️ User requested modifications');
                  // Could implement modification request logic here
                  break;

                case 'cancel':
                  console.log('❌ User cancelled workflow');
                  await sequentialExecutionController.deactivateAllAgents();
                  sequentialExecutionController.clearQueue();
                  break;
              }

              // Close dialog
              setConfirmationDialog({ isOpen: false, report: null, isLoading: false });

            } catch (error) {
              console.error('Error handling user decision:', error);
            }
          }}
          report={confirmationDialog.report}
          isLoading={confirmationDialog.isLoading}
        />
      )}
    </div>
  );
};
