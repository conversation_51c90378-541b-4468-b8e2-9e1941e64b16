// components/settings/settings-sync-service.ts

import { SettingsManager, AllSettings } from './settings-manager';

/**
 * ✅ Settings Synchronization Service
 * Handles real-time synchronization of settings between main and floating windows
 */
export class SettingsSyncService {
  private settingsManager: SettingsManager;
  private isElectron: boolean;
  private listeners: Set<(settings: AllSettings) => void> = new Set();
  private isApplyingUpdate = false; // Flag to prevent IPC feedback loops

  constructor(settingsManager: SettingsManager) {
    this.settingsManager = settingsManager;
    this.isElectron = typeof window !== 'undefined' && !!window.electronAPI;

    console.log('🔄 SettingsSyncService: Initializing', { isElectron: this.isElectron });

    if (this.isElectron) {
      this.setupIpcListeners();
      this.setupSettingsListener();
      console.log('✅ SettingsSyncService: IPC listeners and settings listener set up');
    } else {
      console.log('⚠️ SettingsSyncService: Not in Electron environment, sync disabled');
    }
  }

  /**
   * Set up IPC listeners for cross-window synchronization
   */
  private setupIpcListeners(): void {
    if (!this.isElectron || !window.electronAPI?.ipc) return;

    // Listen for settings updates from other windows
    window.electronAPI.ipc.on('settings-state-update', (settingsData: AllSettings) => {
      console.log('🔄 SettingsSyncService: Received settings update from another window');

      // Use a flag to prevent re-broadcasting this update
      this.isApplyingUpdate = true;
      try {
        // Apply the settings update silently to prevent feedback loops
        this.settingsManager.applySettingsSnapshotSilent(settingsData);

        // Notify local listeners
        this.notifyListeners(settingsData);
      } finally {
        this.isApplyingUpdate = false;
      }
    });

    // Listen for panel change requests from other windows
    window.electronAPI.ipc.on('settings-panel-changed', (panelId: string) => {
      console.log('🎯 SettingsSyncService: Panel changed in another window:', panelId);
      
      // Dispatch custom event for UI components to listen to
      window.dispatchEvent(new CustomEvent('settings-panel-changed', { 
        detail: { panelId } 
      }));
    });

    // Handle requests for current settings state
    window.electronAPI.ipc.on('request-settings-state', (requestData: any) => {
      console.log('📤 SettingsSyncService: Settings state requested by another window');
      
      // Send current settings state
      const currentSettings = this.settingsManager.getSettings();
      window.electronAPI.ipc.send('settings-state-update', currentSettings);
    });
  }

  /**
   * Set up listener for local settings changes to broadcast to other windows
   */
  private setupSettingsListener(): void {
    const handleSettingsChange = (newSettings: AllSettings) => {
      // If this change is a result of an IPC update, do not broadcast it again.
      if (this.isApplyingUpdate) {
        return;
      }

      console.log('📡 SettingsSyncService: Broadcasting settings change to other windows', {
        hasApiKeys: Object.keys(newSettings.apiKeys || {}).length > 0,
        systemTheme: newSettings.system?.theme,
        agentCount: newSettings.agents?.length || 0
      });

      if (this.isElectron && window.electronAPI?.ipc) {
        window.electronAPI.ipc.send('settings-state-update', newSettings);
      }

      // Notify local listeners
      this.notifyListeners(newSettings);
    };

    this.settingsManager.onSettingsChange(handleSettingsChange);
    console.log('✅ SettingsSyncService: Settings change listener registered');
  }

  /**
   * Broadcast panel change to other windows
   */
  public broadcastPanelChange(panelId: string): void {
    console.log('📡 SettingsSyncService: Broadcasting panel change:', panelId);
    
    if (this.isElectron && window.electronAPI?.ipc) {
      window.electronAPI.ipc.send('settings-panel-changed', panelId);
    }
  }

  /**
   * Request initial settings state from other windows
   */
  public requestInitialState(): void {
    console.log('📥 SettingsSyncService: Requesting initial settings state');
    
    if (this.isElectron && window.electronAPI?.ipc) {
      window.electronAPI.ipc.send('request-settings-state', {
        requestId: `settings-${Date.now()}`,
        windowId: `window-${Date.now()}`
      });
    }
  }

  /**
   * Add listener for settings changes
   */
  public addListener(listener: (settings: AllSettings) => void): void {
    this.listeners.add(listener);
  }

  /**
   * Remove listener for settings changes
   */
  public removeListener(listener: (settings: AllSettings) => void): void {
    this.listeners.delete(listener);
  }

  /**
   * Notify all listeners of settings changes
   */
  private notifyListeners(settings: AllSettings): void {
    this.listeners.forEach(listener => {
      try {
        listener(settings);
      } catch (error) {
        console.error('❌ SettingsSyncService: Error in settings listener:', error);
      }
    });
  }

  /**
   * Clean up listeners and IPC handlers
   */
  public cleanup(): void {
    console.log('🧹 SettingsSyncService: Cleaning up');

    this.listeners.clear();

    if (this.isElectron && window.electronAPI?.ipc) {
      try {
        // Remove IPC listeners - check if off method exists
        if (typeof window.electronAPI.ipc.off === 'function') {
          window.electronAPI.ipc.off('settings-state-update');
          window.electronAPI.ipc.off('settings-panel-changed');
          window.electronAPI.ipc.off('request-settings-state');
        } else {
          console.warn('⚠️ SettingsSyncService: IPC off method not available');
        }
      } catch (error) {
        console.error('❌ SettingsSyncService: Failed to remove IPC listeners:', error);
      }
    }
  }
}

/**
 * ✅ Global Settings Sync Service Instance
 */
let globalSettingsSyncService: SettingsSyncService | null = null;

export const getGlobalSettingsSyncService = (settingsManager: SettingsManager): SettingsSyncService => {
  if (!globalSettingsSyncService) {
    globalSettingsSyncService = new SettingsSyncService(settingsManager);
  }
  return globalSettingsSyncService;
};

export const cleanupGlobalSettingsSyncService = (): void => {
  if (globalSettingsSyncService) {
    globalSettingsSyncService.cleanup();
    globalSettingsSyncService = null;
  }
};