"use client"

import { useEffect, useRef } from 'react';
import { useTheme } from 'next-themes';
import { useSystemSettings } from './settings-context';

/**
 * ✅ Theme Bridge Component
 * Synchronizes SystemSettings.theme with next-themes
 * Ensures theme changes in System tab immediately affect UI
 *
 * 🔧 SURGICAL FIX: Handles initialization race conditions
 */
export const ThemeBridge: React.FC = () => {
  const { systemSettings, updateSystemSettings } = useSystemSettings();
  const { setTheme, theme: currentTheme } = useTheme();
  const lastSystemTheme = useRef<string | null>(null);
  const isInitialized = useRef<boolean>(false);

  // ✅ SURGICAL FIX: Initialize theme from SettingsManager on first load
  useEffect(() => {
    if (!isInitialized.current && systemSettings.theme && currentTheme) {
      console.log(`🎨 Theme bridge initialization: SettingsManager(${systemSettings.theme}) vs next-themes(${currentTheme})`);

      // If SettingsManager has a different theme than next-themes, use SettingsManager as source of truth
      if (systemSettings.theme !== currentTheme) {
        console.log(`🔧 SURGICAL FIX: Syncing next-themes to SettingsManager theme: ${systemSettings.theme}`);
        setTheme(systemSettings.theme);
      } else if (currentTheme !== 'system' && systemSettings.theme === 'system') {
        // If next-themes has a specific theme but SettingsManager says system, update SettingsManager
        console.log(`🔧 SURGICAL FIX: Syncing SettingsManager to next-themes theme: ${currentTheme}`);
        updateSystemSettings({ theme: currentTheme as 'light' | 'dark' | 'system' });
      }

      lastSystemTheme.current = systemSettings.theme;
      isInitialized.current = true;
    }
  }, [systemSettings.theme, currentTheme, setTheme, updateSystemSettings]);

  // ✅ Handle subsequent theme changes from SettingsManager
  useEffect(() => {
    if (isInitialized.current && systemSettings.theme !== lastSystemTheme.current) {
      console.log(`🎨 Theme bridge: ${lastSystemTheme.current} → ${systemSettings.theme}`);

      // Validate theme value before applying
      if (['light', 'dark', 'system'].includes(systemSettings.theme)) {
        setTheme(systemSettings.theme);
        lastSystemTheme.current = systemSettings.theme;
      } else {
        console.warn(`Invalid theme value: ${systemSettings.theme}`);
      }
    }
  }, [systemSettings.theme, setTheme]);

  // This component doesn't render anything - it's just for side effects
  return null;
};
