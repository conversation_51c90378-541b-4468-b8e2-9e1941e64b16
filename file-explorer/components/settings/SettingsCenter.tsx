// components/settings/SettingsCenter.tsx
"use client"

import React, { useState, Suspense, lazy, useEffect } from 'react';
import { SettingsManager } from './settings-manager';
import { CompleteAgentManager } from '../agents/agent-manager-complete';
import { getGlobalSettingsSyncService } from './settings-sync-service';
import PerformanceMonitor from './performance-monitor';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import {
  Settings,
  Monitor,
  Bot,
  Key,
  DollarSign,
  Shield,
  Code,
  Terminal,
  Brain,
  TestTube,
  Search,
  ExternalLink
} from 'lucide-react';
import { cn } from '@/lib/utils';

// ✅ Regular imports for core panels to avoid chunk loading issues in dev mode
import SystemSettingsPanel from './panels/SystemSettingsPanel';
import TestingSettingsPanel from './panels/TestingSettingsPanel';

// ✅ Lazy load non-core settings panels for performance
const AgentSettingsPanel = lazy(() => import('./panels/AgentSettingsPanel'));
const APIKeySettingsPanel = lazy(() => import('./panels/APIKeySettingsPanel'));
const CostSettingsPanel = lazy(() => import('./panels/CostSettingsPanel'));
const PrivacySettingsPanel = lazy(() => import('./panels/PrivacySettingsPanel'));
const EditorSettingsPanel = lazy(() => import('./panels/EditorSettingsPanel'));
const TerminalSettingsPanel = lazy(() => import('./panels/TerminalSettingsPanel'));
const TaskmasterSettingsPanel = lazy(() => import('./panels/TaskmasterSettingsPanel'));

interface SettingsCenterProps {
  settingsManager: SettingsManager;
  agentManager?: CompleteAgentManager;
  onClose: () => void;
}

interface SettingsCategory {
  id: string;
  title: string;
  icon: React.ComponentType<{ className?: string }>;
  description: string;
  component: React.ComponentType<any>;
  badge?: string;
  devOnly?: boolean;
}

const LoadingSpinner = () => (
  <div className="flex items-center justify-center h-64">
    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
  </div>
);

export const SettingsCenter: React.FC<SettingsCenterProps> = ({
  settingsManager,
  agentManager,
  onClose
}) => {
  const [activeCategory, setActiveCategory] = useState('system');
  const [searchQuery, setSearchQuery] = useState('');
  const { toast } = useToast();

  // ✅ Initialize sync service
  const syncService = getGlobalSettingsSyncService(settingsManager);

  // ✅ Handle opening floating settings window
  const handleOpenFloatingWindow = () => {
    if (typeof window !== 'undefined' && window.electronAPI?.openSettingsWindow) {
      window.electronAPI.openSettingsWindow();
    } else {
      toast({
        title: "Electron API Not Available",
        description: "Floating settings window can only be opened in Electron desktop app.",
        variant: "destructive",
      });
    }
  };

  // ✅ Handle category change with sync
  const handleCategoryChange = (categoryId: string) => {
    setActiveCategory(categoryId);
    syncService.broadcastPanelChange(categoryId);
  };

  // ✅ Listen for panel changes from other windows
  useEffect(() => {
    const handlePanelChange = (event: CustomEvent) => {
      const { panelId } = event.detail;
      console.log('🎯 SettingsCenter: Received panel change from another window:', panelId);
      setActiveCategory(panelId);
    };

    window.addEventListener('settings-panel-changed', handlePanelChange as EventListener);
    return () => {
      window.removeEventListener('settings-panel-changed', handlePanelChange as EventListener);
    };
  }, []);

  // ✅ Settings categories configuration
  const categories: SettingsCategory[] = [
    {
      id: 'system',
      title: 'System',
      icon: Monitor,
      description: 'Theme, auto-save, and system preferences',
      component: SystemSettingsPanel
    },
    {
      id: 'agents',
      title: 'Agents',
      icon: Bot,
      description: 'AI agent configuration and models',
      component: AgentSettingsPanel
    },
    {
      id: 'api-keys',
      title: 'API Keys',
      icon: Key,
      description: 'Manage API keys for AI providers',
      component: APIKeySettingsPanel
    },
    {
      id: 'cost',
      title: 'Cost Management',
      icon: DollarSign,
      description: 'Budget limits and cost tracking',
      component: CostSettingsPanel
    },
    {
      id: 'privacy',
      title: 'Privacy',
      icon: Shield,
      description: 'Data sharing and privacy controls',
      component: PrivacySettingsPanel
    },
    {
      id: 'editor',
      title: 'Editor',
      icon: Code,
      description: 'Code editor preferences and behavior',
      component: EditorSettingsPanel
    },
    {
      id: 'terminal',
      title: 'Terminal',
      icon: Terminal,
      description: 'Terminal appearance and behavior',
      component: TerminalSettingsPanel
    },
    {
      id: 'taskmaster',
      title: 'Taskmaster',
      icon: Brain,
      description: 'AI task orchestration settings',
      component: TaskmasterSettingsPanel,
      badge: 'AI'
    },
    {
      id: 'testing',
      title: 'Testing',
      icon: TestTube,
      description: 'Development and testing tools',
      component: TestingSettingsPanel,
      badge: 'DEV',
      devOnly: true
    }
  ];

  // ✅ Filter categories based on search and dev mode
  const filteredCategories = categories.filter(category => {
    // Hide dev-only categories in production
    if (category.devOnly && process.env.NODE_ENV === 'production') {
      return false;
    }
    
    // Hide testing category if no agentManager
    if (category.id === 'testing' && !agentManager) {
      return false;
    }

    // Filter by search query
    if (searchQuery) {
      return category.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
             category.description.toLowerCase().includes(searchQuery.toLowerCase());
    }

    return true;
  });

  const activePanel = categories.find(cat => cat.id === activeCategory);

  return (
    <div className="flex h-full bg-background">
      {/* ✅ Sidebar Navigation */}
      <div className="w-80 border-r bg-muted/30 flex flex-col">
        <div className="p-4 border-b flex-shrink-0">
          <div className="flex items-center gap-2 mb-4">
            <Settings className="h-5 w-5" />
            <h1 className="text-lg font-semibold">Settings</h1>
          </div>

          {/* ✅ Search Input */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search settings..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-9"
            />
          </div>
        </div>

        {/* ✅ Category List with proper scrolling */}
        <ScrollArea className="flex-1 min-h-0">
          <div className="p-2">
            {filteredCategories.map((category) => {
              const Icon = category.icon;
              const isActive = activeCategory === category.id;

              return (
                <button
                  key={category.id}
                  onClick={() => handleCategoryChange(category.id)}
                  className={cn(
                    "w-full text-left p-3 rounded-lg transition-colors mb-1",
                    "hover:bg-accent hover:text-accent-foreground",
                    isActive && "bg-accent text-accent-foreground"
                  )}
                >
                  <div className="flex items-center gap-3">
                    <Icon className="h-4 w-4 flex-shrink-0" />
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2">
                        <span className="font-medium truncate">{category.title}</span>
                        {category.badge && (
                          <Badge variant="secondary" className="text-xs">
                            {category.badge}
                          </Badge>
                        )}
                      </div>
                      <p className="text-sm text-muted-foreground truncate">
                        {category.description}
                      </p>
                    </div>
                  </div>
                </button>
              );
            })}
          </div>
        </ScrollArea>
      </div>

      {/* ✅ Settings Panel Content */}
      <div className="flex-1 flex flex-col min-w-0">
        {/* ✅ Panel Header */}
        <div className="border-b p-4 flex-shrink-0">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              {activePanel && <activePanel.icon className="h-5 w-5" />}
              <div>
                <h2 className="text-xl font-semibold">{activePanel?.title}</h2>
                <p className="text-sm text-muted-foreground">{activePanel?.description}</p>
              </div>
            </div>

            {/* ✅ Floating Window Button */}
            <Button
              variant="outline"
              size="sm"
              onClick={handleOpenFloatingWindow}
              className="flex items-center gap-2"
            >
              <ExternalLink className="h-4 w-4" />
              Open in Window
            </Button>
          </div>
        </div>

        {/* ✅ Panel Content with proper scrolling */}
        <div className="flex-1 overflow-hidden min-h-0">
          <ScrollArea className="h-full">
            <div className="p-6">
              <Suspense fallback={<LoadingSpinner />}>
                {activePanel && (
                  <activePanel.component
                    settingsManager={settingsManager}
                    agentManager={agentManager}
                    onClose={onClose}
                  />
                )}
              </Suspense>
            </div>
          </ScrollArea>
        </div>
      </div>

      {/* ✅ PERFORMANCE FIX: Performance Monitor (temporarily disabled to prevent infinite loops) */}
      {process.env.NODE_ENV === 'development' && false && <PerformanceMonitor />}
    </div>
  );
};

export default SettingsCenter;
