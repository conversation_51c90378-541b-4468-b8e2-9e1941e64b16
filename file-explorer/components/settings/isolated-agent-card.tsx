// components/settings/isolated-agent-card.tsx
import React, { useEffect, useMemo, useState, useCallback, useRef } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { Info } from 'lucide-react';
import { AgentSettings } from './settings-manager';
import {
  TemperatureSlider,
  ToggleSwitch,
  AgentProviderSelect,
  AgentModelSelect,
  AgentMaxTokensInput,
  AgentCustomPromptTextarea
} from './isolated-agent-controls';

/**
 * Temperature compatibility check (from Task 26)
 */
function getTemperatureSupportInfo(provider: string, modelId: string): {
  supported: boolean;
  reason: string;
} {
  const TEMPERATURE_COMPATIBILITY: Record<string, { supported: boolean; excludedModels?: string[]; reason: string }> = {
    openai: { supported: true, reason: 'OpenAI API supports temperature for all models' },
    anthropic: { supported: true, reason: 'Anthropic Claude API supports temperature for all models' },
    openrouter: { supported: true, reason: 'OpenRouter proxies temperature to underlying providers' },
    azure: { supported: true, reason: 'Azure OpenAI uses same API as OpenAI' },
    google: { supported: true, reason: 'Google Gemini API supports temperature parameter' },
    deepseek: {
      supported: true,
      excludedModels: ['deepseek-reasoner'],
      reason: 'DeepSeek API supports temperature except for reasoning models'
    },
    fireworks: { supported: true, reason: 'Fireworks AI API supports temperature for all models' }
  };

  const config = TEMPERATURE_COMPATIBILITY[provider];

  if (!config) {
    return { supported: false, reason: 'Temperature support unknown for this provider' };
  }

  if (!config.supported) {
    return { supported: false, reason: config.reason };
  }

  // Check if model is specifically excluded
  if (config.excludedModels && config.excludedModels.some(excluded =>
    modelId.toLowerCase().includes(excluded.toLowerCase())
  )) {
    return { supported: false, reason: 'This specific model does not support temperature tuning' };
  }

  return { supported: true, reason: config.reason };
}

interface IsolatedAgentCardProps {
  agent: AgentSettings;
  providers: string[];
  getModelsForProvider: (provider: string) => string[];
  updateAgent: (agentId: string, updates: Partial<AgentSettings>) => void;
}

/**
 * Completely isolated AgentCard component with local state management
 * Each agent row manages its own state and only commits changes when needed
 * Follows fundamental principles: UI inputs use local state first
 */
export const IsolatedAgentCard = React.memo<IsolatedAgentCardProps>(({
  agent,
  providers,
  getModelsForProvider,
  updateAgent
}) => {
  // ✅ PERFORMANCE FIX: Consolidated state object to reduce useState calls
  const [localState, setLocalState] = useState({
    enabled: agent.enabled,
    temperature: agent.temperature,
    provider: agent.provider,
    model: agent.model,
    maxTokens: agent.maxTokens,
    customPrompt: agent.customPrompt || ''
  });

  // ✅ PERFORMANCE FIX: Memoize agent hash to detect actual changes
  const agentHash = useMemo(() =>
    `${agent.enabled}:${agent.temperature}:${agent.provider}:${agent.model}:${agent.maxTokens}:${agent.customPrompt || ''}`,
    [agent.enabled, agent.temperature, agent.provider, agent.model, agent.maxTokens, agent.customPrompt]
  );

  // ✅ PERFORMANCE FIX: Only sync when agent actually changes (not on every render)
  const lastAgentHash = useRef<string>('');
  useEffect(() => {
    if (agentHash !== lastAgentHash.current) {
      lastAgentHash.current = agentHash;
      setLocalState({
        enabled: agent.enabled,
        temperature: agent.temperature,
        provider: agent.provider,
        model: agent.model,
        maxTokens: agent.maxTokens,
        customPrompt: agent.customPrompt || ''
      });
    }
  }, [agentHash, agent]);

  // ✅ PERFORMANCE FIX: Optimized handlers using consolidated state
  const handleToggle = useCallback(() => {
    console.time('toggle-latency');
    const newEnabled = !localState.enabled;
    setLocalState(prev => ({ ...prev, enabled: newEnabled }));
    updateAgent(agent.id, { enabled: newEnabled });
    console.timeEnd('toggle-latency');
  }, [localState.enabled, agent.id, updateAgent]);

  const handleTempChange = useCallback((value: number) => {
    console.time('slider-latency');
    setLocalState(prev => ({ ...prev, temperature: value }));
    console.timeEnd('slider-latency');
  }, []);

  const commitTempChange = useCallback(() => {
    console.time('slider-commit');
    updateAgent(agent.id, { temperature: localState.temperature });
    console.timeEnd('slider-commit');
  }, [localState.temperature, agent.id, updateAgent]);

  const handleProviderChange = useCallback((newProvider: string) => {
    const provider = newProvider as AgentSettings['provider'];
    setLocalState(prev => ({ ...prev, provider }));
    updateAgent(agent.id, { provider });
  }, [agent.id, updateAgent]);

  const handleModelChange = useCallback((newModel: string) => {
    setLocalState(prev => ({ ...prev, model: newModel }));
    updateAgent(agent.id, { model: newModel });
  }, [agent.id, updateAgent]);

  const handleMaxTokensChange = useCallback((newMaxTokens: number) => {
    setLocalState(prev => ({ ...prev, maxTokens: newMaxTokens }));
    updateAgent(agent.id, { maxTokens: newMaxTokens });
  }, [agent.id, updateAgent]);

  const handleCustomPromptChange = useCallback((newPrompt: string) => {
    setLocalState(prev => ({ ...prev, customPrompt: newPrompt }));
    updateAgent(agent.id, { customPrompt: newPrompt });
  }, [agent.id, updateAgent]);

  // ✅ PERFORMANCE FIX: Memoize temperature support calculation using local state
  const temperatureSupport = useMemo(() =>
    getTemperatureSupportInfo(localState.provider, localState.model),
    [localState.provider, localState.model]
  );

  // ✅ PERFORMANCE FIX: Memoize available models for current provider using local state
  const availableModels = useMemo(() =>
    getModelsForProvider(localState.provider),
    [localState.provider, getModelsForProvider]
  );

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            {agent.name}
            <Badge variant={localState.enabled ? 'default' : 'secondary'}>
              {localState.enabled ? 'Enabled' : 'Disabled'}
            </Badge>
          </CardTitle>
          <ToggleSwitch
            enabled={localState.enabled}
            onToggle={handleToggle}
          />
        </div>
        <CardDescription>
          Capabilities: {agent.capabilities.join(', ')}
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Provider, Model, Max Tokens Grid */}
        <div className="grid grid-cols-3 gap-4">
          <div className="space-y-2">
            <Label>Provider</Label>
            <AgentProviderSelect
              agentId={agent.id}
              value={localState.provider}
              providers={providers}
              onChange={(_, newProvider) => handleProviderChange(newProvider)}
            />
          </div>

          <div className="space-y-2">
            <Label>Model</Label>
            <AgentModelSelect
              agentId={agent.id}
              value={localState.model}
              models={availableModels}
              onChange={(_, newModel) => handleModelChange(newModel)}
            />
          </div>

          <div className="space-y-2">
            <Label>Max Tokens</Label>
            <AgentMaxTokensInput
              agentId={agent.id}
              value={localState.maxTokens}
              onChange={(_, newMaxTokens) => handleMaxTokensChange(newMaxTokens)}
            />
          </div>
        </div>

        {/* Temperature Control with Conditional Display */}
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <Label>Temperature</Label>
            {!temperatureSupport.supported && (
              <Tooltip>
                <TooltipTrigger>
                  <Info className="h-4 w-4 text-muted-foreground" />
                </TooltipTrigger>
                <TooltipContent>
                  <p className="max-w-xs">{temperatureSupport.reason}</p>
                </TooltipContent>
              </Tooltip>
            )}
          </div>

          {temperatureSupport.supported ? (
            <TemperatureSlider
              value={localState.temperature}
              onChange={handleTempChange}
              onCommit={commitTempChange}
              min={0}
              max={localState.provider === 'anthropic' ? 1 : 2}
              step={0.1}
              provider={localState.provider}
            />
          ) : (
            <div className="flex items-center gap-2 p-3 bg-muted rounded-md">
              <Info className="h-4 w-4 text-muted-foreground flex-shrink-0" />
              <span className="text-sm text-muted-foreground">
                Temperature not supported for this model
              </span>
            </div>
          )}
        </div>

        {/* Custom Prompt (if defined) */}
        {localState.customPrompt !== undefined && (
          <div className="space-y-2">
            <Label>Custom Prompt</Label>
            <AgentCustomPromptTextarea
              agentId={agent.id}
              value={localState.customPrompt}
              onChange={(_, newPrompt) => handleCustomPromptChange(newPrompt)}
            />
          </div>
        )}
      </CardContent>
    </Card>
  );
});

IsolatedAgentCard.displayName = 'IsolatedAgentCard';
