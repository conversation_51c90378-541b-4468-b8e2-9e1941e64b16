// components/settings/panels/APIKeySettingsPanel.tsx
import React from 'react';
import { SettingsManager } from '../settings-manager';
import { ApiKeysSettings } from '../api-keys-settings';
import { useSettings } from '../settings-context';

interface APIKeySettingsPanelProps {
  settingsManager: SettingsManager;
}

/**
 * ✅ API Keys Settings Panel
 * Wraps the existing APIKeysSettings for the centralized settings UI
 */
const APIKeySettingsPanel: React.FC<APIKeySettingsPanelProps> = ({ settingsManager }) => {
  const { settings } = useSettings();

  const updateAPIKey = (provider: string, key: string) => {
    settingsManager.updateAPIKey(provider, key);
  };

  const removeAPIKey = (provider: string) => {
    settingsManager.removeAPIKey(provider);
  };

  return (
    <div className="p-6">
      <ApiKeysSettings
        settingsManager={settingsManager}
        settings={settings}
      />
    </div>
  );
};

export default APIKeySettingsPanel;