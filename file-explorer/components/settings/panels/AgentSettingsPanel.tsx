// components/settings/panels/AgentSettingsPanel.tsx
import React, { useState, useCallback, useMemo, useEffect } from 'react';
import { SettingsManager } from '../settings-manager';
import { CompleteAgentManager } from '../../agents/agent-manager-complete';
import { IsolatedAgentCard } from '../isolated-agent-card';
import { useSettings } from '../settings-context';
import { getAgentProviders, getProviderModels } from '../../agents/llm-provider-registry';
import { Button } from '../../ui/button';
import { Save, AlertCircle, RotateCcw } from 'lucide-react';
import { Alert, AlertDescription } from '../../ui/alert';

interface AgentSettingsPanelProps {
  settingsManager: SettingsManager;
  agentManager?: CompleteAgentManager;
}

/**
 * ✅ Agent Settings Panel with Manual Save Control
 * Uses the same pattern as the main settings UI with IsolatedAgentCard components
 * Now includes manual save functionality instead of auto-save
 */
const AgentSettingsPanel: React.FC<AgentSettingsPanelProps> = ({
  settingsManager,
  agentManager
}) => {
  const { settings } = useSettings();
  const [isSaving, setIsSaving] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // ✅ PERFORMANCE FIX: Memoize provider data to prevent recreation
  const providerData = useMemo(() => ({
    providers: getAgentProviders(),
    getModelsForProvider: getProviderModels
  }), []);

  // ✅ PERFORMANCE FIX: Debounced change detection to reduce expensive checks
  const checkForChanges = useCallback(() => {
    const hasChanges = settingsManager.hasUnsavedAgentChanges();
    setHasUnsavedChanges(hasChanges);
  }, [settingsManager]);

  // ✅ PERFORMANCE FIX: Debounced update function
  const updateAgent = useCallback((agentId: string, updates: any) => {
    settingsManager.updateAgentSettings(agentId, updates);

    // Debounce change checking to avoid excessive calls
    setTimeout(checkForChanges, 100);
  }, [settingsManager, checkForChanges]);

  // ✅ PERFORMANCE FIX: Check for changes on mount and when settings change
  useEffect(() => {
    // Use setTimeout to avoid potential setState during render
    const timeoutId = setTimeout(checkForChanges, 0);
    return () => clearTimeout(timeoutId);
  }, [settings.agents, checkForChanges]);

  // ✅ MANUAL SAVE FIX: Handle manual save
  const handleSave = useCallback(async () => {
    setIsSaving(true);
    try {
      settingsManager.saveAgentSettings();
      setHasUnsavedChanges(false);
      console.log('✅ Agent settings saved successfully');
    } catch (error) {
      console.error('❌ Failed to save agent settings:', error);
    } finally {
      setIsSaving(false);
    }
  }, [settingsManager]);

  // ✅ MANUAL SAVE FIX: Handle discard changes
  const handleDiscardChanges = useCallback(() => {
    try {
      // Discard unsaved changes and revert to last saved state
      settingsManager.discardAgentChanges();
      setHasUnsavedChanges(false);
      console.log('✅ Agent settings changes discarded');
    } catch (error) {
      console.error('❌ Failed to discard changes:', error);
    }
  }, [settingsManager]);

  return (
    <div className="space-y-6">
      {/* ✅ MANUAL SAVE FIX: Unsaved changes alert and save button */}
      {hasUnsavedChanges && (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            You have unsaved changes to your agent settings. Click "Save Agent Settings" to persist your changes.
          </AlertDescription>
        </Alert>
      )}

      {/* ✅ MANUAL SAVE FIX: Save and discard buttons */}
      <div className="flex justify-end gap-3">
        {hasUnsavedChanges && (
          <Button
            onClick={handleDiscardChanges}
            disabled={isSaving}
            variant="outline"
            className="min-w-[140px]"
          >
            <RotateCcw className="h-4 w-4 mr-2" />
            Discard Changes
          </Button>
        )}
        <Button
          onClick={handleSave}
          disabled={isSaving || !hasUnsavedChanges}
          variant={hasUnsavedChanges ? "default" : "outline"}
          className="min-w-[160px]"
        >
          <Save className="h-4 w-4 mr-2" />
          {isSaving ? 'Saving...' : 'Save Agent Settings'}
        </Button>
      </div>

      <div className="grid gap-4">
        {settings.agents.map((agent) => (
          <IsolatedAgentCard
            key={agent.id}
            agent={agent}
            providers={providerData.providers}
            getModelsForProvider={providerData.getModelsForProvider}
            updateAgent={updateAgent}
          />
        ))}
      </div>
    </div>
  );
};

export default AgentSettingsPanel;
