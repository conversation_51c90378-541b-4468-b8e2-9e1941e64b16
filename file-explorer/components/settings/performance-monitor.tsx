// components/settings/performance-monitor.tsx
// ✅ PERFORMANCE FIX: Real-time performance monitoring for Settings UI

import React, { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Activity, Zap, Clock, MemoryStick } from 'lucide-react';

interface PerformanceMetrics {
  renderTime: number;
  memoryUsage: number;
  reRenderCount: number;
  lastUpdate: number;
  fps: number;
  settingsOperations: number;
}

interface PerformanceMonitorProps {
  enabled?: boolean;
  className?: string;
}

/**
 * ✅ PERFORMANCE FIX: Performance Monitor Component
 * Tracks real-time performance metrics for Settings UI
 */
export const PerformanceMonitor: React.FC<PerformanceMonitorProps> = ({
  enabled = process.env.NODE_ENV === 'development',
  className = ''
}) => {
  // ✅ CRITICAL FIX: Safety check to prevent issues in production
  if (process.env.NODE_ENV === 'production' || !enabled) {
    return null;
  }
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    renderTime: 0,
    memoryUsage: 0,
    reRenderCount: 0,
    lastUpdate: Date.now(),
    fps: 60,
    settingsOperations: 0
  });

  const [isVisible, setIsVisible] = useState(false);
  const renderStartTime = useRef<number>(0);
  const frameCount = useRef<number>(0);
  const lastFrameTime = useRef<number>(Date.now());

  // ✅ CRITICAL FIX: Track render performance without causing infinite loops
  useEffect(() => {
    if (!enabled) return;

    renderStartTime.current = performance.now();

    return () => {
      const renderTime = performance.now() - renderStartTime.current;
      // Use setTimeout to avoid setState during render cycle
      setTimeout(() => {
        setMetrics(prev => ({
          ...prev,
          renderTime,
          reRenderCount: prev.reRenderCount + 1,
          lastUpdate: Date.now()
        }));
      }, 0);
    };
  }, [enabled]); // ✅ CRITICAL FIX: Add enabled dependency to prevent infinite loop

  // Track FPS and memory usage
  useEffect(() => {
    if (!enabled) return;

    const interval = setInterval(() => {
      const now = Date.now();
      const deltaTime = now - lastFrameTime.current;
      frameCount.current++;

      if (deltaTime >= 1000) {
        const fps = Math.round((frameCount.current * 1000) / deltaTime);
        frameCount.current = 0;
        lastFrameTime.current = now;

        // Get memory usage if available
        const memoryInfo = (performance as any).memory;
        const memoryUsage = memoryInfo ? Math.round(memoryInfo.usedJSHeapSize / 1024 / 1024) : 0;

        setMetrics(prev => ({
          ...prev,
          fps,
          memoryUsage,
          lastUpdate: now
        }));
      }
    }, 100);

    return () => clearInterval(interval);
  }, [enabled]);

  // ✅ CRITICAL FIX: Track settings operations without causing infinite loops
  useEffect(() => {
    if (!enabled) return;

    const originalConsoleTime = console.time;
    const originalConsoleTimeEnd = console.timeEnd;

    console.time = (label?: string) => {
      if (label && (label.includes('toggle') || label.includes('slider') || label.includes('settings'))) {
        // Use setTimeout to avoid setState during render cycle
        setTimeout(() => {
          setMetrics(prev => ({ ...prev, settingsOperations: prev.settingsOperations + 1 }));
        }, 0);
      }
      return originalConsoleTime.call(console, label);
    };

    return () => {
      console.time = originalConsoleTime;
      console.timeEnd = originalConsoleTimeEnd;
    };
  }, [enabled]);

  // ✅ CRITICAL FIX: Additional safety check
  if (!enabled) return null;

  const getPerformanceStatus = () => {
    if (metrics.renderTime > 16) return { status: 'poor', color: 'destructive' };
    if (metrics.renderTime > 8) return { status: 'fair', color: 'warning' };
    return { status: 'good', color: 'default' };
  };

  const getFPSStatus = () => {
    if (metrics.fps < 30) return { status: 'poor', color: 'destructive' };
    if (metrics.fps < 50) return { status: 'fair', color: 'warning' };
    return { status: 'good', color: 'default' };
  };

  const performanceStatus = getPerformanceStatus();
  const fpsStatus = getFPSStatus();

  return (
    <div className={`fixed bottom-4 right-4 z-50 ${className}`}>
      {!isVisible ? (
        <Button
          variant="outline"
          size="sm"
          onClick={() => setIsVisible(true)}
          className="bg-background/80 backdrop-blur-sm"
        >
          <Activity className="h-4 w-4" />
        </Button>
      ) : (
        <Card className="w-80 bg-background/95 backdrop-blur-sm border-border/50">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm flex items-center gap-2">
                <Activity className="h-4 w-4" />
                Performance Monitor
              </CardTitle>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsVisible(false)}
                className="h-6 w-6 p-0"
              >
                ×
              </Button>
            </div>
          </CardHeader>
          <CardContent className="space-y-3">
            {/* Render Performance */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">Render Time</span>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant={performanceStatus.color as any}>
                  {metrics.renderTime.toFixed(1)}ms
                </Badge>
                <span className="text-xs text-muted-foreground">
                  {performanceStatus.status}
                </span>
              </div>
            </div>

            {/* FPS */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Zap className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">FPS</span>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant={fpsStatus.color as any}>
                  {metrics.fps}
                </Badge>
                <span className="text-xs text-muted-foreground">
                  {fpsStatus.status}
                </span>
              </div>
            </div>

            {/* Memory Usage */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <MemoryStick className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">Memory</span>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="outline">
                  {metrics.memoryUsage}MB
                </Badge>
              </div>
            </div>

            {/* Re-render Count */}
            <div className="flex items-center justify-between">
              <span className="text-sm">Re-renders</span>
              <Badge variant="outline">
                {metrics.reRenderCount}
              </Badge>
            </div>

            {/* Settings Operations */}
            <div className="flex items-center justify-between">
              <span className="text-sm">Operations</span>
              <Badge variant="outline">
                {metrics.settingsOperations}
              </Badge>
            </div>

            {/* Reset Button */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => setMetrics({
                renderTime: 0,
                memoryUsage: metrics.memoryUsage,
                reRenderCount: 0,
                lastUpdate: Date.now(),
                fps: metrics.fps,
                settingsOperations: 0
              })}
              className="w-full"
            >
              Reset Metrics
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default PerformanceMonitor;
