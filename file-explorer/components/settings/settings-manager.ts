// components/settings/settings-manager.ts
import { getConfigStoreBrowser, ConfigStoreBrowser } from '../background/config-store-browser';

export interface AgentSettings {
  id: string;
  name: string;
  enabled: boolean;
  model: string;
  provider: 'openai' | 'anthropic' | 'openrouter' | 'azure' | 'google' | 'deepseek' | 'fireworks' | 'mcp';
  maxTokens: number;
  temperature: number;
  customPrompt?: string;
  capabilities: string[];
  // ✅ MCP Protocol Support
  useMCP?: boolean;
  mcpConfig?: {
    serverId: string;
    serverCommand?: string;
    serverArgs?: string[];
    fallbackToLLM?: boolean;
  };
}

export interface SystemSettings {
  theme: 'light' | 'dark' | 'system';
  autoSave: boolean;
  autoSaveInterval: number; // in seconds
  maxConcurrentTasks: number;
  defaultTimeout: number; // in milliseconds
  enableTelemetry: boolean;
  debugMode: boolean;
  testModeEnabled: boolean; // Enable stress testing and development features
}

export interface CostSettings {
  budgetLimit: number; // monthly budget in USD
  alertThreshold: number; // percentage of budget
  trackUsage: boolean;
  showCostEstimates: boolean;
  preferCheaperModels: boolean;
}

export interface PrivacySettings {
  shareUsageData: boolean;
  localOnly: boolean;
  encryptPrompts: boolean;
  clearHistoryOnExit: boolean;
  maxHistoryDays: number;
}

export interface EditorSettings {
  fontSize: number;
  fontFamily: string;
  tabSize: number;
  wordWrap: boolean;
  lineNumbers: boolean;
  minimap: boolean;
  autoFormat: boolean;
  autoComplete: boolean;
}

export interface TerminalSettings {
  theme: 'dark' | 'light' | 'system';
  fontFamily: string;
  fontSize: number;
  shell: string;
  cols: number;
  rows: number;
  scrollback: number;
  cursorBlink: boolean;
  lineHeight: number;
}

export interface RecentProject {
  name: string;
  path: string;
  lastOpened: number;
}

export interface MCPSettings {
  enabled: boolean;
  servers: Record<string, {
    name: string;
    command: string;
    args: string[];
    enabled: boolean;
    autoConnect: boolean;
  }>;
  defaultServer?: string;
  timeout: number;
  maxRetries: number;
}

export interface AllSettings {
  system: SystemSettings;
  agents: AgentSettings[];
  cost: CostSettings;
  privacy: PrivacySettings;
  editor: EditorSettings;
  terminal: TerminalSettings;
  apiKeys: Record<string, string>;
  mcp: MCPSettings;
  recentProjects: RecentProject[];
}

export class SettingsManager {
  private settings: AllSettings;
  private listeners: ((settings: AllSettings) => void)[] = [];
  private configStore: ConfigStoreBrowser;
  private initialized = false;

  constructor() {
    this.settings = this.getDefaultSettings();
    this.configStore = getConfigStoreBrowser();
    this.loadSettings();
  }

  private getDefaultSettings(): AllSettings {
    return {
      system: {
        theme: 'system',
        autoSave: true,
        autoSaveInterval: 30,
        maxConcurrentTasks: 5, // ✅ PERFORMANCE FIX: Enable parallel agent execution (was 1)
        defaultTimeout: 15000, // ✅ PERFORMANCE FIX: Reduced from 30s to 15s for faster responsiveness
        enableTelemetry: false,
        debugMode: false,
        testModeEnabled: false
      },
      agents: [
        {
          id: 'micromanager',
          name: 'Micromanager',
          enabled: true,
          model: 'claude-3-5-sonnet-20240620',
          provider: 'anthropic',
          maxTokens: 8000,
          temperature: 0.7,
          capabilities: ['task_orchestration', 'agent_coordination']
        },
        {
          id: 'intern',
          name: 'Intern',
          enabled: true,
          model: 'deepseek-v3',
          provider: 'deepseek',
          maxTokens: 2000,
          temperature: 0.3,
          capabilities: ['simple_tasks', 'boilerplate_generation']
        },
        {
          id: 'junior',
          name: 'Junior',
          enabled: true,
          model: 'deepseek-v3',
          provider: 'deepseek',
          maxTokens: 4000,
          temperature: 0.5,
          capabilities: ['single_file_implementation', 'moderate_complexity']
        },
        {
          id: 'midlevel',
          name: 'MidLevel',
          enabled: true,
          model: 'claude-3-5-sonnet-20240620',
          provider: 'anthropic',
          maxTokens: 6000,
          temperature: 0.6,
          capabilities: ['multi_file_implementation', 'component_integration']
        },
        {
          id: 'senior',
          name: 'Senior',
          enabled: true,
          model: 'deepseek-v3',
          provider: 'deepseek',
          maxTokens: 8000,
          temperature: 0.7,
          capabilities: ['complex_system_implementation', 'architectural_decisions']
        },
        {
          id: 'researcher',
          name: 'Researcher',
          enabled: true,
          model: 'claude-3-5-sonnet-20240620',
          provider: 'anthropic',
          maxTokens: 6000,
          temperature: 0.4,
          capabilities: ['codebase_analysis', 'pattern_recognition']
        },
        {
          id: 'architect',
          name: 'Architect',
          enabled: true,
          model: 'deepseek-v3',
          provider: 'deepseek',
          maxTokens: 8000,
          temperature: 0.6,
          capabilities: ['system_design', 'technical_strategy']
        },
        {
          id: 'designer',
          name: 'Designer',
          enabled: true,
          model: 'claude-3-5-sonnet-20240620',
          provider: 'anthropic',
          maxTokens: 4000,
          temperature: 0.8,
          capabilities: ['ui_design', 'component_styling']
        },
        {
          id: 'tester',
          name: 'Tester',
          enabled: true,
          model: 'deepseek-v3',
          provider: 'deepseek',
          maxTokens: 4000,
          temperature: 0.4,
          capabilities: ['test_generation', 'quality_assurance']
        }
      ],
      cost: {
        budgetLimit: 100,
        alertThreshold: 80,
        trackUsage: true,
        showCostEstimates: true,
        preferCheaperModels: false
      },
      privacy: {
        shareUsageData: false,
        localOnly: true,
        encryptPrompts: true,
        clearHistoryOnExit: false,
        maxHistoryDays: 30
      },
      editor: {
        fontSize: 14,
        fontFamily: 'JetBrains Mono, Monaco, Consolas, monospace',
        tabSize: 2,
        wordWrap: true,
        lineNumbers: true,
        minimap: true,
        autoFormat: true,
        autoComplete: true
      },
      terminal: {
        theme: 'system',
        fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
        fontSize: 13,
        shell: process.platform === 'win32' ? 'powershell.exe' : 'bash',
        cols: 80,
        rows: 24,
        scrollback: 1000,
        cursorBlink: true,
        lineHeight: 1.2
      },
      apiKeys: {},
      mcp: {
        enabled: false,
        servers: {
          'claude-desktop': {
            name: 'Claude Desktop',
            command: 'claude-desktop',
            args: ['--mcp'],
            enabled: false,
            autoConnect: false
          },
          'roo-code': {
            name: 'Roo Code',
            command: 'roo',
            args: ['--mcp-server'],
            enabled: false,
            autoConnect: false
          },
          'cursor-ai': {
            name: 'Cursor AI',
            command: 'cursor',
            args: ['--mcp-mode'],
            enabled: false,
            autoConnect: false
          }
        },
        timeout: 30000,
        maxRetries: 3
      },
      recentProjects: []
    };
  }

  private async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      await this.configStore.initialize();
      this.initialized = true;
      console.log('✅ SettingsManager: ConfigStore initialized successfully');
    } catch (error) {
      console.error('❌ SettingsManager: Failed to initialize config store:', error);
      console.log('🔄 SettingsManager: Falling back to localStorage mode');
      // ✅ CRITICAL FIX: Set initialized to true even in fallback mode
      this.initialized = true;
    }
  }

  private loadSettings(): void {
    this.loadSettingsAsync().catch(error => {
      console.error('Failed to load settings:', error);
    });
  }

  private async loadSettingsAsync(): Promise<void> {
    try {
      // Skip loading during SSR
      if (typeof window === 'undefined') {
        console.log('🔄 SettingsManager: Skipping settings load during SSR');
        return;
      }

      console.log('🔄 SettingsManager: Starting settings load...');
      await this.initialize();

      if (this.initialized) {
        try {
          console.log('🔄 SettingsManager: Loading from ConfigStore...');
          // Load from persistent storage
          const systemSettings = await this.configStore.getGlobalSettingsByCategory('system');
          const agentSettings = await this.configStore.getGlobalSettingsByCategory('agents');
          const costSettings = await this.configStore.getGlobalSettingsByCategory('cost');
          const privacySettings = await this.configStore.getGlobalSettingsByCategory('privacy');
          const editorSettings = await this.configStore.getGlobalSettingsByCategory('editor');
          const terminalSettings = await this.configStore.getGlobalSettingsByCategory('terminal');
          const mcpSettings = await this.configStore.getGlobalSettingsByCategory('mcp');
          const apiKeys = await this.configStore.getGlobalSettingsByCategory('apiKeys');
          const recentProjectsSettings = await this.configStore.getGlobalSettingsByCategory('recentProjects');

          console.log('🔄 SettingsManager: ConfigStore data loaded:', {
            systemSettings: Object.keys(systemSettings).length,
            apiKeys: Object.keys(apiKeys).length,
            agentSettings: agentSettings.agents ? agentSettings.agents.length : 0
          });

          if (Object.keys(systemSettings).length > 0) {
            this.settings.system = { ...this.settings.system, ...systemSettings };
          }
          if (Object.keys(costSettings).length > 0) {
            this.settings.cost = { ...this.settings.cost, ...costSettings };
          }
          if (Object.keys(privacySettings).length > 0) {
            this.settings.privacy = { ...this.settings.privacy, ...privacySettings };
          }
          if (Object.keys(editorSettings).length > 0) {
            this.settings.editor = { ...this.settings.editor, ...editorSettings };
          }
          if (Object.keys(terminalSettings).length > 0) {
            this.settings.terminal = { ...this.settings.terminal, ...terminalSettings };
          }
          if (Object.keys(apiKeys).length > 0) {
            this.settings.apiKeys = { ...this.settings.apiKeys, ...apiKeys };
            console.log('✅ SettingsManager: API keys loaded from ConfigStore:', Object.keys(apiKeys));
          }

          // Handle agent settings array
          if (agentSettings.agents) {
            this.settings.agents = agentSettings.agents;
          }

          // ✅ MANUAL SAVE FIX: Initialize saved agent snapshot after loading
          this.updateSavedAgentSnapshot();

          // ✅ Handle MCP settings object (MISSING LOAD)
          if (mcpSettings.mcp) {
            this.settings.mcp = { ...this.settings.mcp, ...mcpSettings.mcp };
          }

          // Handle recent projects array
          if (recentProjectsSettings.recentProjects) {
            this.settings.recentProjects = recentProjectsSettings.recentProjects;
          }

          console.log('✅ SettingsManager: Settings loaded from ConfigStore successfully');
        } catch (configStoreError) {
          console.error('❌ SettingsManager: ConfigStore operations failed:', configStoreError);
          console.log('🔄 SettingsManager: Falling back to localStorage...');
          this.loadFromLocalStorage();
        }
      } else {
        console.log('🔄 SettingsManager: ConfigStore not initialized, using localStorage fallback');
        this.loadFromLocalStorage();
      }
    } catch (error) {
      console.error('❌ SettingsManager: Failed to load settings:', error);
      console.log('🔄 SettingsManager: Using localStorage fallback due to error');
      this.loadFromLocalStorage();
    }
  }

  private loadFromLocalStorage(): void {
    try {
      // Check if localStorage is available (not available during SSR)
      if (typeof window !== 'undefined' && window.localStorage) {
        console.log('🔄 SettingsManager: Loading from localStorage...');
        const stored = localStorage.getItem('synapse-settings');
        if (stored) {
          const parsed = JSON.parse(stored);
          this.settings = { ...this.settings, ...parsed };

          console.log('✅ SettingsManager: Settings loaded from localStorage:', {
            hasApiKeys: Object.keys(this.settings.apiKeys || {}).length > 0,
            apiKeyProviders: Object.keys(this.settings.apiKeys || {}),
            hasAgents: this.settings.agents?.length > 0
          });
        } else {
          console.log('⚠️ SettingsManager: No stored settings found in localStorage');
        }

        // ✅ CRITICAL FIX: Ensure initialized flag is set after localStorage load
        if (!this.initialized) {
          this.initialized = true;
          console.log('✅ SettingsManager: Marked as initialized after localStorage load');
        }

        // ✅ SURGICAL FIX: Sync with next-themes storage on initialization
        this.syncWithNextThemes();

        // ✅ MANUAL SAVE FIX: Initialize saved agent snapshot after loading from localStorage
        this.updateSavedAgentSnapshot();
      } else {
        console.log('⚠️ SettingsManager: localStorage not available');
      }
    } catch (error) {
      console.error('❌ SettingsManager: Failed to load settings from localStorage:', error);
      // ✅ CRITICAL FIX: Still mark as initialized even if localStorage fails
      if (!this.initialized) {
        this.initialized = true;
        console.log('✅ SettingsManager: Marked as initialized despite localStorage error');
      }
    }
  }

  /**
   * ✅ SURGICAL FIX: Sync theme with next-themes storage
   * Ensures consistent theme state between SettingsManager and next-themes
   */
  private syncWithNextThemes(): void {
    try {
      if (typeof window !== 'undefined' && window.localStorage) {
        const nextThemesTheme = localStorage.getItem('synapse-theme');
        const settingsTheme = this.settings.system.theme;

        console.log(`🔧 Theme sync check: SettingsManager(${settingsTheme}) vs next-themes(${nextThemesTheme})`);

        if (nextThemesTheme && nextThemesTheme !== settingsTheme) {
          // If next-themes has a different theme, update SettingsManager to match
          if (['light', 'dark', 'system'].includes(nextThemesTheme)) {
            console.log(`🔧 SURGICAL FIX: Syncing SettingsManager to next-themes: ${nextThemesTheme}`);
            this.settings.system.theme = nextThemesTheme as 'light' | 'dark' | 'system';
            this.saveSettings();
          }
        } else if (!nextThemesTheme && settingsTheme) {
          // If next-themes has no theme but SettingsManager does, sync to next-themes
          console.log(`🔧 SURGICAL FIX: Syncing next-themes to SettingsManager: ${settingsTheme}`);
          localStorage.setItem('synapse-theme', settingsTheme);
        }
      }
    } catch (error) {
      console.error('Failed to sync with next-themes:', error);
    }
  }

  public saveSettings(): void {
    this.saveSettingsAsync().catch(error => {
      console.error('Failed to save settings:', error);
    });
  }

  private async saveSettingsAsync(): Promise<void> {
    try {
      // Skip saving during SSR
      if (typeof window === 'undefined') {
        return;
      }

      await this.initialize();

      if (this.initialized) {
        // Save to persistent storage
        await this.saveToConfigStore();
      } else {
        // Fall back to localStorage
        this.saveToLocalStorage();
      }

      // ✅ MANUAL SAVE FIX: Update saved agent snapshot after successful save
      this.updateSavedAgentSnapshot();

      this.notifyListeners();
    } catch (error) {
      console.error('Failed to save settings to config store:', error);
      this.saveToLocalStorage();
      // ✅ MANUAL SAVE FIX: Update snapshot even on fallback save
      this.updateSavedAgentSnapshot();
      this.notifyListeners();
    }
  }

  private async saveToConfigStore(): Promise<void> {
    // Save system settings
    for (const [key, value] of Object.entries(this.settings.system)) {
      await this.configStore.setGlobalSetting('system', key, value);
    }

    // ✅ Save agent settings as a single object (CRITICAL FIX)
    await this.configStore.setGlobalSetting('agents', 'agents', this.settings.agents);

    // Save cost settings
    for (const [key, value] of Object.entries(this.settings.cost)) {
      await this.configStore.setGlobalSetting('cost', key, value);
    }

    // Save privacy settings
    for (const [key, value] of Object.entries(this.settings.privacy)) {
      await this.configStore.setGlobalSetting('privacy', key, value);
    }

    // Save editor settings
    for (const [key, value] of Object.entries(this.settings.editor)) {
      await this.configStore.setGlobalSetting('editor', key, value);
    }

    // Save terminal settings
    for (const [key, value] of Object.entries(this.settings.terminal)) {
      await this.configStore.setGlobalSetting('terminal', key, value);
    }

    // ✅ Save MCP settings as a single object (MISSING SAVE)
    await this.configStore.setGlobalSetting('mcp', 'mcp', this.settings.mcp);

    // Save API keys (encrypted)
    for (const [key, value] of Object.entries(this.settings.apiKeys)) {
      await this.configStore.setGlobalSetting('apiKeys', key, value, true);
    }

    // Save recent projects as a single object
    await this.configStore.setGlobalSetting('recentProjects', 'recentProjects', this.settings.recentProjects);
  }

  private saveToLocalStorage(): void {
    try {
      // Check if localStorage is available (not available during SSR)
      if (typeof window !== 'undefined' && window.localStorage) {
        localStorage.setItem('synapse-settings', JSON.stringify(this.settings));
      }
    } catch (error) {
      console.error('Failed to save settings to localStorage:', error);
    }
  }

  // ✅ PERFORMANCE FIX: Cache settings object to prevent unnecessary object creation
  private cachedSettings: AllSettings | null = null;
  private settingsVersion = 0;

  public getSettings(): AllSettings {
    // Return cached settings if available and current
    if (this.cachedSettings && this.settingsVersion === this.lastSettingsVersion) {
      return this.cachedSettings;
    }

    // Create new cached settings
    this.cachedSettings = { ...this.settings };
    this.lastSettingsVersion = this.settingsVersion;
    return this.cachedSettings;
  }

  private lastSettingsVersion = 0;

  public updateSystemSettings(updates: Partial<SystemSettings>): void {
    this.settings.system = { ...this.settings.system, ...updates };

    // ✅ SURGICAL FIX: Sync theme changes to next-themes storage
    if (updates.theme && typeof window !== 'undefined' && window.localStorage) {
      console.log(`🔧 SURGICAL FIX: Syncing theme change to next-themes: ${updates.theme}`);
      localStorage.setItem('synapse-theme', updates.theme);
    }

    this.saveSettings();
  }

  public updateAgentSettings(agentId: string, updates: Partial<AgentSettings>): void {
    const agentIndex = this.settings.agents.findIndex(a => a.id === agentId);
    if (agentIndex !== -1) {
      this.settings.agents[agentIndex] = { ...this.settings.agents[agentIndex], ...updates };
      // ✅ INTEGRATION FIX: Re-enable auto-save for agent settings to ensure persistence
      this.saveSettings(); // Agent settings should persist immediately
      this.notifyListeners(); // Still notify UI of changes for immediate feedback
    }
  }

  /**
   * ✅ MANUAL SAVE FIX: Save agent settings manually when user clicks save button
   */
  public saveAgentSettings(): void {
    console.log('💾 SettingsManager: Manually saving agent settings...');
    this.saveSettings();
  }

  /**
   * ✅ PERFORMANCE FIX: Optimized unsaved changes detection
   * Uses hash comparison instead of expensive JSON stringify
   */
  private lastSavedAgentSettings: AgentSettings[] | null = null;
  private lastSavedAgentHash: string | null = null;
  private currentAgentHash: string | null = null;

  private calculateAgentHash(agents: AgentSettings[]): string {
    // Create a simple hash based on key properties instead of full JSON stringify
    return agents.map(agent =>
      `${agent.id}:${agent.enabled}:${agent.provider}:${agent.model}:${agent.temperature}:${agent.maxTokens}:${agent.customPrompt || ''}`
    ).join('|');
  }

  public hasUnsavedAgentChanges(): boolean {
    if (!this.lastSavedAgentSettings) {
      // First time - assume no changes
      this.lastSavedAgentSettings = JSON.parse(JSON.stringify(this.settings.agents));
      this.lastSavedAgentHash = this.calculateAgentHash(this.settings.agents);
      return false;
    }

    // Calculate current hash only if needed
    const currentHash = this.calculateAgentHash(this.settings.agents);

    // Compare hashes instead of full JSON stringify
    return currentHash !== this.lastSavedAgentHash;
  }

  /**
   * ✅ PERFORMANCE FIX: Optimized snapshot update
   */
  private updateSavedAgentSnapshot(): void {
    this.lastSavedAgentSettings = JSON.parse(JSON.stringify(this.settings.agents));
    this.lastSavedAgentHash = this.calculateAgentHash(this.settings.agents);
  }

  /**
   * ✅ MANUAL SAVE FIX: Discard unsaved agent changes
   */
  public discardAgentChanges(): void {
    if (this.lastSavedAgentSettings) {
      console.log('🔄 SettingsManager: Discarding unsaved agent changes...');
      this.settings.agents = JSON.parse(JSON.stringify(this.lastSavedAgentSettings));
      this.notifyListeners();
    }
  }

  public updateCostSettings(updates: Partial<CostSettings>): void {
    this.settings.cost = { ...this.settings.cost, ...updates };
    this.saveSettings();
  }

  public updatePrivacySettings(updates: Partial<PrivacySettings>): void {
    this.settings.privacy = { ...this.settings.privacy, ...updates };
    this.saveSettings();
  }

  public updateEditorSettings(updates: Partial<EditorSettings>): void {
    this.settings.editor = { ...this.settings.editor, ...updates };
    this.saveSettings();
  }

  public updateTerminalSettings(updates: Partial<TerminalSettings>): void {
    this.settings.terminal = { ...this.settings.terminal, ...updates };
    this.saveSettings();
  }

  public setApiKey(provider: string, key: string): void {
    this.settings.apiKeys[provider] = key;
    this.saveSettings();
  }

  public getApiKey(provider: string): string | undefined {
    return this.settings.apiKeys[provider];
  }

  public removeApiKey(provider: string): void {
    delete this.settings.apiKeys[provider];
    this.saveSettings();
  }

  // ✅ MCP Settings Management
  public updateMCPSettings(updates: Partial<MCPSettings>): void {
    this.settings.mcp = { ...this.settings.mcp, ...updates };
    this.saveSettings();
  }

  public getMCPSettings(): MCPSettings {
    return { ...this.settings.mcp };
  }

  public updateMCPServer(serverId: string, updates: Partial<MCPSettings['servers'][string]>): void {
    if (!this.settings.mcp.servers[serverId]) {
      this.settings.mcp.servers[serverId] = {
        name: serverId,
        command: '',
        args: [],
        enabled: false,
        autoConnect: false
      };
    }
    this.settings.mcp.servers[serverId] = { ...this.settings.mcp.servers[serverId], ...updates };
    this.saveSettings();
  }

  public removeMCPServer(serverId: string): void {
    delete this.settings.mcp.servers[serverId];
    this.saveSettings();
  }

  public onSettingsChange(listener: (settings: AllSettings) => void): void {
    this.listeners.push(listener);
  }

  public offSettingsChange(listener: (settings: AllSettings) => void): void {
    const index = this.listeners.indexOf(listener);
    if (index > -1) {
      this.listeners.splice(index, 1);
    }
  }

  // ✅ PERFORMANCE FIX: Debounced listener notification to prevent excessive re-renders
  private notificationTimeout: NodeJS.Timeout | null = null;

  private notifyListeners(): void {
    // Invalidate cached settings
    this.settingsVersion++;
    this.cachedSettings = null;

    // Debounce notifications to prevent excessive re-renders
    if (this.notificationTimeout) {
      clearTimeout(this.notificationTimeout);
    }

    this.notificationTimeout = setTimeout(() => {
      this.listeners.forEach(listener => listener(this.getSettings()));
      this.notificationTimeout = null;
    }, 16); // 16ms debounce (one frame at 60fps)
  }

  // ✅ Get current system settings (for theme bridge)
  public getSystemSettings(): SystemSettings {
    return { ...this.settings.system };
  }

  /**
   * Get a complete snapshot of all settings for export
   */
  public getFullSettingsSnapshot(): AllSettings {
    return { ...this.settings };
  }

  /**
   * Export settings as JSON string (legacy method for backward compatibility)
   */
  public exportSettings(): string {
    // Remove sensitive data before export
    const exportData = { ...this.settings };
    exportData.apiKeys = {}; // Don't export API keys

    // Add metadata
    const exportWithMetadata = {
      ...exportData,
      _metadata: {
        exportedAt: new Date().toISOString(),
        version: '1.0.0',
        source: 'Synapse Agent System'
      }
    };

    return JSON.stringify(exportWithMetadata, null, 2);
  }

  /**
   * Export settings as a downloadable blob
   */
  public exportSettingsAsBlob(): Blob {
    const { exportSettings } = require('../../lib/io/settings-exporter');
    return exportSettings(this.settings, { includeApiKeys: false });
  }

  /**
   * Apply a validated settings snapshot
   */
  public applySettingsSnapshot(settings: Partial<AllSettings>): void {
    // Preserve API keys (never overwrite them from import)
    const currentApiKeys = this.settings.apiKeys;

    // Apply imported settings
    if (settings.system) {
      this.settings.system = { ...this.settings.system, ...settings.system };
    }

    if (settings.agents) {
      this.settings.agents = settings.agents;
    }

    if (settings.cost) {
      this.settings.cost = { ...this.settings.cost, ...settings.cost };
    }

    if (settings.privacy) {
      this.settings.privacy = { ...this.settings.privacy, ...settings.privacy };
    }

    if (settings.editor) {
      this.settings.editor = { ...this.settings.editor, ...settings.editor };
    }

    if (settings.terminal) {
      this.settings.terminal = { ...this.settings.terminal, ...settings.terminal };
    }

    // Always preserve current API keys
    this.settings.apiKeys = currentApiKeys;

    this.saveSettings();
    this.notifyListeners();
  }

  /**
   * ✅ Apply settings snapshot silently (for sync service)
   * Updates settings without triggering save or broadcast to prevent feedback loops
   */
  public applySettingsSnapshotSilent(settings: Partial<AllSettings>): void {
    console.log('🔄 SettingsManager: Applying settings snapshot silently');

    // Preserve API keys (never overwrite them from sync)
    const currentApiKeys = this.settings.apiKeys;

    // Apply synced settings
    if (settings.system) {
      this.settings.system = { ...this.settings.system, ...settings.system };
    }

    if (settings.agents) {
      this.settings.agents = settings.agents;
    }

    if (settings.cost) {
      this.settings.cost = { ...this.settings.cost, ...settings.cost };
    }

    if (settings.privacy) {
      this.settings.privacy = { ...this.settings.privacy, ...settings.privacy };
    }

    if (settings.editor) {
      this.settings.editor = { ...this.settings.editor, ...settings.editor };
    }

    if (settings.terminal) {
      this.settings.terminal = { ...this.settings.terminal, ...settings.terminal };
    }

    if (settings.mcp) {
      this.settings.mcp = { ...this.settings.mcp, ...settings.mcp };
    }

    if (settings.recentProjects) {
      this.settings.recentProjects = settings.recentProjects;
    }

    // Always preserve current API keys
    this.settings.apiKeys = currentApiKeys;

    // Only notify listeners, don't save or broadcast
    this.notifyListeners();
  }

  /**
   * Import settings with validation (legacy method for backward compatibility)
   */
  public importSettings(settingsJson: string): boolean {
    try {
      const { importSettings } = require('../../lib/io/settings-exporter');
      const result = importSettings(settingsJson);

      if (!result.success) {
        console.error('Failed to import settings:', result.error);
        return false;
      }

      if (result.settings) {
        this.applySettingsSnapshot(result.settings);
      }

      return true;
    } catch (error) {
      console.error('Failed to import settings:', error);
      return false;
    }
  }

  public resetToDefaults(): void {
    const apiKeys = this.settings.apiKeys; // Preserve API keys
    this.settings = this.getDefaultSettings();
    this.settings.apiKeys = apiKeys;
    this.saveSettings();
  }

  // Project Management Methods
  public async createProject(name: string, path: string, template?: string): Promise<string> {
    await this.initialize();

    if (!this.initialized) {
      throw new Error('Config store not initialized');
    }

    const { createDefaultProjectConfig } = await import('../background/default-configs');
    const projectConfig = createDefaultProjectConfig(path, template as any);
    projectConfig.name = name;

    const created = await this.configStore.createProject(projectConfig);
    return created.id;
  }

  public async getProject(id: string) {
    await this.initialize();

    if (!this.initialized) {
      return null;
    }

    return await this.configStore.getProject(id);
  }

  public async getProjectByPath(path: string) {
    await this.initialize();

    if (!this.initialized) {
      return null;
    }

    return await this.configStore.getProjectByPath(path);
  }

  public async getAllProjects() {
    await this.initialize();

    if (!this.initialized) {
      return [];
    }

    return await this.configStore.getAllProjects();
  }

  public async updateProject(id: string, updates: any) {
    await this.initialize();

    if (!this.initialized) {
      throw new Error('Config store not initialized');
    }

    return await this.configStore.updateProject(id, updates);
  }

  public async deleteProject(id: string): Promise<boolean> {
    await this.initialize();

    if (!this.initialized) {
      throw new Error('Config store not initialized');
    }

    return await this.configStore.deleteProject(id);
  }

  // ✅ Recent Projects Management (migrated from legacy settings manager)
  public async getRecentProjects(): Promise<RecentProject[]> {
    return [...this.settings.recentProjects];
  }

  public async addRecentProject(name: string, path: string): Promise<void> {
    const existingIndex = this.settings.recentProjects.findIndex(p => p.path === path);

    const project: RecentProject = {
      name,
      path,
      lastOpened: Date.now()
    };

    if (existingIndex >= 0) {
      // Update existing project
      this.settings.recentProjects[existingIndex] = project;
    } else {
      // Add new project
      this.settings.recentProjects.unshift(project);
    }

    // Keep only the most recent projects (max 10)
    this.settings.recentProjects = this.settings.recentProjects
      .sort((a, b) => b.lastOpened - a.lastOpened)
      .slice(0, 10);

    this.saveSettings();
  }

  public async removeRecentProject(path: string): Promise<void> {
    this.settings.recentProjects = this.settings.recentProjects.filter(p => p.path !== path);
    this.saveSettings();
  }

  public async clearRecentProjects(): Promise<void> {
    this.settings.recentProjects = [];
    this.saveSettings();
  }

  // Database management (via IPC in Electron)
  public async backupDatabase(backupPath: string): Promise<void> {
    await this.initialize();

    if (!this.initialized) {
      throw new Error('Config store not initialized');
    }

    if (typeof window !== 'undefined' && window.electronAPI?.configStore) {
      await window.electronAPI.configStore.backupDatabase(backupPath);
    } else {
      console.warn('Database backup not available in browser environment');
    }
  }

  public async vacuumDatabase(): Promise<void> {
    await this.initialize();

    if (!this.initialized) {
      throw new Error('Config store not initialized');
    }

    if (typeof window !== 'undefined' && window.electronAPI?.configStore) {
      await window.electronAPI.configStore.vacuumDatabase();
    } else {
      console.warn('Database vacuum not available in browser environment');
    }
  }

  public getConfigStore(): ConfigStoreBrowser {
    return this.configStore;
  }

  public isInitialized(): boolean {
    return this.initialized;
  }

  /**
   * Force reload settings from storage
   */
  public async forceReloadSettings(): Promise<void> {
    console.log('🔄 [Settings Manager] Force reloading settings...');
    await this.loadSettingsAsync();
    console.log('✅ [Settings Manager] Settings reloaded');
  }
}

// ✅ CRITICAL FIX: Removed singleton export to prevent circular imports
// Use getGlobalSettingsManager() from './global-settings' instead