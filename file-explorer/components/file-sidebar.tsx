/**
 * File Sidebar - Streamlined Implementation
 * 
 * Reduced from 1552 lines to under 200 lines by extracting functionality into modular components.
 * This serves as the main interface that delegates to specialized modules.
 */

"use client"

import { useEffect, useCallback } from "react"
import { useToast } from "@/components/ui/use-toast"
import { useFileSidebarStore, loadExplorerSettings, saveExplorerSettings } from "./file-sidebar/useFileSidebarStore"
import { useFileSidebarEvents } from "./file-sidebar/FileSidebarEvents"
import { SidebarLayout } from "./file-sidebar/SidebarLayout"
import { ProjectSection } from "./file-sidebar/ProjectSection"
import { UnsavedChangesDialog } from "./file-sidebar/dialogs/UnsavedChangesDialog"
import { PRDDialog } from "./file-sidebar/dialogs/PRDDialog"
import { OrchestrationDialog } from "./file-sidebar/dialogs/OrchestrationDialog"
import { ExplorerSettingsDialog } from "./file-sidebar/dialogs/ExplorerSettingsDialog"
import { filterFiles } from "./file-sidebar/sidebar-utils"
import { FileSidebarProps, PRDValidationResult, PRDParseResult } from "./file-sidebar/types"

// ✅ Extend window interface for file explorer refresh
declare global {
  interface Window {
    refreshFileExplorer?: () => Promise<void>;
  }
}

export default function FileSidebar({
  onFileSelect,
  onCreateProject,
  onRefreshRequest
}: FileSidebarProps) {
  const { toast } = useToast()
  
  // State from store
  const {
    projects,
    searchQuery,
    selectedFile,
    showUnsavedChangesDialog,
    showPRDDialog,
    showExplorerSettings,
    showOrchestrationDialog,
    currentProjectPath,
    prdValidated,
    explorerSettings,
    setSearchQuery,
    setShowExplorerSettings,
    setExplorerSettings,
    setPrdValidated,
    resetProjectCreation,
    addProject,
    setShowOrchestrationDialog,
    setShowUnsavedChangesDialog,
    setShowPRDDialog
  } = useFileSidebarStore()

  // Event handlers
  const {
    handleFileSelect,
    handleToggleFolder,
    handleUnsavedChangesConfirm,
    handleOpenProject,
    handleStartOrchestration,
    handleLoadRecentProjects
  } = useFileSidebarEvents()

  // Load Explorer settings from localStorage on mount
  useEffect(() => {
    loadExplorerSettings()
  }, [])

  // Load recent projects on component mount (only once)
  useEffect(() => {
    handleLoadRecentProjects()
  }, []) // ✅ CRITICAL FIX: Remove handleLoadRecentProjects dependency to prevent infinite loop

  // ✅ Auto-refresh mechanism
  const refreshFileExplorer = useCallback(async () => {
    try {
      const { activeProjectService } = await import('../services/active-project-service');
      const activeProject = activeProjectService.getActiveProject();

      if (activeProject?.path) {
        const existingProject = projects.find(p => p.path === activeProject.path);
        if (!existingProject) {
          const { loadProjectFromPath } = await import('./file-sidebar/sidebar-utils');
          const newProject = await loadProjectFromPath(activeProject.path, activeProject.name);
          if (newProject) addProject(newProject);
        }
      }
      await handleLoadRecentProjects();
    } catch (error) {
      console.error('❌ Error refreshing file explorer:', error);
    }
  }, [projects, addProject, handleLoadRecentProjects]); // ✅ Keep necessary dependencies but ensure they're stable

  // Expose refresh function
  useEffect(() => {
    if (onRefreshRequest) window.refreshFileExplorer = refreshFileExplorer;
    return () => { if (window.refreshFileExplorer) delete window.refreshFileExplorer; };
  }, [onRefreshRequest, refreshFileExplorer]);

  // Handle file selection with content loading
  const handleFileSelectWithCallback = useCallback(async (file: any) => {
    const fileWithContent = await handleFileSelect(file);
    onFileSelect(fileWithContent);
  }, [handleFileSelect, onFileSelect]);

  // Handle create project
  const handleCreateProject = useCallback(() => {
    onCreateProject?.();
  }, [onCreateProject]);

  // ✅ Handle PRD upload completion
  const handlePRDUploaded = useCallback((filePath: string, validation: PRDValidationResult) => {
    setPrdValidated(validation.isValid);
    if (validation.isValid) {
      toast({
        title: "PRD Validated",
        description: `PRD uploaded with quality score: ${validation.score}/100`,
      });
    }
  }, [setPrdValidated, toast]);

  // ✅ Handle PRD parsing completion
  const handlePRDParsed = useCallback((result: PRDParseResult) => {
    if (result.success && currentProjectPath) {
      toast({
        title: "PRD Parsed",
        description: `Generated ${result.taskCount} tasks from PRD`,
      });
    } else {
      toast({
        title: "PRD Parsing Failed",
        description: result.error || "Failed to parse PRD with Taskmaster",
        variant: "destructive",
      });
    }
  }, [currentProjectPath, toast]);

  // ✅ Handle PRD validation change
  const handlePRDValidationChange = useCallback((isValid: boolean) => {
    setPrdValidated(isValid);
  }, [setPrdValidated]);

  // Handle PRD dialog cancel
  const handlePRDCancel = useCallback(() => {
    resetProjectCreation();
  }, [resetProjectCreation]);

  // ARCHITECTURE RESTORATION: Handle task submission to Micromanager
  const handleTasksSubmittedToMicromanager = useCallback((result: { success: boolean; tasksSubmitted: number; error?: string }) => {
    if (result.success) {
      toast({
        title: "Tasks Submitted to Micromanager",
        description: `Successfully submitted ${result.tasksSubmitted} tasks for proper Synapse orchestration.`,
      });
      setShowOrchestrationDialog(false);
    } else {
      toast({
        title: "Task Submission Failed",
        description: result.error || "Failed to submit tasks to Micromanager",
        variant: "destructive",
      });
    }
  }, [toast]);

  // Handle settings save
  const handleSettingsSave = useCallback(() => {
    saveExplorerSettings();
    toast({
      title: "Settings saved",
      description: "Explorer settings have been updated successfully.",
    });
    setShowExplorerSettings(false);
  }, [setShowExplorerSettings, toast]);

  // Filter files based on search query
  const filteredProjects = searchQuery ? filterFiles(projects, searchQuery) : projects;

  return (
    <>
      <SidebarLayout
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        onCreateProject={handleCreateProject}
        onOpenProject={handleOpenProject}
        onStartOrchestration={handleStartOrchestration}
        onShowSettings={() => setShowExplorerSettings(true)}
        projectCount={projects.length}
        hasProjects={projects.length > 0}
      >
        <ProjectSection
          projects={filteredProjects}
          selectedFile={selectedFile}
          onToggle={handleToggleFolder}
          onSelect={handleFileSelectWithCallback}
          onCreateProject={handleCreateProject}
          onOpenProject={handleOpenProject}
        />
      </SidebarLayout>

      {/* Dialogs */}
      <UnsavedChangesDialog
        open={showUnsavedChangesDialog}
        onOpenChange={setShowUnsavedChangesDialog}
        onConfirm={handleUnsavedChangesConfirm}
      />

      <PRDDialog
        open={showPRDDialog}
        onOpenChange={setShowPRDDialog}
        currentProjectPath={currentProjectPath}
        prdValidated={prdValidated}
        onPRDUploaded={handlePRDUploaded}
        onPRDParsed={handlePRDParsed}
        onValidationChange={handlePRDValidationChange}
        onCancel={handlePRDCancel}
      />

      <OrchestrationDialog
        open={showOrchestrationDialog}
        onOpenChange={setShowOrchestrationDialog}
        onTasksSubmitted={handleTasksSubmittedToMicromanager}
      />

      <ExplorerSettingsDialog
        open={showExplorerSettings}
        onOpenChange={setShowExplorerSettings}
        settings={explorerSettings}
        onSettingsChange={setExplorerSettings}
        onSave={handleSettingsSave}
      />
    </>
  )
}
