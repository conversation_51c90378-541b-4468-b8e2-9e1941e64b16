/**
 * Semantic Analysis Types
 * 
 * Shared type definitions for semantic code analysis system.
 * Extracted from semantic-code-analysis.ts for better modularity.
 */

export interface ASTNode {
  id: string;
  type: 'Program' | 'FunctionDeclaration' | 'ClassDeclaration' | 'VariableDeclaration' | 'ImportDeclaration' | 'ExportDeclaration' | 'MethodDefinition' | 'Property' | 'Identifier' | 'CallExpression' | 'MemberExpression' | 'ArrowFunctionExpression' | 'BlockStatement' | 'ReturnStatement' | 'IfStatement' | 'ForStatement' | 'WhileStatement' | 'TryStatement' | 'ThrowStatement';
  name?: string;
  value?: any;
  children: ASTNode[];
  parent?: ASTNode;
  startLine: number;
  endLine: number;
  startColumn: number;
  endColumn: number;
  filePath: string;
  language: string;
  metadata: {
    visibility?: 'public' | 'private' | 'protected';
    isAsync?: boolean;
    isStatic?: boolean;
    isExported?: boolean;
    isDefault?: boolean;
    parameters?: Parameter[];
    returnType?: string;
    decorators?: string[];
    annotations?: string[];
    complexity?: number;
    dependencies?: string[];
    documentation?: string;
  };
}

export interface Parameter {
  name: string;
  type?: string;
  optional?: boolean;
  defaultValue?: string;
}

export interface CodePattern {
  id: string;
  name: string;
  type: 'design_pattern' | 'anti_pattern' | 'code_smell' | 'best_practice' | 'architectural_pattern' | 'idiom';
  description: string;
  language: string;
  pattern: string; // Regex or AST pattern
  examples: string[];
  severity: 'info' | 'warning' | 'error' | 'critical';
  category: 'structure' | 'performance' | 'maintainability' | 'security' | 'readability' | 'testing';
  confidence: number; // 0-1
  suggestions: string[];
  relatedPatterns: string[];
}

export interface PatternMatch {
  pattern: CodePattern;
  location: {
    filePath: string;
    startLine: number;
    endLine: number;
    startColumn: number;
    endColumn: number;
  };
  confidence: number;
  context: string;
  suggestions: string[];
  severity: CodePattern['severity'];
}

export interface DependencyNode {
  id: string;
  name: string;
  type: 'file' | 'module' | 'package' | 'function' | 'class' | 'variable';
  filePath: string;
  language: string;
  version?: string;
  isExternal: boolean;
  dependencies: string[]; // IDs of dependencies
  dependents: string[]; // IDs of dependents
  metadata: {
    size?: number;
    complexity?: number;
    lastModified?: number;
    isDeprecated?: boolean;
    securityIssues?: string[];
    performanceImpact?: 'low' | 'medium' | 'high';
  };
}

export interface DependencyGraph {
  nodes: Map<string, DependencyNode>;
  edges: Map<string, DependencyEdge>;
  cycles: string[][]; // Arrays of node IDs forming cycles
  layers: string[][]; // Topologically sorted layers
  metrics: {
    totalNodes: number;
    totalEdges: number;
    cycleCount: number;
    maxDepth: number;
    averageDependencies: number;
    criticalPath: string[];
  };
}

export interface DependencyEdge {
  id: string;
  sourceId: string;
  targetId: string;
  type: 'import' | 'require' | 'call' | 'inheritance' | 'composition' | 'aggregation' | 'usage';
  strength: number; // 0-1, how strong the dependency is
  isCircular: boolean;
  metadata: {
    importType?: 'default' | 'named' | 'namespace' | 'dynamic';
    usageCount?: number;
    firstSeen?: number;
    lastSeen?: number;
  };
}

export interface CodeQualityMetrics {
  filePath: string;
  language: string;
  metrics: {
    linesOfCode: number;
    cyclomaticComplexity: number;
    cognitiveComplexity: number;
    maintainabilityIndex: number;
    technicalDebt: number; // minutes
    duplicateLines: number;
    testCoverage?: number;
    codeSmells: number;
    bugs: number;
    vulnerabilities: number;
    hotspots: number;
  };
  scores: {
    overall: number; // 0-100
    maintainability: number;
    reliability: number;
    security: number;
    performance: number;
    readability: number;
  };
  issues: QualityIssue[];
  suggestions: string[];
  trends: {
    complexityTrend: 'improving' | 'stable' | 'degrading';
    maintainabilityTrend: 'improving' | 'stable' | 'degrading';
    lastAnalysis: number;
    previousScores?: CodeQualityMetrics['scores'];
  };
}

export interface QualityIssue {
  id: string;
  type: 'code_smell' | 'bug' | 'vulnerability' | 'hotspot' | 'duplication' | 'complexity';
  severity: 'info' | 'minor' | 'major' | 'critical' | 'blocker';
  message: string;
  description: string;
  location: {
    startLine: number;
    endLine: number;
    startColumn: number;
    endColumn: number;
  };
  rule: string;
  category: string;
  effort: number; // minutes to fix
  suggestions: string[];
  examples?: string[];
}

export interface AnalysisOptions {
  includeAST: boolean;
  includePatterns: boolean;
  includeDependencies: boolean;
  includeQualityMetrics: boolean;
  includeSemanticAnalysis: boolean;
  maxDepth: number;
  languages: string[];
  excludePatterns: string[];
  customPatterns: CodePattern[];
}

export interface AnalysisResult {
  filePath: string;
  language: string;
  timestamp: number;
  ast?: ASTNode;
  patterns: PatternMatch[];
  dependencies: DependencyNode;
  qualityMetrics: CodeQualityMetrics;
  semanticInfo: {
    concepts: string[];
    entities: string[];
    relationships: string[];
    complexity: number;
    readability: number;
  };
  suggestions: string[];
  warnings: string[];
  errors: string[];
}

export interface SemanticAnalysisConfig {
  enableASTAnalysis: boolean;
  enablePatternRecognition: boolean;
  enableDependencyAnalysis: boolean;
  enableQualityMetrics: boolean;
  enableSemanticSearch: boolean;
  maxFileSize: number; // bytes
  maxAnalysisTime: number; // milliseconds
  cacheResults: boolean;
  cacheExpiry: number; // milliseconds
  parallelAnalysis: boolean;
  maxConcurrentAnalyses: number;
  customPatterns: CodePattern[];
  qualityThresholds: {
    maintainability: number;
    complexity: number;
    duplication: number;
    coverage: number;
  };
}

export interface WorkerMessage {
  id: string;
  type: 'analyze' | 'parse' | 'pattern' | 'quality' | 'dependency';
  payload: any;
}

export interface WorkerResponse {
  id: string;
  success: boolean;
  result?: any;
  error?: string;
  duration?: number;
}

export interface ExecutionCostEstimate {
  cpuTime: number; // milliseconds
  memoryUsage: number; // bytes
  tokenCount: number;
  complexity: 'low' | 'medium' | 'high' | 'extreme';
  estimatedDuration: number; // milliseconds
  recommendedWorker: boolean;
}

export interface SemanticRanking {
  relevanceScore: number; // 0-1
  qualityScore: number; // 0-1
  complexityScore: number; // 0-1
  priorityScore: number; // 0-1
  overallRank: number; // 0-1
  factors: {
    fileSize: number;
    dependencies: number;
    patterns: number;
    issues: number;
    lastModified: number;
  };
}
