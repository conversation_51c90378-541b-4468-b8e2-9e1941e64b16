/**
 * Execution Cost Estimator
 * 
 * Estimates CPU, memory, and token costs for semantic analysis operations.
 * Helps determine when to use background workers vs main thread.
 */

import { ExecutionCostEstimate, AnalysisOptions } from './types';

/**
 * Estimate execution cost for file analysis
 */
export function estimateAnalysisCost(
  filePath: string,
  contentLength: number,
  options: AnalysisOptions
): ExecutionCostEstimate {
  const baseMetrics = calculateBaseMetrics(contentLength);
  const optionsMultiplier = calculateOptionsMultiplier(options);
  const languageMultiplier = getLanguageMultiplier(filePath);

  const cpuTime = baseMetrics.cpuTime * optionsMultiplier * languageMultiplier;
  const memoryUsage = baseMetrics.memoryUsage * optionsMultiplier;
  const tokenCount = estimateTokenCount(contentLength);

  const complexity = determineComplexity(cpuTime, memoryUsage, contentLength);
  const estimatedDuration = cpuTime * getPerformanceMultiplier();
  const recommendedWorker = shouldUseWorker(complexity, estimatedDuration);

  return {
    cpuTime,
    memoryUsage,
    tokenCount,
    complexity,
    estimatedDuration,
    recommendedWorker
  };
}

/**
 * Calculate base metrics from content length
 */
function calculateBaseMetrics(contentLength: number): { cpuTime: number; memoryUsage: number } {
  // Base calculations (empirically derived)
  const cpuTime = Math.max(10, contentLength * 0.05); // ~0.05ms per character
  const memoryUsage = Math.max(1024, contentLength * 8); // ~8 bytes per character for processing

  return { cpuTime, memoryUsage };
}

/**
 * Calculate multiplier based on analysis options
 */
function calculateOptionsMultiplier(options: AnalysisOptions): number {
  let multiplier = 1.0;

  if (options.includeAST) multiplier += 2.0; // AST parsing is expensive
  if (options.includePatterns) multiplier += 1.5; // Pattern matching is moderately expensive
  if (options.includeDependencies) multiplier += 1.0; // Dependency analysis is moderate
  if (options.includeQualityMetrics) multiplier += 2.5; // Quality metrics are expensive
  if (options.includeSemanticAnalysis) multiplier += 1.8; // Semantic analysis is expensive

  // Custom patterns add overhead
  if (options.customPatterns.length > 0) {
    multiplier += options.customPatterns.length * 0.1;
  }

  // Max depth affects complexity
  if (options.maxDepth > 5) {
    multiplier += (options.maxDepth - 5) * 0.2;
  }

  return multiplier;
}

/**
 * Get language-specific multiplier
 */
function getLanguageMultiplier(filePath: string): number {
  const extension = filePath.split('.').pop()?.toLowerCase();
  
  const languageMultipliers: Record<string, number> = {
    'ts': 1.3,  // TypeScript is more complex to parse
    'tsx': 1.4, // TSX even more so
    'js': 1.0,  // JavaScript baseline
    'jsx': 1.1, // JSX slightly more complex
    'py': 1.2,  // Python moderate complexity
    'java': 1.5, // Java is verbose and complex
    'cpp': 1.6, // C++ is very complex
    'c': 1.4,   // C is moderately complex
    'cs': 1.3,  // C# moderate complexity
    'php': 1.1, // PHP moderate
    'rb': 1.2,  // Ruby moderate
    'go': 1.1,  // Go is relatively simple
    'rs': 1.4   // Rust is complex
  };

  return languageMultipliers[extension || ''] || 1.0;
}

/**
 * Estimate token count for LLM processing
 */
function estimateTokenCount(contentLength: number): number {
  // Rough estimate: ~4 characters per token for code
  return Math.ceil(contentLength / 4);
}

/**
 * Determine complexity level
 */
function determineComplexity(
  cpuTime: number, 
  memoryUsage: number, 
  contentLength: number
): 'low' | 'medium' | 'high' | 'extreme' {
  // Define thresholds
  const thresholds = {
    cpuTime: { medium: 100, high: 500, extreme: 2000 },
    memory: { medium: 1024 * 1024, high: 10 * 1024 * 1024, extreme: 50 * 1024 * 1024 },
    content: { medium: 10000, high: 50000, extreme: 200000 }
  };

  let score = 0;

  // CPU time scoring
  if (cpuTime >= thresholds.cpuTime.extreme) score += 4;
  else if (cpuTime >= thresholds.cpuTime.high) score += 3;
  else if (cpuTime >= thresholds.cpuTime.medium) score += 2;
  else score += 1;

  // Memory usage scoring
  if (memoryUsage >= thresholds.memory.extreme) score += 4;
  else if (memoryUsage >= thresholds.memory.high) score += 3;
  else if (memoryUsage >= thresholds.memory.medium) score += 2;
  else score += 1;

  // Content length scoring
  if (contentLength >= thresholds.content.extreme) score += 4;
  else if (contentLength >= thresholds.content.high) score += 3;
  else if (contentLength >= thresholds.content.medium) score += 2;
  else score += 1;

  // Determine complexity based on average score
  const avgScore = score / 3;
  if (avgScore >= 3.5) return 'extreme';
  if (avgScore >= 2.5) return 'high';
  if (avgScore >= 1.5) return 'medium';
  return 'low';
}

/**
 * Get performance multiplier based on system capabilities
 */
function getPerformanceMultiplier(): number {
  // In a real implementation, this would check:
  // - CPU cores and speed
  // - Available memory
  // - Current system load
  // - Browser/Node.js performance characteristics
  
  // For now, return a conservative estimate
  return 1.2; // Assume 20% overhead for safety
}

/**
 * Determine if worker should be used
 */
function shouldUseWorker(complexity: string, estimatedDuration: number): boolean {
  // Use worker for:
  // - High or extreme complexity
  // - Operations taking more than 50ms
  // - Any operation that might block the main thread
  
  if (complexity === 'extreme' || complexity === 'high') return true;
  if (estimatedDuration > 50) return true;
  if (complexity === 'medium' && estimatedDuration > 25) return true;
  
  return false;
}

/**
 * Estimate batch processing cost
 */
export function estimateBatchCost(
  files: Array<{ filePath: string; contentLength: number }>,
  options: AnalysisOptions,
  maxConcurrency: number = 4
): {
  totalCost: ExecutionCostEstimate;
  batchCount: number;
  estimatedTotalTime: number;
  recommendedStrategy: 'sequential' | 'parallel' | 'worker-pool';
} {
  const individualCosts = files.map(file => 
    estimateAnalysisCost(file.filePath, file.contentLength, options)
  );

  const totalCpuTime = individualCosts.reduce((sum, cost) => sum + cost.cpuTime, 0);
  const totalMemoryUsage = Math.max(...individualCosts.map(cost => cost.memoryUsage));
  const totalTokenCount = individualCosts.reduce((sum, cost) => sum + cost.tokenCount, 0);

  const batchCount = Math.ceil(files.length / maxConcurrency);
  const avgComplexity = determineAverageComplexity(individualCosts);
  
  // Estimate parallel execution time
  const parallelTime = Math.max(...individualCosts.map(cost => cost.estimatedDuration));
  const estimatedTotalTime = parallelTime * batchCount;

  const recommendedStrategy = determineProcessingStrategy(
    individualCosts,
    files.length,
    estimatedTotalTime
  );

  return {
    totalCost: {
      cpuTime: totalCpuTime,
      memoryUsage: totalMemoryUsage,
      tokenCount: totalTokenCount,
      complexity: avgComplexity,
      estimatedDuration: estimatedTotalTime,
      recommendedWorker: recommendedStrategy === 'worker-pool'
    },
    batchCount,
    estimatedTotalTime,
    recommendedStrategy
  };
}

/**
 * Determine average complexity
 */
function determineAverageComplexity(costs: ExecutionCostEstimate[]): 'low' | 'medium' | 'high' | 'extreme' {
  const complexityScores = costs.map(cost => {
    switch (cost.complexity) {
      case 'low': return 1;
      case 'medium': return 2;
      case 'high': return 3;
      case 'extreme': return 4;
      default: return 1;
    }
  });

  const avgScore = complexityScores.reduce((sum, score) => sum + score, 0) / complexityScores.length;

  if (avgScore >= 3.5) return 'extreme';
  if (avgScore >= 2.5) return 'high';
  if (avgScore >= 1.5) return 'medium';
  return 'low';
}

/**
 * Determine optimal processing strategy
 */
function determineProcessingStrategy(
  costs: ExecutionCostEstimate[],
  fileCount: number,
  totalTime: number
): 'sequential' | 'parallel' | 'worker-pool' {
  const highComplexityCount = costs.filter(cost => 
    cost.complexity === 'high' || cost.complexity === 'extreme'
  ).length;

  const workerRecommendedCount = costs.filter(cost => cost.recommendedWorker).length;

  // Use worker pool if many files need workers or total time is high
  if (workerRecommendedCount > fileCount * 0.3 || totalTime > 1000) {
    return 'worker-pool';
  }

  // Use parallel processing for moderate loads
  if (fileCount > 2 && highComplexityCount < fileCount * 0.5) {
    return 'parallel';
  }

  // Use sequential for simple cases
  return 'sequential';
}

/**
 * Get memory usage recommendations
 */
export function getMemoryRecommendations(estimate: ExecutionCostEstimate): {
  recommended: number;
  minimum: number;
  optimal: number;
  warnings: string[];
} {
  const warnings: string[] = [];
  const baseMemory = estimate.memoryUsage;

  // Add overhead for processing
  const recommended = baseMemory * 2;
  const minimum = baseMemory * 1.2;
  const optimal = baseMemory * 3;

  if (estimate.complexity === 'extreme') {
    warnings.push('Extremely high memory usage expected - consider processing in smaller chunks');
  }

  if (estimate.memoryUsage > 100 * 1024 * 1024) { // 100MB
    warnings.push('Large memory allocation required - monitor for memory leaks');
  }

  if (estimate.recommendedWorker) {
    warnings.push('Consider using worker threads to prevent main thread blocking');
  }

  return {
    recommended,
    minimum,
    optimal,
    warnings
  };
}
