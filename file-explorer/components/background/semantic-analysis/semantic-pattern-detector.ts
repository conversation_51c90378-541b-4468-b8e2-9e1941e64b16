/**
 * Seman<PERSON> Detector
 * 
 * Handles detection of code patterns, anti-patterns, and design patterns.
 * Extracted from semantic-code-analysis.ts for better modularity.
 */

import { CodePattern, PatternMatch } from './types';

/**
 * Built-in code patterns for detection
 */
export const BUILT_IN_PATTERNS: CodePattern[] = [
  {
    id: 'singleton-pattern',
    name: 'Singleton Pattern',
    type: 'design_pattern',
    description: 'Ensures a class has only one instance and provides global access',
    language: 'typescript',
    pattern: 'class\\s+\\w+\\s*{[^}]*private\\s+static\\s+instance[^}]*getInstance\\s*\\([^}]*}',
    examples: ['class Singleton { private static instance: Singleton; }'],
    severity: 'info',
    category: 'structure',
    confidence: 0.8,
    suggestions: ['Consider using dependency injection instead'],
    relatedPatterns: ['factory-pattern']
  },
  {
    id: 'god-class',
    name: 'God Class',
    type: 'anti_pattern',
    description: 'A class that knows too much or does too much',
    language: 'typescript',
    pattern: 'class\\s+\\w+\\s*{[^}]{500,}', // Simplified pattern
    examples: ['class GodClass { /* hundreds of lines */ }'],
    severity: 'warning',
    category: 'maintainability',
    confidence: 0.7,
    suggestions: ['Break down into smaller, focused classes', 'Apply Single Responsibility Principle'],
    relatedPatterns: ['large-class']
  },
  {
    id: 'magic-numbers',
    name: 'Magic Numbers',
    type: 'code_smell',
    description: 'Numeric literals that lack explanation',
    language: 'any',
    pattern: '\\b\\d{2,}\\b',
    examples: ['if (status === 404)', 'timeout = 5000'],
    severity: 'warning',
    category: 'readability',
    confidence: 0.6,
    suggestions: ['Replace with named constants', 'Add explanatory comments'],
    relatedPatterns: ['hardcoded-values']
  },
  {
    id: 'long-parameter-list',
    name: 'Long Parameter List',
    type: 'code_smell',
    description: 'Functions with too many parameters',
    language: 'any',
    pattern: '\\([^)]{80,}\\)',
    examples: ['function process(a, b, c, d, e, f, g, h) {}'],
    severity: 'warning',
    category: 'maintainability',
    confidence: 0.8,
    suggestions: ['Use parameter objects', 'Break down the function'],
    relatedPatterns: ['god-function']
  },
  {
    id: 'empty-catch',
    name: 'Empty Catch Block',
    type: 'anti_pattern',
    description: 'Catch blocks that silently ignore exceptions',
    language: 'any',
    pattern: 'catch\\s*\\([^)]*\\)\\s*{\\s*}',
    examples: ['try { ... } catch (e) { }'],
    severity: 'error',
    category: 'reliability',
    confidence: 0.9,
    suggestions: ['Log the error', 'Handle the exception appropriately', 'Re-throw if necessary'],
    relatedPatterns: ['error-handling']
  },
  {
    id: 'hardcoded-credentials',
    name: 'Hardcoded Credentials',
    type: 'anti_pattern',
    description: 'Credentials embedded directly in code',
    language: 'any',
    pattern: '(password|secret|key|token)\\s*[=:]\\s*[\'"][^\'"]+[\'"]',
    examples: ['const password = "secret123"', 'apiKey: "abc123"'],
    severity: 'critical',
    category: 'security',
    confidence: 0.9,
    suggestions: ['Use environment variables', 'Use secure configuration management'],
    relatedPatterns: ['security-vulnerability']
  },
  {
    id: 'nested-loops',
    name: 'Nested Loops',
    type: 'code_smell',
    description: 'Deeply nested loops that may impact performance',
    language: 'any',
    pattern: 'for\\s*\\([^}]*{[^}]*for\\s*\\(',
    examples: ['for (let i = 0; i < n; i++) { for (let j = 0; j < m; j++) { ... } }'],
    severity: 'warning',
    category: 'performance',
    confidence: 0.7,
    suggestions: ['Consider algorithmic improvements', 'Use more efficient data structures'],
    relatedPatterns: ['performance-hotspot']
  },
  {
    id: 'sql-injection-risk',
    name: 'SQL Injection Risk',
    type: 'anti_pattern',
    description: 'String concatenation in SQL queries',
    language: 'any',
    pattern: '(SELECT|INSERT|UPDATE|DELETE).*\\+.*[\'"]',
    examples: ['query = "SELECT * FROM users WHERE id = " + userId'],
    severity: 'critical',
    category: 'security',
    confidence: 0.8,
    suggestions: ['Use parameterized queries', 'Use ORM with proper escaping'],
    relatedPatterns: ['security-vulnerability']
  }
];

/**
 * Recognize code patterns in content
 */
export async function recognizePatterns(
  content: string, 
  language: string, 
  customPatterns: CodePattern[] = [],
  filePath: string = ''
): Promise<PatternMatch[]> {
  const patterns = [...BUILT_IN_PATTERNS, ...customPatterns];
  const matches: PatternMatch[] = [];

  for (const pattern of patterns) {
    if (pattern.language !== language && pattern.language !== 'any') {
      continue;
    }

    try {
      const patternMatches = await detectPattern(content, pattern, filePath);
      matches.push(...patternMatches);
    } catch (error) {
      console.error(`Error applying pattern ${pattern.id}:`, error);
    }
  }

  return matches.sort((a, b) => b.confidence - a.confidence);
}

/**
 * Detect a specific pattern in content
 */
async function detectPattern(content: string, pattern: CodePattern, filePath: string): Promise<PatternMatch[]> {
  const matches: PatternMatch[] = [];
  
  try {
    const regex = new RegExp(pattern.pattern, 'gi');
    let match;

    while ((match = regex.exec(content)) !== null) {
      const lineNumber = content.substring(0, match.index).split('\n').length;
      const endLineNumber = content.substring(0, match.index + match[0].length).split('\n').length;

      // Calculate confidence based on context
      const contextualConfidence = calculateContextualConfidence(content, match, pattern);

      const patternMatch: PatternMatch = {
        pattern,
        location: {
          filePath,
          startLine: lineNumber,
          endLine: endLineNumber,
          startColumn: match.index - content.lastIndexOf('\n', match.index) - 1,
          endColumn: match.index + match[0].length - content.lastIndexOf('\n', match.index) - 1
        },
        confidence: contextualConfidence,
        context: extractContext(content, match.index, 100),
        suggestions: pattern.suggestions,
        severity: pattern.severity
      };

      matches.push(patternMatch);
    }
  } catch (error) {
    console.error(`Error in pattern detection for ${pattern.id}:`, error);
  }

  return matches;
}

/**
 * Calculate contextual confidence for a pattern match
 */
function calculateContextualConfidence(content: string, match: RegExpExecArray, pattern: CodePattern): number {
  let confidence = pattern.confidence;

  // Adjust confidence based on context
  const context = extractContext(content, match.index, 200);
  
  // Reduce confidence if in comments
  if (isInComment(content, match.index)) {
    confidence *= 0.3;
  }

  // Reduce confidence if in string literals (unless it's a security pattern)
  if (isInStringLiteral(content, match.index) && pattern.category !== 'security') {
    confidence *= 0.5;
  }

  // Increase confidence for security patterns in actual code
  if (pattern.category === 'security' && !isInComment(content, match.index)) {
    confidence *= 1.2;
  }

  // Adjust based on surrounding code quality
  const surroundingComplexity = calculateSurroundingComplexity(context);
  if (surroundingComplexity > 10) {
    confidence *= 1.1; // Higher confidence in complex code
  }

  return Math.min(1.0, Math.max(0.1, confidence));
}

/**
 * Extract context around a match
 */
function extractContext(content: string, index: number, contextLength: number): string {
  const start = Math.max(0, index - contextLength / 2);
  const end = Math.min(content.length, index + contextLength / 2);
  return content.substring(start, end);
}

/**
 * Check if index is within a comment
 */
function isInComment(content: string, index: number): boolean {
  const beforeIndex = content.substring(0, index);
  
  // Check for single-line comment
  const lastNewline = beforeIndex.lastIndexOf('\n');
  const lineContent = beforeIndex.substring(lastNewline + 1);
  if (lineContent.includes('//')) {
    return true;
  }

  // Check for multi-line comment
  const lastCommentStart = beforeIndex.lastIndexOf('/*');
  const lastCommentEnd = beforeIndex.lastIndexOf('*/');
  
  return lastCommentStart > lastCommentEnd;
}

/**
 * Check if index is within a string literal
 */
function isInStringLiteral(content: string, index: number): boolean {
  const beforeIndex = content.substring(0, index);
  
  // Count unescaped quotes
  let singleQuotes = 0;
  let doubleQuotes = 0;
  let backQuotes = 0;
  
  for (let i = 0; i < beforeIndex.length; i++) {
    const char = beforeIndex[i];
    const prevChar = i > 0 ? beforeIndex[i - 1] : '';
    
    if (char === "'" && prevChar !== '\\') singleQuotes++;
    if (char === '"' && prevChar !== '\\') doubleQuotes++;
    if (char === '`' && prevChar !== '\\') backQuotes++;
  }
  
  return (singleQuotes % 2 === 1) || (doubleQuotes % 2 === 1) || (backQuotes % 2 === 1);
}

/**
 * Calculate complexity of surrounding code
 */
function calculateSurroundingComplexity(context: string): number {
  const patterns = [
    /\bif\s*\(/g,
    /\bwhile\s*\(/g,
    /\bfor\s*\(/g,
    /\bswitch\s*\(/g,
    /\btry\s*{/g,
    /&&|\|\|/g
  ];

  let complexity = 0;
  for (const pattern of patterns) {
    const matches = context.match(pattern);
    if (matches) complexity += matches.length;
  }

  return complexity;
}

/**
 * Get patterns by category
 */
export function getPatternsByCategory(category: CodePattern['category']): CodePattern[] {
  return BUILT_IN_PATTERNS.filter(pattern => pattern.category === category);
}

/**
 * Get patterns by type
 */
export function getPatternsByType(type: CodePattern['type']): CodePattern[] {
  return BUILT_IN_PATTERNS.filter(pattern => pattern.type === type);
}

/**
 * Get patterns by severity
 */
export function getPatternsBySeverity(severity: CodePattern['severity']): CodePattern[] {
  return BUILT_IN_PATTERNS.filter(pattern => pattern.severity === severity);
}

/**
 * Add custom pattern
 */
export function addCustomPattern(pattern: CodePattern): void {
  // Validate pattern
  if (!pattern.id || !pattern.name || !pattern.pattern) {
    throw new Error('Invalid pattern: missing required fields');
  }

  // Test pattern regex
  try {
    new RegExp(pattern.pattern);
  } catch (error) {
    throw new Error(`Invalid pattern regex: ${error.message}`);
  }

  BUILT_IN_PATTERNS.push(pattern);
}

/**
 * Remove custom pattern
 */
export function removeCustomPattern(patternId: string): boolean {
  const index = BUILT_IN_PATTERNS.findIndex(p => p.id === patternId);
  if (index > -1) {
    BUILT_IN_PATTERNS.splice(index, 1);
    return true;
  }
  return false;
}

/**
 * Validate pattern configuration
 */
export function validatePattern(pattern: Partial<CodePattern>): { valid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!pattern.id) errors.push('Pattern ID is required');
  if (!pattern.name) errors.push('Pattern name is required');
  if (!pattern.pattern) errors.push('Pattern regex is required');
  if (!pattern.type) errors.push('Pattern type is required');
  if (!pattern.category) errors.push('Pattern category is required');
  if (!pattern.severity) errors.push('Pattern severity is required');

  if (pattern.pattern) {
    try {
      new RegExp(pattern.pattern);
    } catch (error) {
      errors.push(`Invalid regex pattern: ${error.message}`);
    }
  }

  if (pattern.confidence !== undefined && (pattern.confidence < 0 || pattern.confidence > 1)) {
    errors.push('Confidence must be between 0 and 1');
  }

  return {
    valid: errors.length === 0,
    errors
  };
}
