/**
 * Semantic Analysis Utilities
 * 
 * Common parsing, validation, and transformation utilities for semantic analysis.
 * Extracted from semantic-code-analysis.ts for better modularity.
 */

import { ASTNode, DependencyNode, CodeQualityMetrics, QualityIssue } from './types';

/**
 * Generate cache key for analysis results
 */
export function generateCacheKey(filePath: string, content: string): string {
  // Simple hash function for cache key
  const hash = content.split('').reduce((a, b) => {
    a = ((a << 5) - a) + b.charCodeAt(0);
    return a & a;
  }, 0);
  return `${filePath}-${hash}`;
}

/**
 * Detect programming language from file path
 */
export function detectLanguage(filePath: string): string {
  const extension = filePath.split('.').pop()?.toLowerCase();
  const languageMap: Record<string, string> = {
    'ts': 'typescript',
    'tsx': 'typescript',
    'js': 'javascript',
    'jsx': 'javascript',
    'py': 'python',
    'java': 'java',
    'cpp': 'cpp',
    'c': 'c',
    'cs': 'csharp',
    'php': 'php',
    'rb': 'ruby',
    'go': 'go',
    'rs': 'rust'
  };
  return languageMap[extension || ''] || 'unknown';
}

/**
 * Split array into chunks for parallel processing
 */
export function chunkArray<T>(array: T[], chunkSize: number): T[][] {
  const chunks: T[][] = [];
  for (let i = 0; i < array.length; i += chunkSize) {
    chunks.push(array.slice(i, i + chunkSize));
  }
  return chunks;
}

/**
 * Create empty dependency node
 */
export function createEmptyDependencyNode(filePath: string, language: string): DependencyNode {
  return {
    id: generateDependencyId(filePath),
    name: filePath.split('/').pop() || filePath,
    type: 'file',
    filePath,
    language,
    isExternal: false,
    dependencies: [],
    dependents: [],
    metadata: {}
  };
}

/**
 * Create empty quality metrics
 */
export function createEmptyQualityMetrics(filePath: string, language: string): CodeQualityMetrics {
  return {
    filePath,
    language,
    metrics: {
      linesOfCode: 0,
      cyclomaticComplexity: 0,
      cognitiveComplexity: 0,
      maintainabilityIndex: 0,
      technicalDebt: 0,
      duplicateLines: 0,
      codeSmells: 0,
      bugs: 0,
      vulnerabilities: 0,
      hotspots: 0
    },
    scores: {
      overall: 0,
      maintainability: 0,
      reliability: 0,
      security: 0,
      performance: 0,
      readability: 0
    },
    issues: [],
    suggestions: [],
    trends: {
      complexityTrend: 'stable',
      maintainabilityTrend: 'stable',
      lastAnalysis: Date.now()
    }
  };
}

/**
 * Calculate cyclomatic complexity
 */
export function calculateCyclomaticComplexity(content: string): number {
  // Count decision points: if, while, for, case, catch, &&, ||, ?:
  const patterns = [
    /\bif\s*\(/g,
    /\bwhile\s*\(/g,
    /\bfor\s*\(/g,
    /\bcase\s+/g,
    /\bcatch\s*\(/g,
    /&&/g,
    /\|\|/g,
    /\?/g
  ];

  let complexity = 1; // Base complexity
  for (const pattern of patterns) {
    const matches = content.match(pattern);
    if (matches) {
      complexity += matches.length;
    }
  }

  return complexity;
}

/**
 * Calculate cognitive complexity
 */
export function calculateCognitiveComplexity(content: string): number {
  // Simplified cognitive complexity calculation
  let complexity = 0;
  const lines = content.split('\n');
  let nestingLevel = 0;

  for (const line of lines) {
    const trimmed = line.trim();

    // Increase nesting for blocks
    if (trimmed.includes('{')) nestingLevel++;
    if (trimmed.includes('}')) nestingLevel = Math.max(0, nestingLevel - 1);

    // Add complexity for control structures
    if (/\b(if|while|for|switch)\s*\(/.test(trimmed)) {
      complexity += nestingLevel + 1;
    }

    // Add complexity for logical operators
    const logicalOps = (trimmed.match(/&&|\|\|/g) || []).length;
    complexity += logicalOps;
  }

  return complexity;
}

/**
 * Calculate maintainability index
 */
export function calculateMaintainabilityIndex(linesOfCode: number, cyclomaticComplexity: number): number {
  // Simplified maintainability index calculation
  const halsteadVolume = linesOfCode * 4.7; // Simplified
  const maintainabilityIndex = Math.max(0,
    171 - 5.2 * Math.log(halsteadVolume) - 0.23 * cyclomaticComplexity - 16.2 * Math.log(linesOfCode)
  );
  return Math.round(maintainabilityIndex);
}

/**
 * Calculate simple complexity metric
 */
export function calculateSimpleComplexity(content: string): number {
  // Simple complexity based on various factors
  const lines = content.split('\n').length;
  const functions = (content.match(/function|=>/g) || []).length;
  const classes = (content.match(/class\s+\w+/g) || []).length;
  const imports = (content.match(/import|require/g) || []).length;

  return Math.round((lines * 0.1) + (functions * 2) + (classes * 3) + (imports * 0.5));
}

/**
 * Calculate readability score
 */
export function calculateReadabilityScore(content: string): number {
  const lines = content.split('\n');
  const totalLines = lines.length;
  const nonEmptyLines = lines.filter(line => line.trim()).length;
  const commentLines = lines.filter(line => line.trim().startsWith('//') || line.trim().startsWith('/*')).length;

  // Calculate various readability factors
  const commentRatio = commentLines / nonEmptyLines;
  const avgLineLength = lines.reduce((sum, line) => sum + line.length, 0) / totalLines;
  const emptyLineRatio = (totalLines - nonEmptyLines) / totalLines;

  // Score based on good practices
  let score = 50; // Base score

  // Good comment ratio (10-30%)
  if (commentRatio >= 0.1 && commentRatio <= 0.3) score += 20;
  else if (commentRatio > 0.05) score += 10;

  // Reasonable line length (< 100 chars)
  if (avgLineLength < 80) score += 15;
  else if (avgLineLength < 120) score += 10;
  else score -= 10;

  // Good use of whitespace (5-15% empty lines)
  if (emptyLineRatio >= 0.05 && emptyLineRatio <= 0.15) score += 15;
  else if (emptyLineRatio > 0.02) score += 5;

  return Math.max(0, Math.min(100, score));
}

/**
 * Calculate AST depth
 */
export function calculateASTDepth(node: ASTNode): number {
  if (node.children.length === 0) return 1;

  const childDepths = node.children.map(child => calculateASTDepth(child));
  return 1 + Math.max(...childDepths);
}

/**
 * Extract entities from AST
 */
export function extractEntitiesFromAST(ast: ASTNode, entities: string[]): void {
  if (ast.name) {
    entities.push(ast.name);
  }

  for (const child of ast.children) {
    extractEntitiesFromAST(child, entities);
  }
}

/**
 * Generate dependency ID
 */
export function generateDependencyId(filePath: string): string {
  return `dep-${filePath.replace(/[^a-zA-Z0-9]/g, '-')}`;
}

/**
 * Generate quality suggestions
 */
export function generateQualitySuggestions(complexity: number, linesOfCode: number, codeSmells: number): string[] {
  const suggestions: string[] = [];

  if (complexity > 15) {
    suggestions.push('High complexity detected - consider refactoring into smaller functions');
  }

  if (linesOfCode > 500) {
    suggestions.push('Large file detected - consider splitting into multiple modules');
  }

  if (codeSmells > 5) {
    suggestions.push('Multiple code smells detected - review for maintainability improvements');
  }

  return suggestions;
}

/**
 * Create quality issues from different types of problems
 */
export function createIssuesFromSmells(count: number): QualityIssue[] {
  if (count === 0) return [];

  return [{
    id: 'code-smells',
    type: 'code_smell',
    severity: count > 5 ? 'major' : 'minor',
    message: `${count} code smell${count > 1 ? 's' : ''} detected`,
    description: 'Code smells indicate areas that may need refactoring',
    location: { startLine: 1, endLine: 1, startColumn: 1, endColumn: 1 },
    rule: 'code-quality',
    category: 'maintainability',
    effort: count * 15,
    suggestions: ['Review code for refactoring opportunities', 'Apply clean code principles']
  }];
}

export function createIssuesFromBugs(count: number): QualityIssue[] {
  if (count === 0) return [];

  return [{
    id: 'potential-bugs',
    type: 'bug',
    severity: count > 2 ? 'critical' : 'major',
    message: `${count} potential bug${count > 1 ? 's' : ''} detected`,
    description: 'Potential bugs that may cause runtime errors',
    location: { startLine: 1, endLine: 1, startColumn: 1, endColumn: 1 },
    rule: 'bug-detection',
    category: 'reliability',
    effort: count * 30,
    suggestions: ['Add null checks', 'Handle promises properly', 'Review loop conditions']
  }];
}

export function createIssuesFromVulnerabilities(count: number): QualityIssue[] {
  if (count === 0) return [];

  return [{
    id: 'security-vulnerabilities',
    type: 'vulnerability',
    severity: 'critical',
    message: `${count} security vulnerabilit${count > 1 ? 'ies' : 'y'} detected`,
    description: 'Security vulnerabilities that may expose the application to attacks',
    location: { startLine: 1, endLine: 1, startColumn: 1, endColumn: 1 },
    rule: 'security-check',
    category: 'security',
    effort: count * 60,
    suggestions: ['Remove hardcoded secrets', 'Sanitize user inputs', 'Use parameterized queries']
  }];
}

export function createIssuesFromHotspots(count: number): QualityIssue[] {
  if (count === 0) return [];

  return [{
    id: 'performance-hotspots',
    type: 'hotspot',
    severity: count > 2 ? 'major' : 'minor',
    message: `${count} performance hotspot${count > 1 ? 's' : ''} detected`,
    description: 'Performance hotspots that may impact application speed',
    location: { startLine: 1, endLine: 1, startColumn: 1, endColumn: 1 },
    rule: 'performance-check',
    category: 'performance',
    effort: count * 45,
    suggestions: ['Optimize nested loops', 'Use asynchronous operations', 'Cache expensive computations']
  }];
}
