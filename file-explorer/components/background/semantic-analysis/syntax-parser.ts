/**
 * Syntax Parser
 * 
 * Handles AST generation and syntax tree analysis.
 * Extracted from semantic-code-analysis.ts for better modularity.
 */

import { ASTNode } from './types';

/**
 * Parse AST from code content
 * 
 * This is a simplified AST parser for demonstration.
 * In a real implementation, you would use language-specific parsers like:
 * - TypeScript: typescript compiler API
 * - JavaScript: @babel/parser, acorn, or esprima
 * - Python: ast module
 * - Java: JavaParser
 */
export async function parseAST(content: string, language: string, filePath: string = ''): Promise<ASTNode> {
  const lines = content.split('\n');

  const rootNode: ASTNode = {
    id: 'root',
    type: 'Program',
    children: [],
    startLine: 1,
    endLine: lines.length,
    startColumn: 0,
    endColumn: 0,
    filePath,
    language,
    metadata: {}
  };

  // Simple pattern-based parsing for demonstration
  const functionPattern = /(?:function|const|let|var)\s+(\w+)\s*(?:=\s*)?(?:\([^)]*\))?\s*(?:=>)?\s*{/g;
  const classPattern = /class\s+(\w+)(?:\s+extends\s+\w+)?\s*{/g;
  const importPattern = /import\s+(?:{[^}]+}|\w+|\*\s+as\s+\w+)\s+from\s+['"][^'"]+['"]/g;

  let nodeId = 1;

  // Parse functions
  await parseFunctions(content, rootNode, functionPattern, nodeId, language, filePath);
  nodeId += rootNode.children.filter(n => n.type === 'FunctionDeclaration').length;

  // Parse classes
  await parseClasses(content, rootNode, classPattern, nodeId, language, filePath);
  nodeId += rootNode.children.filter(n => n.type === 'ClassDeclaration').length;

  // Parse imports
  await parseImports(content, rootNode, importPattern, nodeId, language, filePath);

  return rootNode;
}

/**
 * Parse function declarations
 */
async function parseFunctions(
  content: string, 
  rootNode: ASTNode, 
  pattern: RegExp, 
  startNodeId: number, 
  language: string, 
  filePath: string
): Promise<void> {
  let match;
  let nodeId = startNodeId;

  while ((match = pattern.exec(content)) !== null) {
    const lineNumber = content.substring(0, match.index).split('\n').length;
    const functionNode: ASTNode = {
      id: `func_${nodeId++}`,
      type: 'FunctionDeclaration',
      name: match[1],
      children: [],
      parent: rootNode,
      startLine: lineNumber,
      endLine: lineNumber + 10, // Simplified
      startColumn: match.index - content.lastIndexOf('\n', match.index) - 1,
      endColumn: match.index + match[0].length,
      filePath,
      language,
      metadata: {
        isExported: content.includes(`export ${match[0]}`),
        parameters: await extractParameters(match[0]),
        complexity: calculateFunctionComplexity(content, match.index)
      }
    };
    rootNode.children.push(functionNode);
  }
}

/**
 * Parse class declarations
 */
async function parseClasses(
  content: string, 
  rootNode: ASTNode, 
  pattern: RegExp, 
  startNodeId: number, 
  language: string, 
  filePath: string
): Promise<void> {
  let match;
  let nodeId = startNodeId;

  while ((match = pattern.exec(content)) !== null) {
    const lineNumber = content.substring(0, match.index).split('\n').length;
    const classNode: ASTNode = {
      id: `class_${nodeId++}`,
      type: 'ClassDeclaration',
      name: match[1],
      children: [],
      parent: rootNode,
      startLine: lineNumber,
      endLine: lineNumber + 20, // Simplified
      startColumn: match.index - content.lastIndexOf('\n', match.index) - 1,
      endColumn: match.index + match[0].length,
      filePath,
      language,
      metadata: {
        isExported: content.includes(`export class ${match[1]}`),
        extends: match[0].includes('extends') ? ['BaseClass'] : [],
        complexity: calculateClassComplexity(content, match.index)
      }
    };

    // Parse methods within the class
    await parseClassMethods(content, classNode, match.index, nodeId);
    
    rootNode.children.push(classNode);
  }
}

/**
 * Parse import declarations
 */
async function parseImports(
  content: string, 
  rootNode: ASTNode, 
  pattern: RegExp, 
  startNodeId: number, 
  language: string, 
  filePath: string
): Promise<void> {
  let match;
  let nodeId = startNodeId;

  while ((match = pattern.exec(content)) !== null) {
    const lineNumber = content.substring(0, match.index).split('\n').length;
    const importNode: ASTNode = {
      id: `import_${nodeId++}`,
      type: 'ImportDeclaration',
      children: [],
      parent: rootNode,
      startLine: lineNumber,
      endLine: lineNumber,
      startColumn: match.index - content.lastIndexOf('\n', match.index) - 1,
      endColumn: match.index + match[0].length,
      filePath,
      language,
      metadata: {
        importType: determineImportType(match[0]),
        source: extractImportSource(match[0])
      }
    };
    rootNode.children.push(importNode);
  }
}

/**
 * Parse methods within a class
 */
async function parseClassMethods(content: string, classNode: ASTNode, classStartIndex: number, startNodeId: number): Promise<void> {
  // Find the class body
  const classContent = extractClassBody(content, classStartIndex);
  if (!classContent) return;

  const methodPattern = /(?:public|private|protected)?\s*(?:static)?\s*(\w+)\s*\([^)]*\)\s*{/g;
  let match;
  let nodeId = startNodeId;

  while ((match = methodPattern.exec(classContent)) !== null) {
    const methodNode: ASTNode = {
      id: `method_${nodeId++}`,
      type: 'MethodDefinition',
      name: match[1],
      children: [],
      parent: classNode,
      startLine: classNode.startLine + classContent.substring(0, match.index).split('\n').length,
      endLine: classNode.startLine + classContent.substring(0, match.index).split('\n').length + 5, // Simplified
      startColumn: match.index - classContent.lastIndexOf('\n', match.index) - 1,
      endColumn: match.index + match[0].length,
      filePath: classNode.filePath,
      language: classNode.language,
      metadata: {
        visibility: extractVisibility(match[0]),
        isStatic: match[0].includes('static'),
        parameters: await extractParameters(match[0])
      }
    };
    classNode.children.push(methodNode);
  }
}

/**
 * Extract parameters from function/method signature
 */
async function extractParameters(signature: string): Promise<any[]> {
  const paramMatch = signature.match(/\(([^)]*)\)/);
  if (!paramMatch || !paramMatch[1].trim()) return [];

  const paramString = paramMatch[1];
  const params = paramString.split(',').map(param => {
    const trimmed = param.trim();
    const parts = trimmed.split(':');
    return {
      name: parts[0]?.trim() || 'unknown',
      type: parts[1]?.trim() || 'any',
      optional: trimmed.includes('?'),
      defaultValue: trimmed.includes('=') ? trimmed.split('=')[1]?.trim() : undefined
    };
  });

  return params;
}

/**
 * Calculate function complexity
 */
function calculateFunctionComplexity(content: string, functionStartIndex: number): number {
  const functionBody = extractFunctionBody(content, functionStartIndex);
  if (!functionBody) return 1;

  // Count decision points
  const patterns = [/\bif\s*\(/g, /\bwhile\s*\(/g, /\bfor\s*\(/g, /\bswitch\s*\(/g];
  let complexity = 1;

  for (const pattern of patterns) {
    const matches = functionBody.match(pattern);
    if (matches) complexity += matches.length;
  }

  return complexity;
}

/**
 * Calculate class complexity
 */
function calculateClassComplexity(content: string, classStartIndex: number): number {
  const classBody = extractClassBody(content, classStartIndex);
  if (!classBody) return 1;

  // Count methods and properties
  const methods = (classBody.match(/\w+\s*\([^)]*\)\s*{/g) || []).length;
  const properties = (classBody.match(/\w+\s*[:=]/g) || []).length;

  return methods + properties;
}

/**
 * Extract function body from content
 */
function extractFunctionBody(content: string, startIndex: number): string | null {
  let braceCount = 0;
  let inFunction = false;
  let functionStart = -1;

  for (let i = startIndex; i < content.length; i++) {
    const char = content[i];
    
    if (char === '{') {
      if (!inFunction) {
        inFunction = true;
        functionStart = i;
      }
      braceCount++;
    } else if (char === '}') {
      braceCount--;
      if (braceCount === 0 && inFunction) {
        return content.substring(functionStart, i + 1);
      }
    }
  }

  return null;
}

/**
 * Extract class body from content
 */
function extractClassBody(content: string, startIndex: number): string | null {
  let braceCount = 0;
  let inClass = false;
  let classStart = -1;

  for (let i = startIndex; i < content.length; i++) {
    const char = content[i];
    
    if (char === '{') {
      if (!inClass) {
        inClass = true;
        classStart = i;
      }
      braceCount++;
    } else if (char === '}') {
      braceCount--;
      if (braceCount === 0 && inClass) {
        return content.substring(classStart, i + 1);
      }
    }
  }

  return null;
}

/**
 * Determine import type
 */
function determineImportType(importStatement: string): string {
  if (importStatement.includes('* as')) return 'namespace';
  if (importStatement.includes('{')) return 'named';
  if (importStatement.includes('import(')) return 'dynamic';
  return 'default';
}

/**
 * Extract import source
 */
function extractImportSource(importStatement: string): string {
  const match = importStatement.match(/from\s+['"]([^'"]+)['"]/);
  return match ? match[1] : '';
}

/**
 * Extract visibility modifier
 */
function extractVisibility(methodSignature: string): 'public' | 'private' | 'protected' {
  if (methodSignature.includes('private')) return 'private';
  if (methodSignature.includes('protected')) return 'protected';
  return 'public';
}
