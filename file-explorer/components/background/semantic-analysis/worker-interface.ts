/**
 * Worker Interface
 * 
 * Abstraction layer for background processing using Web Workers (browser) or worker_threads (Node.js).
 * Prepares for future async execution without implementing workers yet.
 */

import { WorkerMessage, WorkerResponse, AnalysisOptions, AnalysisResult } from './types';

/**
 * Worker pool manager for semantic analysis
 */
export class SemanticWorkerPool {
  private workers: Map<string, SemanticWorker> = new Map();
  private taskQueue: PendingTask[] = [];
  private maxWorkers: number;
  private activeWorkers: number = 0;

  constructor(maxWorkers: number = 4) {
    this.maxWorkers = maxWorkers;
  }

  /**
   * Submit analysis task to worker pool
   */
  async submitTask(
    type: 'analyze' | 'parse' | 'pattern' | 'quality' | 'dependency',
    payload: any
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      const taskId = generateTaskId();
      const task: PendingTask = {
        id: taskId,
        type,
        payload,
        resolve,
        reject,
        timestamp: Date.now()
      };

      this.taskQueue.push(task);
      this.processQueue();
    });
  }

  /**
   * Process task queue
   */
  private async processQueue(): Promise<void> {
    if (this.taskQueue.length === 0 || this.activeWorkers >= this.maxWorkers) {
      return;
    }

    const task = this.taskQueue.shift();
    if (!task) return;

    try {
      this.activeWorkers++;
      const worker = await this.getAvailableWorker();
      const result = await worker.executeTask(task);
      task.resolve(result);
    } catch (error) {
      task.reject(error);
    } finally {
      this.activeWorkers--;
      // Process next task
      setTimeout(() => this.processQueue(), 0);
    }
  }

  /**
   * Get available worker (creates new one if needed)
   */
  private async getAvailableWorker(): Promise<SemanticWorker> {
    // For now, return a mock worker since we're not implementing actual workers yet
    const workerId = `worker_${Date.now()}`;
    const worker = new SemanticWorker(workerId);
    this.workers.set(workerId, worker);
    return worker;
  }

  /**
   * Terminate all workers
   */
  async terminate(): Promise<void> {
    for (const worker of this.workers.values()) {
      await worker.terminate();
    }
    this.workers.clear();
    this.activeWorkers = 0;
    
    // Reject all pending tasks
    for (const task of this.taskQueue) {
      task.reject(new Error('Worker pool terminated'));
    }
    this.taskQueue = [];
  }

  /**
   * Get pool statistics
   */
  getStats(): {
    activeWorkers: number;
    maxWorkers: number;
    queueLength: number;
    totalWorkers: number;
  } {
    return {
      activeWorkers: this.activeWorkers,
      maxWorkers: this.maxWorkers,
      queueLength: this.taskQueue.length,
      totalWorkers: this.workers.size
    };
  }
}

/**
 * Individual semantic analysis worker
 */
export class SemanticWorker {
  private id: string;
  private busy: boolean = false;
  private startTime: number;

  constructor(id: string) {
    this.id = id;
    this.startTime = Date.now();
  }

  /**
   * Execute analysis task
   */
  async executeTask(task: PendingTask): Promise<any> {
    if (this.busy) {
      throw new Error('Worker is busy');
    }

    this.busy = true;
    const startTime = Date.now();

    try {
      // For now, execute synchronously since we're not implementing actual workers
      const result = await this.processTask(task);
      
      return {
        id: task.id,
        success: true,
        result,
        duration: Date.now() - startTime
      } as WorkerResponse;
    } catch (error) {
      return {
        id: task.id,
        success: false,
        error: error.message,
        duration: Date.now() - startTime
      } as WorkerResponse;
    } finally {
      this.busy = false;
    }
  }

  /**
   * Process specific task type
   */
  private async processTask(task: PendingTask): Promise<any> {
    switch (task.type) {
      case 'analyze':
        return await this.performFullAnalysis(task.payload);
      case 'parse':
        return await this.performParsing(task.payload);
      case 'pattern':
        return await this.performPatternDetection(task.payload);
      case 'quality':
        return await this.performQualityAnalysis(task.payload);
      case 'dependency':
        return await this.performDependencyAnalysis(task.payload);
      default:
        throw new Error(`Unknown task type: ${task.type}`);
    }
  }

  /**
   * Perform full semantic analysis
   */
  private async performFullAnalysis(payload: {
    content: string;
    filePath: string;
    options: AnalysisOptions;
  }): Promise<AnalysisResult> {
    // Import analysis modules dynamically to avoid circular dependencies
    const { parseAST } = await import('./syntax-parser');
    const { recognizePatterns } = await import('./semantic-pattern-detector');
    const { detectLanguage, createEmptyDependencyNode, createEmptyQualityMetrics } = await import('./semantic-utils');

    const { content, filePath, options } = payload;
    const language = detectLanguage(filePath);

    const result: AnalysisResult = {
      filePath,
      language,
      timestamp: Date.now(),
      patterns: [],
      dependencies: createEmptyDependencyNode(filePath, language),
      qualityMetrics: createEmptyQualityMetrics(filePath, language),
      semanticInfo: {
        concepts: [],
        entities: [],
        relationships: [],
        complexity: 0,
        readability: 0
      },
      suggestions: [],
      warnings: [],
      errors: []
    };

    try {
      // Parse AST if requested
      if (options.includeAST) {
        result.ast = await parseAST(content, language, filePath);
      }

      // Detect patterns if requested
      if (options.includePatterns) {
        result.patterns = await recognizePatterns(content, language, options.customPatterns, filePath);
      }

      // Add basic semantic info
      if (options.includeSemanticAnalysis) {
        result.semanticInfo = await this.extractSemanticInfo(content, language);
      }

    } catch (error) {
      result.errors.push(`Analysis error: ${error.message}`);
    }

    return result;
  }

  /**
   * Perform AST parsing only
   */
  private async performParsing(payload: { content: string; language: string; filePath: string }): Promise<any> {
    const { parseAST } = await import('./syntax-parser');
    return await parseAST(payload.content, payload.language, payload.filePath);
  }

  /**
   * Perform pattern detection only
   */
  private async performPatternDetection(payload: {
    content: string;
    language: string;
    customPatterns: any[];
    filePath: string;
  }): Promise<any> {
    const { recognizePatterns } = await import('./semantic-pattern-detector');
    return await recognizePatterns(payload.content, payload.language, payload.customPatterns, payload.filePath);
  }

  /**
   * Perform quality analysis only
   */
  private async performQualityAnalysis(payload: { content: string; filePath: string }): Promise<any> {
    const { 
      calculateCyclomaticComplexity,
      calculateCognitiveComplexity,
      calculateMaintainabilityIndex,
      calculateReadabilityScore,
      createEmptyQualityMetrics
    } = await import('./semantic-utils');

    const { content, filePath } = payload;
    const language = payload.language || 'unknown';
    
    const metrics = createEmptyQualityMetrics(filePath, language);
    
    // Calculate basic metrics
    metrics.metrics.linesOfCode = content.split('\n').length;
    metrics.metrics.cyclomaticComplexity = calculateCyclomaticComplexity(content);
    metrics.metrics.cognitiveComplexity = calculateCognitiveComplexity(content);
    metrics.metrics.maintainabilityIndex = calculateMaintainabilityIndex(
      metrics.metrics.linesOfCode,
      metrics.metrics.cyclomaticComplexity
    );

    // Calculate readability
    const readability = calculateReadabilityScore(content);
    metrics.scores.readability = readability;
    metrics.scores.overall = (readability + metrics.metrics.maintainabilityIndex) / 2;

    return metrics;
  }

  /**
   * Perform dependency analysis only
   */
  private async performDependencyAnalysis(payload: { content: string; filePath: string }): Promise<any> {
    const { createEmptyDependencyNode } = await import('./semantic-utils');
    
    // Simple dependency extraction
    const dependencies: string[] = [];
    const importMatches = payload.content.match(/import\s+.*?from\s+['"]([^'"]+)['"]/g);
    
    if (importMatches) {
      for (const match of importMatches) {
        const sourceMatch = match.match(/from\s+['"]([^'"]+)['"]/);
        if (sourceMatch) {
          dependencies.push(sourceMatch[1]);
        }
      }
    }

    const node = createEmptyDependencyNode(payload.filePath, 'unknown');
    node.dependencies = dependencies;
    
    return node;
  }

  /**
   * Extract semantic information
   */
  private async extractSemanticInfo(content: string, language: string): Promise<any> {
    const { calculateSimpleComplexity, calculateReadabilityScore } = await import('./semantic-utils');

    // Extract basic semantic information
    const concepts = this.extractConcepts(content);
    const entities = this.extractEntities(content);
    const relationships = this.extractRelationships(content);
    
    return {
      concepts,
      entities,
      relationships,
      complexity: calculateSimpleComplexity(content),
      readability: calculateReadabilityScore(content)
    };
  }

  /**
   * Extract concepts from content
   */
  private extractConcepts(content: string): string[] {
    // Simple concept extraction based on common programming concepts
    const concepts: string[] = [];
    const conceptPatterns = [
      { pattern: /class\s+\w+/g, concept: 'object-oriented' },
      { pattern: /function\s+\w+/g, concept: 'functional' },
      { pattern: /async\s+/g, concept: 'asynchronous' },
      { pattern: /Promise/g, concept: 'promises' },
      { pattern: /try\s*{/g, concept: 'error-handling' },
      { pattern: /import\s+/g, concept: 'modular' }
    ];

    for (const { pattern, concept } of conceptPatterns) {
      if (pattern.test(content)) {
        concepts.push(concept);
      }
    }

    return [...new Set(concepts)]; // Remove duplicates
  }

  /**
   * Extract entities from content
   */
  private extractEntities(content: string): string[] {
    const entities: string[] = [];
    
    // Extract class names
    const classMatches = content.match(/class\s+(\w+)/g);
    if (classMatches) {
      entities.push(...classMatches.map(match => match.replace('class ', '')));
    }

    // Extract function names
    const functionMatches = content.match(/function\s+(\w+)/g);
    if (functionMatches) {
      entities.push(...functionMatches.map(match => match.replace('function ', '')));
    }

    return [...new Set(entities)]; // Remove duplicates
  }

  /**
   * Extract relationships from content
   */
  private extractRelationships(content: string): string[] {
    const relationships: string[] = [];
    
    if (content.includes('extends')) relationships.push('inheritance');
    if (content.includes('implements')) relationships.push('implementation');
    if (content.includes('import')) relationships.push('dependency');
    if (content.includes('new ')) relationships.push('instantiation');

    return [...new Set(relationships)]; // Remove duplicates
  }

  /**
   * Terminate worker
   */
  async terminate(): Promise<void> {
    this.busy = false;
    // In a real implementation, this would terminate the actual worker thread
  }

  /**
   * Get worker info
   */
  getInfo(): { id: string; busy: boolean; uptime: number } {
    return {
      id: this.id,
      busy: this.busy,
      uptime: Date.now() - this.startTime
    };
  }
}

/**
 * Pending task interface
 */
interface PendingTask {
  id: string;
  type: 'analyze' | 'parse' | 'pattern' | 'quality' | 'dependency';
  payload: any;
  resolve: (value: any) => void;
  reject: (error: Error) => void;
  timestamp: number;
}

/**
 * Generate unique task ID
 */
function generateTaskId(): string {
  return `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Create worker pool instance
 */
export function createWorkerPool(maxWorkers: number = 4): SemanticWorkerPool {
  return new SemanticWorkerPool(maxWorkers);
}

/**
 * Check if workers are supported in current environment
 */
export function isWorkerSupported(): boolean {
  // Check for Web Workers (browser)
  if (typeof Worker !== 'undefined') {
    return true;
  }

  // Check for worker_threads (Node.js)
  try {
    require('worker_threads');
    return true;
  } catch {
    return false;
  }
}

/**
 * Get recommended worker count based on system
 */
export function getRecommendedWorkerCount(): number {
  // In browser
  if (typeof navigator !== 'undefined' && navigator.hardwareConcurrency) {
    return Math.max(2, Math.min(8, navigator.hardwareConcurrency - 1));
  }

  // In Node.js
  try {
    const os = require('os');
    const cpuCount = os.cpus().length;
    return Math.max(2, Math.min(8, cpuCount - 1));
  } catch {
    return 4; // Default fallback
  }
}
