/**
 * Semantic Analysis Module - Barrel Exports
 * 
 * Unified export surface for all semantic analysis functionality.
 * Provides clean imports for consumers of the semantic analysis system.
 */

// Core types
export * from './types';

// Syntax parsing
export { parseAST } from './syntax-parser';

// Pattern detection
export { 
  recognizePatterns,
  BUILT_IN_PATTERNS,
  getPatternsByCategory,
  getPatternsByType,
  getPatternsBySeverity,
  addCustomPattern,
  removeCustomPattern,
  validatePattern
} from './semantic-pattern-detector';

// Cost estimation
export { 
  estimateAnalysisCost,
  estimateBatchCost,
  getMemoryRecommendations
} from './execution-cost-estimator';

// Ranking and scoring
export { 
  calculateSemanticRanking,
  rankFilesByScore,
  getTopFilesByCriteria,
  calculateTrendAnalysis
} from './semantic-ranking-engine';

// Worker interface
export { 
  SemanticWorkerPool,
  SemanticWorker,
  createWorkerPool,
  isWorkerSupported,
  getRecommendedWorkerCount
} from './worker-interface';

// Utilities
export { 
  generateCacheKey,
  detectLanguage,
  chunkArray,
  createEmptyDependencyNode,
  createEmptyQualityMetrics,
  calculateCyclomaticComplexity,
  calculateCognitiveComplexity,
  calculateMaintainabilityIndex,
  calculateSimpleComplexity,
  calculateReadabilityScore,
  calculateASTDepth,
  extractEntitiesFromAST,
  generateDependencyId,
  generateQualitySuggestions,
  createIssuesFromSmells,
  createIssuesFromBugs,
  createIssuesFromVulnerabilities,
  createIssuesFromHotspots
} from './semantic-utils';
