/**
 * Semantic Ranking Engine
 * 
 * Handles relevance scoring, ranking logic, and priority determination for analysis results.
 * Extracted from semantic-code-analysis.ts for better modularity.
 */

import { AnalysisResult, SemanticRanking, CodeQualityMetrics, PatternMatch } from './types';

/**
 * Calculate semantic ranking for analysis results
 */
export function calculateSemanticRanking(results: AnalysisResult[]): SemanticRanking[] {
  return results.map(result => {
    const relevanceScore = calculateRelevanceScore(result);
    const qualityScore = calculateQualityScore(result.qualityMetrics);
    const complexityScore = calculateComplexityScore(result);
    const priorityScore = calculatePriorityScore(result);
    
    const overallRank = (relevanceScore + qualityScore + complexityScore + priorityScore) / 4;

    return {
      relevanceScore,
      qualityScore,
      complexityScore,
      priorityScore,
      overallRank,
      factors: {
        fileSize: result.qualityMetrics.metrics.linesOfCode,
        dependencies: result.dependencies.dependencies.length,
        patterns: result.patterns.length,
        issues: result.qualityMetrics.issues.length,
        lastModified: result.timestamp
      }
    };
  });
}

/**
 * Calculate relevance score based on semantic information
 */
function calculateRelevanceScore(result: AnalysisResult): number {
  let score = 0.5; // Base score

  // Factor in semantic concepts
  const conceptCount = result.semanticInfo.concepts.length;
  score += Math.min(0.3, conceptCount * 0.02); // Up to 0.3 for concepts

  // Factor in entities
  const entityCount = result.semanticInfo.entities.length;
  score += Math.min(0.2, entityCount * 0.01); // Up to 0.2 for entities

  // Factor in relationships
  const relationshipCount = result.semanticInfo.relationships.length;
  score += Math.min(0.2, relationshipCount * 0.05); // Up to 0.2 for relationships

  // Factor in file type importance
  const fileTypeScore = getFileTypeImportance(result.filePath);
  score *= fileTypeScore;

  // Factor in export/public visibility
  if (result.ast) {
    const exportedItems = countExportedItems(result.ast);
    score += Math.min(0.1, exportedItems * 0.02); // Up to 0.1 for exports
  }

  return Math.max(0, Math.min(1, score));
}

/**
 * Calculate quality score from metrics
 */
function calculateQualityScore(metrics: CodeQualityMetrics): number {
  const scores = metrics.scores;
  
  // Weighted average of quality scores
  const weights = {
    overall: 0.3,
    maintainability: 0.25,
    reliability: 0.2,
    security: 0.15,
    performance: 0.1
  };

  const weightedScore = (
    scores.overall * weights.overall +
    scores.maintainability * weights.maintainability +
    scores.reliability * weights.reliability +
    scores.security * weights.security +
    scores.performance * weights.performance
  ) / 100; // Convert from 0-100 to 0-1

  // Penalize for critical issues
  const criticalIssues = metrics.issues.filter(issue => 
    issue.severity === 'critical' || issue.severity === 'blocker'
  ).length;
  
  const penalty = Math.min(0.5, criticalIssues * 0.1);
  
  return Math.max(0, Math.min(1, weightedScore - penalty));
}

/**
 * Calculate complexity score (lower complexity = higher score)
 */
function calculateComplexityScore(result: AnalysisResult): number {
  const metrics = result.qualityMetrics.metrics;
  
  // Normalize complexity metrics (invert so lower complexity = higher score)
  const cyclomaticScore = Math.max(0, 1 - (metrics.cyclomaticComplexity / 20));
  const cognitiveScore = Math.max(0, 1 - (metrics.cognitiveComplexity / 30));
  const sizeScore = Math.max(0, 1 - (metrics.linesOfCode / 1000));
  const semanticComplexityScore = Math.max(0, 1 - (result.semanticInfo.complexity / 50));

  // Weighted average
  return (cyclomaticScore * 0.3 + cognitiveScore * 0.3 + sizeScore * 0.2 + semanticComplexityScore * 0.2);
}

/**
 * Calculate priority score based on various factors
 */
function calculatePriorityScore(result: AnalysisResult): number {
  let score = 0.5; // Base score

  // High priority for files with security issues
  const securityIssues = result.qualityMetrics.issues.filter(issue => 
    issue.category === 'security'
  ).length;
  score += Math.min(0.3, securityIssues * 0.1);

  // High priority for files with many dependencies
  const dependencyCount = result.dependencies.dependencies.length;
  score += Math.min(0.2, dependencyCount * 0.02);

  // High priority for files with anti-patterns
  const antiPatterns = result.patterns.filter(pattern => 
    pattern.pattern.type === 'anti_pattern'
  ).length;
  score += Math.min(0.2, antiPatterns * 0.05);

  // High priority for recently modified files
  const daysSinceModified = (Date.now() - result.timestamp) / (1000 * 60 * 60 * 24);
  if (daysSinceModified < 7) {
    score += 0.1; // Recent files get priority boost
  }

  // High priority for core/main files
  if (isCorefile(result.filePath)) {
    score += 0.15;
  }

  return Math.max(0, Math.min(1, score));
}

/**
 * Get file type importance multiplier
 */
function getFileTypeImportance(filePath: string): number {
  const fileName = filePath.split('/').pop()?.toLowerCase() || '';
  const extension = fileName.split('.').pop() || '';

  // Core files get higher importance
  if (fileName.includes('index') || fileName.includes('main') || fileName.includes('app')) {
    return 1.3;
  }

  // Configuration files
  if (fileName.includes('config') || fileName.includes('setting')) {
    return 1.2;
  }

  // Test files get lower importance
  if (fileName.includes('test') || fileName.includes('spec')) {
    return 0.7;
  }

  // Documentation files get lower importance
  if (extension === 'md' || extension === 'txt') {
    return 0.5;
  }

  // Source code files
  const sourceExtensions = ['ts', 'tsx', 'js', 'jsx', 'py', 'java', 'cpp', 'c', 'cs'];
  if (sourceExtensions.includes(extension)) {
    return 1.0;
  }

  return 0.8; // Default for other files
}

/**
 * Count exported items in AST
 */
function countExportedItems(ast: any): number {
  let count = 0;
  
  function traverse(node: any) {
    if (node.metadata?.isExported) {
      count++;
    }
    
    if (node.children) {
      for (const child of node.children) {
        traverse(child);
      }
    }
  }
  
  traverse(ast);
  return count;
}

/**
 * Check if file is a core file
 */
function isCorefile(filePath: string): boolean {
  const fileName = filePath.split('/').pop()?.toLowerCase() || '';
  const corePatterns = [
    'index',
    'main',
    'app',
    'server',
    'client',
    'router',
    'controller',
    'service',
    'model',
    'component'
  ];
  
  return corePatterns.some(pattern => fileName.includes(pattern));
}

/**
 * Rank files by overall score
 */
export function rankFilesByScore(
  results: AnalysisResult[], 
  rankings: SemanticRanking[]
): Array<{ result: AnalysisResult; ranking: SemanticRanking; rank: number }> {
  const combined = results.map((result, index) => ({
    result,
    ranking: rankings[index],
    rank: 0
  }));

  // Sort by overall rank (descending)
  combined.sort((a, b) => b.ranking.overallRank - a.ranking.overallRank);

  // Assign ranks
  combined.forEach((item, index) => {
    item.rank = index + 1;
  });

  return combined;
}

/**
 * Get top files by specific criteria
 */
export function getTopFilesByCriteria(
  results: AnalysisResult[],
  rankings: SemanticRanking[],
  criteria: 'quality' | 'complexity' | 'priority' | 'relevance',
  limit: number = 10
): Array<{ result: AnalysisResult; ranking: SemanticRanking; score: number }> {
  const combined = results.map((result, index) => ({
    result,
    ranking: rankings[index],
    score: rankings[index][`${criteria}Score`]
  }));

  return combined
    .sort((a, b) => b.score - a.score)
    .slice(0, limit);
}

/**
 * Calculate trend analysis
 */
export function calculateTrendAnalysis(
  currentResults: AnalysisResult[],
  previousResults?: AnalysisResult[]
): {
  improving: number;
  stable: number;
  degrading: number;
  newFiles: number;
  removedFiles: number;
  trends: Array<{
    filePath: string;
    trend: 'improving' | 'stable' | 'degrading' | 'new';
    scoreChange?: number;
  }>;
} {
  if (!previousResults) {
    return {
      improving: 0,
      stable: 0,
      degrading: currentResults.length,
      newFiles: currentResults.length,
      removedFiles: 0,
      trends: currentResults.map(result => ({
        filePath: result.filePath,
        trend: 'new' as const
      }))
    };
  }

  const currentRankings = calculateSemanticRanking(currentResults);
  const previousRankings = calculateSemanticRanking(previousResults);

  const currentMap = new Map(currentResults.map((result, index) => [
    result.filePath, 
    { result, ranking: currentRankings[index] }
  ]));

  const previousMap = new Map(previousResults.map((result, index) => [
    result.filePath, 
    { result, ranking: previousRankings[index] }
  ]));

  let improving = 0;
  let stable = 0;
  let degrading = 0;
  const trends: Array<{
    filePath: string;
    trend: 'improving' | 'stable' | 'degrading' | 'new';
    scoreChange?: number;
  }> = [];

  // Analyze existing files
  for (const [filePath, current] of currentMap) {
    const previous = previousMap.get(filePath);
    
    if (!previous) {
      trends.push({ filePath, trend: 'new' });
      continue;
    }

    const scoreChange = current.ranking.overallRank - previous.ranking.overallRank;
    
    if (scoreChange > 0.05) {
      improving++;
      trends.push({ filePath, trend: 'improving', scoreChange });
    } else if (scoreChange < -0.05) {
      degrading++;
      trends.push({ filePath, trend: 'degrading', scoreChange });
    } else {
      stable++;
      trends.push({ filePath, trend: 'stable', scoreChange });
    }
  }

  const newFiles = currentResults.length - (currentResults.length - trends.filter(t => t.trend === 'new').length);
  const removedFiles = previousResults.length - currentResults.length + newFiles;

  return {
    improving,
    stable,
    degrading,
    newFiles,
    removedFiles,
    trends
  };
}
