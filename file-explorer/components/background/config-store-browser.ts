// components/background/config-store-browser.ts
// Browser-safe version of ConfigStore that uses IPC to communicate with main process

export interface ProjectConfig {
  id: string;
  name: string;
  path: string;
  settings: Record<string, any>;
  namingConventions: NamingConventions;
  codeArchitecture: CodeArchitecture;
  styleGuide: StyleGuide;
  createdAt: Date;
  updatedAt: Date;
}

export interface NamingConventions {
  files: {
    caseStyle: 'camelCase' | 'kebab-case' | 'snake_case' | 'PascalCase';
    extensions: string[];
    patterns: Record<string, string>;
  };
  variables: {
    caseStyle: 'camelCase' | 'snake_case' | 'PascalCase';
    constants: 'UPPER_SNAKE_CASE' | 'camelCase';
    private: string;
  };
  functions: {
    caseStyle: 'camelCase' | 'snake_case' | 'PascalCase';
    async: string;
    handlers: string;
  };
  classes: {
    caseStyle: 'PascalCase' | 'camelCase';
    interfaces: string;
    abstract: string;
    types: string;
  };
}

export interface CodeArchitecture {
  patterns: string[];
  structure: {
    srcDir: string;
    testDir: string;
    docsDir: string;
    configDir: string;
  };
  dependencies: {
    allowed: string[];
    forbidden: string[];
    internal: Record<string, string[]>;
  };
  rules: {
    maxFileSize: number;
    maxFunctionLength: number;
    maxClassSize: number;
    cyclomaticComplexity: number;
  };
}

export interface StyleGuide {
  formatting: {
    indentSize: number;
    indentType: 'spaces' | 'tabs';
    lineLength: number;
    trailingCommas: boolean;
    semicolons: boolean;
  };
  imports: {
    sortOrder: string[];
    groupSeparation: boolean;
    aliasPatterns: Record<string, string>;
  };
  comments: {
    requireJSDoc: boolean;
    headerTemplate: string;
    todoFormat: string;
  };
}

export interface GlobalSettings {
  id: string;
  category: string;
  key: string;
  value: any;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array';
  encrypted: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Browser-safe ConfigStore that uses IPC
export class ConfigStoreBrowser {
  private initialized = false;

  public async initialize(): Promise<void> {
    if (this.initialized) return;

    // Check if we're in an Electron renderer process
    if (typeof window === 'undefined' || !window.electronAPI || !window.electronAPI.configStore) {
      console.log('🔄 ConfigStore: Running in browser/non-Electron environment, using localStorage fallback (this is normal for development)');
      this.initialized = true;
      return;
    }

    try {
      // Initialize via IPC
      await window.electronAPI.configStore.initialize();
      this.initialized = true;
      console.log('✅ ConfigStore: Initialized via Electron IPC successfully');
    } catch (error) {
      console.error('❌ ConfigStore: Failed to initialize via IPC:', error);
      console.log('🔄 ConfigStore: Falling back to localStorage mode');
      this.initialized = true; // Continue with localStorage fallback
    }
  }

  // Project Configuration Methods
  public async createProject(config: Omit<ProjectConfig, 'createdAt' | 'updatedAt'>): Promise<ProjectConfig> {
    await this.initialize();

    if (typeof window !== 'undefined' && window.electronAPI?.configStore) {
      return await window.electronAPI.configStore.createProject(config);
    }

    // Fallback to localStorage
    const now = new Date();
    const projectConfig: ProjectConfig = {
      ...config,
      createdAt: now,
      updatedAt: now
    };

    if (typeof window !== 'undefined' && window.localStorage) {
      const projects = this.getProjectsFromStorage();
      projects.push(projectConfig);
      localStorage.setItem('synapse-projects', JSON.stringify(projects));
    }

    return projectConfig;
  }

  public async getProject(id: string): Promise<ProjectConfig | null> {
    await this.initialize();

    if (typeof window !== 'undefined' && window.electronAPI?.configStore) {
      return await window.electronAPI.configStore.getProject(id);
    }

    // Fallback to localStorage
    const projects = this.getProjectsFromStorage();
    return projects.find(p => p.id === id) || null;
  }

  public async getProjectByPath(path: string): Promise<ProjectConfig | null> {
    await this.initialize();

    if (typeof window !== 'undefined' && window.electronAPI?.configStore) {
      return await window.electronAPI.configStore.getProjectByPath(path);
    }

    // Fallback to localStorage
    const projects = this.getProjectsFromStorage();
    return projects.find(p => p.path === path) || null;
  }

  public async getAllProjects(): Promise<ProjectConfig[]> {
    await this.initialize();

    if (typeof window !== 'undefined' && window.electronAPI?.configStore) {
      return await window.electronAPI.configStore.getAllProjects();
    }

    // Fallback to localStorage
    return this.getProjectsFromStorage();
  }

  public async updateProject(id: string, updates: Partial<ProjectConfig>): Promise<ProjectConfig | null> {
    await this.initialize();

    if (typeof window !== 'undefined' && window.electronAPI?.configStore) {
      return await window.electronAPI.configStore.updateProject(id, updates);
    }

    // Fallback to localStorage
    if (typeof window !== 'undefined' && window.localStorage) {
      const projects = this.getProjectsFromStorage();
      const index = projects.findIndex(p => p.id === id);

      if (index === -1) return null;

      const updated = { ...projects[index], ...updates, updatedAt: new Date() };
      projects[index] = updated;
      localStorage.setItem('synapse-projects', JSON.stringify(projects));
      return updated;
    }

    return null;
  }

  public async deleteProject(id: string): Promise<boolean> {
    await this.initialize();

    if (typeof window !== 'undefined' && window.electronAPI?.configStore) {
      return await window.electronAPI.configStore.deleteProject(id);
    }

    // Fallback to localStorage
    if (typeof window !== 'undefined' && window.localStorage) {
      const projects = this.getProjectsFromStorage();
      const index = projects.findIndex(p => p.id === id);

      if (index === -1) return false;

      projects.splice(index, 1);
      localStorage.setItem('synapse-projects', JSON.stringify(projects));
      return true;
    }

    return false;
  }

  // Global Settings Methods
  public async setGlobalSetting(category: string, key: string, value: any, encrypted = false): Promise<void> {
    await this.initialize();

    if (typeof window !== 'undefined' && window.electronAPI?.configStore) {
      return await window.electronAPI.configStore.setGlobalSetting(category, key, value, encrypted);
    }

    // Fallback to localStorage
    if (typeof window !== 'undefined' && window.localStorage) {
      const settings = this.getGlobalSettingsFromStorage();
      const settingKey = `${category}.${key}`;
      settings[settingKey] = { value, type: this.getValueType(value), encrypted };
      localStorage.setItem('synapse-global-settings', JSON.stringify(settings));
    }
  }

  public async getGlobalSetting<T = any>(category: string, key: string): Promise<T | null> {
    await this.initialize();

    if (typeof window !== 'undefined' && window.electronAPI?.configStore) {
      return await window.electronAPI.configStore.getGlobalSetting(category, key);
    }

    // Fallback to localStorage
    const settings = this.getGlobalSettingsFromStorage();
    const settingKey = `${category}.${key}`;
    const setting = settings[settingKey];

    return setting ? setting.value : null;
  }

  public async getGlobalSettingsByCategory(category: string): Promise<Record<string, any>> {
    await this.initialize();

    if (typeof window !== 'undefined' && window.electronAPI?.configStore) {
      return await window.electronAPI.configStore.getGlobalSettingsByCategory(category);
    }

    // Fallback to localStorage
    const settings = this.getGlobalSettingsFromStorage();
    const result: Record<string, any> = {};

    for (const [key, setting] of Object.entries(settings)) {
      if (key.startsWith(`${category}.`)) {
        const settingKey = key.substring(category.length + 1);
        result[settingKey] = setting.value;
      }
    }

    return result;
  }

  public async deleteGlobalSetting(category: string, key: string): Promise<boolean> {
    await this.initialize();

    if (typeof window !== 'undefined' && window.electronAPI?.configStore) {
      return await window.electronAPI.configStore.deleteGlobalSetting(category, key);
    }

    // Fallback to localStorage
    if (typeof window !== 'undefined' && window.localStorage) {
      const settings = this.getGlobalSettingsFromStorage();
      const settingKey = `${category}.${key}`;

      if (settingKey in settings) {
        delete settings[settingKey];
        localStorage.setItem('synapse-global-settings', JSON.stringify(settings));
        return true;
      }
    }

    return false;
  }

  // Helper methods for localStorage fallback
  private getProjectsFromStorage(): ProjectConfig[] {
    try {
      if (typeof window === 'undefined' || !window.localStorage) {
        return [];
      }

      const stored = localStorage.getItem('synapse-projects');
      if (stored) {
        const projects = JSON.parse(stored);
        return projects.map((p: any) => ({
          ...p,
          createdAt: new Date(p.createdAt),
          updatedAt: new Date(p.updatedAt)
        }));
      }
    } catch (error) {
      console.error('Failed to load projects from localStorage:', error);
    }
    return [];
  }

  private getGlobalSettingsFromStorage(): Record<string, any> {
    try {
      if (typeof window === 'undefined' || !window.localStorage) {
        return {};
      }

      const stored = localStorage.getItem('synapse-global-settings');
      return stored ? JSON.parse(stored) : {};
    } catch (error) {
      console.error('Failed to load global settings from localStorage:', error);
      return {};
    }
  }

  private getValueType(value: any): string {
    if (typeof value === 'string') return 'string';
    if (typeof value === 'number') return 'number';
    if (typeof value === 'boolean') return 'boolean';
    if (Array.isArray(value)) return 'array';
    if (typeof value === 'object') return 'object';
    return 'string';
  }

  public isInitialized(): boolean {
    return this.initialized;
  }
}

// Singleton instance
let configStoreBrowser: ConfigStoreBrowser | null = null;

export function getConfigStoreBrowser(): ConfigStoreBrowser {
  if (!configStoreBrowser) {
    configStoreBrowser = new ConfigStoreBrowser();
  }
  return configStoreBrowser;
}
