/**
 * Semantic Code Analysis - Streamlined Shell
 *
 * Reduced from 1536 lines to under 200 lines by extracting functionality into modular components.
 * This serves as the main orchestration interface that delegates to specialized modules.
 */

// Re-export the new modular implementation
export { SemanticCodeAnalysis } from './semantic-analysis/semantic-code-analysis';

// Re-export types for backward compatibility
export * from './semantic-analysis/types';

// Re-export utility functions for backward compatibility
export {
  parseAST,
  recognizePatterns,
  BUILT_IN_PATTERNS,
  estimateAnalysisCost,
  calculateSemanticRanking,
  createWorkerPool,
  isWorkerSupported,
  getRecommendedWorkerCount,
  generateCacheKey,
  detectLanguage,
  calculateCyclomaticComplexity,
  calculateCognitiveComplexity,
  calculateReadabilityScore
} from './semantic-analysis';

// Legacy function for backward compatibility
export function getSemanticCodeAnalysis(projectId: string) {
  return new (require('./semantic-analysis/semantic-code-analysis').SemanticCodeAnalysis)(projectId);
}