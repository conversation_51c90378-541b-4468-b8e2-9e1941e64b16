  107273 total
    3302 ./components/agents/agent-manager-complete.ts
    1718 ./components/agents/implementation/junior-agent.ts
    1552 ./components/file-sidebar.tsx
    1536 ./components/background/semantic-code-analysis.ts
    1522 ./electron/main.ts
    1495 ./components/background/git-integration.ts
    1481 ./components/agents/complete-integration.tsx
    1451 ./components/background/context-compression.ts
    1432 ./components/agents/micromanager-agent.ts
    1290 ./components/project/create-project-wizard.tsx
    1281 ./app/page.tsx
    1266 ./components/background/knowledge-graph.ts
    1174 ./components/background/context-history.ts
    1144 ./components/agents/implementation/midlevel-agent.ts
    1091 ./components/kanban/board-context.tsx
    1078 ./components/background/security-performance-optimizer.ts
    1062 ./components/settings/settings-manager.ts
    1005 ./components/agents/agent-execution-service.ts
    1003 ./components/monaco-editor.tsx
     997 ./components/inspector/TaskTimelineInspector.tsx
     974 ./components/background/coordination-protocols.ts
     974 ./components/background/context-prefetcher.ts
     969 ./components/orchestrators/taskmaster-orchestration-ui.tsx
     950 ./components/agents/llm-request-service.ts
     941 ./components/background/project-dictionary.ts
     938 ./components/middleware/error-resolution-coordinator.ts
     936 ./electron/services/claude-taskmaster-service.ts
     931 ./electron/services/llm-service.ts
     769 ./services/analytics-service.ts
     763 ./components/ui/sidebar.tsx
     760 ./components/project/taskmaster-task-generation-ui.tsx
     758 ./components/agents/implementation/senior-agent.ts
     741 ./components/middleware/continuous-learning-agent.ts
     737 ./components/agents/task-status-service.ts
     730 ./components/intake/prd-upload-ui.tsx
     724 ./components/background/error-detector.ts
     714 ./components/kanban/kanban-board.tsx
     698 ./components/background/context-relevance-scorer.ts
     683 ./components/background/monaco-integration.ts
     667 ./components/background/code-completion-enhancer.ts
     662 ./components/agents/specialized/researcher-agent.ts
     655 ./components/background/file-operations.ts
     648 ./components/background/syntax-analyzer.ts
     640 ./components/terminal/TerminalPanel.tsx
     635 ./components/terminal/TerminalBootstrap.tsx
     630 ./components/background/context-cache-manager.ts
     610 ./components/background/agent-registry.ts
     608 ./components/inspector/ConcurrencyOverlay.tsx
     605 ./components/background/task-queue.ts
     590 ./hooks/useAgentChat.ts
     582 ./components/background/file-transaction-manager.ts
     574 ./components/settings/isolated-system-tab.tsx
     567 ./hooks/useAgentChatSync.ts
     556 ./components/kanban/card-detail-view.tsx
     556 ./components/agents/agent-task-coordinator.ts
     545 ./components/agents/agent-context-memory.ts
     530 ./components/agents/implementation/intern-agent.ts
     515 ./components/analytics/AgentAnalyticsTab.tsx
     509 ./components/services/agent-task-escalation-service.ts
     508 ./components/agents/agent-base.ts
     496 ./components/agents/agent-integration.tsx
     495 ./components/terminal/MultiSessionTerminal.tsx
     490 ./components/settings/api-keys-settings.tsx
     482 ./components/background/file-system-monitor.ts
     477 ./scripts/validateModelMetadata.ts
     470 ./components/agents/task-orchestrator.ts
     459 ./components/middleware/agent-state-monitor.ts
     458 ./components/chat/AgentChatPanel.tsx
     451 ./components/background/index.ts
     442 ./components/kanban/lib/board-ipc-bridge.ts
     436 ./services/logger.ts
     430 ./components/services/agent-terminal-bridge.ts
     421 ./components/background/code-indexer.ts
     420 ./components/workspace/semantic-indexer.ts
     419 ./lib/mcp-bridge-service.ts
     413 ./electron/services/board-state-service.ts
     408 ./components/debug/TokenUsageOverlay.tsx
     408 ./components/agents/agent-execution-trace.tsx
     408 ./components/adapters/taskmaster-adapter.ts
     406 ./components/settings/isolated-taskmaster-tab.tsx
     404 ./components/agents/specialized/designer-agent.ts
     402 ./components/background/config-store.ts
     402 ./components/agents/llm-response-cache.ts
     381 ./components/agents/refactor-service.tsx
     380 ./components/agents/kanban-task-bridge.ts
     379 ./components/background/editor-state-service.ts
     376 ./components/debug/StreamReplayDebugger.tsx
     376 ./components/background/message-bus.ts
     375 ./components/dialogs/AgentManagementDialog.tsx
     374 ./components/services/agent-task-feedback-service.ts
     365 ./components/ui/chart.tsx
     361 ./electron/services/TerminalSessionManager.ts
     361 ./components/budget/use-threshold-alerts.tsx
     358 ./components/editor/editor-action-state.ts
     358 ./components/agents/agent-work-tracker.ts
     357 ./components/history/AgentHistoryTab.tsx
     356 ./components/background/config-store-browser.ts
     354 ./components/terminal/TerminalLogsPanel.tsx
     354 ./components/background/vector-database.ts
     352 ./services/agent-history-store.ts
     352 ./components/background/file-sync-service.ts
     343 ./components/agents/agent-manager.ts
     340 ./components/agents/openai-model-selector.tsx
     339 ./electron/services/mcp-service.ts
     331 ./components/analytics/AnalyticsCharts.tsx
     327 ./components/settings/isolated-terminal-tab.tsx
     324 ./components/background/semantic-search.ts
     323 ./services/agent-events.ts
     321 ./services/claude-taskmaster-service.ts
     321 ./components/kanban/kanban-card.tsx
     321 ./components/agents/task-dispatcher.ts
     320 ./lib/model-optimizer.ts
     318 ./components/background/default-configs.ts
     316 ./lib/cost-tracker.ts
     316 ./components/settings/SettingsCenter.tsx
     310 ./lib/utils/telemetry.ts
     307 ./lib/alert-manager.ts
     302 ./components/agents/model-registry-service.ts
     299 ./lib/utils/use-concurrency.ts
     298 ./components/kanban/kanban-visualizer.ts
     294 ./components/debug/stream-replay-store.tsx
     292 ./components/budget/budget-status.tsx
     290 ./services/active-project-service.ts
     287 ./lib/mcp-initialization-service.ts
     280 ./components/agents/anthropic-models.ts
     280 ./components/agents/agent-performance-optimizer.ts
     279 ./lib/utils/concurrency-manager.ts
     278 ./hooks/useAgentAnalytics.ts
     275 ./components/background/database-manager.ts
     271 ./components/budget/alert-display.tsx
     268 ./hooks/useAgentHistory.ts
     268 ./components/settings/isolated-agent-card.tsx
     266 ./components/agents/universal-model-selector.tsx
     264 ./services/chat-history.ts
     263 ./components/agents/index.ts
     262 ./lib/utils/debug.ts
     262 ./components/ui/carousel.tsx
     262 ./components/agents/llm-provider-registry.ts
     260 ./services/global-chat-state.ts
     257 ./components/settings/isolated-agent-controls.tsx
     257 ./components/kanban/lib/board-agent-api.ts
     256 ./components/history/HistoryCard.tsx
     256 ./components/file-explorer.tsx
     255 ./components/settings/performance-monitor.tsx
     254 ./electron/utils/concurrency-manager.ts
     253 ./lib/utils/use-telemetry.ts
     252 ./components/debug/stream-recorder.ts
     251 ./components/agents/anthropic-model-selector.tsx
     250 ./utils/system-message-utils.ts
     249 ./components/kanban/lib/kanban-events.ts
     244 ./components/command-palette.tsx
     242 ./types/analytics.ts
     239 ./components/editor/editor-action-provider.tsx
     237 ./lib/notification-service.ts
     237 ./components/kanban/agent-board-controller.tsx
     236 ./components/ui/menubar.tsx
     234 ./components/kanban/create-card-dialog.tsx
     234 ./components/kanban/agent-activity-panel.tsx
     232 ./components/services/terminal-session-logging-service.ts
     229 ./lib/llm-client.ts
     229 ./electron/services/agent-state-service.ts
     228 ./components/agents/capability-tags.ts
     227 ./components/terminal/FloatingTerminalPanel.tsx
     226 ./electron/utils/telemetry.ts
     224 ./types/electron.d.ts
     224 ./components/dialogs/DialogManager.ts
     222 ./components/agents/unified-model-service.ts
     222 ./components/agents/task-completion-tracker.ts
     220 ./components/auto-save/auto-save-service.ts
     217 ./services/agent-shell-service.ts
     215 ./components/debug/logger-test-panel.tsx
     213 ./scripts/listModels.ts
     210 ./components/terminal/terminal-event-bus.ts
     210 ./components/kanban/kanban-column.tsx
     209 ./lib/io/settings-exporter.ts
     209 ./components/settings/isolated-editor-tab.tsx
     208 ./components/kanban/TaskLogViewer.tsx
     206 ./components/kanban/agent-integration-dialog.tsx
     203 ./components/chat/agent-status-widget.tsx
     201 ./components/code-editor.tsx
     200 ./components/ui/dropdown-menu.tsx
     200 ./components/ui/context-menu.tsx
     199 ./scripts/discover-unverified-models.ts
     199 ./components/agents/llm-integration-service.ts
     196 ./types/agent-history.ts
     196 ./hooks/useAgentPresence.ts
     195 ./lib/budget-enforcer.ts
     194 ./hooks/use-toast.ts
     194 ./components/ui/use-toast.ts
     193 ./components/agents/shared-agent-state.tsx
     186 ./components/chat/live-presence-panel.tsx
     184 ./components/settings/isolated-cost-tab.tsx
     184 ./components/ai-chat-panel.tsx
     183 ./components/settings/settings-sync-service.ts
     181 ./components/budget/budget-error-handler.tsx
     178 ./lib/utils/use-debug.ts
     178 ./components/ui/form.tsx
     175 ./pages/api/telemetry.ts
     175 ./lib/agent-ipc-bridge.ts
     168 ./components/editor/editor-state-provider.tsx
     166 ./components/agents/openai-models.ts
     164 ./pages/test-orchestration.tsx
     160 ./components/ui/select.tsx
     159 ./components/settings/panels/TestingSettingsPanel.tsx
     153 ./components/ui/command.tsx
     151 ./components/auto-save/auto-save-test.tsx
     149 ./components/dialogs/Dialog.tsx
     148 ./components/kanban/kanban-swimlane.tsx
     146 ./components/ui/alert-dialog.tsx
     146 ./components/ai-assistant.tsx
     144 ./electron/utils/debug.ts
     144 ./components/terminal/TerminalHeader.tsx
     144 ./components/settings/system-status-map.ts
     140 ./components/ui/sheet.tsx
     140 ./components/agents/specialized/architect-agent.ts
     135 ./components/dialogs/BoardSettingsDialog.tsx
     134 ./components/settings/panels/AgentSettingsPanel.tsx
     132 ./components/ui/dialog.tsx
     132 ./components/settings/isolated-privacy-tab.tsx
     132 ./components/kanban/legend-edit-dialog.tsx
     129 ./components/ui/toast.tsx
     128 ./components/ui/navigation-menu.tsx
     124 ./components/ui/project-status-bar.tsx
     123 ./components/agents/specialized/tester-agent.ts
     122 ./app/timeline/page.tsx
     119 ./services/logger-init.ts
     119 ./components/services/logger-init-provider.tsx
     118 ./components/ui/drawer.tsx
     118 ./components/settings/setting-status-indicator.tsx
     117 ./components/ui/table.tsx
     117 ./components/ui/pagination.tsx
     116 ./components/agents/curated-models-config.ts
     115 ./components/ui/breadcrumb.tsx
     112 ./types/agent-events.ts
     112 ./components/intake/prd-intake-service.ts
     112 ./components/auto-save/auto-save-engine.ts
     106 ./components/kanban/board-settings-dialog.tsx
     103 ./components/agents/google-models.ts
     102 ./components/terminal/ResizableBottomTerminal.tsx
      97 ./components/dialogs/types.ts
      96 ./tailwind.config.ts
      96 ./components/middleware/task-classifier.ts
      96 ./app/chat/page.tsx
      95 ./components/kanban/header.tsx
      93 ./components/ui/resizable-left-panel.tsx
      92 ./components/ui/resizable-panel.tsx
      89 ./components/dialogs/CreateBoardDialog.tsx
      88 ./hooks/use-logger-init.ts
      88 ./components/chat/sync-status-indicator.tsx
      88 ./app/kanban/[boardId]/kanban-window-client.tsx
      88 ./app/editor/page.tsx
      86 ./components/agents/fireworks-models.ts
      85 ./components/middleware/resource-optimizer.ts
      85 ./app/settings/page.tsx
      80 ./components/kanban/create-board-dialog.tsx
      79 ./components/ui/card.tsx
      79 ./components/agents/deepseek-models.ts
      75 ./app/explorer/page.tsx
      74 ./components/kanban/edit-swimlane-dialog.tsx
      74 ./components/dialogs/CreateSwimlaneDialog.tsx
      74 ./components/dialogs/CreateColumnDialog.tsx
      72 ./app/agent-system/page.tsx
      71 ./components/ui/input-otp.tsx
      70 ./components/window-launcher.tsx
      69 ./types/chat.ts
      69 ./components/dialogs/DialogPortal.tsx
      68 ./components/settings/settings-context.tsx
      67 ./components/settings/client-settings-wrapper.tsx
      66 ./components/ui/calendar.tsx
      66 ./components/terminal/HorizontalTerminalPanel.tsx
      65 ./lib/budget-error.ts
      65 ./components/kanban/create-swimlane-dialog.tsx
      65 ./components/kanban/create-column-dialog.tsx
      63 ./components/auto-save/auto-save-provider.tsx
      62 ./electron/types/board-types.ts
      61 ./components/ui/toggle-group.tsx
      61 ./components/dialogs/DialogProvider.tsx
      60 ./lib/agent-constants.ts
      60 ./electron/agent-constants.ts
      59 ./components/ui/alert.tsx
      58 ./components/ui/accordion.tsx
      57 ./components/settings/theme-bridge.tsx
      56 ./components/ui/button.tsx
      56 ./components/theme-toggle.tsx
      55 ./components/ui/tabs.tsx
      55 ./components/middleware/execution-manager.ts
      55 ./components/agents/pricing-display.tsx
      50 ./components/ui/avatar.tsx
      50 ./components/middleware/context-provider.ts
      49 ./components/ui/icon.tsx
      48 ./components/ui/scroll-area.tsx
      48 ./components/dialogs/TestDialog.tsx
      47 ./electron/board-constants.ts
      45 ./components/ui/toggle.tsx
      45 ./components/ui/resizable.tsx
      44 ./components/ui/radio-group.tsx
      43 ./components/agents/isolated-history-tab.tsx
      43 ./components/agents/isolated-analytics-tab.tsx
      42 ./components/middleware/result-validator.ts
      41 ./app/terminal/page.tsx
      39 ./components/kanban/kanban-legend.tsx
      36 ./components/ui/badge.tsx
      36 ./components/settings/panels/APIKeySettingsPanel.tsx
      35 ./components/ui/toaster.tsx
      34 ./components/kanban/search-provider.tsx
      32 ./components/dialogs/useDialog.ts
      31 ./components/ui/sonner.tsx
      31 ./components/ui/separator.tsx
      31 ./components/ui/popover.tsx
      30 ./components/ui/tooltip.tsx
      30 ./components/ui/checkbox.tsx
      30 ./components/settings/panels/TerminalSettingsPanel.tsx
      30 ./components/settings/panels/TaskmasterSettingsPanel.tsx
      30 ./components/settings/panels/SystemSettingsPanel.tsx
      30 ./components/settings/panels/PrivacySettingsPanel.tsx
      30 ./components/settings/panels/EditorSettingsPanel.tsx
      30 ./components/settings/panels/CostSettingsPanel.tsx
      29 ./components/ui/switch.tsx
      29 ./components/ui/hover-card.tsx
      28 ./components/ui/slider.tsx
      28 ./components/ui/progress.tsx
      26 ./components/ui/label.tsx
      22 ./components/ui/textarea.tsx
      22 ./components/ui/input.tsx
      20 ./components/settings/global-settings.ts
      19 ./hooks/use-mobile.tsx
      19 ./components/ui/use-mobile.tsx
      18 ./components/theme-provider.tsx
      18 ./app/kanban/[boardId]/page.tsx
      15 ./components/ui/skeleton.tsx
      11 ./components/ui/collapsible.tsx
      11 ./components/dialogs/index.ts
       7 ./components/ui/aspect-ratio.tsx
       6 ./lib/utils.ts
       3 ./components/terminal/index.ts
       3 ./app/loading.tsx
