nabil             1171   0.9  0.3 1623575696  42976   ??  S    Sat09PM  12:48.56 /private/var/folders/xx/vxm1gd0d3kb5pbr04_9slkf80000gn/T/AppTranslocation/5FA18659-0E8B-49FA-9B01-6EF882157374/d/Visual Studio Code.app/Contents/Frameworks/Code Helper.app/Contents/MacOS/Code Helper --type=utility --utility-sub-type=node.mojom.NodeService --lang=en-GB --service-sandbox-type=none --user-data-dir=/Users/<USER>/Library/Application Support/Code --standard-schemes=vscode-webview,vscode-file --enable-sandbox --secure-schemes=vscode-webview,vscode-file --cors-schemes=vscode-webview,vscode-file --fetch-schemes=vscode-webview,vscode-file --service-worker-schemes=vscode-webview --code-cache-schemes=vscode-webview,vscode-file --shared-files --field-trial-handle=1718379636,r,6428224502770462630,13202989381050995355,262144 --enable-features=DocumentPolicyIncludeJSCallStacksInCrashReports,EarlyEstablishGpuChannel,EstablishGpuChannelAsync,ScreenCaptureKitPickerScreen,ScreenCaptureKitStreamPickerSonoma --disable-features=CalculateNativeWinOcclusion,MacWebContentsOcclusion,SpareRendererForSitePerProcess,TimeoutHangingVideoCaptureStarts --variations-seed-version
nabil             1041   0.3  0.6 1623609600  97968   ??  S    Sat09PM  32:23.51 /private/var/folders/xx/vxm1gd0d3kb5pbr04_9slkf80000gn/T/AppTranslocation/5FA18659-0E8B-49FA-9B01-6EF882157374/d/Visual Studio Code.app/Contents/Frameworks/Code Helper (Plugin).app/Contents/MacOS/Code Helper (Plugin) --type=utility --utility-sub-type=node.mojom.NodeService --lang=en-GB --service-sandbox-type=none --dns-result-order=ipv4first --inspect-port=0 --user-data-dir=/Users/<USER>/Library/Application Support/Code --standard-schemes=vscode-webview,vscode-file --enable-sandbox --secure-schemes=vscode-webview,vscode-file --cors-schemes=vscode-webview,vscode-file --fetch-schemes=vscode-webview,vscode-file --service-worker-schemes=vscode-webview --code-cache-schemes=vscode-webview,vscode-file --shared-files --field-trial-handle=1718379636,r,6428224502770462630,13202989381050995355,262144 --enable-features=DocumentPolicyIncludeJSCallStacksInCrashReports,EarlyEstablishGpuChannel,EstablishGpuChannelAsync,ScreenCaptureKitPickerScreen,ScreenCaptureKitStreamPickerSonoma --disable-features=CalculateNativeWinOcclusion,MacWebContentsOcclusion,SpareRendererForSitePerProcess,TimeoutHangingVideoCaptureStarts --variations-seed-version
nabil            47882   0.1  0.2 1623607552  40720   ??  S    Thu01PM   4:11.26 /private/var/folders/xx/vxm1gd0d3kb5pbr04_9slkf80000gn/T/AppTranslocation/5FA18659-0E8B-49FA-9B01-6EF882157374/d/Visual Studio Code.app/Contents/Frameworks/Code Helper (Plugin).app/Contents/MacOS/Code Helper (Plugin) --type=utility --utility-sub-type=node.mojom.NodeService --lang=en-GB --service-sandbox-type=none --dns-result-order=ipv4first --inspect-port=0 --user-data-dir=/Users/<USER>/Library/Application Support/Code --standard-schemes=vscode-webview,vscode-file --enable-sandbox --secure-schemes=vscode-webview,vscode-file --cors-schemes=vscode-webview,vscode-file --fetch-schemes=vscode-webview,vscode-file --service-worker-schemes=vscode-webview --code-cache-schemes=vscode-webview,vscode-file --shared-files --field-trial-handle=1718379636,r,6428224502770462630,13202989381050995355,262144 --enable-features=DocumentPolicyIncludeJSCallStacksInCrashReports,EarlyEstablishGpuChannel,EstablishGpuChannelAsync,ScreenCaptureKitPickerScreen,ScreenCaptureKitStreamPickerSonoma --disable-features=CalculateNativeWinOcclusion,MacWebContentsOcclusion,SpareRendererForSitePerProcess,TimeoutHangingVideoCaptureStarts --variations-seed-version
root             68678   0.0  0.0 410334432   1904   ??  Ss    6:54AM   0:01.48 /Library/Filesystems/tuxera_ntfs.fs/Contents/Resources/Support/10.9/tuxera_ntfs /dev/rdisk8s2 /Volumes/Software -orecover,cbcio,sfmconv,streams_interface=openxattr,native_xattr,nfconv,aligned_io,fssubtype=0,iosize=1048576,local,nodev,noowners,nosuid,adaptiveuid,adaptivegid
nabil            47881   0.0  0.1 1623592384  22192   ??  S    Thu01PM   0:18.90 /private/var/folders/xx/vxm1gd0d3kb5pbr04_9slkf80000gn/T/AppTranslocation/5FA18659-0E8B-49FA-9B01-6EF882157374/d/Visual Studio Code.app/Contents/Frameworks/Code Helper.app/Contents/MacOS/Code Helper --type=utility --utility-sub-type=node.mojom.NodeService --lang=en-GB --service-sandbox-type=none --user-data-dir=/Users/<USER>/Library/Application Support/Code --standard-schemes=vscode-webview,vscode-file --enable-sandbox --secure-schemes=vscode-webview,vscode-file --cors-schemes=vscode-webview,vscode-file --fetch-schemes=vscode-webview,vscode-file --service-worker-schemes=vscode-webview --code-cache-schemes=vscode-webview,vscode-file --shared-files --field-trial-handle=1718379636,r,6428224502770462630,13202989381050995355,262144 --enable-features=DocumentPolicyIncludeJSCallStacksInCrashReports,EarlyEstablishGpuChannel,EstablishGpuChannelAsync,ScreenCaptureKitPickerScreen,ScreenCaptureKitStreamPickerSonoma --disable-features=CalculateNativeWinOcclusion,MacWebContentsOcclusion,SpareRendererForSitePerProcess,TimeoutHangingVideoCaptureStarts --variations-seed-version
nabil            43931   0.0  0.1 1621825808  22848   ??  S    Thu11AM   0:05.17 /private/var/folders/xx/vxm1gd0d3kb5pbr04_9slkf80000gn/T/AppTranslocation/5FA18659-0E8B-49FA-9B01-6EF882157374/d/Visual Studio Code.app/Contents/Frameworks/Code Helper (Plugin).app/Contents/MacOS/Code Helper (Plugin) /private/var/folders/xx/vxm1gd0d3kb5pbr04_9slkf80000gn/T/AppTranslocation/5FA18659-0E8B-49FA-9B01-6EF882157374/d/Visual Studio Code.app/Contents/Resources/app/extensions/html-language-features/server/dist/node/htmlServerMain --node-ipc --clientProcessId=1041
nabil            83016   0.0  0.1 1621799184  15616   ??  S    Tue11AM   0:10.78 /private/var/folders/xx/vxm1gd0d3kb5pbr04_9slkf80000gn/T/AppTranslocation/5FA18659-0E8B-49FA-9B01-6EF882157374/d/Visual Studio Code.app/Contents/Frameworks/Code Helper (Plugin).app/Contents/MacOS/Code Helper (Plugin) /private/var/folders/xx/vxm1gd0d3kb5pbr04_9slkf80000gn/T/AppTranslocation/5FA18659-0E8B-49FA-9B01-6EF882157374/d/Visual Studio Code.app/Contents/Resources/app/extensions/css-language-features/server/dist/node/cssServerMain --node-ipc --clientProcessId=1041
nabil             3116   0.0  0.1 1621797136  19824   ??  S    Sat09PM   0:17.36 /private/var/folders/xx/vxm1gd0d3kb5pbr04_9slkf80000gn/T/AppTranslocation/5FA18659-0E8B-49FA-9B01-6EF882157374/d/Visual Studio Code.app/Contents/Frameworks/Code Helper (Plugin).app/Contents/MacOS/Code Helper (Plugin) /private/var/folders/xx/vxm1gd0d3kb5pbr04_9slkf80000gn/T/AppTranslocation/5FA18659-0E8B-49FA-9B01-6EF882157374/d/Visual Studio Code.app/Contents/Resources/app/extensions/markdown-language-features/dist/serverWorkerMain --node-ipc --clientProcessId=1041
nabil             2183   0.0  0.1 1621779728  22048   ??  S    Sat09PM   0:03.61 /private/var/folders/xx/vxm1gd0d3kb5pbr04_9slkf80000gn/T/AppTranslocation/5FA18659-0E8B-49FA-9B01-6EF882157374/d/Visual Studio Code.app/Contents/Frameworks/Code Helper (Plugin).app/Contents/MacOS/Code Helper (Plugin) /private/var/folders/xx/vxm1gd0d3kb5pbr04_9slkf80000gn/T/AppTranslocation/5FA18659-0E8B-49FA-9B01-6EF882157374/d/Visual Studio Code.app/Contents/Resources/app/extensions/node_modules/typescript/lib/typingsInstaller.js --globalTypingsCacheLocation /Users/<USER>/Library/Caches/typescript/5.8 --enableTelemetry --typesMapLocation /private/var/folders/xx/vxm1gd0d3kb5pbr04_9slkf80000gn/T/AppTranslocation/5FA18659-0E8B-49FA-9B01-6EF882157374/d/Visual Studio Code.app/Contents/Resources/app/extensions/node_modules/typescript/lib/typesMap.json --validateDefaultNpmLocation
nabil             2182   0.0  0.1 1621782800  24912   ??  S    Sat09PM  11:58.98 /private/var/folders/xx/vxm1gd0d3kb5pbr04_9slkf80000gn/T/AppTranslocation/5FA18659-0E8B-49FA-9B01-6EF882157374/d/Visual Studio Code.app/Contents/Frameworks/Code Helper (Plugin).app/Contents/MacOS/Code Helper (Plugin) --max-old-space-size=3072 /private/var/folders/xx/vxm1gd0d3kb5pbr04_9slkf80000gn/T/AppTranslocation/5FA18659-0E8B-49FA-9B01-6EF882157374/d/Visual Studio Code.app/Contents/Resources/app/extensions/node_modules/typescript/lib/tsserver.js --useInferredProjectPerProjectRoot --enableTelemetry --cancellationPipeName /var/folders/xx/vxm1gd0d3kb5pbr04_9slkf80000gn/T/vscode-typescript501/0fc375160b89d7a5ae05/tscancellation-8e9065d1d1d9be33912a.tmp* --locale en --noGetErrOnBackgroundUpdate --canUseWatchEvents --validateDefaultNpmLocation --useNodeIpc
nabil             2181   0.0  0.1 **********  23712   ??  S    Sat09PM   0:31.89 /private/var/folders/xx/vxm1gd0d3kb5pbr04_9slkf80000gn/T/AppTranslocation/5FA18659-0E8B-49FA-9B01-6EF882157374/d/Visual Studio Code.app/Contents/Frameworks/Code Helper (Plugin).app/Contents/MacOS/Code Helper (Plugin) --max-old-space-size=3072 /private/var/folders/xx/vxm1gd0d3kb5pbr04_9slkf80000gn/T/AppTranslocation/5FA18659-0E8B-49FA-9B01-6EF882157374/d/Visual Studio Code.app/Contents/Resources/app/extensions/node_modules/typescript/lib/tsserver.js --serverMode partialSemantic --useInferredProjectPerProjectRoot --disableAutomaticTypingAcquisition --cancellationPipeName /var/folders/xx/vxm1gd0d3kb5pbr04_9slkf80000gn/T/vscode-typescript501/0fc375160b89d7a5ae05/tscancellation-9d0aec801882da9c4f11.tmp* --locale en --noGetErrOnBackgroundUpdate --canUseWatchEvents --validateDefaultNpmLocation --useNodeIpc
nabil             1124   0.0  0.1 **********  23392   ??  S    Sat09PM   0:20.07 /private/var/folders/xx/vxm1gd0d3kb5pbr04_9slkf80000gn/T/AppTranslocation/5FA18659-0E8B-49FA-9B01-6EF882157374/d/Visual Studio Code.app/Contents/Frameworks/Code Helper (Plugin).app/Contents/MacOS/Code Helper (Plugin) /private/var/folders/xx/vxm1gd0d3kb5pbr04_9slkf80000gn/T/AppTranslocation/5FA18659-0E8B-49FA-9B01-6EF882157374/d/Visual Studio Code.app/Contents/Resources/app/extensions/json-language-features/server/dist/node/jsonServerMain --node-ipc --clientProcessId=1041
nabil             1040   0.0  0.2 **********  28096   ??  S    Sat09PM   2:48.95 /private/var/folders/xx/vxm1gd0d3kb5pbr04_9slkf80000gn/T/AppTranslocation/5FA18659-0E8B-49FA-9B01-6EF882157374/d/Visual Studio Code.app/Contents/Frameworks/Code Helper.app/Contents/MacOS/Code Helper --type=utility --utility-sub-type=node.mojom.NodeService --lang=en-GB --service-sandbox-type=none --user-data-dir=/Users/<USER>/Library/Application Support/Code --standard-schemes=vscode-webview,vscode-file --enable-sandbox --secure-schemes=vscode-webview,vscode-file --cors-schemes=vscode-webview,vscode-file --fetch-schemes=vscode-webview,vscode-file --service-worker-schemes=vscode-webview --code-cache-schemes=vscode-webview,vscode-file --shared-files --field-trial-handle=1718379636,r,6428224502770462630,13202989381050995355,262144 --enable-features=DocumentPolicyIncludeJSCallStacksInCrashReports,EarlyEstablishGpuChannel,EstablishGpuChannelAsync,ScreenCaptureKitPickerScreen,ScreenCaptureKitStreamPickerSonoma --disable-features=CalculateNativeWinOcclusion,MacWebContentsOcclusion,SpareRendererForSitePerProcess,TimeoutHangingVideoCaptureStarts --variations-seed-version
nabil              956   0.0  0.2 1623614816  28032   ??  S    Sat09PM   0:54.35 /private/var/folders/xx/vxm1gd0d3kb5pbr04_9slkf80000gn/T/AppTranslocation/5FA18659-0E8B-49FA-9B01-6EF882157374/d/Visual Studio Code.app/Contents/Frameworks/Code Helper.app/Contents/MacOS/Code Helper --type=utility --utility-sub-type=node.mojom.NodeService --lang=en-GB --service-sandbox-type=none --user-data-dir=/Users/<USER>/Library/Application Support/Code --standard-schemes=vscode-webview,vscode-file --enable-sandbox --secure-schemes=vscode-webview,vscode-file --cors-schemes=vscode-webview,vscode-file --fetch-schemes=vscode-webview,vscode-file --service-worker-schemes=vscode-webview --code-cache-schemes=vscode-webview,vscode-file --shared-files --field-trial-handle=1718379636,r,6428224502770462630,13202989381050995355,262144 --enable-features=DocumentPolicyIncludeJSCallStacksInCrashReports,EarlyEstablishGpuChannel,EstablishGpuChannelAsync,ScreenCaptureKitPickerScreen,ScreenCaptureKitStreamPickerSonoma --disable-features=CalculateNativeWinOcclusion,MacWebContentsOcclusion,SpareRendererForSitePerProcess,TimeoutHangingVideoCaptureStarts --variations-seed-version
nabil            89759   0.0  0.0 410201296   1648 s264  S+    2:54PM   0:00.01 bash scripts/profile-electron.sh
