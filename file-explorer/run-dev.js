#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');

// Ensure we're in the correct directory
process.chdir(__dirname);

console.log('🚀 Starting Synapse File Explorer Development Server...');
console.log('📁 Working directory:', process.cwd());

// Kill any existing processes on port 4444
console.log('🔄 Killing existing processes on port 4444...');
const killPort = spawn('lsof', ['-ti:4444'], { stdio: 'pipe' });
killPort.stdout.on('data', (data) => {
  const pids = data.toString().trim().split('\n').filter(pid => pid);
  pids.forEach(pid => {
    try {
      process.kill(pid, 'SIGKILL');
      console.log(`✅ Killed process ${pid}`);
    } catch (err) {
      console.log(`⚠️  Could not kill process ${pid}:`, err.message);
    }
  });
});

killPort.on('close', () => {
  console.log('🔄 Starting Next.js development server...');
  
  // Start Next.js dev server
  const nextProcess = spawn('node', [
    path.join(__dirname, 'node_modules', 'next', 'dist', 'bin', 'next'),
    'dev',
    '-p',
    '4444'
  ], {
    stdio: 'inherit',
    cwd: __dirname
  });

  nextProcess.on('error', (err) => {
    console.error('❌ Failed to start Next.js:', err);
    process.exit(1);
  });

  nextProcess.on('close', (code) => {
    console.log(`Next.js process exited with code ${code}`);
  });

  // Wait for Next.js to be ready, then start Electron
  setTimeout(() => {
    console.log('🔄 Compiling Electron main process...');
    
    const compileProcess = spawn('npx', ['tsc', '-p', 'tsconfig.electron.json'], {
      stdio: 'inherit',
      cwd: __dirname
    });

    compileProcess.on('close', (code) => {
      if (code === 0) {
        console.log('✅ Electron compilation successful');
        
        // Copy preload script
        console.log('🔄 Copying preload script...');
        const copyProcess = spawn('cp', ['electron/preload.js', 'dist-electron/'], {
          stdio: 'inherit',
          cwd: __dirname
        });

        copyProcess.on('close', (copyCode) => {
          if (copyCode === 0) {
            console.log('✅ Preload script copied');
            
            // Start Electron
            console.log('🚀 Starting Electron...');
            const electronProcess = spawn('npx', ['electron', '.', '--dev'], {
              stdio: 'inherit',
              cwd: __dirname
            });

            electronProcess.on('error', (err) => {
              console.error('❌ Failed to start Electron:', err);
            });

            electronProcess.on('close', (electronCode) => {
              console.log(`Electron process exited with code ${electronCode}`);
              // Kill Next.js when Electron closes
              nextProcess.kill();
              process.exit(electronCode);
            });
          } else {
            console.error('❌ Failed to copy preload script');
            nextProcess.kill();
            process.exit(1);
          }
        });
      } else {
        console.error('❌ Electron compilation failed');
        nextProcess.kill();
        process.exit(1);
      }
    });
  }, 5000); // Wait 5 seconds for Next.js to start
});
