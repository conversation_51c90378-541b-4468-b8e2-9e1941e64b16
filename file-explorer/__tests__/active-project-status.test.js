/**
 * TEST SCRIPT: Check Active Project Status
 * 
 * This script checks if there's an active project set and investigates
 * why agents might not be creating files.
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 INVESTIGATING ACTIVE PROJECT STATUS\n');

// Check if localStorage has an active project
console.log('📋 Checking localStorage for active project...');
try {
  // Simulate localStorage check (this would normally be in browser)
  console.log('⚠️ Cannot check localStorage from Node.js - this needs to be checked in the Electron app');
} catch (error) {
  console.log('❌ Failed to check localStorage:', error.message);
}

// Check if there are any project directories in the current workspace
console.log('\n📁 Checking for project directories in current workspace...');
const currentDir = process.cwd();
console.log('Current working directory:', currentDir);

try {
  const items = fs.readdirSync(currentDir);
  const directories = items.filter(item => {
    try {
      return fs.statSync(path.join(currentDir, item)).isDirectory() && !item.startsWith('.');
    } catch {
      return false;
    }
  });
  
  console.log('Available directories:', directories);
  
  // Check for project indicators
  const projectIndicators = [
    'package.json',
    '.project',
    'tsconfig.json',
    'next.config.js',
    'vite.config.js',
    'src',
    'components'
  ];
  
  console.log('\n🔍 Checking for project indicators in current directory...');
  projectIndicators.forEach(indicator => {
    const exists = fs.existsSync(path.join(currentDir, indicator));
    console.log(`${exists ? '✅' : '❌'} ${indicator}: ${exists ? 'Found' : 'Not found'}`);
  });
  
  // Check subdirectories for project indicators
  console.log('\n🔍 Checking subdirectories for project indicators...');
  directories.slice(0, 5).forEach(dir => { // Check first 5 directories
    const dirPath = path.join(currentDir, dir);
    console.log(`\n📂 Checking ${dir}/`);
    
    projectIndicators.forEach(indicator => {
      const exists = fs.existsSync(path.join(dirPath, indicator));
      if (exists) {
        console.log(`  ✅ ${indicator}: Found`);
      }
    });
  });
  
} catch (error) {
  console.log('❌ Failed to read current directory:', error.message);
}

// Check if there are any recent projects in common locations
console.log('\n🏠 Checking common project locations...');
const commonProjectPaths = [
  path.join(process.env.HOME || process.env.USERPROFILE || '', 'Projects'),
  path.join(process.env.HOME || process.env.USERPROFILE || '', 'Documents'),
  path.join(process.env.HOME || process.env.USERPROFILE || '', 'Desktop'),
  '/tmp/test-project',
  './test-project'
];

commonProjectPaths.forEach(projectPath => {
  try {
    if (fs.existsSync(projectPath)) {
      const stat = fs.statSync(projectPath);
      if (stat.isDirectory()) {
        console.log(`✅ ${projectPath}: Directory exists`);
        
        // Check if it has project indicators
        const hasPackageJson = fs.existsSync(path.join(projectPath, 'package.json'));
        const hasProjectFile = fs.existsSync(path.join(projectPath, '.project'));
        const hasSrcDir = fs.existsSync(path.join(projectPath, 'src'));
        
        if (hasPackageJson || hasProjectFile || hasSrcDir) {
          console.log(`  🎯 Looks like a project! (package.json: ${hasPackageJson}, .project: ${hasProjectFile}, src/: ${hasSrcDir})`);
        }
      }
    } else {
      console.log(`❌ ${projectPath}: Does not exist`);
    }
  } catch (error) {
    console.log(`❌ ${projectPath}: Error checking - ${error.message}`);
  }
});

console.log('\n💡 RECOMMENDATIONS:');
console.log('1. Open the Electron app and check the Project Status Bar at the bottom');
console.log('2. If no project is active, use File Explorer to open or create a project');
console.log('3. Look for "No project open" in the status bar');
console.log('4. Use the Create Project Wizard or Open Project to set an active project');
console.log('5. Once a project is active, agents will be able to create files');

console.log('\n🔧 DEBUGGING STEPS:');
console.log('1. Start the Electron app: npm run electron:dev');
console.log('2. Check the Project Status Bar (bottom of the window)');
console.log('3. If no project is open, click File Explorer and create/open a project');
console.log('4. Try running an agent task after setting an active project');
console.log('5. Check the logs for "Agent execution blocked: no active project selected"');

console.log('\n📝 EXPECTED BEHAVIOR:');
console.log('- With NO active project: Agents should log "Agent execution blocked: no active project selected"');
console.log('- With active project: Agents should create files in the project directory');
console.log('- All file operations should be logged with the new validation system');

console.log('\n✅ INVESTIGATION COMPLETE');
console.log('The issue is likely that no active project is set, which blocks all agent file operations.');
console.log('This is actually CORRECT behavior - the system is working as designed for security.');
