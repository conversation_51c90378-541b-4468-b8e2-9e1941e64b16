# Test Files Directory

This directory contains test scripts that were moved from the root directory to comply with User Guidelines regarding test file organization.

## Moved Files

- `execution-validation.test.js` - Tests execution validation and logging enforcement
- `logging-upgrade.test.js` - Tests upgraded logging system functionality  
- `real-execution-flow.test.js` - Tests real agent execution flow with project validation
- `active-project-status.test.js` - Tests active project status checking

## Purpose

These files were originally standalone test scripts in the production codebase root. They have been moved here to:

1. Comply with User Guidelines prohibiting test files in production code
2. Maintain proper separation between test and production code
3. Keep test functionality available for development purposes

## Usage

These are Node.js test scripts that can be run independently:

```bash
node __tests__/execution-validation.test.js
node __tests__/logging-upgrade.test.js
node __tests__/real-execution-flow.test.js
node __tests__/active-project-status.test.js
```

## Note

These scripts simulate various system behaviors and are intended for development and debugging purposes only.
