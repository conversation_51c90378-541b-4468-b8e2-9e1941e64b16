#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');

// Start Next.js development server
const nextBin = path.join(__dirname, 'node_modules', 'next', 'dist', 'bin', 'next');
const nextProcess = spawn('node', [nextBin, 'dev', '-p', '4444'], {
  stdio: 'inherit',
  cwd: __dirname
});

nextProcess.on('error', (err) => {
  console.error('Failed to start Next.js:', err);
  process.exit(1);
});

nextProcess.on('exit', (code) => {
  console.log(`Next.js exited with code ${code}`);
  process.exit(code);
});

// Handle process termination
process.on('SIGINT', () => {
  nextProcess.kill('SIGINT');
});

process.on('SIGTERM', () => {
  nextProcess.kill('SIGTERM');
});
