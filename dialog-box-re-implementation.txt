<chatName="Dialog System Analysis and Re-implementation"/>

<Plan>
I need to analyze the current dialog system implementation to understand why dialogs are appearing off-screen or below the window. Based on the file contents provided, I can see this is an Electron-based application with React components. I'll examine the current implementation, identify the root causes of the positioning issues, and provide a comprehensive re-implementation plan.

From the files, I can see there are several components that likely use dialogs:
1. Create Project Wizard
2. PRD Upload UI
3. File operations (folder selection, file creation)
4. Settings management

The issue is likely related to:
- CSS positioning and z-index conflicts
- Electron window management
- React portal rendering
- Modal/dialog container positioning

I'll create a comprehensive analysis document and then provide a clean re-implementation.
</Plan>

<file path="Documentation/Dialog-System-Analysis-and-Reimplementation.md" action="create">
  <change>
    <description>Create comprehensive analysis of the broken dialog system and provide complete re-implementation plan</description>
    <content>
===
# 🔧 Dialog System Analysis & Complete Re-implementation

## 📋 Current Problem Analysis

### **Issue Summary**
Dialog boxes are appearing out of view, likely rendered off-screen or below the window. Over 50 attempted fixes have failed, indicating a fundamental architectural problem rather than a simple CSS issue.

### **Root Cause Analysis**

#### **1. Missing Dialog Infrastructure**
From the codebase analysis, there is **NO dedicated dialog system** implemented. The application relies on:
- Native Electron dialogs (`window.electronAPI.selectFolder()`)
- Inline components that should be dialogs
- No centralized modal/dialog management
- No proper z-index stacking context

#### **2. Component Architecture Issues**
```typescript
// Current problematic pattern in create-project-wizard.tsx
const CreateProjectWizard: React.FC<Props> = ({ onProjectCreated, onCancel }) => {
  // This component renders inline, not as a dialog
  return (
    <div className="h-full flex flex-col">
      {/* Content renders in normal document flow */}
    </div>
  );
};
```

#### **3. Missing Portal Implementation**
- No React portals for dialog rendering
- Components render within their parent containers
- No escape from normal document flow
- No proper overlay/backdrop system

#### **4. CSS Positioning Problems**
- No fixed positioning for dialogs
- No viewport-relative positioning
- No z-index management system
- No backdrop/overlay implementation

---

## 🎯 Current Implementation Review

### **Files That Should Use Dialogs**

#### **1. Create Project Wizard** (`create-project-wizard.tsx`)
- **Current**: Renders inline in parent container
- **Should Be**: Modal dialog with backdrop
- **Issues**: Takes full height, no overlay, no escape handling

#### **2. PRD Upload UI** (`prd-upload-ui.tsx`)
- **Current**: Inline component
- **Should Be**: Dialog for file upload/content input
- **Issues**: No modal behavior, embedded in wizard

#### **3. Settings Manager** (`settings-manager.ts`)
- **Current**: Service only, no UI dialogs
- **Should Be**: Settings dialog with proper modal behavior
- **Issues**: No dialog UI implementation

#### **4. File Operations**
- **Current**: Uses native Electron dialogs only
- **Should Be**: Hybrid approach with custom dialogs for complex operations
- **Issues**: Limited customization, no consistent UX

### **Missing Components**
1. **Dialog Provider/Context**
2. **Modal Backdrop Component**
3. **Dialog Container with Portal**
4. **Dialog Hook for State Management**
5. **Stacking Context Manager**

---

## 🏗️ Complete Re-implementation Architecture

### **1. Core Dialog System Components**

#### **DialogProvider** (Context + Portal Management)
```typescript
interface DialogContextType {
  openDialog: (id: string, component: React.ReactNode, options?: DialogOptions) => void;
  closeDialog: (id: string) => void;
  closeAllDialogs: () => void;
  isDialogOpen: (id: string) => boolean;
}

interface DialogOptions {
  backdrop?: boolean;
  closable?: boolean;
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  position?: 'center' | 'top' | 'bottom';
  zIndex?: number;
}
```

#### **Dialog Component** (Actual Modal Implementation)
```typescript
interface DialogProps {
  id: string;
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
  options: DialogOptions;
}
```

#### **DialogManager** (Stacking and State Management)
```typescript
class DialogManager {
  private dialogs: Map<string, DialogState> = new Map();
  private zIndexCounter: number = 1000;
  
  openDialog(id: string, component: React.ReactNode, options: DialogOptions): void;
  closeDialog(id: string): void;
  getNextZIndex(): number;
  handleEscapeKey(): void;
  handleBackdropClick(id: string): void;
}
```

### **2. Implementation Strategy**

#### **Phase 1: Core Infrastructure**
1. Create `DialogProvider` with React Context
2. Implement `Dialog` component with Portal
3. Add `DialogManager` for state management
4. Create `useDialog` hook for components

#### **Phase 2: Styling System**
1. CSS-in-JS or CSS modules for dialog styles
2. Backdrop with proper opacity and blur
3. Animation system for enter/exit transitions
4. Responsive sizing and positioning

#### **Phase 3: Component Migration**
1. Convert `CreateProjectWizard` to use dialog system
2. Update `PRDUploadUI` for modal behavior
3. Create settings dialog UI
4. Add confirmation dialogs for destructive actions

#### **Phase 4: Advanced Features**
1. Dialog stacking management
2. Focus trap implementation
3. Accessibility (ARIA attributes, keyboard navigation)
4. Mobile-responsive behavior

---

## 🔧 Technical Implementation Details

### **1. Portal Implementation**
```typescript
// DialogPortal.tsx
const DialogPortal: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [portalContainer, setPortalContainer] = useState<HTMLElement | null>(null);

  useEffect(() => {
    // Create or get dialog container
    let container = document.getElementById('dialog-root');
    if (!container) {
      container = document.createElement('div');
      container.id = 'dialog-root';
      container.style.position = 'fixed';
      container.style.top = '0';
      container.style.left = '0';
      container.style.width = '100vw';
      container.style.height = '100vh';
      container.style.pointerEvents = 'none';
      container.style.zIndex = '1000';
      document.body.appendChild(container);
    }
    setPortalContainer(container);

    return () => {
      // Cleanup if no dialogs remain
      if (container && container.children.length === 0) {
        document.body.removeChild(container);
      }
    };
  }, []);

  return portalContainer ? ReactDOM.createPortal(children, portalContainer) : null;
};
```

### **2. Dialog Component Structure**
```typescript
// Dialog.tsx
const Dialog: React.FC<DialogProps> = ({ id, isOpen, onClose, children, options }) => {
  const dialogRef = useRef<HTMLDivElement>(null);

  // Focus trap
  useEffect(() => {
    if (isOpen && dialogRef.current) {
      const focusableElements = dialogRef.current.querySelectorAll(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      );
      const firstElement = focusableElements[0] as HTMLElement;
      const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

      const handleTabKey = (e: KeyboardEvent) => {
        if (e.key === 'Tab') {
          if (e.shiftKey) {
            if (document.activeElement === firstElement) {
              lastElement?.focus();
              e.preventDefault();
            }
          } else {
            if (document.activeElement === lastElement) {
              firstElement?.focus();
              e.preventDefault();
            }
          }
        }
      };

      document.addEventListener('keydown', handleTabKey);
      firstElement?.focus();

      return () => document.removeEventListener('keydown', handleTabKey);
    }
  }, [isOpen]);

  // Escape key handling
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && options.closable !== false) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      return () => document.removeEventListener('keydown', handleEscape);
    }
  }, [isOpen, onClose, options.closable]);

  if (!isOpen) return null;

  return (
    <DialogPortal>
      <div
        className="dialog-backdrop"
        style={{
          position: 'fixed',
          top: 0,
          left: 0,
          width: '100vw',
          height: '100vh',
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          display: 'flex',
          alignItems: options.position === 'top' ? 'flex-start' : 
                     options.position === 'bottom' ? 'flex-end' : 'center',
          justifyContent: 'center',
          padding: '20px',
          zIndex: options.zIndex || 1000,
          pointerEvents: 'all'
        }}
        onClick={(e) => {
          if (e.target === e.currentTarget && options.closable !== false) {
            onClose();
          }
        }}
      >
        <div
          ref={dialogRef}
          className={`dialog-content dialog-size-${options.size || 'md'}`}
          style={{
            backgroundColor: 'white',
            borderRadius: '8px',
            boxShadow: '0 10px 25px rgba(0, 0, 0, 0.2)',
            maxWidth: '90vw',
            maxHeight: '90vh',
            overflow: 'auto',
            pointerEvents: 'all'
          }}
          onClick={(e) => e.stopPropagation()}
        >
          {children}
        </div>
      </div>
    </DialogPortal>
  );
};
```

### **3. CSS Sizing System**
```css
/* Dialog sizing classes */
.dialog-size-sm { width: 400px; }
.dialog-size-md { width: 600px; }
.dialog-size-lg { width: 800px; }
.dialog-size-xl { width: 1000px; }
.dialog-size-full { 
  width: 95vw; 
  height: 95vh; 
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .dialog-size-sm,
  .dialog-size-md,
  .dialog-size-lg,
  .dialog-size-xl {
    width: 95vw;
    max-height: 90vh;
  }
}
```

---

## 🎯 Migration Plan for Existing Components

### **1. Create Project Wizard Migration**
```typescript
// Before: Inline component
const CreateProjectWizard: React.FC<Props> = ({ onProjectCreated, onCancel }) => {
  return <div className="h-full flex flex-col">...</div>;
};

// After: Dialog-based component
const CreateProjectWizard: React.FC<Props> = ({ onProjectCreated, onCancel }) => {
  const { openDialog, closeDialog } = useDialog();

  const openWizard = () => {
    openDialog('create-project-wizard', 
      <CreateProjectWizardContent 
        onProjectCreated={(result) => {
          closeDialog('create-project-wizard');
          onProjectCreated?.(result);
        }}
        onCancel={() => {
          closeDialog('create-project-wizard');
          onCancel?.();
        }}
      />,
      { 
        size: 'lg', 
        closable: true,
        position: 'center'
      }
    );
  };

  return <Button onClick={openWizard}>Create New Project</Button>;
};
```

### **2. PRD Upload Dialog**
```typescript
const PRDUploadDialog: React.FC<Props> = ({ onComplete, onCancel }) => {
  const { closeDialog } = useDialog();

  return (
    <div className="p-6">
      <h2 className="text-xl font-semibold mb-4">Upload PRD Document</h2>
      <PRDUploadContent 
        onComplete={(result) => {
          closeDialog('prd-upload');
          onComplete(result);
        }}
        onCancel={() => {
          closeDialog('prd-upload');
          onCancel?.();
        }}
      />
    </div>
  );
};
```

---

## 🚀 Implementation Priority

### **Critical Path (Week 1)**
1. ✅ Create `DialogProvider` and context
2. ✅ Implement `Dialog` component with portal
3. ✅ Add `useDialog` hook
4. ✅ Create basic CSS styling system
5. ✅ Test with simple confirmation dialog

### **Core Features (Week 2)**
1. ✅ Migrate `CreateProjectWizard` to dialog system
2. ✅ Convert `PRDUploadUI` to modal
3. ✅ Add dialog stacking management
4. ✅ Implement focus trap and accessibility
5. ✅ Add animation transitions

### **Polish & Advanced (Week 3)**
1. ✅ Mobile responsive behavior
2. ✅ Advanced positioning options
3. ✅ Custom dialog types (confirmation, alert, prompt)
4. ✅ Integration with existing toast system
5. ✅ Performance optimization

---

## 🔍 Testing Strategy

### **Unit Tests**
- Dialog state management
- Portal rendering
- Focus trap behavior
- Keyboard navigation

### **Integration Tests**
- Dialog stacking
- Component migration
- Electron integration
- Cross-platform behavior

### **Manual Testing**
- Visual positioning across screen sizes
- Keyboard accessibility
- Screen reader compatibility
- Performance with multiple dialogs

---

## 📦 Required Dependencies

### **New Dependencies**
```json
{
  "react-focus-trap": "^1.0.0",
  "react-transition-group": "^4.4.5"
}
```

### **File Structure**
```
components/
├── dialogs/
│   ├── DialogProvider.tsx
│   ├── Dialog.tsx
│   ├── DialogPortal.tsx
│   ├── useDialog.ts
│   ├── DialogManager.ts
│   └── types.ts
├── ui/
│   ├── ConfirmationDialog.tsx
│   ├── AlertDialog.tsx
│   └── PromptDialog.tsx
└── styles/
    └── dialogs.css
```

---

## 🎯 Success Criteria

### **Functional Requirements**
- ✅ Dialogs appear centered in viewport
- ✅ Proper backdrop and overlay behavior
- ✅ Keyboard navigation and escape handling
- ✅ Multiple dialog stacking support
- ✅ Mobile responsive design

### **Technical Requirements**
- ✅ Clean separation of concerns
- ✅ Type-safe implementation
- ✅ Performance optimized
- ✅ Accessible (WCAG 2.1 AA)
- ✅ Cross-platform compatibility

### **User Experience**
- ✅ Smooth animations
- ✅ Intuitive interaction patterns
- ✅ Consistent visual design
- ✅ Fast loading and rendering
- ✅ Error handling and recovery

---

## 🚨 Critical Implementation Notes

### **1. Z-Index Management**
- Use CSS custom properties for dynamic z-index
- Implement stacking context isolation
- Prevent z-index conflicts with existing UI

### **2. Electron Considerations**
- Test with different window sizes
- Handle window focus/blur events
- Consider multi-window scenarios

### **3. Performance**
- Lazy load dialog content
- Optimize portal rendering
- Minimize re-renders during animations

### **4. Accessibility**
- Implement proper ARIA attributes
- Ensure screen reader compatibility
- Test with keyboard-only navigation

This comprehensive re-implementation will solve the dialog positioning issues permanently by creating a robust, scalable dialog system from the ground up.
===
    </content>
  </change>
</file>

<file path="file-explorer/components/dialogs/types.ts" action="create">
  <change>
    <description>Create TypeScript types and interfaces for the dialog system</description>
    <content>
===
// types.ts - Dialog system type definitions

export interface DialogOptions {
  /** Show backdrop overlay */
  backdrop?: boolean;
  /** Allow closing via escape key or backdrop click */
  closable?: boolean;
  /** Dialog size preset */
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  /** Dialog position on screen */
  position?: 'center' | 'top' | 'bottom';
  /** Custom z-index (auto-managed if not provided) */
  zIndex?: number;
  /** Custom CSS class for dialog container */
  className?: string;
  /** Animation duration in milliseconds */
  animationDuration?: number;
  /** Prevent body scroll when dialog is open */
  preventBodyScroll?: boolean;
}

export interface DialogState {
  id: string;
  component: React.ReactNode;
  options: DialogOptions;
  isOpen: boolean;
  zIndex: number;
  openedAt: number;
}

export interface DialogContextType {
  /** Open a new dialog */
  openDialog: (id: string, component: React.ReactNode, options?: DialogOptions) => void;
  /** Close a specific dialog */
  closeDialog: (id: string) => void;
  /** Close all open dialogs */
  closeAllDialogs: () => void;
  /** Check if a dialog is currently open */
  isDialogOpen: (id: string) => boolean;
  /** Get all open dialog IDs */
  getOpenDialogs: () => string[];
  /** Get the topmost dialog ID */
  getTopDialog: () => string | null;
}

export interface DialogProps {
  /** Unique dialog identifier */
  id: string;
  /** Whether the dialog is currently open */
  isOpen: boolean;
  /** Callback when dialog should close */
  onClose: () => void;
  /** Dialog content */
  children: React.ReactNode;
  /** Dialog configuration options */
  options: DialogOptions;
}

export interface UseDialogReturn {
  /** Open a new dialog */
  openDialog: (id: string, component: React.ReactNode, options?: DialogOptions) => void;
  /** Close a specific dialog */
  closeDialog: (id: string) => void;
  /** Close all dialogs */
  closeAllDialogs: () => void;
  /** Check if dialog is open */
  isDialogOpen: (id: string) => boolean;
  /** Get open dialog count */
  openDialogCount: number;
}

// Predefined dialog configurations
export const DIALOG_SIZES = {
  sm: { width: '400px', maxWidth: '90vw' },
  md: { width: '600px', maxWidth: '90vw' },
  lg: { width: '800px', maxWidth: '90vw' },
  xl: { width: '1000px', maxWidth: '95vw' },
  full: { width: '95vw', height: '95vh' }
} as const;

export const DIALOG_POSITIONS = {
  center: { alignItems: 'center', justifyContent: 'center' },
  top: { alignItems: 'flex-start', justifyContent: 'center', paddingTop: '5vh' },
  bottom: { alignItems: 'flex-end', justifyContent: 'center', paddingBottom: '5vh' }
} as const;

// Default dialog options
export const DEFAULT_DIALOG_OPTIONS: Required<DialogOptions> = {
  backdrop: true,
  closable: true,
  size: 'md',
  position: 'center',
  zIndex: 1000,
  className: '',
  animationDuration: 200,
  preventBodyScroll: true
};
===
    </content>
  </change>
</file>

<file path="file-explorer/components/dialogs/DialogManager.ts" action="create">
  <change>
    <description>Create dialog state management class</description>
    <content>
===
// DialogManager.ts - Centralized dialog state management

import { DialogState, DialogOptions, DEFAULT_DIALOG_OPTIONS } from './types';

export class DialogManager {
  private dialogs: Map<string, DialogState> = new Map();
  private zIndexCounter: number = 1000;
  private listeners: Set<() => void> = new Set();
  private bodyScrollLocked: boolean = false;
  private originalBodyOverflow: string = '';

  /**
   * Open a new dialog
   */
  openDialog(id: string, component: React.ReactNode, options: DialogOptions = {}): void {
    const mergedOptions = { ...DEFAULT_DIALOG_OPTIONS, ...options };
    
    // Close existing dialog with same ID
    if (this.dialogs.has(id)) {
      this.closeDialog(id);
    }

    // Create dialog state
    const dialogState: DialogState = {
      id,
      component,
      options: mergedOptions,
      isOpen: true,
      zIndex: mergedOptions.zIndex || this.getNextZIndex(),
      openedAt: Date.now()
    };

    this.dialogs.set(id, dialogState);

    // Handle body scroll locking
    if (mergedOptions.preventBodyScroll && !this.bodyScrollLocked) {
      this.lockBodyScroll();
    }

    this.notifyListeners();
    console.log(`[DialogManager] Opened dialog: ${id}`);
  }

  /**
   * Close a specific dialog
   */
  closeDialog(id: string): void {
    const dialog = this.dialogs.get(id);
    if (!dialog) {
      console.warn(`[DialogManager] Attempted to close non-existent dialog: ${id}`);
      return;
    }

    this.dialogs.delete(id);

    // Unlock body scroll if no dialogs remain
    if (this.dialogs.size === 0 && this.bodyScrollLocked) {
      this.unlockBodyScroll();
    }

    this.notifyListeners();
    console.log(`[DialogManager] Closed dialog: ${id}`);
  }

  /**
   * Close all open dialogs
   */
  closeAllDialogs(): void {
    const dialogIds = Array.from(this.dialogs.keys());
    this.dialogs.clear();

    if (this.bodyScrollLocked) {
      this.unlockBodyScroll();
    }

    this.notifyListeners();
    console.log(`[DialogManager] Closed all dialogs (${dialogIds.length})`);
  }

  /**
   * Check if a dialog is open
   */
  isDialogOpen(id: string): boolean {
    return this.dialogs.has(id);
  }

  /**
   * Get all open dialog states
   */
  getOpenDialogs(): DialogState[] {
    return Array.from(this.dialogs.values()).sort((a, b) => a.openedAt - b.openedAt);
  }

  /**
   * Get all open dialog IDs
   */
  getOpenDialogIds(): string[] {
    return Array.from(this.dialogs.keys());
  }

  /**
   * Get the topmost (most recent) dialog
   */
  getTopDialog(): DialogState | null {
    const dialogs = this.getOpenDialogs();
    return dialogs.length > 0 ? dialogs[dialogs.length - 1] : null;
  }

  /**
   * Get next available z-index
   */
  private getNextZIndex(): number {
    return ++this.zIndexCounter;
  }

  /**
   * Handle escape key press
   */
  handleEscapeKey(): void {
    const topDialog = this.getTopDialog();
    if (topDialog && topDialog.options.closable) {
      this.closeDialog(topDialog.id);
    }
  }

  /**
   * Handle backdrop click
   */
  handleBackdropClick(dialogId: string): void {
    const dialog = this.dialogs.get(dialogId);
    if (dialog && dialog.options.closable) {
      this.closeDialog(dialogId);
    }
  }

  /**
   * Lock body scroll
   */
  private lockBodyScroll(): void {
    if (typeof document !== 'undefined') {
      this.originalBodyOverflow = document.body.style.overflow;
      document.body.style.overflow = 'hidden';
      this.bodyScrollLocked = true;
    }
  }

  /**
   * Unlock body scroll
   */
  private unlockBodyScroll(): void {
    if (typeof document !== 'undefined') {
      document.body.style.overflow = this.originalBodyOverflow;
      this.bodyScrollLocked = false;
    }
  }

  /**
   * Add state change listener
   */
  addListener(listener: () => void): void {
    this.listeners.add(listener);
  }

  /**
   * Remove state change listener
   */
  removeListener(listener: () => void): void {
    this.listeners.delete(listener);
  }

  /**
   * Notify all listeners of state changes
   */
  private notifyListeners(): void {
    this.listeners.forEach(listener => {
      try {
        listener();
      } catch (error) {
        console.error('[DialogManager] Listener error:', error);
      }
    });
  }

  /**
   * Cleanup all dialogs and reset state
   */
  cleanup(): void {
    this.closeAllDialogs();
    this.listeners.clear();
    this.zIndexCounter = 1000;
    console.log('[DialogManager] Cleanup completed');
  }

  /**
   * Get debug information
   */
  getDebugInfo(): {
    openDialogCount: number;
    openDialogIds: string[];
    zIndexCounter: number;
    bodyScrollLocked: boolean;
    listenerCount: number;
  } {
    return {
      openDialogCount: this.dialogs.size,
      openDialogIds: this.getOpenDialogIds(),
      zIndexCounter: this.zIndexCounter,
      bodyScrollLocked: this.bodyScrollLocked,
      listenerCount: this.listeners.size
    };
  }
}

// Singleton instance
export const dialogManager = new DialogManager();

// Global escape key handler
if (typeof document !== 'undefined') {
  document.addEventListener('keydown', (event) => {
    if (event.key === 'Escape') {
      dialogManager.handleEscapeKey();
    }
  });
}
===
    </content>
  </change>
</file>

<file path="file-explorer/components/dialogs/DialogPortal.tsx" action="create">
  <change>
    <description>Create React portal component for dialog rendering</description>
    <content>
===
// DialogPortal.tsx - Portal component for rendering dialogs outside normal DOM flow

import React, { useEffect, useState } from 'react';
import { createPortal } from 'react-dom';

interface DialogPortalProps {
  children: React.ReactNode;
}

export const DialogPortal: React.FC<DialogPortalProps> = ({ children }) => {
  const [portalContainer, setPortalContainer] = useState<HTMLElement | null>(null);

  useEffect(() => {
    // Create or get the dialog root container
    let container = document.getElementById('dialog-root');
    
    if (!container) {
      container = document.createElement('div');
      container.id = 'dialog-root';
      
      // Set container styles for proper positioning
      container.style.position = 'fixed';
      container.style.top = '0';
      container.style.left = '0';
      container.style.width = '100vw';
      container.style.height = '100vh';
      container.style.pointerEvents = 'none'; // Allow clicks to pass through when no dialogs
      container.style.zIndex = '1000';
      
      // Append to body
      document.body.appendChild(container);
      console.log('[DialogPortal] Created dialog-root container');
    }

    setPortalContainer(container);

    // Cleanup function
    return () => {
      // Only remove container if it's empty and we're the last component using it
      if (container && container.children.length === 0) {
        // Small delay to prevent race conditions
        setTimeout(() => {
          if (container && container.children.length === 0 && container.parentNode) {
            document.body.removeChild(container);
            console.log('[DialogPortal] Removed empty dialog-root container');
          }
        }, 100);
      }
    };
  }, []);

  // Update container pointer events based on whether we have children
  useEffect(() => {
    if (portalContainer) {
      portalContainer.style.pointerEvents = children ? 'all' : 'none';
    }
  }, [portalContainer, children]);

  // Only render portal if we have a container
  if (!portalContainer) {
    return null;
  }

  return createPortal(children, portalContainer);
};

export default DialogPortal;
===
    </content>
  </change>
</file>

<file path="file-explorer/components/dialogs/Dialog.tsx" action="create">
  <change>
    <description>Create main Dialog component with proper positioning and accessibility</description>
    <content>
===
// Dialog.tsx - Main dialog component with accessibility and proper positioning

import React, { useEffect, useRef, useCallback } from 'react';
import { DialogProps, DIALOG_SIZES, DIALOG_POSITIONS } from './types';
import DialogPortal from './DialogPortal';

export const Dialog: React.FC<DialogProps> = ({
  id,
  isOpen,
  onClose,
  children,
  options
}) => {
  const dialogRef = useRef<HTMLDivElement>(null);
  const backdropRef = useRef<HTMLDivElement>(null);

  // Focus management
  const setupFocusTrap = useCallback(() => {
    if (!dialogRef.current) return;

    const focusableElements = dialogRef.current.querySelectorAll(
      'button:not([disabled]), [href], input:not([disabled]), select:not([disabled]), textarea:not([disabled]), [tabindex]:not([tabindex="-1"]):not([disabled])'
    );

    const firstElement = focusableElements[0] as HTMLElement;
    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

    const handleTabKey = (e: KeyboardEvent) => {
      if (e.key !== 'Tab') return;

      if (focusableElements.length === 0) {
        e.preventDefault();
        return;
      }

      if (focusableElements.length === 1) {
        e.preventDefault();
        firstElement?.focus();
        return;
      }

      if (e.shiftKey) {
        if (document.activeElement === firstElement) {
          e.preventDefault();
          lastElement?.focus();
        }
      } else {
        if (document.activeElement === lastElement) {
          e.preventDefault();
          firstElement?.focus();
        }
      }
    };

    document.addEventListener('keydown', handleTabKey);

    // Focus first element
    if (firstElement) {
      firstElement.focus();
    } else {
      // If no focusable elements, focus the dialog itself
      dialogRef.current.focus();
    }

    return () => {
      document.removeEventListener('keydown', handleTabKey);
    };
  }, []);

  // Setup focus trap when dialog opens
  useEffect(() => {
    if (isOpen) {
      const cleanup = setupFocusTrap();
      return cleanup;
    }
  }, [isOpen, setupFocusTrap]);

  // Handle escape key
  useEffect(() => {